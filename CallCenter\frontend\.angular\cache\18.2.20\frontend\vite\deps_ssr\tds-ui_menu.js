import { createRequire } from 'module';const require = createRequire(import.meta.url);
import {
  TDSMenuComponent,
  TDSMenuFooterDirective,
  TDSMenuGroupInlineComponent,
  TDSMenuGroupPopupComponent,
  TDSMenuItemComponent,
  TDSMenuModule,
  TDSMenuService,
  TDSMenuServiceFactory
} from "./chunk-5CVR7W7N.js";
import "./chunk-LICHUDVX.js";
import "./chunk-WKYNVKBH.js";
import "./chunk-NQSZR37A.js";
import "./chunk-OMWWZ65K.js";
import "./chunk-6BGFCIZB.js";
import "./chunk-4PCOC6ME.js";
import "./chunk-O2K6NUWL.js";
import "./chunk-D3JV2RY4.js";
import "./chunk-A2D67SU4.js";
import "./chunk-VVZCKIK2.js";
import "./chunk-PFNSG66E.js";
import "./chunk-NCYSEW5N.js";
import "./chunk-NQ4HTGF6.js";
export {
  TDSMenuComponent,
  TDSMenuFooterDirective,
  TDSMenuGroupInlineComponent,
  TDSMenuGroupPopupComponent,
  TDSMenuItemComponent,
  TDSMenuModule,
  TDSMenuService,
  TDSMenuServiceFactory
};
//# sourceMappingURL=tds-ui_menu.js.map
