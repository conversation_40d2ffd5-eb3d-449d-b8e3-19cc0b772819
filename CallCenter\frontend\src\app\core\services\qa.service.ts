import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { 
  QAAssessment, 
  QATemplate, 
  QASummary, 
  QAAssessmentStatus 
} from '../../shared/models/qa.interface';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class QAService {
  private readonly apiUrl = `${environment.apiUrl}/qa`;
  private assessmentsSubject = new BehaviorSubject<QAAssessment[]>([]);
  public assessments$ = this.assessmentsSubject.asObservable();

  constructor(private http: HttpClient) {}

  /**
   * Get QA assessments with optional filtering
   */
  getAssessments(
    page: number = 1, 
    pageSize: number = 20, 
    filters?: {
      agentIds?: string[];
      assessorIds?: string[];
      status?: QAAssessmentStatus[];
      dateFrom?: Date;
      dateTo?: Date;
    }
  ): Observable<{ assessments: QAAssessment[]; total: number }> {
    let params = new HttpParams()
      .set('page', page.toString())
      .set('pageSize', pageSize.toString());

    if (filters) {
      if (filters.agentIds?.length) {
        params = params.set('agentIds', filters.agentIds.join(','));
      }
      if (filters.assessorIds?.length) {
        params = params.set('assessorIds', filters.assessorIds.join(','));
      }
      if (filters.status?.length) {
        params = params.set('status', filters.status.join(','));
      }
      if (filters.dateFrom) {
        params = params.set('dateFrom', filters.dateFrom.toISOString());
      }
      if (filters.dateTo) {
        params = params.set('dateTo', filters.dateTo.toISOString());
      }
    }

    return this.http.get<{ assessments: QAAssessment[]; total: number }>(`${this.apiUrl}/assessments`, { params }).pipe(
      map(response => ({
        ...response,
        assessments: response.assessments.map(assessment => ({
          ...assessment,
          createdAt: new Date(assessment.createdAt),
          updatedAt: new Date(assessment.updatedAt),
          completedAt: assessment.completedAt ? new Date(assessment.completedAt) : undefined
        }))
      })),
      catchError(this.handleError)
    );
  }

  /**
   * Get a single QA assessment by ID
   */
  getAssessmentById(id: string): Observable<QAAssessment> {
    return this.http.get<QAAssessment>(`${this.apiUrl}/assessments/${id}`).pipe(
      map(assessment => ({
        ...assessment,
        createdAt: new Date(assessment.createdAt),
        updatedAt: new Date(assessment.updatedAt),
        completedAt: assessment.completedAt ? new Date(assessment.completedAt) : undefined
      })),
      catchError(this.handleError)
    );
  }

  /**
   * Create a new QA assessment
   */
  createAssessment(callId: string, templateId: string): Observable<QAAssessment> {
    return this.http.post<QAAssessment>(`${this.apiUrl}/assessments`, {
      callId,
      templateId
    }).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Update QA assessment
   */
  updateAssessment(id: string, assessment: Partial<QAAssessment>): Observable<QAAssessment> {
    return this.http.put<QAAssessment>(`${this.apiUrl}/assessments/${id}`, assessment).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Submit QA assessment for review
   */
  submitAssessment(id: string): Observable<QAAssessment> {
    return this.http.patch<QAAssessment>(`${this.apiUrl}/assessments/${id}/submit`, {}).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Get QA templates
   */
  getTemplates(): Observable<QATemplate[]> {
    return this.http.get<QATemplate[]>(`${this.apiUrl}/templates`).pipe(
      map(templates => templates.map(template => ({
        ...template,
        createdAt: new Date(template.createdAt),
        updatedAt: new Date(template.updatedAt)
      }))),
      catchError(this.handleError)
    );
  }

  /**
   * Get QA summary/dashboard data
   */
  getQASummary(dateFrom?: Date, dateTo?: Date): Observable<QASummary> {
    let params = new HttpParams();
    
    if (dateFrom) {
      params = params.set('dateFrom', dateFrom.toISOString());
    }
    if (dateTo) {
      params = params.set('dateTo', dateTo.toISOString());
    }

    return this.http.get<QASummary>(`${this.apiUrl}/summary`, { params }).pipe(
      map(summary => ({
        ...summary,
        trendData: summary.trendData.map(trend => ({
          ...trend,
          date: new Date(trend.date)
        }))
      })),
      catchError(this.handleError)
    );
  }

  private handleError = (error: any): Observable<never> => {
    console.error('QAService Error:', error);
    throw error;
  };
}
