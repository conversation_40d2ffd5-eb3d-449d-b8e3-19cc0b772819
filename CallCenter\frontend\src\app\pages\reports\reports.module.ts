import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { ReportsComponent } from './reports/reports.component';
import { AgentPerformanceChartComponent } from './agent-performance-chart/agent-performance-chart.component';
import { CallCategoryChartComponent } from './call-category-chart/call-category-chart.component';
import { DetailedMetricsTableComponent } from './detailed-metrics-table/detailed-metrics-table.component';
import { QualityTrendChartComponent } from './quality-trend-chart/quality-trend-chart.component';
import { ReportFilterComponent } from './report-filter/report-filter.component';

const routes: Routes = [
  {
    path: '',
    component: ReportsComponent
  }
];

@NgModule({
  declarations: [],
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    ReportsComponent,
    AgentPerformanceChartComponent,
    CallCategoryChartComponent,
    DetailedMetricsTableComponent,
    QualityTrendChartComponent,
    ReportFilterComponent
  ],
  exports: [RouterModule]
})
export class ReportsModule { }
