import { createRequire } from 'module';const require = createRequire(import.meta.url);
import {
  CDK_DRAG_CONFIG,
  CDK_DRAG_HANDLE,
  CDK_DRAG_PARENT,
  CDK_DRAG_PLACEHOLDER,
  CDK_DRAG_PREVIEW,
  CDK_DROP_LIST,
  CDK_DROP_LIST_GROUP,
  CdkDrag,
  CdkDragHandle,
  CdkDragPlaceholder,
  CdkDragPreview,
  CdkDropList,
  CdkDropListGroup,
  DragDrop,
  DragDropModule,
  DragDropRegistry,
  DragRef,
  DropListRef,
  copyArrayItem,
  moveItemInArray,
  transferArrayItem
} from "./chunk-5UVKNXL4.js";
import "./chunk-OMWWZ65K.js";
import "./chunk-6BGFCIZB.js";
import "./chunk-D3JV2RY4.js";
import "./chunk-PFNSG66E.js";
import "./chunk-NCYSEW5N.js";
import "./chunk-NQ4HTGF6.js";
export {
  CD<PERSON>_DRAG_CONFIG,
  CDK_DRAG_HANDLE,
  CDK_DRAG_PARENT,
  CDK_DRAG_PLACEHOLDER,
  CDK_DRAG_PREVIEW,
  CDK_DROP_LIST,
  CDK_DROP_LIST_GROUP,
  CdkDrag,
  CdkDragHandle,
  CdkDragPlaceholder,
  CdkDragPreview,
  CdkDropList,
  CdkDropListGroup,
  DragDrop,
  DragDropModule,
  DragDropRegistry,
  DragRef,
  DropListRef,
  copyArrayItem,
  moveItemInArray,
  transferArrayItem
};
//# sourceMappingURL=@angular_cdk_drag-drop.js.map
