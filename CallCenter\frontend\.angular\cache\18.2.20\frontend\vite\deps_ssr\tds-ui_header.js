import { createRequire } from 'module';const require = createRequire(import.meta.url);
import {
  TDSHeaderComponent,
  TDSHeaderDirective,
  TDSHeaderModule
} from "./chunk-ZROE2BFO.js";
import "./chunk-WKYNVKBH.js";
import "./chunk-5V3G56AG.js";
import "./chunk-NQSZR37A.js";
import "./chunk-5UVKNXL4.js";
import "./chunk-OMWWZ65K.js";
import "./chunk-6BGFCIZB.js";
import "./chunk-4PCOC6ME.js";
import "./chunk-O2K6NUWL.js";
import "./chunk-D3JV2RY4.js";
import "./chunk-HAA4PWAV.js";
import "./chunk-A2D67SU4.js";
import "./chunk-VVZCKIK2.js";
import "./chunk-PFNSG66E.js";
import "./chunk-NCYSEW5N.js";
import "./chunk-NQ4HTGF6.js";
export {
  TDSHeaderComponent,
  TDSHeaderDirective,
  TDSHeaderModule
};
//# sourceMappingURL=tds-ui_header.js.map
