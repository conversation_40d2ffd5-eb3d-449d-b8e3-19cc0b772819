{"version": 3, "sources": ["../../../../../../node_modules/tds-ui/fesm2022/tds-ui-dropdown.mjs"], "sourcesContent": ["import { __decorate } from 'tslib';\nimport { ESCAPE, hasModifier<PERSON>ey } from '@angular/cdk/keycodes';\nimport { TemplatePortal } from '@angular/cdk/portal';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, Directive, Input, Output, NgModule, inject, TemplateRef, Component, ViewEncapsulation, ChangeDetectionStrategy, ViewChild, Injectable } from '@angular/core';\nimport { Subject, BehaviorSubject, merge, fromEvent, EMPTY, combineLatest, Subscription } from 'rxjs';\nimport { mapTo, map, switchMap, filter, auditTime, distinctUntilChanged, takeUntil, first } from 'rxjs/operators';\nimport { POSITION_MAP } from 'tds-ui/core/overlay';\nimport { InputBoolean } from 'tds-ui/shared/utility';\nimport * as i1 from '@angular/cdk/overlay';\nimport { ConnectionPositionPair } from '@angular/cdk/overlay';\nimport * as i2 from '@angular/cdk/platform';\nimport * as i1$1 from 'tds-ui/menu';\nimport { TDSMenuService, TDSMenuModule } from 'tds-ui/menu';\nimport { slideMotion } from 'tds-ui/core/animation';\nimport { TDSNoAnimationDirective } from 'tds-ui/core/no-animation';\nimport { NgClass, NgStyle } from '@angular/common';\nimport * as i2$1 from '@angular/cdk/bidi';\nimport { fromEventOutsideAngular } from 'tds-ui/core/util';\nconst _c0 = [\"*\"];\nfunction TDSDropdownMenuComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 0);\n    i0.ɵɵlistener(\"@slideMotion.done\", function TDSDropdownMenuComponent_ng_template_0_Template_div_animation_slideMotion_done_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAnimationEvent($event));\n    })(\"mouseenter\", function TDSDropdownMenuComponent_ng_template_0_Template_div_mouseenter_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.setMouseState(true));\n    })(\"mouseleave\", function TDSDropdownMenuComponent_ng_template_0_Template_div_mouseleave_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.setMouseState(false));\n    });\n    i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2);\n    i0.ɵɵprojection(3);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"tds-dropdown-rtl\", ctx_r1.dir === \"rtl\");\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.overlayClassName)(\"ngStyle\", ctx_r1.overlayStyle)(\"@slideMotion\", undefined);\n  }\n}\nconst listOfPositions$1 = [POSITION_MAP.bottomLeft, POSITION_MAP.bottomRight, POSITION_MAP.topRight, POSITION_MAP.topLeft];\nclass TDSDropDownDirective {\n  setDropdownMenuValue(key, value) {\n    if (this.tdsDropdownMenu) {\n      this.tdsDropdownMenu.setValue(key, value);\n    }\n  }\n  constructor(elementRef, overlay, renderer, viewContainerRef, platform) {\n    this.elementRef = elementRef;\n    this.overlay = overlay;\n    this.renderer = renderer;\n    this.viewContainerRef = viewContainerRef;\n    this.platform = platform;\n    this.overlayRef = null;\n    this.destroy$ = new Subject();\n    this.positionStrategy = this.overlay.position().flexibleConnectedTo(this.elementRef.nativeElement).withLockedPosition().withTransformOriginOn('.tds-dropdown');\n    this.inputVisible$ = new BehaviorSubject(false);\n    this.trigger$ = new BehaviorSubject('hover');\n    this.overlayClose$ = new Subject();\n    this.tdsDropdownMenu = null;\n    this.trigger = 'hover';\n    this.matchWidthElement = null;\n    this.backdrop = false;\n    this.clickHide = true;\n    this.disabled = false;\n    this.visible = false;\n    this.autoClose = true;\n    this.overlayClassName = '';\n    this.overlayStyle = {};\n    this.placement = 'bottomLeft';\n    this.visibleChange = new EventEmitter();\n    this.tdsDropdownOutsideClick = new EventEmitter();\n  }\n  ngOnInit() {}\n  ngAfterViewInit() {\n    if (this.tdsDropdownMenu) {\n      const nativeElement = this.elementRef.nativeElement;\n      /** host mouse state **/\n      const hostMouseState$ = merge(fromEvent(nativeElement, 'mouseenter').pipe(mapTo(true)), fromEvent(nativeElement, 'mouseleave').pipe(mapTo(false)));\n      /** menu mouse state **/\n      const menuMouseState$ = this.tdsDropdownMenu.mouseState$;\n      /** merged mouse state **/\n      const mergedMouseState$ = merge(menuMouseState$, hostMouseState$);\n      /** host click state **/\n      const hostClickState$ = fromEvent(nativeElement, 'click').pipe(map(() => !this.visible));\n      /** visible state switch by trigger **/\n      const visibleStateByTrigger$ = this.trigger$.pipe(switchMap(trigger => {\n        if (trigger === 'hover') {\n          return mergedMouseState$;\n        } else if (trigger === 'click') {\n          return hostClickState$;\n        } else {\n          return EMPTY;\n        }\n      }));\n      const descendantMenuItemClick$ = this.tdsDropdownMenu.descendantMenuItemClick$.pipe(filter(() => this.clickHide), mapTo(false));\n      const domTriggerVisible$ = merge(visibleStateByTrigger$, descendantMenuItemClick$, this.overlayClose$).pipe(filter(() => !this.disabled));\n      const visible$ = merge(this.inputVisible$, domTriggerVisible$);\n      combineLatest([visible$, this.tdsDropdownMenu.isChildSubMenuOpen$]).pipe(map(([visible, sub]) => visible || sub), auditTime(150), distinctUntilChanged(), filter(() => this.platform.isBrowser), takeUntil(this.destroy$)).subscribe(visible => {\n        const element = this.matchWidthElement ? this.matchWidthElement.nativeElement : nativeElement;\n        const triggerWidth = element.getBoundingClientRect().width;\n        if (this.visible !== visible) {\n          this.visibleChange.emit(visible);\n        }\n        this.visible = visible;\n        if (visible) {\n          /** set up overlayRef **/\n          if (!this.overlayRef) {\n            /** new overlay **/\n            this.overlayRef = this.overlay.create({\n              positionStrategy: this.positionStrategy,\n              minWidth: triggerWidth,\n              disposeOnNavigation: true,\n              hasBackdrop: this.backdrop && this.trigger === 'click',\n              scrollStrategy: this.autoClose ? this.overlay.scrollStrategies.close() : this.overlay.scrollStrategies.reposition()\n            });\n            merge(this.overlayRef.backdropClick(), this.overlayRef.detachments(), this.overlayRef.outsidePointerEvents().pipe(filter(e => !this.elementRef.nativeElement.contains(e.target))), this.overlayRef.keydownEvents().pipe(filter(e => e.keyCode === ESCAPE && !hasModifierKey(e)))).pipe(filter(() => this.autoClose), takeUntil(this.destroy$)).subscribe(() => {\n              this.overlayClose$.next(false);\n            });\n            merge(this.overlayRef.backdropClick(), this.overlayRef.outsidePointerEvents().pipe(filter(e => !this.elementRef.nativeElement.contains(e.target)))).pipe(takeUntil(this.destroy$)).subscribe(e => {\n              this.tdsDropdownOutsideClick.next(e);\n            });\n          } else {\n            /** update overlay config **/\n            const overlayConfig = this.overlayRef.getConfig();\n            overlayConfig.minWidth = triggerWidth;\n          }\n          /** open dropdown with animation **/\n          this.positionStrategy.withPositions([POSITION_MAP[this.placement], ...listOfPositions$1]);\n          /** reset portal if needed **/\n          if (!this.portal || this.portal.templateRef !== this.tdsDropdownMenu.templateRef) {\n            this.portal = new TemplatePortal(this.tdsDropdownMenu.templateRef, this.viewContainerRef);\n          }\n          this.overlayRef.attach(this.portal);\n        } else {\n          /** detach overlayRef if needed **/\n          if (this.overlayRef) {\n            this.overlayRef.detach();\n          }\n        }\n      });\n      this.tdsDropdownMenu.animationStateChange$.pipe(takeUntil(this.destroy$)).subscribe(event => {\n        if (event.toState === 'void') {\n          if (this.overlayRef) {\n            this.overlayRef.dispose();\n          }\n          this.overlayRef = null;\n        }\n      });\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n    if (this.overlayRef) {\n      this.overlayRef.dispose();\n      this.overlayRef = null;\n    }\n  }\n  ngOnChanges(changes) {\n    const {\n      visible,\n      disabled,\n      overlayClassName,\n      overlayStyle,\n      trigger,\n      backdrop\n    } = changes;\n    if (trigger) {\n      this.trigger$.next(this.trigger);\n    }\n    if (visible) {\n      this.inputVisible$.next(this.visible);\n    }\n    if (disabled) {\n      const nativeElement = this.elementRef.nativeElement;\n      if (this.disabled) {\n        this.renderer.setAttribute(nativeElement, 'disabled', '');\n        this.inputVisible$.next(false);\n      } else {\n        this.renderer.removeAttribute(nativeElement, 'disabled');\n      }\n    }\n    if (overlayClassName) {\n      this.setDropdownMenuValue('overlayClassName', this.overlayClassName);\n    }\n    if (overlayStyle) {\n      this.setDropdownMenuValue('overlayStyle', this.overlayStyle);\n    }\n    if (backdrop) {\n      // console.warn('`backdrop` in dropdown component will be removed in 12.0.0, please use `hasBackdrop` instead.');\n    }\n  }\n  static {\n    this.ɵfac = function TDSDropDownDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSDropDownDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.Overlay), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i2.Platform));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TDSDropDownDirective,\n      selectors: [[\"\", \"tds-dropdown\", \"\"]],\n      hostAttrs: [1, \"tds-dropdown-trigger\"],\n      inputs: {\n        tdsDropdownMenu: \"tdsDropdownMenu\",\n        trigger: \"trigger\",\n        matchWidthElement: \"matchWidthElement\",\n        backdrop: \"backdrop\",\n        clickHide: \"clickHide\",\n        disabled: \"disabled\",\n        visible: \"visible\",\n        autoClose: \"autoClose\",\n        overlayClassName: \"overlayClassName\",\n        overlayStyle: \"overlayStyle\",\n        placement: \"placement\"\n      },\n      outputs: {\n        visibleChange: \"visibleChange\",\n        tdsDropdownOutsideClick: \"tdsDropdownOutsideClick\"\n      },\n      exportAs: [\"tdsDropdown\"],\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n__decorate([InputBoolean()], TDSDropDownDirective.prototype, \"backdrop\", void 0);\n__decorate([InputBoolean()], TDSDropDownDirective.prototype, \"clickHide\", void 0);\n__decorate([InputBoolean()], TDSDropDownDirective.prototype, \"disabled\", void 0);\n__decorate([InputBoolean()], TDSDropDownDirective.prototype, \"visible\", void 0);\n__decorate([InputBoolean()], TDSDropDownDirective.prototype, \"autoClose\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSDropDownDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[tds-dropdown]',\n      exportAs: 'tdsDropdown',\n      host: {\n        class: 'tds-dropdown-trigger'\n      },\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i1.Overlay\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i0.ViewContainerRef\n  }, {\n    type: i2.Platform\n  }], {\n    tdsDropdownMenu: [{\n      type: Input\n    }],\n    trigger: [{\n      type: Input\n    }],\n    matchWidthElement: [{\n      type: Input\n    }],\n    backdrop: [{\n      type: Input\n    }],\n    clickHide: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    visible: [{\n      type: Input\n    }],\n    autoClose: [{\n      type: Input\n    }],\n    overlayClassName: [{\n      type: Input\n    }],\n    overlayStyle: [{\n      type: Input\n    }],\n    placement: [{\n      type: Input\n    }],\n    visibleChange: [{\n      type: Output\n    }],\n    tdsDropdownOutsideClick: [{\n      type: Output\n    }]\n  });\n})();\nclass TDSContextMenuServiceModule {\n  static {\n    this.ɵfac = function TDSContextMenuServiceModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSContextMenuServiceModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: TDSContextMenuServiceModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSContextMenuServiceModule, [{\n    type: NgModule\n  }], null, null);\n})();\nclass TDSDropDownADirective {\n  constructor() {}\n  static {\n    this.ɵfac = function TDSDropDownADirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSDropDownADirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TDSDropDownADirective,\n      selectors: [[\"a\", \"tds-dropdown\", \"\"]],\n      hostAttrs: [1, \"tds-dropdown-link\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSDropDownADirective, [{\n    type: Directive,\n    args: [{\n      selector: 'a[tds-dropdown]',\n      host: {\n        class: 'tds-dropdown-link'\n      },\n      standalone: true\n    }]\n  }], () => [], null);\n})();\nclass TDSDropdownButtonDirective {\n  constructor(renderer,\n  // @Host() @Optional() private nzButtonGroupComponent: NzButtonGroupComponent,\n  elementRef) {\n    this.renderer = renderer;\n    this.elementRef = elementRef;\n  }\n  ngAfterViewInit() {\n    const parentElement = this.renderer.parentNode(this.elementRef.nativeElement);\n    // if (this.nzButtonGroupComponent && parentElement) {\n    //   this.renderer.addClass(parentElement, 'tds-dropdown-button');\n    // }\n  }\n  static {\n    this.ɵfac = function TDSDropdownButtonDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSDropdownButtonDirective)(i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TDSDropdownButtonDirective,\n      selectors: [[\"\", \"tds-button\", \"\", \"tds-dropdown\", \"\"]],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSDropdownButtonDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[tds-button][tds-dropdown]',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.Renderer2\n  }, {\n    type: i0.ElementRef\n  }], null);\n})();\nclass TDSDropDownItemDirective {\n  /** clear all item selected status except this */\n  clickMenuItem(e) {\n    if (this.disabled) {\n      e.preventDefault();\n      e.stopPropagation();\n    } else {\n      this.TDSMenuService.onDescendantMenuItemClick(this);\n    }\n  }\n  constructor(TDSMenuService, cdr) {\n    this.TDSMenuService = TDSMenuService;\n    this.cdr = cdr;\n    this.destroy$ = new Subject();\n    this.selected$ = new Subject();\n    this.disabled = false;\n    this.selected = false;\n  }\n  ngOnInit() {}\n  ngAfterContentInit() {}\n  ngOnChanges(changes) {\n    if (changes.disabled) {\n      this.cdr.markForCheck();\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function TDSDropDownItemDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSDropDownItemDirective)(i0.ɵɵdirectiveInject(i1$1.TDSMenuService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TDSDropDownItemDirective,\n      selectors: [[\"\", \"tds-dropdown-item\", \"\"]],\n      hostAttrs: [1, \"tds-dropdown-item\"],\n      hostVars: 4,\n      hostBindings: function TDSDropDownItemDirective_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function TDSDropDownItemDirective_click_HostBindingHandler($event) {\n            return ctx.clickMenuItem($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"tds-dropdown-item-disabled\", !!ctx.disabled)(\"tds-dropdown-item-selected\", !!ctx.selected);\n        }\n      },\n      inputs: {\n        disabled: \"disabled\",\n        selected: \"selected\"\n      },\n      exportAs: [\"tdsDropdownItem\"],\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n__decorate([InputBoolean()], TDSDropDownItemDirective.prototype, \"disabled\", void 0);\n__decorate([InputBoolean()], TDSDropDownItemDirective.prototype, \"selected\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSDropDownItemDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[tds-dropdown-item]',\n      exportAs: 'tdsDropdownItem',\n      host: {\n        class: \"tds-dropdown-item \",\n        '[class.tds-dropdown-item-disabled]': \"!!disabled\",\n        '[class.tds-dropdown-item-selected]': \"!!selected\",\n        '(click)': 'clickMenuItem($event)'\n      },\n      standalone: true\n    }]\n  }], () => [{\n    type: i1$1.TDSMenuService\n  }, {\n    type: i0.ChangeDetectorRef\n  }], {\n    disabled: [{\n      type: Input\n    }],\n    selected: [{\n      type: Input\n    }]\n  });\n})();\nclass TDSDropdownMenuComponent {\n  onAnimationEvent(event) {\n    this.animationStateChange$.emit(event);\n  }\n  setMouseState(visible) {\n    this.mouseState$.next(visible);\n  }\n  setValue(key, value) {\n    this[key] = value;\n    this.cdr.markForCheck();\n  }\n  constructor(cdr, elementRef, renderer, viewContainerRef, tdsMenuService, directionality) {\n    this.cdr = cdr;\n    this.elementRef = elementRef;\n    this.renderer = renderer;\n    this.viewContainerRef = viewContainerRef;\n    this.tdsMenuService = tdsMenuService;\n    this.directionality = directionality;\n    this.mouseState$ = new BehaviorSubject(false);\n    this.isChildSubMenuOpen$ = this.tdsMenuService.isChildSubMenuOpen$;\n    this.descendantMenuItemClick$ = this.tdsMenuService.descendantMenuItemClick$;\n    this.animationStateChange$ = new EventEmitter();\n    this.overlayClassName = '';\n    this.overlayStyle = {};\n    this.dir = 'ltr';\n    this.destroy$ = new Subject();\n    this.noAnimation = inject(TDSNoAnimationDirective, {\n      host: true,\n      optional: true\n    });\n  }\n  ngOnInit() {\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.cdr.detectChanges();\n    });\n    this.dir = this.directionality.value;\n  }\n  ngAfterContentInit() {\n    this.renderer.removeChild(this.renderer.parentNode(this.elementRef.nativeElement), this.elementRef.nativeElement);\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function TDSDropdownMenuComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSDropdownMenuComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i1$1.TDSMenuService), i0.ɵɵdirectiveInject(i2$1.Directionality));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TDSDropdownMenuComponent,\n      selectors: [[\"tds-dropdown-menu\"]],\n      viewQuery: function TDSDropdownMenuComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(TemplateRef, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templateRef = _t.first);\n        }\n      },\n      exportAs: [\"tdsDropdownMenu\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([TDSMenuService\n      // /** menu is inside dropdown-menu component **/\n      // {\n      //   provide: TDSIsMenuInsideDropDownToken,\n      //   useValue: true\n      // }\n      ]), i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 1,\n      vars: 0,\n      consts: [[1, \"tds-dropdown\", \"min-w-full\", \"relative\", 3, \"mouseenter\", \"mouseleave\", \"ngClass\", \"ngStyle\"], [1, \"tds-dropdown-menu-outer-box\"], [1, \"tds-dropdown-menu-panel\"]],\n      template: function TDSDropdownMenuComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, TDSDropdownMenuComponent_ng_template_0_Template, 4, 5, \"ng-template\");\n        }\n      },\n      dependencies: [NgClass, NgStyle],\n      encapsulation: 2,\n      data: {\n        animation: [slideMotion]\n      },\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSDropdownMenuComponent, [{\n    type: Component,\n    args: [{\n      selector: `tds-dropdown-menu`,\n      exportAs: `tdsDropdownMenu`,\n      animations: [slideMotion],\n      providers: [TDSMenuService\n      // /** menu is inside dropdown-menu component **/\n      // {\n      //   provide: TDSIsMenuInsideDropDownToken,\n      //   useValue: true\n      // }\n      ],\n      template: `\n    <ng-template>\n      <div \n        class=\"tds-dropdown  min-w-full relative\"\n        [class.tds-dropdown-rtl]=\"dir === 'rtl'\"\n        [ngClass]=\"overlayClassName\"\n        [ngStyle]=\"overlayStyle\"\n        @slideMotion\n        (@slideMotion.done)=\"onAnimationEvent($event)\"       \n        (mouseenter)=\"setMouseState(true)\"\n        (mouseleave)=\"setMouseState(false)\"\n      >\n      <div class=\"tds-dropdown-menu-outer-box\" >\n        <div class='tds-dropdown-menu-panel'>\n          <ng-content></ng-content>\n        </div>        \n      </div>\n        \n      </div>\n    </ng-template>\n  `,\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      imports: [NgClass, NgStyle],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i0.ViewContainerRef\n  }, {\n    type: i1$1.TDSMenuService\n  }, {\n    type: i2$1.Directionality\n  }], {\n    templateRef: [{\n      type: ViewChild,\n      args: [TemplateRef, {\n        static: true\n      }]\n    }]\n  });\n})();\nclass TDSDropDownModule {\n  static {\n    this.ɵfac = function TDSDropDownModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSDropDownModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: TDSDropDownModule,\n      imports: [TDSDropDownDirective, TDSDropDownADirective, TDSDropdownMenuComponent, TDSDropdownButtonDirective, TDSDropDownItemDirective, TDSContextMenuServiceModule],\n      exports: [TDSMenuModule, TDSDropDownDirective, TDSDropDownADirective, TDSDropdownMenuComponent, TDSDropdownButtonDirective, TDSDropDownItemDirective]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [TDSContextMenuServiceModule, TDSMenuModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSDropDownModule, [{\n    type: NgModule,\n    args: [{\n      imports: [TDSDropDownDirective, TDSDropDownADirective, TDSDropdownMenuComponent, TDSDropdownButtonDirective, TDSDropDownItemDirective, TDSContextMenuServiceModule],\n      exports: [TDSMenuModule, TDSDropDownDirective, TDSDropDownADirective, TDSDropdownMenuComponent, TDSDropdownButtonDirective, TDSDropDownItemDirective]\n    }]\n  }], null, null);\n})();\nconst listOfPositions = [new ConnectionPositionPair({\n  originX: 'start',\n  originY: 'top'\n}, {\n  overlayX: 'start',\n  overlayY: 'top'\n}), new ConnectionPositionPair({\n  originX: 'start',\n  originY: 'top'\n}, {\n  overlayX: 'start',\n  overlayY: 'bottom'\n}), new ConnectionPositionPair({\n  originX: 'start',\n  originY: 'top'\n}, {\n  overlayX: 'end',\n  overlayY: 'bottom'\n}), new ConnectionPositionPair({\n  originX: 'start',\n  originY: 'top'\n}, {\n  overlayX: 'end',\n  overlayY: 'top'\n})];\nclass TDSContextMenuService {\n  constructor(ngZone, overlay) {\n    this.ngZone = ngZone;\n    this.overlay = overlay;\n    this.overlayRef = null;\n    this.closeSubscription = Subscription.EMPTY;\n  }\n  create($event, tdsDropdownMenuComponent) {\n    this.close(true);\n    const {\n      x,\n      y\n    } = $event;\n    if ($event instanceof MouseEvent) {\n      $event.preventDefault();\n    }\n    const positionStrategy = this.overlay.position().flexibleConnectedTo({\n      x,\n      y\n    }).withPositions(listOfPositions).withTransformOriginOn('.tds-dropdown');\n    this.overlayRef = this.overlay.create({\n      positionStrategy,\n      disposeOnNavigation: true,\n      scrollStrategy: this.overlay.scrollStrategies.close()\n    });\n    this.closeSubscription = new Subscription();\n    this.closeSubscription.add(tdsDropdownMenuComponent.descendantMenuItemClick$.subscribe(() => this.close()));\n    this.closeSubscription.add(merge(fromEventOutsideAngular(document, 'click').pipe(filter(event => !!this.overlayRef && !this.overlayRef.overlayElement.contains(event.target)), /** handle firefox contextmenu event **/\n    filter(event => event.button !== 2)), fromEventOutsideAngular(document, 'keydown').pipe(filter(event => event.key === 'Escape'))).pipe(first()).subscribe(() => this.ngZone.run(() => this.close())));\n    return this.overlayRef.attach(new TemplatePortal(tdsDropdownMenuComponent.templateRef, tdsDropdownMenuComponent.viewContainerRef));\n  }\n  close(clear = false) {\n    if (this.overlayRef) {\n      this.overlayRef.detach();\n      if (clear) {\n        this.overlayRef.dispose();\n      }\n      this.overlayRef = null;\n      this.closeSubscription.unsubscribe();\n    }\n  }\n  static {\n    this.ɵfac = function TDSContextMenuService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSContextMenuService)(i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(i1.Overlay));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: TDSContextMenuService,\n      factory: TDSContextMenuService.ɵfac,\n      providedIn: TDSContextMenuServiceModule\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSContextMenuService, [{\n    type: Injectable,\n    args: [{\n      providedIn: TDSContextMenuServiceModule\n    }]\n  }], () => [{\n    type: i0.NgZone\n  }, {\n    type: i1.Overlay\n  }], null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TDSContextMenuService, TDSContextMenuServiceModule, TDSDropDownADirective, TDSDropDownDirective, TDSDropDownItemDirective, TDSDropDownModule, TDSDropdownButtonDirective, TDSDropdownMenuComponent };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA,kBAA+F;AAC/F,uBAAiG;AAajG,IAAM,MAAM,CAAC,GAAG;AAChB,SAAS,gDAAgD,IAAI,KAAK;AAChE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,qBAAqB,SAAS,0FAA0F,QAAQ;AAC5I,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,iBAAiB,MAAM,CAAC;AAAA,IACvD,CAAC,EAAE,cAAc,SAAS,4EAA4E;AACpG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,cAAc,IAAI,CAAC;AAAA,IAClD,CAAC,EAAE,cAAc,SAAS,4EAA4E;AACpG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,cAAc,KAAK,CAAC;AAAA,IACnD,CAAC;AACD,IAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC;AAC1C,IAAG,aAAa,CAAC;AACjB,IAAG,aAAa,EAAE,EAAE;AAAA,EACtB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,YAAY,oBAAoB,OAAO,QAAQ,KAAK;AACvD,IAAG,WAAW,WAAW,OAAO,gBAAgB,EAAE,WAAW,OAAO,YAAY,EAAE,gBAAgB,MAAS;AAAA,EAC7G;AACF;AACA,IAAM,oBAAoB,CAAC,aAAa,YAAY,aAAa,aAAa,aAAa,UAAU,aAAa,OAAO;AACzH,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,qBAAqB,KAAK,OAAO;AAC/B,QAAI,KAAK,iBAAiB;AACxB,WAAK,gBAAgB,SAAS,KAAK,KAAK;AAAA,IAC1C;AAAA,EACF;AAAA,EACA,YAAY,YAAY,SAAS,UAAU,kBAAkB,UAAU;AACrE,SAAK,aAAa;AAClB,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,mBAAmB;AACxB,SAAK,WAAW;AAChB,SAAK,aAAa;AAClB,SAAK,WAAW,IAAI,oBAAQ;AAC5B,SAAK,mBAAmB,KAAK,QAAQ,SAAS,EAAE,oBAAoB,KAAK,WAAW,aAAa,EAAE,mBAAmB,EAAE,sBAAsB,eAAe;AAC7J,SAAK,gBAAgB,IAAI,4BAAgB,KAAK;AAC9C,SAAK,WAAW,IAAI,4BAAgB,OAAO;AAC3C,SAAK,gBAAgB,IAAI,oBAAQ;AACjC,SAAK,kBAAkB;AACvB,SAAK,UAAU;AACf,SAAK,oBAAoB;AACzB,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,SAAK,UAAU;AACf,SAAK,YAAY;AACjB,SAAK,mBAAmB;AACxB,SAAK,eAAe,CAAC;AACrB,SAAK,YAAY;AACjB,SAAK,gBAAgB,IAAI,aAAa;AACtC,SAAK,0BAA0B,IAAI,aAAa;AAAA,EAClD;AAAA,EACA,WAAW;AAAA,EAAC;AAAA,EACZ,kBAAkB;AAChB,QAAI,KAAK,iBAAiB;AACxB,YAAM,gBAAgB,KAAK,WAAW;AAEtC,YAAM,sBAAkB,uBAAM,uBAAU,eAAe,YAAY,EAAE,SAAK,wBAAM,IAAI,CAAC,OAAG,uBAAU,eAAe,YAAY,EAAE,SAAK,wBAAM,KAAK,CAAC,CAAC;AAEjJ,YAAM,kBAAkB,KAAK,gBAAgB;AAE7C,YAAM,wBAAoB,mBAAM,iBAAiB,eAAe;AAEhE,YAAM,sBAAkB,uBAAU,eAAe,OAAO,EAAE,SAAK,sBAAI,MAAM,CAAC,KAAK,OAAO,CAAC;AAEvF,YAAM,yBAAyB,KAAK,SAAS,SAAK,4BAAU,aAAW;AACrE,YAAI,YAAY,SAAS;AACvB,iBAAO;AAAA,QACT,WAAW,YAAY,SAAS;AAC9B,iBAAO;AAAA,QACT,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF,CAAC,CAAC;AACF,YAAM,2BAA2B,KAAK,gBAAgB,yBAAyB,SAAK,yBAAO,MAAM,KAAK,SAAS,OAAG,wBAAM,KAAK,CAAC;AAC9H,YAAM,yBAAqB,mBAAM,wBAAwB,0BAA0B,KAAK,aAAa,EAAE,SAAK,yBAAO,MAAM,CAAC,KAAK,QAAQ,CAAC;AACxI,YAAM,eAAW,mBAAM,KAAK,eAAe,kBAAkB;AAC7D,qCAAc,CAAC,UAAU,KAAK,gBAAgB,mBAAmB,CAAC,EAAE,SAAK,sBAAI,CAAC,CAAC,SAAS,GAAG,MAAM,WAAW,GAAG,OAAG,4BAAU,GAAG,OAAG,uCAAqB,OAAG,yBAAO,MAAM,KAAK,SAAS,SAAS,OAAG,4BAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,aAAW;AAC9O,cAAM,UAAU,KAAK,oBAAoB,KAAK,kBAAkB,gBAAgB;AAChF,cAAM,eAAe,QAAQ,sBAAsB,EAAE;AACrD,YAAI,KAAK,YAAY,SAAS;AAC5B,eAAK,cAAc,KAAK,OAAO;AAAA,QACjC;AACA,aAAK,UAAU;AACf,YAAI,SAAS;AAEX,cAAI,CAAC,KAAK,YAAY;AAEpB,iBAAK,aAAa,KAAK,QAAQ,OAAO;AAAA,cACpC,kBAAkB,KAAK;AAAA,cACvB,UAAU;AAAA,cACV,qBAAqB;AAAA,cACrB,aAAa,KAAK,YAAY,KAAK,YAAY;AAAA,cAC/C,gBAAgB,KAAK,YAAY,KAAK,QAAQ,iBAAiB,MAAM,IAAI,KAAK,QAAQ,iBAAiB,WAAW;AAAA,YACpH,CAAC;AACD,mCAAM,KAAK,WAAW,cAAc,GAAG,KAAK,WAAW,YAAY,GAAG,KAAK,WAAW,qBAAqB,EAAE,SAAK,yBAAO,OAAK,CAAC,KAAK,WAAW,cAAc,SAAS,EAAE,MAAM,CAAC,CAAC,GAAG,KAAK,WAAW,cAAc,EAAE,SAAK,yBAAO,OAAK,EAAE,YAAY,UAAU,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,EAAE,SAAK,yBAAO,MAAM,KAAK,SAAS,OAAG,4BAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM;AAC7V,mBAAK,cAAc,KAAK,KAAK;AAAA,YAC/B,CAAC;AACD,mCAAM,KAAK,WAAW,cAAc,GAAG,KAAK,WAAW,qBAAqB,EAAE,SAAK,yBAAO,OAAK,CAAC,KAAK,WAAW,cAAc,SAAS,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,SAAK,4BAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,OAAK;AAChM,mBAAK,wBAAwB,KAAK,CAAC;AAAA,YACrC,CAAC;AAAA,UACH,OAAO;AAEL,kBAAM,gBAAgB,KAAK,WAAW,UAAU;AAChD,0BAAc,WAAW;AAAA,UAC3B;AAEA,eAAK,iBAAiB,cAAc,CAAC,aAAa,KAAK,SAAS,GAAG,GAAG,iBAAiB,CAAC;AAExF,cAAI,CAAC,KAAK,UAAU,KAAK,OAAO,gBAAgB,KAAK,gBAAgB,aAAa;AAChF,iBAAK,SAAS,IAAI,eAAe,KAAK,gBAAgB,aAAa,KAAK,gBAAgB;AAAA,UAC1F;AACA,eAAK,WAAW,OAAO,KAAK,MAAM;AAAA,QACpC,OAAO;AAEL,cAAI,KAAK,YAAY;AACnB,iBAAK,WAAW,OAAO;AAAA,UACzB;AAAA,QACF;AAAA,MACF,CAAC;AACD,WAAK,gBAAgB,sBAAsB,SAAK,4BAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,WAAS;AAC3F,YAAI,MAAM,YAAY,QAAQ;AAC5B,cAAI,KAAK,YAAY;AACnB,iBAAK,WAAW,QAAQ;AAAA,UAC1B;AACA,eAAK,aAAa;AAAA,QACpB;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AACvB,QAAI,KAAK,YAAY;AACnB,WAAK,WAAW,QAAQ;AACxB,WAAK,aAAa;AAAA,IACpB;AAAA,EACF;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,SAAS;AACX,WAAK,SAAS,KAAK,KAAK,OAAO;AAAA,IACjC;AACA,QAAI,SAAS;AACX,WAAK,cAAc,KAAK,KAAK,OAAO;AAAA,IACtC;AACA,QAAI,UAAU;AACZ,YAAM,gBAAgB,KAAK,WAAW;AACtC,UAAI,KAAK,UAAU;AACjB,aAAK,SAAS,aAAa,eAAe,YAAY,EAAE;AACxD,aAAK,cAAc,KAAK,KAAK;AAAA,MAC/B,OAAO;AACL,aAAK,SAAS,gBAAgB,eAAe,UAAU;AAAA,MACzD;AAAA,IACF;AACA,QAAI,kBAAkB;AACpB,WAAK,qBAAqB,oBAAoB,KAAK,gBAAgB;AAAA,IACrE;AACA,QAAI,cAAc;AAChB,WAAK,qBAAqB,gBAAgB,KAAK,YAAY;AAAA,IAC7D;AACA,QAAI,UAAU;AAAA,IAEd;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,6BAA6B,mBAAmB;AACnE,aAAO,KAAK,qBAAqB,uBAAyB,kBAAqB,UAAU,GAAM,kBAAqB,OAAO,GAAM,kBAAqB,SAAS,GAAM,kBAAqB,gBAAgB,GAAM,kBAAqB,QAAQ,CAAC;AAAA,IAChP;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,gBAAgB,EAAE,CAAC;AAAA,MACpC,WAAW,CAAC,GAAG,sBAAsB;AAAA,MACrC,QAAQ;AAAA,QACN,iBAAiB;AAAA,QACjB,SAAS;AAAA,QACT,mBAAmB;AAAA,QACnB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,UAAU;AAAA,QACV,SAAS;AAAA,QACT,WAAW;AAAA,QACX,kBAAkB;AAAA,QAClB,cAAc;AAAA,QACd,WAAW;AAAA,MACb;AAAA,MACA,SAAS;AAAA,QACP,eAAe;AAAA,QACf,yBAAyB;AAAA,MAC3B;AAAA,MACA,UAAU,CAAC,aAAa;AAAA,MACxB,YAAY;AAAA,MACZ,UAAU,CAAI,oBAAoB;AAAA,IACpC,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,aAAa,CAAC,GAAG,qBAAqB,WAAW,YAAY,MAAM;AAC/E,WAAW,CAAC,aAAa,CAAC,GAAG,qBAAqB,WAAW,aAAa,MAAM;AAChF,WAAW,CAAC,aAAa,CAAC,GAAG,qBAAqB,WAAW,YAAY,MAAM;AAC/E,WAAW,CAAC,aAAa,CAAC,GAAG,qBAAqB,WAAW,WAAW,MAAM;AAC9E,WAAW,CAAC,aAAa,CAAC,GAAG,qBAAqB,WAAW,aAAa,MAAM;AAAA,CAC/E,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,yBAAyB,CAAC;AAAA,MACxB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,8BAAN,MAAM,6BAA4B;AAAA,EAChC,OAAO;AACL,SAAK,OAAO,SAAS,oCAAoC,mBAAmB;AAC1E,aAAO,KAAK,qBAAqB,8BAA6B;AAAA,IAChE;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB,CAAC,CAAC;AAAA,EACnD;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,6BAA6B,CAAC;AAAA,IACpG,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B,cAAc;AAAA,EAAC;AAAA,EACf,OAAO;AACL,SAAK,OAAO,SAAS,8BAA8B,mBAAmB;AACpE,aAAO,KAAK,qBAAqB,wBAAuB;AAAA,IAC1D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,KAAK,gBAAgB,EAAE,CAAC;AAAA,MACrC,WAAW,CAAC,GAAG,mBAAmB;AAAA,MAClC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AACH,IAAM,6BAAN,MAAM,4BAA2B;AAAA,EAC/B,YAAY,UAEZ,YAAY;AACV,SAAK,WAAW;AAChB,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,kBAAkB;AAChB,UAAM,gBAAgB,KAAK,SAAS,WAAW,KAAK,WAAW,aAAa;AAAA,EAI9E;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,mCAAmC,mBAAmB;AACzE,aAAO,KAAK,qBAAqB,6BAA+B,kBAAqB,SAAS,GAAM,kBAAqB,UAAU,CAAC;AAAA,IACtI;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,cAAc,IAAI,gBAAgB,EAAE,CAAC;AAAA,MACtD,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,4BAA4B,CAAC;AAAA,IACnG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,2BAAN,MAAM,0BAAyB;AAAA;AAAA,EAE7B,cAAc,GAAG;AACf,QAAI,KAAK,UAAU;AACjB,QAAE,eAAe;AACjB,QAAE,gBAAgB;AAAA,IACpB,OAAO;AACL,WAAK,eAAe,0BAA0B,IAAI;AAAA,IACpD;AAAA,EACF;AAAA,EACA,YAAYA,iBAAgB,KAAK;AAC/B,SAAK,iBAAiBA;AACtB,SAAK,MAAM;AACX,SAAK,WAAW,IAAI,oBAAQ;AAC5B,SAAK,YAAY,IAAI,oBAAQ;AAC7B,SAAK,WAAW;AAChB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,WAAW;AAAA,EAAC;AAAA,EACZ,qBAAqB;AAAA,EAAC;AAAA,EACtB,YAAY,SAAS;AACnB,QAAI,QAAQ,UAAU;AACpB,WAAK,IAAI,aAAa;AAAA,IACxB;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,iCAAiC,mBAAmB;AACvE,aAAO,KAAK,qBAAqB,2BAA6B,kBAAuB,cAAc,GAAM,kBAAqB,iBAAiB,CAAC;AAAA,IAClJ;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,qBAAqB,EAAE,CAAC;AAAA,MACzC,WAAW,CAAC,GAAG,mBAAmB;AAAA,MAClC,UAAU;AAAA,MACV,cAAc,SAAS,sCAAsC,IAAI,KAAK;AACpE,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,SAAS,SAAS,kDAAkD,QAAQ;AACxF,mBAAO,IAAI,cAAc,MAAM;AAAA,UACjC,CAAC;AAAA,QACH;AACA,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,8BAA8B,CAAC,CAAC,IAAI,QAAQ,EAAE,8BAA8B,CAAC,CAAC,IAAI,QAAQ;AAAA,QAC3G;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,UAAU;AAAA,MACZ;AAAA,MACA,UAAU,CAAC,iBAAiB;AAAA,MAC5B,YAAY;AAAA,MACZ,UAAU,CAAI,oBAAoB;AAAA,IACpC,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,aAAa,CAAC,GAAG,yBAAyB,WAAW,YAAY,MAAM;AACnF,WAAW,CAAC,aAAa,CAAC,GAAG,yBAAyB,WAAW,YAAY,MAAM;AAAA,CAClF,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,sCAAsC;AAAA,QACtC,sCAAsC;AAAA,QACtC,WAAW;AAAA,MACb;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,2BAAN,MAAM,0BAAyB;AAAA,EAC7B,iBAAiB,OAAO;AACtB,SAAK,sBAAsB,KAAK,KAAK;AAAA,EACvC;AAAA,EACA,cAAc,SAAS;AACrB,SAAK,YAAY,KAAK,OAAO;AAAA,EAC/B;AAAA,EACA,SAAS,KAAK,OAAO;AACnB,SAAK,GAAG,IAAI;AACZ,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,YAAY,KAAK,YAAY,UAAU,kBAAkB,gBAAgB,gBAAgB;AACvF,SAAK,MAAM;AACX,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,mBAAmB;AACxB,SAAK,iBAAiB;AACtB,SAAK,iBAAiB;AACtB,SAAK,cAAc,IAAI,4BAAgB,KAAK;AAC5C,SAAK,sBAAsB,KAAK,eAAe;AAC/C,SAAK,2BAA2B,KAAK,eAAe;AACpD,SAAK,wBAAwB,IAAI,aAAa;AAC9C,SAAK,mBAAmB;AACxB,SAAK,eAAe,CAAC;AACrB,SAAK,MAAM;AACX,SAAK,WAAW,IAAI,oBAAQ;AAC5B,SAAK,cAAc,OAAO,yBAAyB;AAAA,MACjD,MAAM;AAAA,MACN,UAAU;AAAA,IACZ,CAAC;AAAA,EACH;AAAA,EACA,WAAW;AACT,SAAK,eAAe,QAAQ,SAAK,4BAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAChF,WAAK,MAAM;AACX,WAAK,IAAI,cAAc;AAAA,IACzB,CAAC;AACD,SAAK,MAAM,KAAK,eAAe;AAAA,EACjC;AAAA,EACA,qBAAqB;AACnB,SAAK,SAAS,YAAY,KAAK,SAAS,WAAW,KAAK,WAAW,aAAa,GAAG,KAAK,WAAW,aAAa;AAAA,EAClH;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,iCAAiC,mBAAmB;AACvE,aAAO,KAAK,qBAAqB,2BAA6B,kBAAqB,iBAAiB,GAAM,kBAAqB,UAAU,GAAM,kBAAqB,SAAS,GAAM,kBAAqB,gBAAgB,GAAM,kBAAuB,cAAc,GAAM,kBAAuB,cAAc,CAAC;AAAA,IACjT;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,mBAAmB,CAAC;AAAA,MACjC,WAAW,SAAS,+BAA+B,IAAI,KAAK;AAC1D,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,aAAa,CAAC;AAAA,QAC/B;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,cAAc,GAAG;AAAA,QACpE;AAAA,MACF;AAAA,MACA,UAAU,CAAC,iBAAiB;AAAA,MAC5B,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,QAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMlC,CAAC,GAAM,mBAAmB;AAAA,MAC1B,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,gBAAgB,cAAc,YAAY,GAAG,cAAc,cAAc,WAAW,SAAS,GAAG,CAAC,GAAG,6BAA6B,GAAG,CAAC,GAAG,yBAAyB,CAAC;AAAA,MAC/K,UAAU,SAAS,kCAAkC,IAAI,KAAK;AAC5D,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,WAAW,GAAG,iDAAiD,GAAG,GAAG,aAAa;AAAA,QACvF;AAAA,MACF;AAAA,MACA,cAAc,CAAC,SAAS,OAAO;AAAA,MAC/B,eAAe;AAAA,MACf,MAAM;AAAA,QACJ,WAAW,CAAC,WAAW;AAAA,MACzB;AAAA,MACA,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY,CAAC,WAAW;AAAA,MACxB,WAAW;AAAA,QAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMZ;AAAA,MACA,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAqBV,qBAAqB;AAAA,MACrB,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,SAAS,CAAC,SAAS,OAAO;AAAA,MAC1B,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAW;AAAA,EACb,CAAC,GAAG;AAAA,IACF,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,QAClB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,OAAO;AACL,SAAK,OAAO,SAAS,0BAA0B,mBAAmB;AAChE,aAAO,KAAK,qBAAqB,oBAAmB;AAAA,IACtD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,sBAAsB,uBAAuB,0BAA0B,4BAA4B,0BAA0B,2BAA2B;AAAA,MAClK,SAAS,CAAC,eAAe,sBAAsB,uBAAuB,0BAA0B,4BAA4B,wBAAwB;AAAA,IACtJ,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,6BAA6B,aAAa;AAAA,IACtD,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,sBAAsB,uBAAuB,0BAA0B,4BAA4B,0BAA0B,2BAA2B;AAAA,MAClK,SAAS,CAAC,eAAe,sBAAsB,uBAAuB,0BAA0B,4BAA4B,wBAAwB;AAAA,IACtJ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,kBAAkB,CAAC,IAAI,uBAAuB;AAAA,EAClD,SAAS;AAAA,EACT,SAAS;AACX,GAAG;AAAA,EACD,UAAU;AAAA,EACV,UAAU;AACZ,CAAC,GAAG,IAAI,uBAAuB;AAAA,EAC7B,SAAS;AAAA,EACT,SAAS;AACX,GAAG;AAAA,EACD,UAAU;AAAA,EACV,UAAU;AACZ,CAAC,GAAG,IAAI,uBAAuB;AAAA,EAC7B,SAAS;AAAA,EACT,SAAS;AACX,GAAG;AAAA,EACD,UAAU;AAAA,EACV,UAAU;AACZ,CAAC,GAAG,IAAI,uBAAuB;AAAA,EAC7B,SAAS;AAAA,EACT,SAAS;AACX,GAAG;AAAA,EACD,UAAU;AAAA,EACV,UAAU;AACZ,CAAC,CAAC;AACF,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B,YAAY,QAAQ,SAAS;AAC3B,SAAK,SAAS;AACd,SAAK,UAAU;AACf,SAAK,aAAa;AAClB,SAAK,oBAAoB,yBAAa;AAAA,EACxC;AAAA,EACA,OAAO,QAAQ,0BAA0B;AACvC,SAAK,MAAM,IAAI;AACf,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,kBAAkB,YAAY;AAChC,aAAO,eAAe;AAAA,IACxB;AACA,UAAM,mBAAmB,KAAK,QAAQ,SAAS,EAAE,oBAAoB;AAAA,MACnE;AAAA,MACA;AAAA,IACF,CAAC,EAAE,cAAc,eAAe,EAAE,sBAAsB,eAAe;AACvE,SAAK,aAAa,KAAK,QAAQ,OAAO;AAAA,MACpC;AAAA,MACA,qBAAqB;AAAA,MACrB,gBAAgB,KAAK,QAAQ,iBAAiB,MAAM;AAAA,IACtD,CAAC;AACD,SAAK,oBAAoB,IAAI,yBAAa;AAC1C,SAAK,kBAAkB,IAAI,yBAAyB,yBAAyB,UAAU,MAAM,KAAK,MAAM,CAAC,CAAC;AAC1G,SAAK,kBAAkB,QAAI,mBAAM,wBAAwB,UAAU,OAAO,EAAE;AAAA,UAAK,yBAAO,WAAS,CAAC,CAAC,KAAK,cAAc,CAAC,KAAK,WAAW,eAAe,SAAS,MAAM,MAAM,CAAC;AAAA;AAAA,UAC5K,yBAAO,WAAS,MAAM,WAAW,CAAC;AAAA,IAAC,GAAG,wBAAwB,UAAU,SAAS,EAAE,SAAK,yBAAO,WAAS,MAAM,QAAQ,QAAQ,CAAC,CAAC,EAAE,SAAK,wBAAM,CAAC,EAAE,UAAU,MAAM,KAAK,OAAO,IAAI,MAAM,KAAK,MAAM,CAAC,CAAC,CAAC;AACpM,WAAO,KAAK,WAAW,OAAO,IAAI,eAAe,yBAAyB,aAAa,yBAAyB,gBAAgB,CAAC;AAAA,EACnI;AAAA,EACA,MAAM,QAAQ,OAAO;AACnB,QAAI,KAAK,YAAY;AACnB,WAAK,WAAW,OAAO;AACvB,UAAI,OAAO;AACT,aAAK,WAAW,QAAQ;AAAA,MAC1B;AACA,WAAK,aAAa;AAClB,WAAK,kBAAkB,YAAY;AAAA,IACrC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,8BAA8B,mBAAmB;AACpE,aAAO,KAAK,qBAAqB,wBAA0B,SAAY,MAAM,GAAM,SAAY,OAAO,CAAC;AAAA,IACzG;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,uBAAsB;AAAA,MAC/B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;", "names": ["TDSMenuService"]}