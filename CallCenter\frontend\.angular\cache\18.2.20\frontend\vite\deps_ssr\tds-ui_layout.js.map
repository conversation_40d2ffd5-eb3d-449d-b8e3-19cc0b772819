{"version": 3, "sources": ["../../../../../../node_modules/tds-ui/fesm2022/tds-ui-layout.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ChangeDetectionStrategy, ViewEncapsulation, ContentChildren, Input, EventEmitter, ContentChild, Output, Directive, NgModule } from '@angular/core';\nimport { TDSHeaderComponent } from 'tds-ui/header';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { __decorate } from 'tslib';\nimport * as i1$1 from 'tds-ui/core/services';\nimport { siderResponsiveMap, tdsLayoutResponsiveMap, TDSDestroyService } from 'tds-ui/core/services';\nimport { TDSMenuComponent } from 'tds-ui/menu';\nimport { toCssPixel, inNextTick, InputBoolean } from 'tds-ui/shared/utility';\nimport { NgTemplateOutlet } from '@angular/common';\nimport * as i1 from '@angular/cdk/platform';\nimport * as i1$2 from '@angular/cdk/bidi';\nconst _c0 = [\"*\"];\nconst _c1 = [\"tds-layout-sider-trigger\", \"\"];\nfunction TDSLayoutSiderTriggerComponent_Conditional_0_ng_template_0_Template(rf, ctx) {}\nfunction TDSLayoutSiderTriggerComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TDSLayoutSiderTriggerComponent_Conditional_0_ng_template_0_Template, 0, 0, \"ng-template\", 2);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    const defaultZeroTrigger_r2 = i0.ɵɵreference(5);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.tdsZeroTrigger || defaultZeroTrigger_r2);\n  }\n}\nfunction TDSLayoutSiderTriggerComponent_Conditional_1_ng_template_0_Template(rf, ctx) {}\nfunction TDSLayoutSiderTriggerComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TDSLayoutSiderTriggerComponent_Conditional_1_ng_template_0_Template, 0, 0, \"ng-template\", 2);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    const defaultTrigger_r3 = i0.ɵɵreference(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.tdsTrigger || defaultTrigger_r3);\n  }\n}\nfunction TDSLayoutSiderTriggerComponent_ng_template_2_Template(rf, ctx) {}\nfunction TDSLayoutSiderTriggerComponent_ng_template_4_Template(rf, ctx) {}\nfunction TDSLayoutSiderComponent_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵlistener(\"click\", function TDSLayoutSiderComponent_Conditional_2_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.setCollapsed(!ctx_r1.tdsCollapsed));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"matchBreakPoint\", ctx_r1.matchBreakPoint)(\"tdsCollapsedWidth\", ctx_r1.tdsCollapsedWidth)(\"tdsCollapsed\", ctx_r1.tdsCollapsed)(\"tdsBreakpoint\", ctx_r1.tdsBreakpoint)(\"tdsReverseArrow\", ctx_r1.tdsReverseArrow)(\"tdsTrigger\", ctx_r1.tdsTrigger)(\"tdsZeroTrigger\", ctx_r1.tdsZeroTrigger)(\"siderWidth\", ctx_r1.widthSetting);\n  }\n}\nfunction TDSLayoutDeviceComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n  }\n}\nfunction TDSLayoutDeviceComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0, 0);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.content.templateRef);\n  }\n}\nclass TDSLayoutContentComponent {\n  constructor() {}\n  static {\n    this.ɵfac = function TDSLayoutContentComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSLayoutContentComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TDSLayoutContentComponent,\n      selectors: [[\"tds-layout-content\"]],\n      hostAttrs: [1, \"tds-layout-content\", \"tds-custom-scroll\"],\n      exportAs: [\"tdsLayoutContent\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 1,\n      vars: 0,\n      template: function TDSLayoutContentComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSLayoutContentComponent, [{\n    type: Component,\n    args: [{\n      selector: 'tds-layout-content',\n      exportAs: 'tdsLayoutContent',\n      preserveWhitespaces: false,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: ` <ng-content></ng-content> `,\n      standalone: true,\n      host: {\n        class: 'tds-layout-content tds-custom-scroll'\n      }\n    }]\n  }], () => [], null);\n})();\nclass TDSLayoutFooterComponent {\n  constructor() {}\n  static {\n    this.ɵfac = function TDSLayoutFooterComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSLayoutFooterComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TDSLayoutFooterComponent,\n      selectors: [[\"tds-layout-footer\"]],\n      hostAttrs: [1, \"tds-layout-footer\"],\n      exportAs: [\"tdsLayoutFooter\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 1,\n      vars: 0,\n      template: function TDSLayoutFooterComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSLayoutFooterComponent, [{\n    type: Component,\n    args: [{\n      selector: 'tds-layout-footer',\n      exportAs: 'tdsLayoutFooter',\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: ` <ng-content></ng-content> `,\n      standalone: true,\n      host: {\n        class: 'tds-layout-footer'\n      }\n    }]\n  }], () => [], null);\n})();\nclass TDSLayoutHeaderComponent {\n  constructor() {}\n  static {\n    this.ɵfac = function TDSLayoutHeaderComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSLayoutHeaderComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TDSLayoutHeaderComponent,\n      selectors: [[\"tds-layout-header\"]],\n      contentQueries: function TDSLayoutHeaderComponent_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, TDSHeaderComponent, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listOfTDSHeaderComponent = _t);\n        }\n      },\n      hostAttrs: [1, \"tds-layout-header\"],\n      hostVars: 2,\n      hostBindings: function TDSLayoutHeaderComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"tds-layout-has-header\", ctx.listOfTDSHeaderComponent.length > 0);\n        }\n      },\n      exportAs: [\"tdsLayoutHeader\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 1,\n      vars: 0,\n      template: function TDSLayoutHeaderComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSLayoutHeaderComponent, [{\n    type: Component,\n    args: [{\n      selector: 'tds-layout-header',\n      exportAs: 'tdsLayoutHeader',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      preserveWhitespaces: false,\n      template: ` <ng-content></ng-content> `,\n      host: {\n        class: 'tds-layout-header',\n        '[class.tds-layout-has-header]': 'listOfTDSHeaderComponent.length > 0'\n      },\n      standalone: true\n    }]\n  }], () => [], {\n    listOfTDSHeaderComponent: [{\n      type: ContentChildren,\n      args: [TDSHeaderComponent]\n    }]\n  });\n})();\nclass TDSLayoutSiderTriggerComponent {\n  constructor() {\n    this.tdsCollapsed = false;\n    this.tdsReverseArrow = false;\n    this.tdsZeroTrigger = null;\n    this.tdsTrigger = undefined;\n    this.matchBreakPoint = false;\n    this.tdsCollapsedWidth = null;\n    this.siderWidth = null;\n    this.tdsBreakpoint = null;\n    this.isZeroTrigger = false;\n    this.isNormalTrigger = false;\n  }\n  updateTriggerType() {\n    this.isZeroTrigger = this.tdsCollapsedWidth === 0 && (this.tdsBreakpoint && this.matchBreakPoint || !this.tdsBreakpoint);\n    this.isNormalTrigger = this.tdsCollapsedWidth !== 0;\n  }\n  ngOnInit() {\n    this.updateTriggerType();\n  }\n  ngOnChanges() {\n    this.updateTriggerType();\n  }\n  static {\n    this.ɵfac = function TDSLayoutSiderTriggerComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSLayoutSiderTriggerComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TDSLayoutSiderTriggerComponent,\n      selectors: [[\"\", \"tds-layout-sider-trigger\", \"\"]],\n      hostVars: 10,\n      hostBindings: function TDSLayoutSiderTriggerComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"width\", ctx.isNormalTrigger ? ctx.siderWidth : null);\n          i0.ɵɵclassProp(\"tds-layout-sider-trigger\", ctx.isNormalTrigger)(\"tds-layout-sider-zero-width-trigger\", ctx.isZeroTrigger)(\"tds-layout-sider-zero-width-trigger-right\", ctx.isZeroTrigger && ctx.tdsReverseArrow)(\"tds-layout-sider-zero-width-trigger-left\", ctx.isZeroTrigger && !ctx.tdsReverseArrow);\n        }\n      },\n      inputs: {\n        tdsCollapsed: \"tdsCollapsed\",\n        tdsReverseArrow: \"tdsReverseArrow\",\n        tdsZeroTrigger: \"tdsZeroTrigger\",\n        tdsTrigger: \"tdsTrigger\",\n        matchBreakPoint: \"matchBreakPoint\",\n        tdsCollapsedWidth: \"tdsCollapsedWidth\",\n        siderWidth: \"siderWidth\",\n        tdsBreakpoint: \"tdsBreakpoint\"\n      },\n      exportAs: [\"tdsLayoutSiderTrigger\"],\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      attrs: _c1,\n      decls: 6,\n      vars: 2,\n      consts: [[\"defaultTrigger\", \"\"], [\"defaultZeroTrigger\", \"\"], [3, \"ngTemplateOutlet\"]],\n      template: function TDSLayoutSiderTriggerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, TDSLayoutSiderTriggerComponent_Conditional_0_Template, 1, 1, null, 2)(1, TDSLayoutSiderTriggerComponent_Conditional_1_Template, 1, 1, null, 2)(2, TDSLayoutSiderTriggerComponent_ng_template_2_Template, 0, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(4, TDSLayoutSiderTriggerComponent_ng_template_4_Template, 0, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵconditional(ctx.isZeroTrigger ? 0 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx.isNormalTrigger ? 1 : -1);\n        }\n      },\n      dependencies: [NgTemplateOutlet],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSLayoutSiderTriggerComponent, [{\n    type: Component,\n    args: [{\n      selector: '[tds-layout-sider-trigger]',\n      exportAs: 'tdsLayoutSiderTrigger',\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    @if (isZeroTrigger) {\n      <ng-template [ngTemplateOutlet]=\"tdsZeroTrigger || defaultZeroTrigger\"></ng-template>\n    }\n    @if (isNormalTrigger) {\n      <ng-template [ngTemplateOutlet]=\"tdsTrigger || defaultTrigger\"></ng-template>\n    }\n    <ng-template #defaultTrigger>\n      <!-- <span nz-icon [nzType]=\"tdsCollapsed ? 'right' : 'left'\" *ngIf=\"!tdsReverseArrow\"></span>\n      <span nz-icon [nzType]=\"tdsCollapsed ? 'left' : 'right'\" *ngIf=\"tdsReverseArrow\"></span> -->\n    </ng-template>\n    <ng-template #defaultZeroTrigger>\n      <!-- <span nz-icon nzType=\"bars\"></span> -->\n    </ng-template>\n    `,\n      host: {\n        '[class.tds-layout-sider-trigger]': 'isNormalTrigger',\n        '[style.width]': 'isNormalTrigger ? siderWidth : null',\n        '[class.tds-layout-sider-zero-width-trigger]': 'isZeroTrigger',\n        '[class.tds-layout-sider-zero-width-trigger-right]': 'isZeroTrigger && tdsReverseArrow',\n        '[class.tds-layout-sider-zero-width-trigger-left]': 'isZeroTrigger && !tdsReverseArrow'\n      },\n      standalone: true,\n      imports: [NgTemplateOutlet]\n    }]\n  }], null, {\n    tdsCollapsed: [{\n      type: Input\n    }],\n    tdsReverseArrow: [{\n      type: Input\n    }],\n    tdsZeroTrigger: [{\n      type: Input\n    }],\n    tdsTrigger: [{\n      type: Input\n    }],\n    matchBreakPoint: [{\n      type: Input\n    }],\n    tdsCollapsedWidth: [{\n      type: Input\n    }],\n    siderWidth: [{\n      type: Input\n    }],\n    tdsBreakpoint: [{\n      type: Input\n    }]\n  });\n})();\nclass TDSLayoutSiderComponent {\n  updateStyleMap() {\n    this.widthSetting = this.tdsCollapsed ? `${this.tdsCollapsedWidth}px` : toCssPixel(this.tdsWidth);\n    this.flexSetting = `0 0 ${this.widthSetting}`;\n    this.cdr.markForCheck();\n  }\n  updateMenuInlineCollapsed() {\n    if (this.tdsMenuDirective && this.tdsMenuDirective.inlineCollapsed == false && this.tdsCollapsedWidth !== 0) {\n      this.tdsMenuDirective.setInlineCollapsed(this.tdsCollapsed);\n    }\n  }\n  setCollapsed(collapsed) {\n    if (collapsed !== this.tdsCollapsed) {\n      this.tdsCollapsed = collapsed;\n      this.tdsCollapsedChange.emit(collapsed);\n      this.updateMenuInlineCollapsed();\n      this.updateStyleMap();\n      this.cdr.markForCheck();\n    }\n  }\n  constructor(platform, cdr, breakpointService) {\n    this.platform = platform;\n    this.cdr = cdr;\n    this.breakpointService = breakpointService;\n    this.destroy$ = new Subject();\n    this.tdsMenuDirective = null;\n    this.tdsCollapsedChange = new EventEmitter();\n    this.tdsWidth = 256;\n    this.tdsTheme = 'dark';\n    this.tdsCollapsedWidth = 52;\n    this.tdsBreakpoint = null;\n    this.tdsZeroTrigger = null;\n    this.tdsTrigger = undefined;\n    this.tdsReverseArrow = false;\n    this.tdsCollapsible = false;\n    this.tdsCollapsed = false;\n    this.matchBreakPoint = false;\n    this.flexSetting = null;\n    this.widthSetting = null;\n  }\n  ngOnInit() {\n    this.updateStyleMap();\n    if (this.platform.isBrowser) {\n      this.breakpointService.subscribe(siderResponsiveMap, true).pipe(takeUntil(this.destroy$)).subscribe(map => {\n        const breakpoint = this.tdsBreakpoint;\n        if (breakpoint) {\n          inNextTick().subscribe(() => {\n            this.matchBreakPoint = !map[breakpoint];\n            this.setCollapsed(this.matchBreakPoint);\n            this.cdr.markForCheck();\n          });\n        }\n      });\n    }\n  }\n  ngOnChanges(changes) {\n    const {\n      tdsCollapsed,\n      tdsCollapsedWidth,\n      tdsWidth\n    } = changes;\n    if (tdsCollapsed || tdsCollapsedWidth || tdsWidth) {\n      this.updateStyleMap();\n    }\n    if (tdsCollapsed) {\n      this.updateMenuInlineCollapsed();\n    }\n  }\n  ngAfterContentInit() {\n    this.updateMenuInlineCollapsed();\n    if (this.tdsMenuDirective) {\n      if (this.tdsCollapsible && this.tdsTrigger != null) {\n        this.tdsMenuDirective.showFooter = false;\n      } else {\n        this.tdsMenuDirective.onOpenChange.subscribe(collapsed => {\n          this.tdsCollapsed = collapsed;\n          this.tdsCollapsedChange.emit(collapsed);\n          this.updateStyleMap();\n          this.cdr.markForCheck();\n        });\n      }\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function TDSLayoutSiderComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSLayoutSiderComponent)(i0.ɵɵdirectiveInject(i1.Platform), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1$1.TDSBreakpointService));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TDSLayoutSiderComponent,\n      selectors: [[\"tds-layout-sider\"]],\n      contentQueries: function TDSLayoutSiderComponent_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, TDSMenuComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.tdsMenuDirective = _t.first);\n        }\n      },\n      hostAttrs: [1, \"tds-layout-sider\"],\n      hostVars: 20,\n      hostBindings: function TDSLayoutSiderComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"flex\", ctx.flexSetting)(\"max-width\", ctx.widthSetting)(\"min-width\", ctx.widthSetting)(\"width\", ctx.widthSetting);\n          i0.ɵɵclassProp(\"tds-layout-sider-zero-width\", ctx.tdsCollapsed && ctx.tdsCollapsedWidth === 0)(\"tds-layout-sider-light\", ctx.tdsTheme === \"light\")(\"tds-layout-sider-dark\", ctx.tdsTheme === \"dark\")(\"tds-layout-sider-default\", ctx.tdsTheme === \"default\")(\"tds-layout-sider-collapsed\", ctx.tdsCollapsed)(\"tds-layout-sider-has-trigger\", ctx.tdsCollapsible && ctx.tdsTrigger !== null);\n        }\n      },\n      inputs: {\n        tdsWidth: \"tdsWidth\",\n        tdsTheme: \"tdsTheme\",\n        tdsCollapsedWidth: \"tdsCollapsedWidth\",\n        tdsBreakpoint: \"tdsBreakpoint\",\n        tdsZeroTrigger: \"tdsZeroTrigger\",\n        tdsTrigger: \"tdsTrigger\",\n        tdsReverseArrow: \"tdsReverseArrow\",\n        tdsCollapsible: \"tdsCollapsible\",\n        tdsCollapsed: \"tdsCollapsed\"\n      },\n      outputs: {\n        tdsCollapsedChange: \"tdsCollapsedChange\"\n      },\n      exportAs: [\"tdsLayoutSider\"],\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 3,\n      vars: 1,\n      consts: [[1, \"tds-layout-sider-children\"], [\"tds-layout-sider-trigger\", \"\", 3, \"matchBreakPoint\", \"tdsCollapsedWidth\", \"tdsCollapsed\", \"tdsBreakpoint\", \"tdsReverseArrow\", \"tdsTrigger\", \"tdsZeroTrigger\", \"siderWidth\"], [\"tds-layout-sider-trigger\", \"\", 3, \"click\", \"matchBreakPoint\", \"tdsCollapsedWidth\", \"tdsCollapsed\", \"tdsBreakpoint\", \"tdsReverseArrow\", \"tdsTrigger\", \"tdsZeroTrigger\", \"siderWidth\"]],\n      template: function TDSLayoutSiderComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵprojection(1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(2, TDSLayoutSiderComponent_Conditional_2_Template, 1, 8, \"div\", 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(ctx.tdsCollapsible && ctx.tdsTrigger !== null ? 2 : -1);\n        }\n      },\n      dependencies: [TDSLayoutSiderTriggerComponent],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n__decorate([InputBoolean()], TDSLayoutSiderComponent.prototype, \"tdsReverseArrow\", void 0);\n__decorate([InputBoolean()], TDSLayoutSiderComponent.prototype, \"tdsCollapsible\", void 0);\n__decorate([InputBoolean()], TDSLayoutSiderComponent.prototype, \"tdsCollapsed\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSLayoutSiderComponent, [{\n    type: Component,\n    args: [{\n      selector: 'tds-layout-sider',\n      exportAs: 'tdsLayoutSider',\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    <div class=\"tds-layout-sider-children\">\n      <ng-content></ng-content>\n    </div>\n    @if (tdsCollapsible && tdsTrigger !== null) {\n      <div\n        tds-layout-sider-trigger\n        [matchBreakPoint]=\"matchBreakPoint\"\n        [tdsCollapsedWidth]=\"tdsCollapsedWidth\"\n        [tdsCollapsed]=\"tdsCollapsed\"\n        [tdsBreakpoint]=\"tdsBreakpoint\"\n        [tdsReverseArrow]=\"tdsReverseArrow\"\n        [tdsTrigger]=\"tdsTrigger\"\n        [tdsZeroTrigger]=\"tdsZeroTrigger\"\n        [siderWidth]=\"widthSetting\"\n        (click)=\"setCollapsed(!tdsCollapsed)\"\n      ></div>\n    }\n    `,\n      host: {\n        class: 'tds-layout-sider',\n        '[class.tds-layout-sider-zero-width]': `tdsCollapsed && tdsCollapsedWidth === 0`,\n        '[class.tds-layout-sider-light]': `tdsTheme === 'light'`,\n        '[class.tds-layout-sider-dark]': `tdsTheme === 'dark'`,\n        '[class.tds-layout-sider-default]': `tdsTheme === 'default'`,\n        '[class.tds-layout-sider-collapsed]': `tdsCollapsed`,\n        '[class.tds-layout-sider-has-trigger]': `tdsCollapsible && tdsTrigger !== null`,\n        '[style.flex]': 'flexSetting',\n        '[style.maxWidth]': 'widthSetting',\n        '[style.minWidth]': 'widthSetting',\n        '[style.width]': 'widthSetting'\n      },\n      standalone: true,\n      imports: [TDSLayoutSiderTriggerComponent]\n    }]\n  }], () => [{\n    type: i1.Platform\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i1$1.TDSBreakpointService\n  }], {\n    tdsMenuDirective: [{\n      type: ContentChild,\n      args: [TDSMenuComponent]\n    }],\n    tdsCollapsedChange: [{\n      type: Output\n    }],\n    tdsWidth: [{\n      type: Input\n    }],\n    tdsTheme: [{\n      type: Input\n    }],\n    tdsCollapsedWidth: [{\n      type: Input\n    }],\n    tdsBreakpoint: [{\n      type: Input\n    }],\n    tdsZeroTrigger: [{\n      type: Input\n    }],\n    tdsTrigger: [{\n      type: Input\n    }],\n    tdsReverseArrow: [{\n      type: Input\n    }],\n    tdsCollapsible: [{\n      type: Input\n    }],\n    tdsCollapsed: [{\n      type: Input\n    }]\n  });\n})();\nclass TDSLayoutComponent {\n  constructor(directionality) {\n    this.directionality = directionality;\n    this.tdsTheme = 'default';\n    this.dir = 'ltr';\n    this.destroy$ = new Subject();\n  }\n  ngOnInit() {\n    this.dir = this.directionality.value;\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n    });\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function TDSLayoutComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSLayoutComponent)(i0.ɵɵdirectiveInject(i1$2.Directionality));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TDSLayoutComponent,\n      selectors: [[\"tds-layout\"]],\n      contentQueries: function TDSLayoutComponent_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, TDSLayoutSiderComponent, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listOfTDSLayoutSiderComponent = _t);\n        }\n      },\n      hostAttrs: [1, \"tds-layout\"],\n      hostVars: 10,\n      hostBindings: function TDSLayoutComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"tds-layout-rtl\", ctx.dir === \"rtl\")(\"tds-layout-has-sider\", ctx.listOfTDSLayoutSiderComponent.length > 0)(\"tds-layout-theme-default\", ctx.tdsTheme === \"default\")(\"tds-layout-theme-light\", ctx.tdsTheme === \"light\")(\"tds-layout-theme-dark\", ctx.tdsTheme === \"dark\");\n        }\n      },\n      inputs: {\n        tdsTheme: \"tdsTheme\"\n      },\n      exportAs: [\"tdsLayout\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 1,\n      vars: 0,\n      template: function TDSLayoutComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSLayoutComponent, [{\n    type: Component,\n    args: [{\n      selector: 'tds-layout',\n      exportAs: 'tdsLayout',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      preserveWhitespaces: false,\n      template: ` <ng-content></ng-content> `,\n      host: {\n        class: 'tds-layout',\n        '[class.tds-layout-rtl]': `dir === 'rtl'`,\n        '[class.tds-layout-has-sider]': 'listOfTDSLayoutSiderComponent.length > 0',\n        '[class.tds-layout-theme-default]': `tdsTheme === 'default'`,\n        '[class.tds-layout-theme-light]': `tdsTheme === 'light'`,\n        '[class.tds-layout-theme-dark]': `tdsTheme === 'dark'`\n      },\n      standalone: true\n    }]\n  }], () => [{\n    type: i1$2.Directionality\n  }], {\n    tdsTheme: [{\n      type: Input\n    }],\n    listOfTDSLayoutSiderComponent: [{\n      type: ContentChildren,\n      args: [TDSLayoutSiderComponent]\n    }]\n  });\n})();\nclass TDSLayoutDeviceContentDirective {\n  constructor(templateRef) {\n    this.templateRef = templateRef;\n  }\n  static {\n    this.ɵfac = function TDSLayoutDeviceContentDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSLayoutDeviceContentDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TDSLayoutDeviceContentDirective,\n      selectors: [[\"\", \"tdsLayoutDeviceContent\", \"\"]],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSLayoutDeviceContentDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[tdsLayoutDeviceContent]',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], null);\n})();\nclass TDSLayoutDeviceComponent {\n  constructor(breakpointService, destroy$, cdr) {\n    this.breakpointService = breakpointService;\n    this.destroy$ = destroy$;\n    this.cdr = cdr;\n    this.tdsDevice = 'desktop';\n    this.isMatch = false;\n  }\n  ngOnInit() {\n    this.breakpointService.subscribe(tdsLayoutResponsiveMap).pipe(takeUntil(this.destroy$)).subscribe(bp => {\n      const isMatch = this.convertSize2BreakPoint().indexOf(bp) > -1;\n      if (isMatch != this.isMatch) {\n        this.isMatch = isMatch;\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  convertSize2BreakPoint() {\n    let result = ['xl', 'xxl'];\n    switch (this.tdsDevice) {\n      case 'mobile':\n        result = ['xs', 'sm'];\n        break;\n      case 'tablet':\n        result = ['md', 'lg'];\n        break;\n      default:\n        result = ['xl', 'xxl'];\n        break;\n    }\n    return result;\n  }\n  static {\n    this.ɵfac = function TDSLayoutDeviceComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSLayoutDeviceComponent)(i0.ɵɵdirectiveInject(i1$1.TDSBreakpointService), i0.ɵɵdirectiveInject(i1$1.TDSDestroyService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TDSLayoutDeviceComponent,\n      selectors: [[\"tds-layout-device\"]],\n      contentQueries: function TDSLayoutDeviceComponent_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, TDSLayoutDeviceContentDirective, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.content = _t.first);\n        }\n      },\n      inputs: {\n        tdsDevice: \"tdsDevice\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([TDSDestroyService]), i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 2,\n      vars: 2,\n      consts: [[3, \"ngTemplateOutlet\"]],\n      template: function TDSLayoutDeviceComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, TDSLayoutDeviceComponent_Conditional_0_Template, 1, 0)(1, TDSLayoutDeviceComponent_Conditional_1_Template, 1, 1, \"ng-container\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵconditional(ctx.isMatch ? 0 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx.isMatch ? 1 : -1);\n        }\n      },\n      dependencies: [NgTemplateOutlet],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSLayoutDeviceComponent, [{\n    type: Component,\n    args: [{\n      selector: 'tds-layout-device',\n      template: `\n @if (isMatch) {\n   <ng-content></ng-content>\n }\n @if (isMatch) {\n   <ng-container [ngTemplateOutlet]=\"content!.templateRef\"></ng-container>\n }\n \n `,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [TDSDestroyService],\n      standalone: true,\n      imports: [NgTemplateOutlet]\n    }]\n  }], () => [{\n    type: i1$1.TDSBreakpointService\n  }, {\n    type: i1$1.TDSDestroyService\n  }, {\n    type: i0.ChangeDetectorRef\n  }], {\n    tdsDevice: [{\n      type: Input\n    }],\n    content: [{\n      type: ContentChild,\n      args: [TDSLayoutDeviceContentDirective]\n    }]\n  });\n})();\nclass TDSLayoutDeviceDirective {\n  constructor(breakpointService, destroy$, viewContainer, template) {\n    this.breakpointService = breakpointService;\n    this.destroy$ = destroy$;\n    this.viewContainer = viewContainer;\n    this.template = template;\n    this.tdsLayoutDevice = 'desktop';\n    this.hasView = false;\n  }\n  ngOnInit() {\n    this.breakpointService.subscribe(tdsLayoutResponsiveMap).pipe(takeUntil(this.destroy$)).subscribe(bp => {\n      const isMatch = this.convertSize2BreakPoint().indexOf(bp) > -1;\n      // create view if true and not created already\n      if (isMatch && !this.hasView) {\n        this.hasView = true;\n        this.viewContainer.createEmbeddedView(this.template);\n      }\n      // destroy view if false and created\n      if (!isMatch && this.hasView) {\n        this.hasView = false;\n        this.viewContainer.clear();\n      }\n    });\n  }\n  convertSize2BreakPoint() {\n    let result = ['xl', 'xxl'];\n    switch (this.tdsLayoutDevice) {\n      case 'mobile':\n        result = ['xs', 'sm'];\n        break;\n      case 'tablet':\n        result = ['md', 'lg'];\n        break;\n      default:\n        result = ['xl', 'xxl'];\n        break;\n    }\n    return result;\n  }\n  static {\n    this.ɵfac = function TDSLayoutDeviceDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSLayoutDeviceDirective)(i0.ɵɵdirectiveInject(i1$1.TDSBreakpointService), i0.ɵɵdirectiveInject(i1$1.TDSDestroyService), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TDSLayoutDeviceDirective,\n      selectors: [[\"\", \"tdsLayoutDevice\", \"\"]],\n      inputs: {\n        tdsLayoutDevice: \"tdsLayoutDevice\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([TDSDestroyService])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSLayoutDeviceDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[tdsLayoutDevice]',\n      providers: [TDSDestroyService],\n      standalone: true\n    }]\n  }], () => [{\n    type: i1$1.TDSBreakpointService\n  }, {\n    type: i1$1.TDSDestroyService\n  }, {\n    type: i0.ViewContainerRef\n  }, {\n    type: i0.TemplateRef\n  }], {\n    tdsLayoutDevice: [{\n      type: Input\n    }]\n  });\n})();\nclass TDSLayoutModule {\n  static {\n    this.ɵfac = function TDSLayoutModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSLayoutModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: TDSLayoutModule,\n      imports: [TDSLayoutComponent, TDSLayoutHeaderComponent, TDSLayoutContentComponent, TDSLayoutFooterComponent, TDSLayoutSiderComponent, TDSLayoutSiderTriggerComponent, TDSLayoutDeviceComponent, TDSLayoutDeviceContentDirective, TDSLayoutDeviceDirective],\n      exports: [TDSLayoutComponent, TDSLayoutHeaderComponent, TDSLayoutContentComponent, TDSLayoutFooterComponent, TDSLayoutSiderComponent, TDSLayoutDeviceComponent, TDSLayoutDeviceContentDirective, TDSLayoutDeviceDirective]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSLayoutModule, [{\n    type: NgModule,\n    args: [{\n      imports: [TDSLayoutComponent, TDSLayoutHeaderComponent, TDSLayoutContentComponent, TDSLayoutFooterComponent, TDSLayoutSiderComponent, TDSLayoutSiderTriggerComponent, TDSLayoutDeviceComponent, TDSLayoutDeviceContentDirective, TDSLayoutDeviceDirective],\n      exports: [TDSLayoutComponent, TDSLayoutHeaderComponent, TDSLayoutContentComponent, TDSLayoutFooterComponent, TDSLayoutSiderComponent, TDSLayoutDeviceComponent, TDSLayoutDeviceContentDirective, TDSLayoutDeviceDirective]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TDSLayoutComponent, TDSLayoutContentComponent, TDSLayoutDeviceComponent, TDSLayoutDeviceContentDirective, TDSLayoutDeviceDirective, TDSLayoutFooterComponent, TDSLayoutHeaderComponent, TDSLayoutModule, TDSLayoutSiderComponent, TDSLayoutSiderTriggerComponent as ɵTDSLayoutSiderTriggerComponent };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,kBAAwB;AACxB,uBAA0B;AAS1B,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,MAAM,CAAC,4BAA4B,EAAE;AAC3C,SAAS,oEAAoE,IAAI,KAAK;AAAC;AACvF,SAAS,sDAAsD,IAAI,KAAK;AACtE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,qEAAqE,GAAG,GAAG,eAAe,CAAC;AAAA,EAC9G;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,UAAM,wBAA2B,YAAY,CAAC;AAC9C,IAAG,WAAW,oBAAoB,OAAO,kBAAkB,qBAAqB;AAAA,EAClF;AACF;AACA,SAAS,oEAAoE,IAAI,KAAK;AAAC;AACvF,SAAS,sDAAsD,IAAI,KAAK;AACtE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,qEAAqE,GAAG,GAAG,eAAe,CAAC;AAAA,EAC9G;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,UAAM,oBAAuB,YAAY,CAAC;AAC1C,IAAG,WAAW,oBAAoB,OAAO,cAAc,iBAAiB;AAAA,EAC1E;AACF;AACA,SAAS,sDAAsD,IAAI,KAAK;AAAC;AACzE,SAAS,sDAAsD,IAAI,KAAK;AAAC;AACzE,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,SAAS,SAAS,sEAAsE;AACpG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,aAAa,CAAC,OAAO,YAAY,CAAC;AAAA,IACjE,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,mBAAmB,OAAO,eAAe,EAAE,qBAAqB,OAAO,iBAAiB,EAAE,gBAAgB,OAAO,YAAY,EAAE,iBAAiB,OAAO,aAAa,EAAE,mBAAmB,OAAO,eAAe,EAAE,cAAc,OAAO,UAAU,EAAE,kBAAkB,OAAO,cAAc,EAAE,cAAc,OAAO,YAAY;AAAA,EAC5U;AACF;AACA,SAAS,gDAAgD,IAAI,KAAK;AAChE,MAAI,KAAK,GAAG;AACV,IAAG,aAAa,CAAC;AAAA,EACnB;AACF;AACA,SAAS,gDAAgD,IAAI,KAAK;AAChE,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,GAAG,CAAC;AAAA,EAC5B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,oBAAoB,OAAO,QAAQ,WAAW;AAAA,EAC9D;AACF;AACA,IAAM,4BAAN,MAAM,2BAA0B;AAAA,EAC9B,cAAc;AAAA,EAAC;AAAA,EACf,OAAO;AACL,SAAK,OAAO,SAAS,kCAAkC,mBAAmB;AACxE,aAAO,KAAK,qBAAqB,4BAA2B;AAAA,IAC9D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,oBAAoB,CAAC;AAAA,MAClC,WAAW,CAAC,GAAG,sBAAsB,mBAAmB;AAAA,MACxD,UAAU,CAAC,kBAAkB;AAAA,MAC7B,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,MACjC,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,UAAU,SAAS,mCAAmC,IAAI,KAAK;AAC7D,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,aAAa,CAAC;AAAA,QACnB;AAAA,MACF;AAAA,MACA,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,2BAA2B,CAAC;AAAA,IAClG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AACH,IAAM,2BAAN,MAAM,0BAAyB;AAAA,EAC7B,cAAc;AAAA,EAAC;AAAA,EACf,OAAO;AACL,SAAK,OAAO,SAAS,iCAAiC,mBAAmB;AACvE,aAAO,KAAK,qBAAqB,2BAA0B;AAAA,IAC7D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,mBAAmB,CAAC;AAAA,MACjC,WAAW,CAAC,GAAG,mBAAmB;AAAA,MAClC,UAAU,CAAC,iBAAiB;AAAA,MAC5B,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,MACjC,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,UAAU,SAAS,kCAAkC,IAAI,KAAK;AAC5D,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,aAAa,CAAC;AAAA,QACnB;AAAA,MACF;AAAA,MACA,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AACH,IAAM,2BAAN,MAAM,0BAAyB;AAAA,EAC7B,cAAc;AAAA,EAAC;AAAA,EACf,OAAO;AACL,SAAK,OAAO,SAAS,iCAAiC,mBAAmB;AACvE,aAAO,KAAK,qBAAqB,2BAA0B;AAAA,IAC7D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,mBAAmB,CAAC;AAAA,MACjC,gBAAgB,SAAS,wCAAwC,IAAI,KAAK,UAAU;AAClF,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,UAAU,oBAAoB,CAAC;AAAA,QACnD;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,2BAA2B;AAAA,QAC9E;AAAA,MACF;AAAA,MACA,WAAW,CAAC,GAAG,mBAAmB;AAAA,MAClC,UAAU;AAAA,MACV,cAAc,SAAS,sCAAsC,IAAI,KAAK;AACpE,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,yBAAyB,IAAI,yBAAyB,SAAS,CAAC;AAAA,QACjF;AAAA,MACF;AAAA,MACA,UAAU,CAAC,iBAAiB;AAAA,MAC5B,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,MACjC,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,UAAU,SAAS,kCAAkC,IAAI,KAAK;AAC5D,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,aAAa,CAAC;AAAA,QACnB;AAAA,MACF;AAAA,MACA,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,qBAAqB;AAAA,MACrB,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,iCAAiC;AAAA,MACnC;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,0BAA0B,CAAC;AAAA,MACzB,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,IAC3B,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,iCAAN,MAAM,gCAA+B;AAAA,EACnC,cAAc;AACZ,SAAK,eAAe;AACpB,SAAK,kBAAkB;AACvB,SAAK,iBAAiB;AACtB,SAAK,aAAa;AAClB,SAAK,kBAAkB;AACvB,SAAK,oBAAoB;AACzB,SAAK,aAAa;AAClB,SAAK,gBAAgB;AACrB,SAAK,gBAAgB;AACrB,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACA,oBAAoB;AAClB,SAAK,gBAAgB,KAAK,sBAAsB,MAAM,KAAK,iBAAiB,KAAK,mBAAmB,CAAC,KAAK;AAC1G,SAAK,kBAAkB,KAAK,sBAAsB;AAAA,EACpD;AAAA,EACA,WAAW;AACT,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACA,cAAc;AACZ,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,uCAAuC,mBAAmB;AAC7E,aAAO,KAAK,qBAAqB,iCAAgC;AAAA,IACnE;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,4BAA4B,EAAE,CAAC;AAAA,MAChD,UAAU;AAAA,MACV,cAAc,SAAS,4CAA4C,IAAI,KAAK;AAC1E,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,SAAS,IAAI,kBAAkB,IAAI,aAAa,IAAI;AACnE,UAAG,YAAY,4BAA4B,IAAI,eAAe,EAAE,uCAAuC,IAAI,aAAa,EAAE,6CAA6C,IAAI,iBAAiB,IAAI,eAAe,EAAE,4CAA4C,IAAI,iBAAiB,CAAC,IAAI,eAAe;AAAA,QACxS;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,cAAc;AAAA,QACd,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,iBAAiB;AAAA,QACjB,mBAAmB;AAAA,QACnB,YAAY;AAAA,QACZ,eAAe;AAAA,MACjB;AAAA,MACA,UAAU,CAAC,uBAAuB;AAAA,MAClC,YAAY;AAAA,MACZ,UAAU,CAAI,sBAAyB,mBAAmB;AAAA,MAC1D,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,kBAAkB,EAAE,GAAG,CAAC,sBAAsB,EAAE,GAAG,CAAC,GAAG,kBAAkB,CAAC;AAAA,MACpF,UAAU,SAAS,wCAAwC,IAAI,KAAK;AAClE,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,GAAG,uDAAuD,GAAG,GAAG,MAAM,CAAC,EAAE,GAAG,uDAAuD,GAAG,GAAG,MAAM,CAAC,EAAE,GAAG,uDAAuD,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,uDAAuD,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AAAA,QACtY;AACA,YAAI,KAAK,GAAG;AACV,UAAG,cAAc,IAAI,gBAAgB,IAAI,EAAE;AAC3C,UAAG,UAAU;AACb,UAAG,cAAc,IAAI,kBAAkB,IAAI,EAAE;AAAA,QAC/C;AAAA,MACF;AAAA,MACA,cAAc,CAAC,gBAAgB;AAAA,MAC/B,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gCAAgC,CAAC;AAAA,IACvG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAeV,MAAM;AAAA,QACJ,oCAAoC;AAAA,QACpC,iBAAiB;AAAA,QACjB,+CAA+C;AAAA,QAC/C,qDAAqD;AAAA,QACrD,oDAAoD;AAAA,MACtD;AAAA,MACA,YAAY;AAAA,MACZ,SAAS,CAAC,gBAAgB;AAAA,IAC5B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,0BAAN,MAAM,yBAAwB;AAAA,EAC5B,iBAAiB;AACf,SAAK,eAAe,KAAK,eAAe,GAAG,KAAK,iBAAiB,OAAO,WAAW,KAAK,QAAQ;AAChG,SAAK,cAAc,OAAO,KAAK,YAAY;AAC3C,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,4BAA4B;AAC1B,QAAI,KAAK,oBAAoB,KAAK,iBAAiB,mBAAmB,SAAS,KAAK,sBAAsB,GAAG;AAC3G,WAAK,iBAAiB,mBAAmB,KAAK,YAAY;AAAA,IAC5D;AAAA,EACF;AAAA,EACA,aAAa,WAAW;AACtB,QAAI,cAAc,KAAK,cAAc;AACnC,WAAK,eAAe;AACpB,WAAK,mBAAmB,KAAK,SAAS;AACtC,WAAK,0BAA0B;AAC/B,WAAK,eAAe;AACpB,WAAK,IAAI,aAAa;AAAA,IACxB;AAAA,EACF;AAAA,EACA,YAAY,UAAU,KAAK,mBAAmB;AAC5C,SAAK,WAAW;AAChB,SAAK,MAAM;AACX,SAAK,oBAAoB;AACzB,SAAK,WAAW,IAAI,oBAAQ;AAC5B,SAAK,mBAAmB;AACxB,SAAK,qBAAqB,IAAI,aAAa;AAC3C,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,oBAAoB;AACzB,SAAK,gBAAgB;AACrB,SAAK,iBAAiB;AACtB,SAAK,aAAa;AAClB,SAAK,kBAAkB;AACvB,SAAK,iBAAiB;AACtB,SAAK,eAAe;AACpB,SAAK,kBAAkB;AACvB,SAAK,cAAc;AACnB,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,WAAW;AACT,SAAK,eAAe;AACpB,QAAI,KAAK,SAAS,WAAW;AAC3B,WAAK,kBAAkB,UAAU,oBAAoB,IAAI,EAAE,SAAK,4BAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,SAAO;AACzG,cAAM,aAAa,KAAK;AACxB,YAAI,YAAY;AACd,qBAAW,EAAE,UAAU,MAAM;AAC3B,iBAAK,kBAAkB,CAAC,IAAI,UAAU;AACtC,iBAAK,aAAa,KAAK,eAAe;AACtC,iBAAK,IAAI,aAAa;AAAA,UACxB,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,gBAAgB,qBAAqB,UAAU;AACjD,WAAK,eAAe;AAAA,IACtB;AACA,QAAI,cAAc;AAChB,WAAK,0BAA0B;AAAA,IACjC;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,SAAK,0BAA0B;AAC/B,QAAI,KAAK,kBAAkB;AACzB,UAAI,KAAK,kBAAkB,KAAK,cAAc,MAAM;AAClD,aAAK,iBAAiB,aAAa;AAAA,MACrC,OAAO;AACL,aAAK,iBAAiB,aAAa,UAAU,eAAa;AACxD,eAAK,eAAe;AACpB,eAAK,mBAAmB,KAAK,SAAS;AACtC,eAAK,eAAe;AACpB,eAAK,IAAI,aAAa;AAAA,QACxB,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,gCAAgC,mBAAmB;AACtE,aAAO,KAAK,qBAAqB,0BAA4B,kBAAqB,QAAQ,GAAM,kBAAqB,iBAAiB,GAAM,kBAAuB,oBAAoB,CAAC;AAAA,IAC1L;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,kBAAkB,CAAC;AAAA,MAChC,gBAAgB,SAAS,uCAAuC,IAAI,KAAK,UAAU;AACjF,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,UAAU,kBAAkB,CAAC;AAAA,QACjD;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,mBAAmB,GAAG;AAAA,QACzE;AAAA,MACF;AAAA,MACA,WAAW,CAAC,GAAG,kBAAkB;AAAA,MACjC,UAAU;AAAA,MACV,cAAc,SAAS,qCAAqC,IAAI,KAAK;AACnE,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,QAAQ,IAAI,WAAW,EAAE,aAAa,IAAI,YAAY,EAAE,aAAa,IAAI,YAAY,EAAE,SAAS,IAAI,YAAY;AAC/H,UAAG,YAAY,+BAA+B,IAAI,gBAAgB,IAAI,sBAAsB,CAAC,EAAE,0BAA0B,IAAI,aAAa,OAAO,EAAE,yBAAyB,IAAI,aAAa,MAAM,EAAE,4BAA4B,IAAI,aAAa,SAAS,EAAE,8BAA8B,IAAI,YAAY,EAAE,gCAAgC,IAAI,kBAAkB,IAAI,eAAe,IAAI;AAAA,QAC5X;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,UAAU;AAAA,QACV,mBAAmB;AAAA,QACnB,eAAe;AAAA,QACf,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,cAAc;AAAA,MAChB;AAAA,MACA,SAAS;AAAA,QACP,oBAAoB;AAAA,MACtB;AAAA,MACA,UAAU,CAAC,gBAAgB;AAAA,MAC3B,YAAY;AAAA,MACZ,UAAU,CAAI,sBAAyB,mBAAmB;AAAA,MAC1D,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,2BAA2B,GAAG,CAAC,4BAA4B,IAAI,GAAG,mBAAmB,qBAAqB,gBAAgB,iBAAiB,mBAAmB,cAAc,kBAAkB,YAAY,GAAG,CAAC,4BAA4B,IAAI,GAAG,SAAS,mBAAmB,qBAAqB,gBAAgB,iBAAiB,mBAAmB,cAAc,kBAAkB,YAAY,CAAC;AAAA,MAChZ,UAAU,SAAS,iCAAiC,IAAI,KAAK;AAC3D,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,UAAG,aAAa,CAAC;AACjB,UAAG,aAAa;AAChB,UAAG,WAAW,GAAG,gDAAgD,GAAG,GAAG,OAAO,CAAC;AAAA,QACjF;AACA,YAAI,KAAK,GAAG;AACV,UAAG,UAAU,CAAC;AACd,UAAG,cAAc,IAAI,kBAAkB,IAAI,eAAe,OAAO,IAAI,EAAE;AAAA,QACzE;AAAA,MACF;AAAA,MACA,cAAc,CAAC,8BAA8B;AAAA,MAC7C,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,aAAa,CAAC,GAAG,wBAAwB,WAAW,mBAAmB,MAAM;AACzF,WAAW,CAAC,aAAa,CAAC,GAAG,wBAAwB,WAAW,kBAAkB,MAAM;AACxF,WAAW,CAAC,aAAa,CAAC,GAAG,wBAAwB,WAAW,gBAAgB,MAAM;AAAA,CACrF,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAmBV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,uCAAuC;AAAA,QACvC,kCAAkC;AAAA,QAClC,iCAAiC;AAAA,QACjC,oCAAoC;AAAA,QACpC,sCAAsC;AAAA,QACtC,wCAAwC;AAAA,QACxC,gBAAgB;AAAA,QAChB,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,iBAAiB;AAAA,MACnB;AAAA,MACA,YAAY;AAAA,MACZ,SAAS,CAAC,8BAA8B;AAAA,IAC1C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAW;AAAA,EACb,CAAC,GAAG;AAAA,IACF,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,YAAY,gBAAgB;AAC1B,SAAK,iBAAiB;AACtB,SAAK,WAAW;AAChB,SAAK,MAAM;AACX,SAAK,WAAW,IAAI,oBAAQ;AAAA,EAC9B;AAAA,EACA,WAAW;AACT,SAAK,MAAM,KAAK,eAAe;AAC/B,SAAK,eAAe,QAAQ,SAAK,4BAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAChF,WAAK,MAAM;AAAA,IACb,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,2BAA2B,mBAAmB;AACjE,aAAO,KAAK,qBAAqB,qBAAuB,kBAAuB,cAAc,CAAC;AAAA,IAChG;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,YAAY,CAAC;AAAA,MAC1B,gBAAgB,SAAS,kCAAkC,IAAI,KAAK,UAAU;AAC5E,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,UAAU,yBAAyB,CAAC;AAAA,QACxD;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,gCAAgC;AAAA,QACnF;AAAA,MACF;AAAA,MACA,WAAW,CAAC,GAAG,YAAY;AAAA,MAC3B,UAAU;AAAA,MACV,cAAc,SAAS,gCAAgC,IAAI,KAAK;AAC9D,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,kBAAkB,IAAI,QAAQ,KAAK,EAAE,wBAAwB,IAAI,8BAA8B,SAAS,CAAC,EAAE,4BAA4B,IAAI,aAAa,SAAS,EAAE,0BAA0B,IAAI,aAAa,OAAO,EAAE,yBAAyB,IAAI,aAAa,MAAM;AAAA,QACxR;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,MACZ;AAAA,MACA,UAAU,CAAC,WAAW;AAAA,MACtB,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,MACjC,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,UAAU,SAAS,4BAA4B,IAAI,KAAK;AACtD,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,aAAa,CAAC;AAAA,QACnB;AAAA,MACF;AAAA,MACA,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,qBAAqB;AAAA,MACrB,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,gCAAgC;AAAA,QAChC,oCAAoC;AAAA,QACpC,kCAAkC;AAAA,QAClC,iCAAiC;AAAA,MACnC;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAW;AAAA,EACb,CAAC,GAAG;AAAA,IACF,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,+BAA+B,CAAC;AAAA,MAC9B,MAAM;AAAA,MACN,MAAM,CAAC,uBAAuB;AAAA,IAChC,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,kCAAN,MAAM,iCAAgC;AAAA,EACpC,YAAY,aAAa;AACvB,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,wCAAwC,mBAAmB;AAC9E,aAAO,KAAK,qBAAqB,kCAAoC,kBAAqB,WAAW,CAAC;AAAA,IACxG;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,0BAA0B,EAAE,CAAC;AAAA,MAC9C,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iCAAiC,CAAC;AAAA,IACxG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,2BAAN,MAAM,0BAAyB;AAAA,EAC7B,YAAY,mBAAmB,UAAU,KAAK;AAC5C,SAAK,oBAAoB;AACzB,SAAK,WAAW;AAChB,SAAK,MAAM;AACX,SAAK,YAAY;AACjB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,WAAW;AACT,SAAK,kBAAkB,UAAU,sBAAsB,EAAE,SAAK,4BAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,QAAM;AACtG,YAAM,UAAU,KAAK,uBAAuB,EAAE,QAAQ,EAAE,IAAI;AAC5D,UAAI,WAAW,KAAK,SAAS;AAC3B,aAAK,UAAU;AACf,aAAK,IAAI,aAAa;AAAA,MACxB;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,yBAAyB;AACvB,QAAI,SAAS,CAAC,MAAM,KAAK;AACzB,YAAQ,KAAK,WAAW;AAAA,MACtB,KAAK;AACH,iBAAS,CAAC,MAAM,IAAI;AACpB;AAAA,MACF,KAAK;AACH,iBAAS,CAAC,MAAM,IAAI;AACpB;AAAA,MACF;AACE,iBAAS,CAAC,MAAM,KAAK;AACrB;AAAA,IACJ;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,iCAAiC,mBAAmB;AACvE,aAAO,KAAK,qBAAqB,2BAA6B,kBAAuB,oBAAoB,GAAM,kBAAuB,iBAAiB,GAAM,kBAAqB,iBAAiB,CAAC;AAAA,IACtM;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,mBAAmB,CAAC;AAAA,MACjC,gBAAgB,SAAS,wCAAwC,IAAI,KAAK,UAAU;AAClF,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,UAAU,iCAAiC,CAAC;AAAA,QAChE;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,UAAU,GAAG;AAAA,QAChE;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,WAAW;AAAA,MACb;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC,iBAAiB,CAAC,GAAM,mBAAmB;AAAA,MAC7E,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,kBAAkB,CAAC;AAAA,MAChC,UAAU,SAAS,kCAAkC,IAAI,KAAK;AAC5D,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,WAAW,GAAG,iDAAiD,GAAG,CAAC,EAAE,GAAG,iDAAiD,GAAG,GAAG,gBAAgB,CAAC;AAAA,QACrJ;AACA,YAAI,KAAK,GAAG;AACV,UAAG,cAAc,IAAI,UAAU,IAAI,EAAE;AACrC,UAAG,UAAU;AACb,UAAG,cAAc,IAAI,UAAU,IAAI,EAAE;AAAA,QACvC;AAAA,MACF;AAAA,MACA,cAAc,CAAC,gBAAgB;AAAA,MAC/B,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MASV,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,WAAW,CAAC,iBAAiB;AAAA,MAC7B,YAAY;AAAA,MACZ,SAAS,CAAC,gBAAgB;AAAA,IAC5B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,+BAA+B;AAAA,IACxC,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,2BAAN,MAAM,0BAAyB;AAAA,EAC7B,YAAY,mBAAmB,UAAU,eAAe,UAAU;AAChE,SAAK,oBAAoB;AACzB,SAAK,WAAW;AAChB,SAAK,gBAAgB;AACrB,SAAK,WAAW;AAChB,SAAK,kBAAkB;AACvB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,WAAW;AACT,SAAK,kBAAkB,UAAU,sBAAsB,EAAE,SAAK,4BAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,QAAM;AACtG,YAAM,UAAU,KAAK,uBAAuB,EAAE,QAAQ,EAAE,IAAI;AAE5D,UAAI,WAAW,CAAC,KAAK,SAAS;AAC5B,aAAK,UAAU;AACf,aAAK,cAAc,mBAAmB,KAAK,QAAQ;AAAA,MACrD;AAEA,UAAI,CAAC,WAAW,KAAK,SAAS;AAC5B,aAAK,UAAU;AACf,aAAK,cAAc,MAAM;AAAA,MAC3B;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,yBAAyB;AACvB,QAAI,SAAS,CAAC,MAAM,KAAK;AACzB,YAAQ,KAAK,iBAAiB;AAAA,MAC5B,KAAK;AACH,iBAAS,CAAC,MAAM,IAAI;AACpB;AAAA,MACF,KAAK;AACH,iBAAS,CAAC,MAAM,IAAI;AACpB;AAAA,MACF;AACE,iBAAS,CAAC,MAAM,KAAK;AACrB;AAAA,IACJ;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,iCAAiC,mBAAmB;AACvE,aAAO,KAAK,qBAAqB,2BAA6B,kBAAuB,oBAAoB,GAAM,kBAAuB,iBAAiB,GAAM,kBAAqB,gBAAgB,GAAM,kBAAqB,WAAW,CAAC;AAAA,IAC3O;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,mBAAmB,EAAE,CAAC;AAAA,MACvC,QAAQ;AAAA,QACN,iBAAiB;AAAA,MACnB;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC,iBAAiB,CAAC,CAAC;AAAA,IACvD,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC,iBAAiB;AAAA,MAC7B,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,OAAO;AACL,SAAK,OAAO,SAAS,wBAAwB,mBAAmB;AAC9D,aAAO,KAAK,qBAAqB,kBAAiB;AAAA,IACpD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,oBAAoB,0BAA0B,2BAA2B,0BAA0B,yBAAyB,gCAAgC,0BAA0B,iCAAiC,wBAAwB;AAAA,MACzP,SAAS,CAAC,oBAAoB,0BAA0B,2BAA2B,0BAA0B,yBAAyB,0BAA0B,iCAAiC,wBAAwB;AAAA,IAC3N,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB,CAAC,CAAC;AAAA,EACnD;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,oBAAoB,0BAA0B,2BAA2B,0BAA0B,yBAAyB,gCAAgC,0BAA0B,iCAAiC,wBAAwB;AAAA,MACzP,SAAS,CAAC,oBAAoB,0BAA0B,2BAA2B,0BAA0B,yBAAyB,0BAA0B,iCAAiC,wBAAwB;AAAA,IAC3N,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}