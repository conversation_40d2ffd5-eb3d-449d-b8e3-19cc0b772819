{"version": 3, "sources": ["../../../../../../node_modules/@angular/common/locales/vi.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val;\n  return 5;\n}\nexport default [\"vi\", [[\"s\", \"c\"], [\"SA\", \"CH\"], u], [[\"SA\", \"CH\"], u, u], [[\"CN\", \"T2\", \"T3\", \"T4\", \"T5\", \"T6\", \"T7\"], [\"CN\", \"Th 2\", \"Th 3\", \"Th 4\", \"Th 5\", \"Th 6\", \"Th 7\"], [\"<PERSON><PERSON> N<PERSON>ật\", \"Thứ Hai\", \"Thứ Ba\", \"Thứ Tư\", \"Th<PERSON> Năm\", \"<PERSON><PERSON><PERSON>\", \"Th<PERSON>\"], [\"CN\", \"T2\", \"T3\", \"T4\", \"T5\", \"T6\", \"T7\"]], u, [[\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\", \"10\", \"11\", \"12\"], [\"thg 1\", \"thg 2\", \"thg 3\", \"thg 4\", \"thg 5\", \"thg 6\", \"thg 7\", \"thg 8\", \"thg 9\", \"thg 10\", \"thg 11\", \"thg 12\"], [\"tháng 1\", \"tháng 2\", \"tháng 3\", \"tháng 4\", \"tháng 5\", \"tháng 6\", \"tháng 7\", \"tháng 8\", \"tháng 9\", \"tháng 10\", \"tháng 11\", \"tháng 12\"]], [[\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\", \"10\", \"11\", \"12\"], [\"Thg 1\", \"Thg 2\", \"Thg 3\", \"Thg 4\", \"Thg 5\", \"Thg 6\", \"Thg 7\", \"Thg 8\", \"Thg 9\", \"Thg 10\", \"Thg 11\", \"Thg 12\"], [\"Tháng 1\", \"Tháng 2\", \"Tháng 3\", \"Tháng 4\", \"Tháng 5\", \"Tháng 6\", \"Tháng 7\", \"Tháng 8\", \"Tháng 9\", \"Tháng 10\", \"Tháng 11\", \"Tháng 12\"]], [[\"tr. CN\", \"sau CN\"], [\"Trước CN\", \"Sau CN\"], [\"Trước Thiên Chúa\", \"Sau Công Nguyên\"]], 1, [6, 0], [\"dd/MM/y\", \"d MMM, y\", \"d MMMM, y\", \"EEEE, d MMMM, y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{0}, {1}\", u, \"{0} {1}\", u], [\",\", \".\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"#,##0.00 ¤\", \"#E0\"], \"VND\", \"₫\", \"Đồng Việt Nam\", {\n  \"AUD\": [\"AU$\", \"$\"],\n  \"BYN\": [u, \"р.\"],\n  \"PHP\": [u, \"₱\"],\n  \"THB\": [\"฿\"],\n  \"TWD\": [\"NT$\"],\n  \"USD\": [\"US$\", \"$\"],\n  \"XXX\": []\n}, \"ltr\", plural];\n"], "mappings": ";;;;AAQA,IAAM,IAAI;AACV,SAAS,OAAO,KAAK;AACnB,QAAM,IAAI;AACV,SAAO;AACT;AACA,IAAO,aAAQ,CAAC,MAAM,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,MAAM,IAAI,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI,GAAG,CAAC,MAAM,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM,GAAG,CAAC,YAAY,WAAW,UAAU,UAAU,WAAW,WAAW,SAAS,GAAG,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,MAAM,IAAI,GAAG,CAAC,SAAS,SAAS,SAAS,SAAS,SAAS,SAAS,SAAS,SAAS,SAAS,UAAU,UAAU,QAAQ,GAAG,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,YAAY,YAAY,UAAU,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,MAAM,IAAI,GAAG,CAAC,SAAS,SAAS,SAAS,SAAS,SAAS,SAAS,SAAS,SAAS,SAAS,UAAU,UAAU,QAAQ,GAAG,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,YAAY,YAAY,UAAU,CAAC,GAAG,CAAC,CAAC,UAAU,QAAQ,GAAG,CAAC,YAAY,QAAQ,GAAG,CAAC,oBAAoB,iBAAiB,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,WAAW,YAAY,aAAa,iBAAiB,GAAG,CAAC,SAAS,YAAY,cAAc,eAAe,GAAG,CAAC,YAAY,GAAG,WAAW,CAAC,GAAG,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,OAAO,GAAG,GAAG,CAAC,aAAa,UAAU,cAAc,KAAK,GAAG,OAAO,KAAK,iBAAiB;AAAA,EACnyC,OAAO,CAAC,OAAO,GAAG;AAAA,EAClB,OAAO,CAAC,GAAG,IAAI;AAAA,EACf,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG;AAAA,EACX,OAAO,CAAC,KAAK;AAAA,EACb,OAAO,CAAC,OAAO,GAAG;AAAA,EAClB,OAAO,CAAC;AACV,GAAG,OAAO,MAAM;", "names": []}