{"version": 3, "sources": ["../../../../../../node_modules/tds-ui/fesm2022/tds-ui-avatar.mjs"], "sourcesContent": ["import { __decorate } from 'tslib';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, Output, ViewChild, ContentChildren, NgModule } from '@angular/core';\nimport * as i2 from 'tds-ui/cdk/pipes/mapper';\nimport { TDSMapperPipeModule } from 'tds-ui/cdk/pipes/mapper';\nimport * as i1 from 'tds-ui/core/config';\nimport { WithConfig } from 'tds-ui/core/config';\nimport { TDSHelperString, InputNumber, InputBoolean } from 'tds-ui/shared/utility';\nconst _c0 = [\"textEl\"];\nfunction TDSAvatarComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\");\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r0.icon);\n  }\n}\nfunction TDSAvatarComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"img\", 6);\n    i0.ɵɵlistener(\"error\", function TDSAvatarComponent_Conditional_1_Template_img_error_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.imgError($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"src\", ctx_r0.tdsSrc, i0.ɵɵsanitizeUrl);\n    i0.ɵɵattribute(\"srcset\", ctx_r0.tdsSrcSet)(\"alt\", ctx_r0.alt)(\"loading\", ctx_r0.attrLoading);\n  }\n}\nfunction TDSAvatarComponent_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 3, 0);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"tdsMapper\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(3, 1, ctx_r0.text, ctx_r0.mapperText));\n  }\n}\nfunction TDSAvatarComponent_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 4);\n    i0.ɵɵelement(1, \"path\", 7)(2, \"path\", 8)(3, \"path\", 9);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TDSAvatarComponent_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 5);\n    i0.ɵɵelement(1, \"rect\", 10)(2, \"path\", 11)(3, \"path\", 12);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TDSAvatarComponent_Conditional_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 2);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"src\", ctx_r0.tdsSrcPlaceholder, i0.ɵɵsanitizeUrl);\n    i0.ɵɵattribute(\"srcset\", ctx_r0.tdsSrcPlaceholder)(\"alt\", ctx_r0.alt)(\"loading\", ctx_r0.attrLoading);\n  }\n}\nconst _c1 = [\"*\"];\nconst TDS_CONFIG_MODULE_NAME = 'avatar';\nclass TDSAvatarComponent {\n  constructor(tdsConfigService, elementRef, cdr) {\n    this.tdsConfigService = tdsConfigService;\n    this.elementRef = elementRef;\n    this.cdr = cdr;\n    this._tdsModuleName = TDS_CONFIG_MODULE_NAME;\n    this.shape = 'circle';\n    this.size = 'md';\n    this.gap = 4;\n    this.isAvatar = true;\n    this.attrLoading = 'eager';\n    this.tdsBordered = false;\n    this.tdsTheme = 'default';\n    this.error = new EventEmitter();\n    this.showPlaceholder = false;\n    this.hasText = false;\n    this.hasSrc = true;\n    this.hasIcon = false;\n    this.textStyles = {};\n    // classMap: NgClassInterface = {};\n    this.customSize = null;\n    this.el = this.elementRef.nativeElement;\n    this.mapperText = text => {\n      if (text.length > 2) {\n        text.substring(0, 2);\n      }\n      return text;\n    };\n  }\n  get hasTdsSrc() {\n    return TDSHelperString.hasValueString(this.tdsSrc);\n  }\n  imgError($event) {\n    this.error.emit($event);\n    if (!$event.defaultPrevented) {\n      this.hasSrc = false;\n      this.hasIcon = false;\n      this.hasText = false;\n      this.showPlaceholder = false;\n      if (TDSHelperString.hasValueString(this.icon)) {\n        this.hasIcon = true;\n      } else if (TDSHelperString.hasValueString(this.text)) {\n        this.hasText = true;\n      } else {\n        this.showPlaceholder = true;\n      }\n      this.cdr.detectChanges();\n      this.setSizeStyle();\n      this.calcStringSize();\n    }\n  }\n  ngOnChanges() {\n    this.hasText = !TDSHelperString.hasValueString(this.tdsSrc) && TDSHelperString.hasValueString(this.text);\n    this.hasIcon = !TDSHelperString.hasValueString(this.tdsSrc) && TDSHelperString.hasValueString(this.icon);\n    this.hasSrc = TDSHelperString.hasValueString(this.tdsSrc);\n    this.showPlaceholder = !this.hasIcon && !this.hasSrc && !this.hasText;\n    this.setSizeStyle();\n    this.calcStringSize();\n  }\n  calcStringSize() {\n    if (!this.hasText || !this.textEl) {\n      return;\n    }\n    const childrenWidth = this.textEl?.nativeElement.offsetWidth;\n    const avatarWidth = this.el.getBoundingClientRect().width;\n    const offset = this.gap * 2 < avatarWidth ? this.gap * 2 : 8;\n    const scale = avatarWidth - offset < childrenWidth ? (avatarWidth - offset) / childrenWidth : 1;\n    this.textStyles = {\n      'transform-origin': `0 center`,\n      transform: `scale(${scale}) translateX(-50%)`\n    };\n    if (this.customSize) {\n      Object.assign(this.textStyles, {\n        lineHeight: this.customSize\n      });\n    }\n    this.cdr.detectChanges();\n  }\n  setSizeStyle() {\n    if (typeof this.size === 'number') {\n      this.customSize = `${this.size}px`;\n    } else {\n      this.customSize = null;\n    }\n    this.cdr.markForCheck();\n  }\n  get hasTdsSrcPlaceholder() {\n    return TDSHelperString.hasValueString(this.tdsSrcPlaceholder);\n  }\n  get element() {\n    return this.el;\n  }\n  static {\n    this.ɵfac = function TDSAvatarComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSAvatarComponent)(i0.ɵɵdirectiveInject(i1.TDSConfigService), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TDSAvatarComponent,\n      selectors: [[\"tds-avatar\"]],\n      viewQuery: function TDSAvatarComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.textEl = _t.first);\n        }\n      },\n      hostAttrs: [1, \"tds-avatar\"],\n      hostVars: 34,\n      hostBindings: function TDSAvatarComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"width\", ctx.customSize)(\"height\", ctx.customSize)(\"line-height\", ctx.customSize)(\"font-size\", ctx.hasIcon && ctx.customSize ? ctx.size / 2 : null, \"px\");\n          i0.ɵɵclassProp(\"tds-avatar-lg\", ctx.size === \"lg\")(\"tds-avatar-sm\", ctx.size === \"sm\")(\"tds-avatar-md\", ctx.size === \"md\")(\"tds-avatar-xl\", ctx.size === \"xl\")(\"tds-avatar-customsize\", ctx.customSize)(\"tds-avatar-square\", ctx.shape === \"square\")(\"tds-avatar-circle\", ctx.shape === \"circle\")(\"tds-avatar-icon\", ctx.icon)(\"tds-avatar-image\", ctx.hasSrc)(\"tds-avatar-bordered\", ctx.tdsBordered)(\"tds-avatar-theme-default\", ctx.tdsTheme === \"default\")(\"tds-avatar-theme-light\", ctx.tdsTheme === \"light\")(\"tds-avatar-theme-dark\", ctx.tdsTheme === \"dark\");\n        }\n      },\n      inputs: {\n        shape: \"shape\",\n        size: \"size\",\n        gap: \"gap\",\n        isAvatar: \"isAvatar\",\n        text: \"text\",\n        tdsSrc: \"tdsSrc\",\n        tdsSrcSet: \"tdsSrcSet\",\n        tdsSrcPlaceholder: \"tdsSrcPlaceholder\",\n        alt: \"alt\",\n        icon: \"icon\",\n        attrLoading: \"attrLoading\",\n        tdsBordered: \"tdsBordered\",\n        tdsTheme: \"tdsTheme\"\n      },\n      outputs: {\n        error: \"error\"\n      },\n      exportAs: [\"tdsAvatar\"],\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 6,\n      vars: 6,\n      consts: [[\"textEl\", \"\"], [3, \"class\"], [3, \"src\"], [1, \"tds-avatar-string\"], [\"viewBox\", \"0 0 36 36\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\", 1, \"w-full\", \"h-full\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"viewBox\", \"0 0 480 480\", \"fill\", \"none\", 1, \"w-full\", \"h-full\"], [3, \"error\", \"src\"], [\"d\", \"M36 0H0V36H36V0Z\", 1, \"fill-neutral-2-50\", \"dark:fill-d-neutral-1-200\"], [\"d\", \"M18 23.03C12.6318 23.0301 7.42685 24.876 3.258 28.258C2.07431 29.241 0.983696 30.3309 0 31.514L0 36H36V31.237C34.9771 30.1771 33.889 29.1822 32.742 28.258C28.5732 24.876 23.3682 23.0301 18 23.03Z\", 1, \"dark:fill-d-neutral-1-400\", \"fill-neutral-1-100\"], [\"d\", \"M18 19.8C19.335 19.8 20.6401 19.4041 21.7501 18.6624C22.8601 17.9207 23.7253 16.8665 24.2362 15.6331C24.7471 14.3997 24.8808 13.0425 24.6203 11.7331C24.3598 10.4238 23.717 9.22102 22.773 8.27702C21.829 7.33302 20.6262 6.69014 19.3169 6.42969C18.0075 6.16924 16.6503 6.30291 15.4169 6.8138C14.1835 7.3247 13.1293 8.18986 12.3876 9.29989C11.6459 10.4099 11.25 11.715 11.25 13.05C11.25 14.8402 11.9612 16.5571 13.227 17.823C14.4929 19.0888 16.2098 19.8 18 19.8Z\", 1, \"dark:fill-d-neutral-1-400\", \"fill-neutral-1-100\"], [\"fill\", \"#E9EDF2\", 1, \"w-full\", \"h-full\"], [\"d\", \"M195.249 231.124C205.01 231.124 212.948 223.163 212.948 213.373C212.948 203.584 205.01 195.623 195.249 195.623C185.487 195.623 177.548 203.584 177.548 213.373C177.548 223.163 185.487 231.124 195.249 231.124Z\", \"fill\", \"#A1ACB8\"], [\"d\", \"M305.874 169H173.124C160.93 169 151 178.958 151 191.188V288.812C151 301.042 160.93 311 173.124 311H305.874C318.07 311 328 301.042 328 288.812V191.188C328 178.958 318.07 169 305.874 169ZM173.124 186.75H305.874C308.318 186.75 310.3 188.738 310.3 191.188V254.192L282.343 221.478C279.378 217.99 275.085 216.126 270.475 216.021C265.89 216.047 261.59 218.088 258.652 221.621L225.782 261.184L215.074 250.472C209.021 244.402 199.17 244.402 193.126 250.472L168.7 274.958V191.188C168.7 188.738 170.682 186.75 173.124 186.75Z\", \"fill\", \"#A1ACB8\"]],\n      template: function TDSAvatarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, TDSAvatarComponent_Conditional_0_Template, 1, 2, \"i\", 1)(1, TDSAvatarComponent_Conditional_1_Template, 1, 4, \"img\", 2)(2, TDSAvatarComponent_Conditional_2_Template, 4, 4, \"span\", 3)(3, TDSAvatarComponent_Conditional_3_Template, 4, 0, \":svg:svg\", 4)(4, TDSAvatarComponent_Conditional_4_Template, 4, 0, \":svg:svg\", 5)(5, TDSAvatarComponent_Conditional_5_Template, 1, 4, \"img\", 2);\n        }\n        if (rf & 2) {\n          i0.ɵɵconditional(ctx.icon && ctx.hasIcon ? 0 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx.tdsSrc && ctx.hasSrc ? 1 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx.text && ctx.hasText ? 2 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx.showPlaceholder && !ctx.hasTdsSrcPlaceholder && ctx.isAvatar ? 3 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx.showPlaceholder && !ctx.hasTdsSrcPlaceholder && !ctx.isAvatar ? 4 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx.showPlaceholder && ctx.hasTdsSrcPlaceholder ? 5 : -1);\n        }\n      },\n      dependencies: [TDSMapperPipeModule, i2.TDSMapperPipe],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n__decorate([WithConfig()], TDSAvatarComponent.prototype, \"shape\", void 0);\n__decorate([WithConfig()], TDSAvatarComponent.prototype, \"size\", void 0);\n__decorate([WithConfig(), InputNumber()], TDSAvatarComponent.prototype, \"gap\", void 0);\n__decorate([InputBoolean()], TDSAvatarComponent.prototype, \"tdsBordered\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSAvatarComponent, [{\n    type: Component,\n    args: [{\n      selector: 'tds-avatar',\n      exportAs: 'tdsAvatar',\n      standalone: true,\n      imports: [TDSMapperPipeModule],\n      template: `\n    @if (icon && hasIcon) {\n      <i [class]=\"icon\"></i>\n    }\n    @if (tdsSrc && hasSrc) {\n      <img [src]=\"tdsSrc\" [attr.srcset]=\"tdsSrcSet\" [attr.alt]=\"alt\" (error)=\"imgError($event)\"\n        [attr.loading]='attrLoading' />\n    }\n    @if (text && hasText) {\n      <span class=\"tds-avatar-string\" #textEl>{{ text | tdsMapper:mapperText }}</span>\n    }\n    @if (showPlaceholder && !hasTdsSrcPlaceholder && isAvatar) {\n      <svg viewBox=\"0 0 36 36\" class=\"w-full h-full \" fill=\"none\"\n        xmlns=\"http://www.w3.org/2000/svg\">\n        <path d=\"M36 0H0V36H36V0Z\"  class=\"fill-neutral-2-50 dark:fill-d-neutral-1-200\" />\n        <path\n          d=\"M18 23.03C12.6318 23.0301 7.42685 24.876 3.258 28.258C2.07431 29.241 0.983696 30.3309 0 31.514L0 36H36V31.237C34.9771 30.1771 33.889 29.1822 32.742 28.258C28.5732 24.876 23.3682 23.0301 18 23.03Z\"\n          class=\"dark:fill-d-neutral-1-400 fill-neutral-1-100\"/>\n          <path\n            d=\"M18 19.8C19.335 19.8 20.6401 19.4041 21.7501 18.6624C22.8601 17.9207 23.7253 16.8665 24.2362 15.6331C24.7471 14.3997 24.8808 13.0425 24.6203 11.7331C24.3598 10.4238 23.717 9.22102 22.773 8.27702C21.829 7.33302 20.6262 6.69014 19.3169 6.42969C18.0075 6.16924 16.6503 6.30291 15.4169 6.8138C14.1835 7.3247 13.1293 8.18986 12.3876 9.29989C11.6459 10.4099 11.25 11.715 11.25 13.05C11.25 14.8402 11.9612 16.5571 13.227 17.823C14.4929 19.0888 16.2098 19.8 18 19.8Z\"\n            class=\"dark:fill-d-neutral-1-400 fill-neutral-1-100\" />\n          </svg>\n        }\n        @if (showPlaceholder && !hasTdsSrcPlaceholder && !isAvatar) {\n          <svg class=\"w-full h-full\"\n            xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 480 480\" fill=\"none\">\n            <rect class=\"w-full h-full\" fill=\"#E9EDF2\" />\n            <path\n              d=\"M195.249 231.124C205.01 231.124 212.948 223.163 212.948 213.373C212.948 203.584 205.01 195.623 195.249 195.623C185.487 195.623 177.548 203.584 177.548 213.373C177.548 223.163 185.487 231.124 195.249 231.124Z\"\n              fill=\"#A1ACB8\" />\n              <path\n                d=\"M305.874 169H173.124C160.93 169 151 178.958 151 191.188V288.812C151 301.042 160.93 311 173.124 311H305.874C318.07 311 328 301.042 328 288.812V191.188C328 178.958 318.07 169 305.874 169ZM173.124 186.75H305.874C308.318 186.75 310.3 188.738 310.3 191.188V254.192L282.343 221.478C279.378 217.99 275.085 216.126 270.475 216.021C265.89 216.047 261.59 218.088 258.652 221.621L225.782 261.184L215.074 250.472C209.021 244.402 199.17 244.402 193.126 250.472L168.7 274.958V191.188C168.7 188.738 170.682 186.75 173.124 186.75Z\"\n                fill=\"#A1ACB8\" />\n              </svg>\n            }\n            @if (showPlaceholder && hasTdsSrcPlaceholder) {\n              <img [src]=\"tdsSrcPlaceholder\" [attr.srcset]=\"tdsSrcPlaceholder\"\n                [attr.alt]=\"alt\" [attr.loading]='attrLoading' />\n            }\n    `,\n      host: {\n        '[class.tds-avatar-lg]': `size === 'lg'`,\n        '[class.tds-avatar-sm]': `size === 'sm'`,\n        '[class.tds-avatar-md]': `size === 'md'`,\n        '[class.tds-avatar-xl]': `size === 'xl'`,\n        '[class.tds-avatar-customsize]': `customSize`,\n        '[class.tds-avatar-square]': `shape === 'square'`,\n        '[class.tds-avatar-circle]': `shape === 'circle'`,\n        '[class.tds-avatar-icon]': `icon`,\n        '[class.tds-avatar-image]': `hasSrc`,\n        '[class.tds-avatar-bordered]': `tdsBordered`,\n        '[class.tds-avatar-theme-default]': 'tdsTheme === \"default\"',\n        '[class.tds-avatar-theme-light]': 'tdsTheme === \"light\"',\n        '[class.tds-avatar-theme-dark]': 'tdsTheme === \"dark\"',\n        '[style.width]': 'customSize',\n        '[style.height]': 'customSize',\n        '[style.line-height]': 'customSize',\n        // size type is number when customSize is true\n        '[style.font-size.px]': '(hasIcon && customSize) ? $any(size) / 2 : null',\n        class: 'tds-avatar'\n      },\n      preserveWhitespaces: false,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None\n    }]\n  }], () => [{\n    type: i1.TDSConfigService\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.ChangeDetectorRef\n  }], {\n    shape: [{\n      type: Input\n    }],\n    size: [{\n      type: Input\n    }],\n    gap: [{\n      type: Input\n    }],\n    isAvatar: [{\n      type: Input\n    }],\n    text: [{\n      type: Input\n    }],\n    tdsSrc: [{\n      type: Input\n    }],\n    tdsSrcSet: [{\n      type: Input\n    }],\n    tdsSrcPlaceholder: [{\n      type: Input\n    }],\n    alt: [{\n      type: Input\n    }],\n    icon: [{\n      type: Input\n    }],\n    attrLoading: [{\n      type: Input\n    }],\n    tdsBordered: [{\n      type: Input\n    }],\n    tdsTheme: [{\n      type: Input\n    }],\n    error: [{\n      type: Output\n    }],\n    textEl: [{\n      type: ViewChild,\n      args: ['textEl', {\n        static: false\n      }]\n    }]\n  });\n})();\nclass TDSAvatarGroupComponent {\n  static {\n    this.ɵfac = function TDSAvatarGroupComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSAvatarGroupComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TDSAvatarGroupComponent,\n      selectors: [[\"tds-avatar-group\"]],\n      contentQueries: function TDSAvatarGroupComponent_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, TDSAvatarComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.lstAvatar = _t);\n        }\n      },\n      hostAttrs: [1, \"tds-avatar-group\"],\n      exportAs: [\"tdsAvatarGroup\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c1,\n      decls: 1,\n      vars: 0,\n      template: function TDSAvatarGroupComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSAvatarGroupComponent, [{\n    type: Component,\n    args: [{\n      selector: 'tds-avatar-group',\n      exportAs: 'tdsAvatarGroup',\n      standalone: true,\n      template: ` <ng-content></ng-content> `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        class: 'tds-avatar-group'\n      }\n    }]\n  }], null, {\n    lstAvatar: [{\n      type: ContentChildren,\n      args: [TDSAvatarComponent, {\n        descendants: true\n      }]\n    }]\n  });\n})();\nclass TDSAvatarModule {\n  static {\n    this.ɵfac = function TDSAvatarModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSAvatarModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: TDSAvatarModule,\n      imports: [TDSAvatarComponent, TDSAvatarGroupComponent],\n      exports: [TDSAvatarComponent, TDSAvatarGroupComponent]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [TDSAvatarComponent]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSAvatarModule, [{\n    type: NgModule,\n    args: [{\n      exports: [TDSAvatarComponent, TDSAvatarGroupComponent],\n      imports: [TDSAvatarComponent, TDSAvatarGroupComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TDSAvatarComponent, TDSAvatarGroupComponent, TDSAvatarModule };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQA,IAAM,MAAM,CAAC,QAAQ;AACrB,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,GAAG;AAAA,EACrB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,OAAO,IAAI;AAAA,EAC3B;AACF;AACA,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,SAAS,SAAS,+DAA+D,QAAQ;AACrG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,SAAS,MAAM,CAAC;AAAA,IAC/C,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,OAAO,OAAO,QAAW,aAAa;AACpD,IAAG,YAAY,UAAU,OAAO,SAAS,EAAE,OAAO,OAAO,GAAG,EAAE,WAAW,OAAO,WAAW;AAAA,EAC7F;AACF;AACA,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,GAAG,CAAC;AACjC,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU,CAAC;AACd,IAAG,kBAAqB,YAAY,GAAG,GAAG,OAAO,MAAM,OAAO,UAAU,CAAC;AAAA,EAC3E;AACF;AACA,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe;AAClB,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,UAAU,GAAG,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC;AACrD,IAAG,aAAa;AAAA,EAClB;AACF;AACA,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe;AAClB,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,UAAU,GAAG,QAAQ,EAAE,EAAE,GAAG,QAAQ,EAAE,EAAE,GAAG,QAAQ,EAAE;AACxD,IAAG,aAAa;AAAA,EAClB;AACF;AACA,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,OAAO,CAAC;AAAA,EAC1B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,OAAO,OAAO,mBAAsB,aAAa;AAC/D,IAAG,YAAY,UAAU,OAAO,iBAAiB,EAAE,OAAO,OAAO,GAAG,EAAE,WAAW,OAAO,WAAW;AAAA,EACrG;AACF;AACA,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,yBAAyB;AAC/B,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,YAAY,kBAAkB,YAAY,KAAK;AAC7C,SAAK,mBAAmB;AACxB,SAAK,aAAa;AAClB,SAAK,MAAM;AACX,SAAK,iBAAiB;AACtB,SAAK,QAAQ;AACb,SAAK,OAAO;AACZ,SAAK,MAAM;AACX,SAAK,WAAW;AAChB,SAAK,cAAc;AACnB,SAAK,cAAc;AACnB,SAAK,WAAW;AAChB,SAAK,QAAQ,IAAI,aAAa;AAC9B,SAAK,kBAAkB;AACvB,SAAK,UAAU;AACf,SAAK,SAAS;AACd,SAAK,UAAU;AACf,SAAK,aAAa,CAAC;AAEnB,SAAK,aAAa;AAClB,SAAK,KAAK,KAAK,WAAW;AAC1B,SAAK,aAAa,UAAQ;AACxB,UAAI,KAAK,SAAS,GAAG;AACnB,aAAK,UAAU,GAAG,CAAC;AAAA,MACrB;AACA,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,IAAI,YAAY;AACd,WAAO,gBAAgB,eAAe,KAAK,MAAM;AAAA,EACnD;AAAA,EACA,SAAS,QAAQ;AACf,SAAK,MAAM,KAAK,MAAM;AACtB,QAAI,CAAC,OAAO,kBAAkB;AAC5B,WAAK,SAAS;AACd,WAAK,UAAU;AACf,WAAK,UAAU;AACf,WAAK,kBAAkB;AACvB,UAAI,gBAAgB,eAAe,KAAK,IAAI,GAAG;AAC7C,aAAK,UAAU;AAAA,MACjB,WAAW,gBAAgB,eAAe,KAAK,IAAI,GAAG;AACpD,aAAK,UAAU;AAAA,MACjB,OAAO;AACL,aAAK,kBAAkB;AAAA,MACzB;AACA,WAAK,IAAI,cAAc;AACvB,WAAK,aAAa;AAClB,WAAK,eAAe;AAAA,IACtB;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,UAAU,CAAC,gBAAgB,eAAe,KAAK,MAAM,KAAK,gBAAgB,eAAe,KAAK,IAAI;AACvG,SAAK,UAAU,CAAC,gBAAgB,eAAe,KAAK,MAAM,KAAK,gBAAgB,eAAe,KAAK,IAAI;AACvG,SAAK,SAAS,gBAAgB,eAAe,KAAK,MAAM;AACxD,SAAK,kBAAkB,CAAC,KAAK,WAAW,CAAC,KAAK,UAAU,CAAC,KAAK;AAC9D,SAAK,aAAa;AAClB,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,iBAAiB;AACf,QAAI,CAAC,KAAK,WAAW,CAAC,KAAK,QAAQ;AACjC;AAAA,IACF;AACA,UAAM,gBAAgB,KAAK,QAAQ,cAAc;AACjD,UAAM,cAAc,KAAK,GAAG,sBAAsB,EAAE;AACpD,UAAM,SAAS,KAAK,MAAM,IAAI,cAAc,KAAK,MAAM,IAAI;AAC3D,UAAM,QAAQ,cAAc,SAAS,iBAAiB,cAAc,UAAU,gBAAgB;AAC9F,SAAK,aAAa;AAAA,MAChB,oBAAoB;AAAA,MACpB,WAAW,SAAS,KAAK;AAAA,IAC3B;AACA,QAAI,KAAK,YAAY;AACnB,aAAO,OAAO,KAAK,YAAY;AAAA,QAC7B,YAAY,KAAK;AAAA,MACnB,CAAC;AAAA,IACH;AACA,SAAK,IAAI,cAAc;AAAA,EACzB;AAAA,EACA,eAAe;AACb,QAAI,OAAO,KAAK,SAAS,UAAU;AACjC,WAAK,aAAa,GAAG,KAAK,IAAI;AAAA,IAChC,OAAO;AACL,WAAK,aAAa;AAAA,IACpB;AACA,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,IAAI,uBAAuB;AACzB,WAAO,gBAAgB,eAAe,KAAK,iBAAiB;AAAA,EAC9D;AAAA,EACA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,2BAA2B,mBAAmB;AACjE,aAAO,KAAK,qBAAqB,qBAAuB,kBAAqB,gBAAgB,GAAM,kBAAqB,UAAU,GAAM,kBAAqB,iBAAiB,CAAC;AAAA,IACjL;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,YAAY,CAAC;AAAA,MAC1B,WAAW,SAAS,yBAAyB,IAAI,KAAK;AACpD,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,KAAK,CAAC;AAAA,QACvB;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,SAAS,GAAG;AAAA,QAC/D;AAAA,MACF;AAAA,MACA,WAAW,CAAC,GAAG,YAAY;AAAA,MAC3B,UAAU;AAAA,MACV,cAAc,SAAS,gCAAgC,IAAI,KAAK;AAC9D,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,SAAS,IAAI,UAAU,EAAE,UAAU,IAAI,UAAU,EAAE,eAAe,IAAI,UAAU,EAAE,aAAa,IAAI,WAAW,IAAI,aAAa,IAAI,OAAO,IAAI,MAAM,IAAI;AACvK,UAAG,YAAY,iBAAiB,IAAI,SAAS,IAAI,EAAE,iBAAiB,IAAI,SAAS,IAAI,EAAE,iBAAiB,IAAI,SAAS,IAAI,EAAE,iBAAiB,IAAI,SAAS,IAAI,EAAE,yBAAyB,IAAI,UAAU,EAAE,qBAAqB,IAAI,UAAU,QAAQ,EAAE,qBAAqB,IAAI,UAAU,QAAQ,EAAE,mBAAmB,IAAI,IAAI,EAAE,oBAAoB,IAAI,MAAM,EAAE,uBAAuB,IAAI,WAAW,EAAE,4BAA4B,IAAI,aAAa,SAAS,EAAE,0BAA0B,IAAI,aAAa,OAAO,EAAE,yBAAyB,IAAI,aAAa,MAAM;AAAA,QACriB;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,OAAO;AAAA,QACP,MAAM;AAAA,QACN,KAAK;AAAA,QACL,UAAU;AAAA,QACV,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,WAAW;AAAA,QACX,mBAAmB;AAAA,QACnB,KAAK;AAAA,QACL,MAAM;AAAA,QACN,aAAa;AAAA,QACb,aAAa;AAAA,QACb,UAAU;AAAA,MACZ;AAAA,MACA,SAAS;AAAA,QACP,OAAO;AAAA,MACT;AAAA,MACA,UAAU,CAAC,WAAW;AAAA,MACtB,YAAY;AAAA,MACZ,UAAU,CAAI,sBAAyB,mBAAmB;AAAA,MAC1D,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,UAAU,EAAE,GAAG,CAAC,GAAG,OAAO,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,mBAAmB,GAAG,CAAC,WAAW,aAAa,QAAQ,QAAQ,SAAS,8BAA8B,GAAG,UAAU,QAAQ,GAAG,CAAC,SAAS,8BAA8B,WAAW,eAAe,QAAQ,QAAQ,GAAG,UAAU,QAAQ,GAAG,CAAC,GAAG,SAAS,KAAK,GAAG,CAAC,KAAK,oBAAoB,GAAG,qBAAqB,2BAA2B,GAAG,CAAC,KAAK,uMAAuM,GAAG,6BAA6B,oBAAoB,GAAG,CAAC,KAAK,8cAA8c,GAAG,6BAA6B,oBAAoB,GAAG,CAAC,QAAQ,WAAW,GAAG,UAAU,QAAQ,GAAG,CAAC,KAAK,mNAAmN,QAAQ,SAAS,GAAG,CAAC,KAAK,sgBAAsgB,QAAQ,SAAS,CAAC;AAAA,MAC18D,UAAU,SAAS,4BAA4B,IAAI,KAAK;AACtD,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,GAAG,2CAA2C,GAAG,GAAG,KAAK,CAAC,EAAE,GAAG,2CAA2C,GAAG,GAAG,OAAO,CAAC,EAAE,GAAG,2CAA2C,GAAG,GAAG,QAAQ,CAAC,EAAE,GAAG,2CAA2C,GAAG,GAAG,YAAY,CAAC,EAAE,GAAG,2CAA2C,GAAG,GAAG,YAAY,CAAC,EAAE,GAAG,2CAA2C,GAAG,GAAG,OAAO,CAAC;AAAA,QAC3Y;AACA,YAAI,KAAK,GAAG;AACV,UAAG,cAAc,IAAI,QAAQ,IAAI,UAAU,IAAI,EAAE;AACjD,UAAG,UAAU;AACb,UAAG,cAAc,IAAI,UAAU,IAAI,SAAS,IAAI,EAAE;AAClD,UAAG,UAAU;AACb,UAAG,cAAc,IAAI,QAAQ,IAAI,UAAU,IAAI,EAAE;AACjD,UAAG,UAAU;AACb,UAAG,cAAc,IAAI,mBAAmB,CAAC,IAAI,wBAAwB,IAAI,WAAW,IAAI,EAAE;AAC1F,UAAG,UAAU;AACb,UAAG,cAAc,IAAI,mBAAmB,CAAC,IAAI,wBAAwB,CAAC,IAAI,WAAW,IAAI,EAAE;AAC3F,UAAG,UAAU;AACb,UAAG,cAAc,IAAI,mBAAmB,IAAI,uBAAuB,IAAI,EAAE;AAAA,QAC3E;AAAA,MACF;AAAA,MACA,cAAc,CAAC,qBAAwB,aAAa;AAAA,MACpD,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,WAAW,CAAC,GAAG,mBAAmB,WAAW,SAAS,MAAM;AACxE,WAAW,CAAC,WAAW,CAAC,GAAG,mBAAmB,WAAW,QAAQ,MAAM;AACvE,WAAW,CAAC,WAAW,GAAG,YAAY,CAAC,GAAG,mBAAmB,WAAW,OAAO,MAAM;AACrF,WAAW,CAAC,aAAa,CAAC,GAAG,mBAAmB,WAAW,eAAe,MAAM;AAAA,CAC/E,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,SAAS,CAAC,mBAAmB;AAAA,MAC7B,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAwCV,MAAM;AAAA,QACJ,yBAAyB;AAAA,QACzB,yBAAyB;AAAA,QACzB,yBAAyB;AAAA,QACzB,yBAAyB;AAAA,QACzB,iCAAiC;AAAA,QACjC,6BAA6B;AAAA,QAC7B,6BAA6B;AAAA,QAC7B,2BAA2B;AAAA,QAC3B,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,oCAAoC;AAAA,QACpC,kCAAkC;AAAA,QAClC,iCAAiC;AAAA,QACjC,iBAAiB;AAAA,QACjB,kBAAkB;AAAA,QAClB,uBAAuB;AAAA;AAAA,QAEvB,wBAAwB;AAAA,QACxB,OAAO;AAAA,MACT;AAAA,MACA,qBAAqB;AAAA,MACrB,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,IACnC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,KAAK,CAAC;AAAA,MACJ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,KAAK,CAAC;AAAA,MACJ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,UAAU;AAAA,QACf,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,0BAAN,MAAM,yBAAwB;AAAA,EAC5B,OAAO;AACL,SAAK,OAAO,SAAS,gCAAgC,mBAAmB;AACtE,aAAO,KAAK,qBAAqB,0BAAyB;AAAA,IAC5D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,kBAAkB,CAAC;AAAA,MAChC,gBAAgB,SAAS,uCAAuC,IAAI,KAAK,UAAU;AACjF,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,UAAU,oBAAoB,CAAC;AAAA,QACnD;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,YAAY;AAAA,QAC/D;AAAA,MACF;AAAA,MACA,WAAW,CAAC,GAAG,kBAAkB;AAAA,MACjC,UAAU,CAAC,gBAAgB;AAAA,MAC3B,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,MACjC,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,UAAU,SAAS,iCAAiC,IAAI,KAAK;AAC3D,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,aAAa,CAAC;AAAA,QACnB;AAAA,MACF;AAAA,MACA,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,QACzB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,OAAO;AACL,SAAK,OAAO,SAAS,wBAAwB,mBAAmB;AAC9D,aAAO,KAAK,qBAAqB,kBAAiB;AAAA,IACpD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,oBAAoB,uBAAuB;AAAA,MACrD,SAAS,CAAC,oBAAoB,uBAAuB;AAAA,IACvD,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,kBAAkB;AAAA,IAC9B,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,oBAAoB,uBAAuB;AAAA,MACrD,SAAS,CAAC,oBAAoB,uBAAuB;AAAA,IACvD,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}