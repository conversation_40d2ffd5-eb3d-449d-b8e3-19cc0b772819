import {
  TDSHeaderComponent
} from "./chunk-QVICSRFO.js";
import "./chunk-FKQFA7PD.js";
import "./chunk-ZKQUDLW6.js";
import "./chunk-LVNZ5KN7.js";
import {
  TDSMenuComponent
} from "./chunk-4K2UE2FQ.js";
import {
  TDSBreakpointService,
  TDSDestroyService,
  siderResponsiveMap,
  tdsLayoutResponsiveMap
} from "./chunk-TWX2LW5V.js";
import "./chunk-TMXQ74JC.js";
import "./chunk-5MEKNSTD.js";
import {
  Directionality,
  Platform
} from "./chunk-66TJ47JP.js";
import "./chunk-ZNBBR6AY.js";
import {
  InputBoolean,
  inNextTick,
  toCssPixel
} from "./chunk-I6D3KPPP.js";
import "./chunk-FY5POWTO.js";
import "./chunk-E3KQTB3D.js";
import "./chunk-AJWNBJMD.js";
import "./chunk-F6TEKBTH.js";
import {
  NgTemplateOutlet
} from "./chunk-RNCPR4CP.js";
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ContentChild,
  ContentChildren,
  Directive,
  EventEmitter,
  Input,
  NgModule,
  Output,
  Subject,
  TemplateRef,
  ViewContainerRef,
  ViewEncapsulation$1,
  __decorate,
  setClassMetadata,
  takeUntil,
  ɵɵNgOnChangesFeature,
  ɵɵProvidersFeature,
  ɵɵStandaloneFeature,
  ɵɵadvance,
  ɵɵclassProp,
  ɵɵconditional,
  ɵɵcontentQuery,
  ɵɵdefineComponent,
  ɵɵdefineDirective,
  ɵɵdefineInjector,
  ɵɵdefineNgModule,
  ɵɵdirectiveInject,
  ɵɵelementContainer,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵloadQuery,
  ɵɵnextContext,
  ɵɵprojection,
  ɵɵprojectionDef,
  ɵɵproperty,
  ɵɵqueryRefresh,
  ɵɵreference,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵstyleProp,
  ɵɵtemplate,
  ɵɵtemplateRefExtractor
} from "./chunk-NGC5FY53.js";
import "./chunk-WDMUDEB6.js";

// node_modules/tds-ui/fesm2022/tds-ui-layout.mjs
var _c0 = ["*"];
var _c1 = ["tds-layout-sider-trigger", ""];
function TDSLayoutSiderTriggerComponent_Conditional_0_ng_template_0_Template(rf, ctx) {
}
function TDSLayoutSiderTriggerComponent_Conditional_0_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, TDSLayoutSiderTriggerComponent_Conditional_0_ng_template_0_Template, 0, 0, "ng-template", 2);
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext();
    const defaultZeroTrigger_r2 = ɵɵreference(5);
    ɵɵproperty("ngTemplateOutlet", ctx_r0.tdsZeroTrigger || defaultZeroTrigger_r2);
  }
}
function TDSLayoutSiderTriggerComponent_Conditional_1_ng_template_0_Template(rf, ctx) {
}
function TDSLayoutSiderTriggerComponent_Conditional_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, TDSLayoutSiderTriggerComponent_Conditional_1_ng_template_0_Template, 0, 0, "ng-template", 2);
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext();
    const defaultTrigger_r3 = ɵɵreference(3);
    ɵɵproperty("ngTemplateOutlet", ctx_r0.tdsTrigger || defaultTrigger_r3);
  }
}
function TDSLayoutSiderTriggerComponent_ng_template_2_Template(rf, ctx) {
}
function TDSLayoutSiderTriggerComponent_ng_template_4_Template(rf, ctx) {
}
function TDSLayoutSiderComponent_Conditional_2_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = ɵɵgetCurrentView();
    ɵɵelementStart(0, "div", 2);
    ɵɵlistener("click", function TDSLayoutSiderComponent_Conditional_2_Template_div_click_0_listener() {
      ɵɵrestoreView(_r1);
      const ctx_r1 = ɵɵnextContext();
      return ɵɵresetView(ctx_r1.setCollapsed(!ctx_r1.tdsCollapsed));
    });
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = ɵɵnextContext();
    ɵɵproperty("matchBreakPoint", ctx_r1.matchBreakPoint)("tdsCollapsedWidth", ctx_r1.tdsCollapsedWidth)("tdsCollapsed", ctx_r1.tdsCollapsed)("tdsBreakpoint", ctx_r1.tdsBreakpoint)("tdsReverseArrow", ctx_r1.tdsReverseArrow)("tdsTrigger", ctx_r1.tdsTrigger)("tdsZeroTrigger", ctx_r1.tdsZeroTrigger)("siderWidth", ctx_r1.widthSetting);
  }
}
function TDSLayoutDeviceComponent_Conditional_0_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵprojection(0);
  }
}
function TDSLayoutDeviceComponent_Conditional_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainer(0, 0);
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext();
    ɵɵproperty("ngTemplateOutlet", ctx_r0.content.templateRef);
  }
}
var TDSLayoutContentComponent = class _TDSLayoutContentComponent {
  constructor() {
  }
  static {
    this.ɵfac = function TDSLayoutContentComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TDSLayoutContentComponent)();
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _TDSLayoutContentComponent,
      selectors: [["tds-layout-content"]],
      hostAttrs: [1, "tds-layout-content", "tds-custom-scroll"],
      exportAs: ["tdsLayoutContent"],
      standalone: true,
      features: [ɵɵStandaloneFeature],
      ngContentSelectors: _c0,
      decls: 1,
      vars: 0,
      template: function TDSLayoutContentComponent_Template(rf, ctx) {
        if (rf & 1) {
          ɵɵprojectionDef();
          ɵɵprojection(0);
        }
      },
      encapsulation: 2,
      changeDetection: 0
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TDSLayoutContentComponent, [{
    type: Component,
    args: [{
      selector: "tds-layout-content",
      exportAs: "tdsLayoutContent",
      preserveWhitespaces: false,
      changeDetection: ChangeDetectionStrategy.OnPush,
      encapsulation: ViewEncapsulation$1.None,
      template: ` <ng-content></ng-content> `,
      standalone: true,
      host: {
        class: "tds-layout-content tds-custom-scroll"
      }
    }]
  }], () => [], null);
})();
var TDSLayoutFooterComponent = class _TDSLayoutFooterComponent {
  constructor() {
  }
  static {
    this.ɵfac = function TDSLayoutFooterComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TDSLayoutFooterComponent)();
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _TDSLayoutFooterComponent,
      selectors: [["tds-layout-footer"]],
      hostAttrs: [1, "tds-layout-footer"],
      exportAs: ["tdsLayoutFooter"],
      standalone: true,
      features: [ɵɵStandaloneFeature],
      ngContentSelectors: _c0,
      decls: 1,
      vars: 0,
      template: function TDSLayoutFooterComponent_Template(rf, ctx) {
        if (rf & 1) {
          ɵɵprojectionDef();
          ɵɵprojection(0);
        }
      },
      encapsulation: 2,
      changeDetection: 0
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TDSLayoutFooterComponent, [{
    type: Component,
    args: [{
      selector: "tds-layout-footer",
      exportAs: "tdsLayoutFooter",
      preserveWhitespaces: false,
      encapsulation: ViewEncapsulation$1.None,
      changeDetection: ChangeDetectionStrategy.OnPush,
      template: ` <ng-content></ng-content> `,
      standalone: true,
      host: {
        class: "tds-layout-footer"
      }
    }]
  }], () => [], null);
})();
var TDSLayoutHeaderComponent = class _TDSLayoutHeaderComponent {
  constructor() {
  }
  static {
    this.ɵfac = function TDSLayoutHeaderComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TDSLayoutHeaderComponent)();
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _TDSLayoutHeaderComponent,
      selectors: [["tds-layout-header"]],
      contentQueries: function TDSLayoutHeaderComponent_ContentQueries(rf, ctx, dirIndex) {
        if (rf & 1) {
          ɵɵcontentQuery(dirIndex, TDSHeaderComponent, 4);
        }
        if (rf & 2) {
          let _t;
          ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.listOfTDSHeaderComponent = _t);
        }
      },
      hostAttrs: [1, "tds-layout-header"],
      hostVars: 2,
      hostBindings: function TDSLayoutHeaderComponent_HostBindings(rf, ctx) {
        if (rf & 2) {
          ɵɵclassProp("tds-layout-has-header", ctx.listOfTDSHeaderComponent.length > 0);
        }
      },
      exportAs: ["tdsLayoutHeader"],
      standalone: true,
      features: [ɵɵStandaloneFeature],
      ngContentSelectors: _c0,
      decls: 1,
      vars: 0,
      template: function TDSLayoutHeaderComponent_Template(rf, ctx) {
        if (rf & 1) {
          ɵɵprojectionDef();
          ɵɵprojection(0);
        }
      },
      encapsulation: 2,
      changeDetection: 0
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TDSLayoutHeaderComponent, [{
    type: Component,
    args: [{
      selector: "tds-layout-header",
      exportAs: "tdsLayoutHeader",
      changeDetection: ChangeDetectionStrategy.OnPush,
      encapsulation: ViewEncapsulation$1.None,
      preserveWhitespaces: false,
      template: ` <ng-content></ng-content> `,
      host: {
        class: "tds-layout-header",
        "[class.tds-layout-has-header]": "listOfTDSHeaderComponent.length > 0"
      },
      standalone: true
    }]
  }], () => [], {
    listOfTDSHeaderComponent: [{
      type: ContentChildren,
      args: [TDSHeaderComponent]
    }]
  });
})();
var TDSLayoutSiderTriggerComponent = class _TDSLayoutSiderTriggerComponent {
  constructor() {
    this.tdsCollapsed = false;
    this.tdsReverseArrow = false;
    this.tdsZeroTrigger = null;
    this.tdsTrigger = void 0;
    this.matchBreakPoint = false;
    this.tdsCollapsedWidth = null;
    this.siderWidth = null;
    this.tdsBreakpoint = null;
    this.isZeroTrigger = false;
    this.isNormalTrigger = false;
  }
  updateTriggerType() {
    this.isZeroTrigger = this.tdsCollapsedWidth === 0 && (this.tdsBreakpoint && this.matchBreakPoint || !this.tdsBreakpoint);
    this.isNormalTrigger = this.tdsCollapsedWidth !== 0;
  }
  ngOnInit() {
    this.updateTriggerType();
  }
  ngOnChanges() {
    this.updateTriggerType();
  }
  static {
    this.ɵfac = function TDSLayoutSiderTriggerComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TDSLayoutSiderTriggerComponent)();
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _TDSLayoutSiderTriggerComponent,
      selectors: [["", "tds-layout-sider-trigger", ""]],
      hostVars: 10,
      hostBindings: function TDSLayoutSiderTriggerComponent_HostBindings(rf, ctx) {
        if (rf & 2) {
          ɵɵstyleProp("width", ctx.isNormalTrigger ? ctx.siderWidth : null);
          ɵɵclassProp("tds-layout-sider-trigger", ctx.isNormalTrigger)("tds-layout-sider-zero-width-trigger", ctx.isZeroTrigger)("tds-layout-sider-zero-width-trigger-right", ctx.isZeroTrigger && ctx.tdsReverseArrow)("tds-layout-sider-zero-width-trigger-left", ctx.isZeroTrigger && !ctx.tdsReverseArrow);
        }
      },
      inputs: {
        tdsCollapsed: "tdsCollapsed",
        tdsReverseArrow: "tdsReverseArrow",
        tdsZeroTrigger: "tdsZeroTrigger",
        tdsTrigger: "tdsTrigger",
        matchBreakPoint: "matchBreakPoint",
        tdsCollapsedWidth: "tdsCollapsedWidth",
        siderWidth: "siderWidth",
        tdsBreakpoint: "tdsBreakpoint"
      },
      exportAs: ["tdsLayoutSiderTrigger"],
      standalone: true,
      features: [ɵɵNgOnChangesFeature, ɵɵStandaloneFeature],
      attrs: _c1,
      decls: 6,
      vars: 2,
      consts: [["defaultTrigger", ""], ["defaultZeroTrigger", ""], [3, "ngTemplateOutlet"]],
      template: function TDSLayoutSiderTriggerComponent_Template(rf, ctx) {
        if (rf & 1) {
          ɵɵtemplate(0, TDSLayoutSiderTriggerComponent_Conditional_0_Template, 1, 1, null, 2)(1, TDSLayoutSiderTriggerComponent_Conditional_1_Template, 1, 1, null, 2)(2, TDSLayoutSiderTriggerComponent_ng_template_2_Template, 0, 0, "ng-template", null, 0, ɵɵtemplateRefExtractor)(4, TDSLayoutSiderTriggerComponent_ng_template_4_Template, 0, 0, "ng-template", null, 1, ɵɵtemplateRefExtractor);
        }
        if (rf & 2) {
          ɵɵconditional(ctx.isZeroTrigger ? 0 : -1);
          ɵɵadvance();
          ɵɵconditional(ctx.isNormalTrigger ? 1 : -1);
        }
      },
      dependencies: [NgTemplateOutlet],
      encapsulation: 2,
      changeDetection: 0
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TDSLayoutSiderTriggerComponent, [{
    type: Component,
    args: [{
      selector: "[tds-layout-sider-trigger]",
      exportAs: "tdsLayoutSiderTrigger",
      preserveWhitespaces: false,
      encapsulation: ViewEncapsulation$1.None,
      changeDetection: ChangeDetectionStrategy.OnPush,
      template: `
    @if (isZeroTrigger) {
      <ng-template [ngTemplateOutlet]="tdsZeroTrigger || defaultZeroTrigger"></ng-template>
    }
    @if (isNormalTrigger) {
      <ng-template [ngTemplateOutlet]="tdsTrigger || defaultTrigger"></ng-template>
    }
    <ng-template #defaultTrigger>
      <!-- <span nz-icon [nzType]="tdsCollapsed ? 'right' : 'left'" *ngIf="!tdsReverseArrow"></span>
      <span nz-icon [nzType]="tdsCollapsed ? 'left' : 'right'" *ngIf="tdsReverseArrow"></span> -->
    </ng-template>
    <ng-template #defaultZeroTrigger>
      <!-- <span nz-icon nzType="bars"></span> -->
    </ng-template>
    `,
      host: {
        "[class.tds-layout-sider-trigger]": "isNormalTrigger",
        "[style.width]": "isNormalTrigger ? siderWidth : null",
        "[class.tds-layout-sider-zero-width-trigger]": "isZeroTrigger",
        "[class.tds-layout-sider-zero-width-trigger-right]": "isZeroTrigger && tdsReverseArrow",
        "[class.tds-layout-sider-zero-width-trigger-left]": "isZeroTrigger && !tdsReverseArrow"
      },
      standalone: true,
      imports: [NgTemplateOutlet]
    }]
  }], null, {
    tdsCollapsed: [{
      type: Input
    }],
    tdsReverseArrow: [{
      type: Input
    }],
    tdsZeroTrigger: [{
      type: Input
    }],
    tdsTrigger: [{
      type: Input
    }],
    matchBreakPoint: [{
      type: Input
    }],
    tdsCollapsedWidth: [{
      type: Input
    }],
    siderWidth: [{
      type: Input
    }],
    tdsBreakpoint: [{
      type: Input
    }]
  });
})();
var TDSLayoutSiderComponent = class _TDSLayoutSiderComponent {
  updateStyleMap() {
    this.widthSetting = this.tdsCollapsed ? `${this.tdsCollapsedWidth}px` : toCssPixel(this.tdsWidth);
    this.flexSetting = `0 0 ${this.widthSetting}`;
    this.cdr.markForCheck();
  }
  updateMenuInlineCollapsed() {
    if (this.tdsMenuDirective && this.tdsMenuDirective.inlineCollapsed == false && this.tdsCollapsedWidth !== 0) {
      this.tdsMenuDirective.setInlineCollapsed(this.tdsCollapsed);
    }
  }
  setCollapsed(collapsed) {
    if (collapsed !== this.tdsCollapsed) {
      this.tdsCollapsed = collapsed;
      this.tdsCollapsedChange.emit(collapsed);
      this.updateMenuInlineCollapsed();
      this.updateStyleMap();
      this.cdr.markForCheck();
    }
  }
  constructor(platform, cdr, breakpointService) {
    this.platform = platform;
    this.cdr = cdr;
    this.breakpointService = breakpointService;
    this.destroy$ = new Subject();
    this.tdsMenuDirective = null;
    this.tdsCollapsedChange = new EventEmitter();
    this.tdsWidth = 256;
    this.tdsTheme = "dark";
    this.tdsCollapsedWidth = 52;
    this.tdsBreakpoint = null;
    this.tdsZeroTrigger = null;
    this.tdsTrigger = void 0;
    this.tdsReverseArrow = false;
    this.tdsCollapsible = false;
    this.tdsCollapsed = false;
    this.matchBreakPoint = false;
    this.flexSetting = null;
    this.widthSetting = null;
  }
  ngOnInit() {
    this.updateStyleMap();
    if (this.platform.isBrowser) {
      this.breakpointService.subscribe(siderResponsiveMap, true).pipe(takeUntil(this.destroy$)).subscribe((map) => {
        const breakpoint = this.tdsBreakpoint;
        if (breakpoint) {
          inNextTick().subscribe(() => {
            this.matchBreakPoint = !map[breakpoint];
            this.setCollapsed(this.matchBreakPoint);
            this.cdr.markForCheck();
          });
        }
      });
    }
  }
  ngOnChanges(changes) {
    const {
      tdsCollapsed,
      tdsCollapsedWidth,
      tdsWidth
    } = changes;
    if (tdsCollapsed || tdsCollapsedWidth || tdsWidth) {
      this.updateStyleMap();
    }
    if (tdsCollapsed) {
      this.updateMenuInlineCollapsed();
    }
  }
  ngAfterContentInit() {
    this.updateMenuInlineCollapsed();
    if (this.tdsMenuDirective) {
      if (this.tdsCollapsible && this.tdsTrigger != null) {
        this.tdsMenuDirective.showFooter = false;
      } else {
        this.tdsMenuDirective.onOpenChange.subscribe((collapsed) => {
          this.tdsCollapsed = collapsed;
          this.tdsCollapsedChange.emit(collapsed);
          this.updateStyleMap();
          this.cdr.markForCheck();
        });
      }
    }
  }
  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
  static {
    this.ɵfac = function TDSLayoutSiderComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TDSLayoutSiderComponent)(ɵɵdirectiveInject(Platform), ɵɵdirectiveInject(ChangeDetectorRef), ɵɵdirectiveInject(TDSBreakpointService));
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _TDSLayoutSiderComponent,
      selectors: [["tds-layout-sider"]],
      contentQueries: function TDSLayoutSiderComponent_ContentQueries(rf, ctx, dirIndex) {
        if (rf & 1) {
          ɵɵcontentQuery(dirIndex, TDSMenuComponent, 5);
        }
        if (rf & 2) {
          let _t;
          ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.tdsMenuDirective = _t.first);
        }
      },
      hostAttrs: [1, "tds-layout-sider"],
      hostVars: 20,
      hostBindings: function TDSLayoutSiderComponent_HostBindings(rf, ctx) {
        if (rf & 2) {
          ɵɵstyleProp("flex", ctx.flexSetting)("max-width", ctx.widthSetting)("min-width", ctx.widthSetting)("width", ctx.widthSetting);
          ɵɵclassProp("tds-layout-sider-zero-width", ctx.tdsCollapsed && ctx.tdsCollapsedWidth === 0)("tds-layout-sider-light", ctx.tdsTheme === "light")("tds-layout-sider-dark", ctx.tdsTheme === "dark")("tds-layout-sider-default", ctx.tdsTheme === "default")("tds-layout-sider-collapsed", ctx.tdsCollapsed)("tds-layout-sider-has-trigger", ctx.tdsCollapsible && ctx.tdsTrigger !== null);
        }
      },
      inputs: {
        tdsWidth: "tdsWidth",
        tdsTheme: "tdsTheme",
        tdsCollapsedWidth: "tdsCollapsedWidth",
        tdsBreakpoint: "tdsBreakpoint",
        tdsZeroTrigger: "tdsZeroTrigger",
        tdsTrigger: "tdsTrigger",
        tdsReverseArrow: "tdsReverseArrow",
        tdsCollapsible: "tdsCollapsible",
        tdsCollapsed: "tdsCollapsed"
      },
      outputs: {
        tdsCollapsedChange: "tdsCollapsedChange"
      },
      exportAs: ["tdsLayoutSider"],
      standalone: true,
      features: [ɵɵNgOnChangesFeature, ɵɵStandaloneFeature],
      ngContentSelectors: _c0,
      decls: 3,
      vars: 1,
      consts: [[1, "tds-layout-sider-children"], ["tds-layout-sider-trigger", "", 3, "matchBreakPoint", "tdsCollapsedWidth", "tdsCollapsed", "tdsBreakpoint", "tdsReverseArrow", "tdsTrigger", "tdsZeroTrigger", "siderWidth"], ["tds-layout-sider-trigger", "", 3, "click", "matchBreakPoint", "tdsCollapsedWidth", "tdsCollapsed", "tdsBreakpoint", "tdsReverseArrow", "tdsTrigger", "tdsZeroTrigger", "siderWidth"]],
      template: function TDSLayoutSiderComponent_Template(rf, ctx) {
        if (rf & 1) {
          ɵɵprojectionDef();
          ɵɵelementStart(0, "div", 0);
          ɵɵprojection(1);
          ɵɵelementEnd();
          ɵɵtemplate(2, TDSLayoutSiderComponent_Conditional_2_Template, 1, 8, "div", 1);
        }
        if (rf & 2) {
          ɵɵadvance(2);
          ɵɵconditional(ctx.tdsCollapsible && ctx.tdsTrigger !== null ? 2 : -1);
        }
      },
      dependencies: [TDSLayoutSiderTriggerComponent],
      encapsulation: 2,
      changeDetection: 0
    });
  }
};
__decorate([InputBoolean()], TDSLayoutSiderComponent.prototype, "tdsReverseArrow", void 0);
__decorate([InputBoolean()], TDSLayoutSiderComponent.prototype, "tdsCollapsible", void 0);
__decorate([InputBoolean()], TDSLayoutSiderComponent.prototype, "tdsCollapsed", void 0);
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TDSLayoutSiderComponent, [{
    type: Component,
    args: [{
      selector: "tds-layout-sider",
      exportAs: "tdsLayoutSider",
      preserveWhitespaces: false,
      encapsulation: ViewEncapsulation$1.None,
      changeDetection: ChangeDetectionStrategy.OnPush,
      template: `
    <div class="tds-layout-sider-children">
      <ng-content></ng-content>
    </div>
    @if (tdsCollapsible && tdsTrigger !== null) {
      <div
        tds-layout-sider-trigger
        [matchBreakPoint]="matchBreakPoint"
        [tdsCollapsedWidth]="tdsCollapsedWidth"
        [tdsCollapsed]="tdsCollapsed"
        [tdsBreakpoint]="tdsBreakpoint"
        [tdsReverseArrow]="tdsReverseArrow"
        [tdsTrigger]="tdsTrigger"
        [tdsZeroTrigger]="tdsZeroTrigger"
        [siderWidth]="widthSetting"
        (click)="setCollapsed(!tdsCollapsed)"
      ></div>
    }
    `,
      host: {
        class: "tds-layout-sider",
        "[class.tds-layout-sider-zero-width]": `tdsCollapsed && tdsCollapsedWidth === 0`,
        "[class.tds-layout-sider-light]": `tdsTheme === 'light'`,
        "[class.tds-layout-sider-dark]": `tdsTheme === 'dark'`,
        "[class.tds-layout-sider-default]": `tdsTheme === 'default'`,
        "[class.tds-layout-sider-collapsed]": `tdsCollapsed`,
        "[class.tds-layout-sider-has-trigger]": `tdsCollapsible && tdsTrigger !== null`,
        "[style.flex]": "flexSetting",
        "[style.maxWidth]": "widthSetting",
        "[style.minWidth]": "widthSetting",
        "[style.width]": "widthSetting"
      },
      standalone: true,
      imports: [TDSLayoutSiderTriggerComponent]
    }]
  }], () => [{
    type: Platform
  }, {
    type: ChangeDetectorRef
  }, {
    type: TDSBreakpointService
  }], {
    tdsMenuDirective: [{
      type: ContentChild,
      args: [TDSMenuComponent]
    }],
    tdsCollapsedChange: [{
      type: Output
    }],
    tdsWidth: [{
      type: Input
    }],
    tdsTheme: [{
      type: Input
    }],
    tdsCollapsedWidth: [{
      type: Input
    }],
    tdsBreakpoint: [{
      type: Input
    }],
    tdsZeroTrigger: [{
      type: Input
    }],
    tdsTrigger: [{
      type: Input
    }],
    tdsReverseArrow: [{
      type: Input
    }],
    tdsCollapsible: [{
      type: Input
    }],
    tdsCollapsed: [{
      type: Input
    }]
  });
})();
var TDSLayoutComponent = class _TDSLayoutComponent {
  constructor(directionality) {
    this.directionality = directionality;
    this.tdsTheme = "default";
    this.dir = "ltr";
    this.destroy$ = new Subject();
  }
  ngOnInit() {
    this.dir = this.directionality.value;
    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe((direction) => {
      this.dir = direction;
    });
  }
  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
  static {
    this.ɵfac = function TDSLayoutComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TDSLayoutComponent)(ɵɵdirectiveInject(Directionality));
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _TDSLayoutComponent,
      selectors: [["tds-layout"]],
      contentQueries: function TDSLayoutComponent_ContentQueries(rf, ctx, dirIndex) {
        if (rf & 1) {
          ɵɵcontentQuery(dirIndex, TDSLayoutSiderComponent, 4);
        }
        if (rf & 2) {
          let _t;
          ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.listOfTDSLayoutSiderComponent = _t);
        }
      },
      hostAttrs: [1, "tds-layout"],
      hostVars: 10,
      hostBindings: function TDSLayoutComponent_HostBindings(rf, ctx) {
        if (rf & 2) {
          ɵɵclassProp("tds-layout-rtl", ctx.dir === "rtl")("tds-layout-has-sider", ctx.listOfTDSLayoutSiderComponent.length > 0)("tds-layout-theme-default", ctx.tdsTheme === "default")("tds-layout-theme-light", ctx.tdsTheme === "light")("tds-layout-theme-dark", ctx.tdsTheme === "dark");
        }
      },
      inputs: {
        tdsTheme: "tdsTheme"
      },
      exportAs: ["tdsLayout"],
      standalone: true,
      features: [ɵɵStandaloneFeature],
      ngContentSelectors: _c0,
      decls: 1,
      vars: 0,
      template: function TDSLayoutComponent_Template(rf, ctx) {
        if (rf & 1) {
          ɵɵprojectionDef();
          ɵɵprojection(0);
        }
      },
      encapsulation: 2,
      changeDetection: 0
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TDSLayoutComponent, [{
    type: Component,
    args: [{
      selector: "tds-layout",
      exportAs: "tdsLayout",
      encapsulation: ViewEncapsulation$1.None,
      changeDetection: ChangeDetectionStrategy.OnPush,
      preserveWhitespaces: false,
      template: ` <ng-content></ng-content> `,
      host: {
        class: "tds-layout",
        "[class.tds-layout-rtl]": `dir === 'rtl'`,
        "[class.tds-layout-has-sider]": "listOfTDSLayoutSiderComponent.length > 0",
        "[class.tds-layout-theme-default]": `tdsTheme === 'default'`,
        "[class.tds-layout-theme-light]": `tdsTheme === 'light'`,
        "[class.tds-layout-theme-dark]": `tdsTheme === 'dark'`
      },
      standalone: true
    }]
  }], () => [{
    type: Directionality
  }], {
    tdsTheme: [{
      type: Input
    }],
    listOfTDSLayoutSiderComponent: [{
      type: ContentChildren,
      args: [TDSLayoutSiderComponent]
    }]
  });
})();
var TDSLayoutDeviceContentDirective = class _TDSLayoutDeviceContentDirective {
  constructor(templateRef) {
    this.templateRef = templateRef;
  }
  static {
    this.ɵfac = function TDSLayoutDeviceContentDirective_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TDSLayoutDeviceContentDirective)(ɵɵdirectiveInject(TemplateRef));
    };
  }
  static {
    this.ɵdir = ɵɵdefineDirective({
      type: _TDSLayoutDeviceContentDirective,
      selectors: [["", "tdsLayoutDeviceContent", ""]],
      standalone: true
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TDSLayoutDeviceContentDirective, [{
    type: Directive,
    args: [{
      selector: "[tdsLayoutDeviceContent]",
      standalone: true
    }]
  }], () => [{
    type: TemplateRef
  }], null);
})();
var TDSLayoutDeviceComponent = class _TDSLayoutDeviceComponent {
  constructor(breakpointService, destroy$, cdr) {
    this.breakpointService = breakpointService;
    this.destroy$ = destroy$;
    this.cdr = cdr;
    this.tdsDevice = "desktop";
    this.isMatch = false;
  }
  ngOnInit() {
    this.breakpointService.subscribe(tdsLayoutResponsiveMap).pipe(takeUntil(this.destroy$)).subscribe((bp) => {
      const isMatch = this.convertSize2BreakPoint().indexOf(bp) > -1;
      if (isMatch != this.isMatch) {
        this.isMatch = isMatch;
        this.cdr.markForCheck();
      }
    });
  }
  convertSize2BreakPoint() {
    let result = ["xl", "xxl"];
    switch (this.tdsDevice) {
      case "mobile":
        result = ["xs", "sm"];
        break;
      case "tablet":
        result = ["md", "lg"];
        break;
      default:
        result = ["xl", "xxl"];
        break;
    }
    return result;
  }
  static {
    this.ɵfac = function TDSLayoutDeviceComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TDSLayoutDeviceComponent)(ɵɵdirectiveInject(TDSBreakpointService), ɵɵdirectiveInject(TDSDestroyService), ɵɵdirectiveInject(ChangeDetectorRef));
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _TDSLayoutDeviceComponent,
      selectors: [["tds-layout-device"]],
      contentQueries: function TDSLayoutDeviceComponent_ContentQueries(rf, ctx, dirIndex) {
        if (rf & 1) {
          ɵɵcontentQuery(dirIndex, TDSLayoutDeviceContentDirective, 5);
        }
        if (rf & 2) {
          let _t;
          ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.content = _t.first);
        }
      },
      inputs: {
        tdsDevice: "tdsDevice"
      },
      standalone: true,
      features: [ɵɵProvidersFeature([TDSDestroyService]), ɵɵStandaloneFeature],
      ngContentSelectors: _c0,
      decls: 2,
      vars: 2,
      consts: [[3, "ngTemplateOutlet"]],
      template: function TDSLayoutDeviceComponent_Template(rf, ctx) {
        if (rf & 1) {
          ɵɵprojectionDef();
          ɵɵtemplate(0, TDSLayoutDeviceComponent_Conditional_0_Template, 1, 0)(1, TDSLayoutDeviceComponent_Conditional_1_Template, 1, 1, "ng-container", 0);
        }
        if (rf & 2) {
          ɵɵconditional(ctx.isMatch ? 0 : -1);
          ɵɵadvance();
          ɵɵconditional(ctx.isMatch ? 1 : -1);
        }
      },
      dependencies: [NgTemplateOutlet],
      encapsulation: 2,
      changeDetection: 0
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TDSLayoutDeviceComponent, [{
    type: Component,
    args: [{
      selector: "tds-layout-device",
      template: `
 @if (isMatch) {
   <ng-content></ng-content>
 }
 @if (isMatch) {
   <ng-container [ngTemplateOutlet]="content!.templateRef"></ng-container>
 }
 
 `,
      encapsulation: ViewEncapsulation$1.None,
      changeDetection: ChangeDetectionStrategy.OnPush,
      providers: [TDSDestroyService],
      standalone: true,
      imports: [NgTemplateOutlet]
    }]
  }], () => [{
    type: TDSBreakpointService
  }, {
    type: TDSDestroyService
  }, {
    type: ChangeDetectorRef
  }], {
    tdsDevice: [{
      type: Input
    }],
    content: [{
      type: ContentChild,
      args: [TDSLayoutDeviceContentDirective]
    }]
  });
})();
var TDSLayoutDeviceDirective = class _TDSLayoutDeviceDirective {
  constructor(breakpointService, destroy$, viewContainer, template) {
    this.breakpointService = breakpointService;
    this.destroy$ = destroy$;
    this.viewContainer = viewContainer;
    this.template = template;
    this.tdsLayoutDevice = "desktop";
    this.hasView = false;
  }
  ngOnInit() {
    this.breakpointService.subscribe(tdsLayoutResponsiveMap).pipe(takeUntil(this.destroy$)).subscribe((bp) => {
      const isMatch = this.convertSize2BreakPoint().indexOf(bp) > -1;
      if (isMatch && !this.hasView) {
        this.hasView = true;
        this.viewContainer.createEmbeddedView(this.template);
      }
      if (!isMatch && this.hasView) {
        this.hasView = false;
        this.viewContainer.clear();
      }
    });
  }
  convertSize2BreakPoint() {
    let result = ["xl", "xxl"];
    switch (this.tdsLayoutDevice) {
      case "mobile":
        result = ["xs", "sm"];
        break;
      case "tablet":
        result = ["md", "lg"];
        break;
      default:
        result = ["xl", "xxl"];
        break;
    }
    return result;
  }
  static {
    this.ɵfac = function TDSLayoutDeviceDirective_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TDSLayoutDeviceDirective)(ɵɵdirectiveInject(TDSBreakpointService), ɵɵdirectiveInject(TDSDestroyService), ɵɵdirectiveInject(ViewContainerRef), ɵɵdirectiveInject(TemplateRef));
    };
  }
  static {
    this.ɵdir = ɵɵdefineDirective({
      type: _TDSLayoutDeviceDirective,
      selectors: [["", "tdsLayoutDevice", ""]],
      inputs: {
        tdsLayoutDevice: "tdsLayoutDevice"
      },
      standalone: true,
      features: [ɵɵProvidersFeature([TDSDestroyService])]
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TDSLayoutDeviceDirective, [{
    type: Directive,
    args: [{
      selector: "[tdsLayoutDevice]",
      providers: [TDSDestroyService],
      standalone: true
    }]
  }], () => [{
    type: TDSBreakpointService
  }, {
    type: TDSDestroyService
  }, {
    type: ViewContainerRef
  }, {
    type: TemplateRef
  }], {
    tdsLayoutDevice: [{
      type: Input
    }]
  });
})();
var TDSLayoutModule = class _TDSLayoutModule {
  static {
    this.ɵfac = function TDSLayoutModule_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TDSLayoutModule)();
    };
  }
  static {
    this.ɵmod = ɵɵdefineNgModule({
      type: _TDSLayoutModule,
      imports: [TDSLayoutComponent, TDSLayoutHeaderComponent, TDSLayoutContentComponent, TDSLayoutFooterComponent, TDSLayoutSiderComponent, TDSLayoutSiderTriggerComponent, TDSLayoutDeviceComponent, TDSLayoutDeviceContentDirective, TDSLayoutDeviceDirective],
      exports: [TDSLayoutComponent, TDSLayoutHeaderComponent, TDSLayoutContentComponent, TDSLayoutFooterComponent, TDSLayoutSiderComponent, TDSLayoutDeviceComponent, TDSLayoutDeviceContentDirective, TDSLayoutDeviceDirective]
    });
  }
  static {
    this.ɵinj = ɵɵdefineInjector({});
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TDSLayoutModule, [{
    type: NgModule,
    args: [{
      imports: [TDSLayoutComponent, TDSLayoutHeaderComponent, TDSLayoutContentComponent, TDSLayoutFooterComponent, TDSLayoutSiderComponent, TDSLayoutSiderTriggerComponent, TDSLayoutDeviceComponent, TDSLayoutDeviceContentDirective, TDSLayoutDeviceDirective],
      exports: [TDSLayoutComponent, TDSLayoutHeaderComponent, TDSLayoutContentComponent, TDSLayoutFooterComponent, TDSLayoutSiderComponent, TDSLayoutDeviceComponent, TDSLayoutDeviceContentDirective, TDSLayoutDeviceDirective]
    }]
  }], null, null);
})();
export {
  TDSLayoutComponent,
  TDSLayoutContentComponent,
  TDSLayoutDeviceComponent,
  TDSLayoutDeviceContentDirective,
  TDSLayoutDeviceDirective,
  TDSLayoutFooterComponent,
  TDSLayoutHeaderComponent,
  TDSLayoutModule,
  TDSLayoutSiderComponent,
  TDSLayoutSiderTriggerComponent as ɵTDSLayoutSiderTriggerComponent
};
//# sourceMappingURL=tds-ui_layout.js.map
