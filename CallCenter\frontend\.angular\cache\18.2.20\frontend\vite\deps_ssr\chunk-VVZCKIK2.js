import { createRequire } from 'module';const require = createRequire(import.meta.url);
import {
  _isNumberValue,
  coerceBooleanProperty,
  coerceCssPixelValue
} from "./chunk-PFNSG66E.js";
import {
  require_cjs,
  require_operators
} from "./chunk-NCYSEW5N.js";
import {
  __toESM
} from "./chunk-NQ4HTGF6.js";

// node_modules/tds-ui/fesm2022/tds-ui-shared-utility.mjs
var import_rxjs = __toESM(require_cjs(), 1);
var import_operators = __toESM(require_operators(), 1);
var TDSHelperArray = class {
  static joinNumber(arr) {
    if (this.hasListValue(arr)) {
      return arr.join(",");
    }
    return "";
  }
  static joinString(arr) {
    if (this.hasListValue(arr)) {
      return arr.join("],[");
    }
    return "";
  }
  static hasListValue(value) {
    return !(value === void 0 || value === null || value.length == 0);
  }
  /**
   * Kiểm tra a là array
   * @param a
   * @returns
   */
  static isArray(a) {
    a = Object.prototype.toString.call(a);
    return "[object Array]" === a || "[object Array Iterator]" === a;
  }
  /**
  * tìm min của mảng a
  * @param a
  * @returns
  */
  static arrayMin(a) {
    for (var c = a.length, k = a[0]; c--; ) a[c] < k && (k = a[c]);
    return k;
  }
  /**
   * tìm max của mảng a
   * @param a
   * @returns
   */
  static arrayMax(a) {
    for (var c = a.length, k = a[0]; c--; ) a[c] > k && (k = a[c]);
    return k;
  }
  /**
   * gộp mảng 2 vào mảng 1
   * @param array1
   * @param array2
   */
  static concat(array1, array2) {
    array1.push.apply(array1, array2);
  }
  //   var exampleData = [
  //     { position: 1, name: 'Hydrogen', weight: 1.0079, symbol: 'H' },
  //     { position: 1, name: 'Helium', weight: 4.0026, symbol: 'H' },
  //     { position: 2, name: 'Lithium', weight: 6.941, symbol: 'L' },    //    
  //   ]
  //   // expect 
  //   // key = ['symbol']
  //   var TREE1 = [
  //     {
  //       symbol: 'H',
  //       children: [
  //         { position: 1, name: 'Hydrogen', weight: 1.0079, symbol: 'H' },
  //         { position: 1, name: 'Helium', weight: 4.0026, symbol: 'H' },
  //       ]
  //     },
  //     {
  //       symbol: 'L',
  //       children: [
  //         { position: 2, name: 'Lithium', weight: 6.941, symbol: 'L' },
  //       ]
  //     },
  //   ]
  //   // OR -------------------------------------------------------------------------
  //   // key = ['symbol', 'position']
  //   var TREE2 = [ 
  //     {
  //       symbol: 'H',
  //       children: [
  //         {
  //           position: 1,
  //           children: [
  //             { position: 1, name: 'Hydrogen', weight: 1.0079, symbol: 'H' },
  //             { position: 1, name: 'Helium', weight: 4.0026, symbol: 'H' },
  //           ]
  //         }
  //       ]
  //     },
  //     {
  //       symbol: 'N',
  //       children: [
  //         {
  //           position: 5,
  //           children: [{ position: 5, name: 'Nitrogen', weight: 14.0067, symbol: 'N' }]
  //         },
  //         {
  //           position: 8,
  //           children: [{ position: 8, name: 'Neon', weight: 20.1797, symbol: 'N' }]
  //         }
  //       ]
  //     },
  //   ]
  /**
   *
   * @param items mảng đối tượng
   * @param keys các  fields cần groupby
   * @returns [[field groupby]:'',children:[]]
   */
  static groupByKey(items, ...keys) {
    if (!this.hasListValue(keys)) {
      return items;
    }
    const result = [];
    const keySet = /* @__PURE__ */ new Set();
    const groupByKey = keys.shift();
    items.forEach((item) => {
      if (!keySet.has(item[groupByKey])) {
        keySet.add(item[groupByKey]);
        result.push({
          [groupByKey]: item[groupByKey],
          children: [item]
        });
      } else {
        result.find((res) => res[groupByKey] === item[groupByKey]).children.push(item);
      }
    });
    if (keys.length) {
      result.forEach((res) => {
        res.children = this.groupByKey(res.children, ...keys);
      });
    }
    return result;
  }
};
var TDSHelperObject = class _TDSHelperObject {
  /**
    * Kiểm tra đối tượng có giá trị hay ko
    * @param value
    *
    */
  static hasValue(value) {
    return !(value === void 0 || value === null);
  }
  /**
  * sao chép đối tượng
  * @param obj
  *
  */
  static cloneObject(obj) {
    if (this.hasValue(obj)) {
      return JSON.parse(JSON.stringify(obj));
    } else {
      return obj;
    }
  }
  static copyObject(taget, source) {
    taget = Object.assign(taget, source);
  }
  static isObject(k, c) {
    return !!k && "object" === typeof k && (!c || !TDSHelperArray.isArray(k));
  }
  /** Coerces a data-bound value (typically a string) to a boolean. */
  static coerceBooleanProperty(value) {
    return value != null && `${value}` !== "false";
  }
  /**
   * kiểm tra đối tượng là function
   * @param fun
   * return true/false
   */
  static isFunction(fun) {
    return typeof fun === "function";
  }
  /**
   * lấy giá trị theo Property của Obj
   * @param obj : object
   * @param field : tên property của obj
   * @param separator : ".", dựa vào separator để tách chuỗi thành mảng các phần tử con, Ví dụ: propa.propb hoặc propA#propB
   * return undefined hoặc giá trị của property
   */
  static getValueByField(obj, field, separator = ".") {
    if (!this.hasValue(obj)) {
      return void 0;
    }
    const keys = field.split(separator);
    let result = obj;
    for (const key of keys) {
      if (result.hasOwnProperty(key)) {
        result = result[key];
      } else {
        return void 0;
      }
    }
    return result;
  }
  static isEmpty(obj) {
    return !_TDSHelperObject.hasValue(obj) || Object.keys(obj).length === 0;
  }
};
var TDSHelperString = class {
  static hasValueString(value) {
    return !(value === void 0 || value === null || value === "");
  }
  static guid() {
    let str = "";
    return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function(c) {
      return ((1 + Math.random()) * 65536 | 0).toString(16).substring(1);
    });
  }
  static gen(count) {
    let out = "";
    for (let i = 0; i < count; i++) {
      out += ((1 + Math.random()) * 65536 | 0).toString(16).substring(1);
    }
    return out;
  }
  static genid() {
    return this.guid().replace(/-/g, "");
  }
  static isString(str) {
    return "string" === typeof str;
  }
  /**
   * replace chuỗi
   * @param str
   * @param searchValue chuỗi cần replace
   * @param replaceValue  chuỗi replace
   * @returns
   */
  static replaceAll(str, searchValue, replaceValue) {
    if (this.hasValueString(str)) {
      if (str.indexOf(searchValue) > -1) {
        return str.split(searchValue).join(replaceValue);
      }
    }
    return str;
  }
  /**
   * Kiểm tra chuỗi chứa tất cả các kí tự là khoảng trắng (space)
   * @param value
   * @returns trả về true nếu tất cả kí tự KHÔNG là khoảng trắng (space)
   */
  static checkStringAllSpace(value) {
    return this.hasValueString(value.trim());
  }
  static getDiacritics() {
    return {
      "Ⓐ": "A",
      "Ａ": "A",
      "À": "A",
      "Á": "A",
      "Â": "A",
      "Ầ": "A",
      "Ấ": "A",
      "Ẫ": "A",
      "Ẩ": "A",
      "Ã": "A",
      "Ā": "A",
      "Ă": "A",
      "Ằ": "A",
      "Ắ": "A",
      "Ẵ": "A",
      "Ẳ": "A",
      "Ȧ": "A",
      "Ǡ": "A",
      "Ä": "A",
      "Ǟ": "A",
      "Ả": "A",
      "Å": "A",
      "Ǻ": "A",
      "Ǎ": "A",
      "Ȁ": "A",
      "Ȃ": "A",
      "Ạ": "A",
      "Ậ": "A",
      "Ặ": "A",
      "Ḁ": "A",
      "Ą": "A",
      "Ⱥ": "A",
      "Ɐ": "A",
      "Ꜳ": "AA",
      "Æ": "AE",
      "Ǽ": "AE",
      "Ǣ": "AE",
      "Ꜵ": "AO",
      "Ꜷ": "AU",
      "Ꜹ": "AV",
      "Ꜻ": "AV",
      "Ꜽ": "AY",
      "Ⓑ": "B",
      "Ｂ": "B",
      "Ḃ": "B",
      "Ḅ": "B",
      "Ḇ": "B",
      "Ƀ": "B",
      "Ƃ": "B",
      "Ɓ": "B",
      "Ⓒ": "C",
      "Ｃ": "C",
      "Ć": "C",
      "Ĉ": "C",
      "Ċ": "C",
      "Č": "C",
      "Ç": "C",
      "Ḉ": "C",
      "Ƈ": "C",
      "Ȼ": "C",
      "Ꜿ": "C",
      "Ⓓ": "D",
      "Ｄ": "D",
      "Ḋ": "D",
      "Ď": "D",
      "Ḍ": "D",
      "Ḑ": "D",
      "Ḓ": "D",
      "Ḏ": "D",
      "Đ": "D",
      "Ƌ": "D",
      "Ɗ": "D",
      "Ɖ": "D",
      "Ꝺ": "D",
      "Ǳ": "DZ",
      "Ǆ": "DZ",
      "ǲ": "Dz",
      "ǅ": "Dz",
      "Ⓔ": "E",
      "Ｅ": "E",
      "È": "E",
      "É": "E",
      "Ê": "E",
      "Ề": "E",
      "Ế": "E",
      "Ễ": "E",
      "Ể": "E",
      "Ẽ": "E",
      "Ē": "E",
      "Ḕ": "E",
      "Ḗ": "E",
      "Ĕ": "E",
      "Ė": "E",
      "Ë": "E",
      "Ẻ": "E",
      "Ě": "E",
      "Ȅ": "E",
      "Ȇ": "E",
      "Ẹ": "E",
      "Ệ": "E",
      "Ȩ": "E",
      "Ḝ": "E",
      "Ę": "E",
      "Ḙ": "E",
      "Ḛ": "E",
      "Ɛ": "E",
      "Ǝ": "E",
      "Ⓕ": "F",
      "Ｆ": "F",
      "Ḟ": "F",
      "Ƒ": "F",
      "Ꝼ": "F",
      "Ⓖ": "G",
      "Ｇ": "G",
      "Ǵ": "G",
      "Ĝ": "G",
      "Ḡ": "G",
      "Ğ": "G",
      "Ġ": "G",
      "Ǧ": "G",
      "Ģ": "G",
      "Ǥ": "G",
      "Ɠ": "G",
      "Ꞡ": "G",
      "Ᵹ": "G",
      "Ꝿ": "G",
      "Ⓗ": "H",
      "Ｈ": "H",
      "Ĥ": "H",
      "Ḣ": "H",
      "Ḧ": "H",
      "Ȟ": "H",
      "Ḥ": "H",
      "Ḩ": "H",
      "Ḫ": "H",
      "Ħ": "H",
      "Ⱨ": "H",
      "Ⱶ": "H",
      "Ɥ": "H",
      "Ⓘ": "I",
      "Ｉ": "I",
      "Ì": "I",
      "Í": "I",
      "Î": "I",
      "Ĩ": "I",
      "Ī": "I",
      "Ĭ": "I",
      "İ": "I",
      "Ï": "I",
      "Ḯ": "I",
      "Ỉ": "I",
      "Ǐ": "I",
      "Ȉ": "I",
      "Ȋ": "I",
      "Ị": "I",
      "Į": "I",
      "Ḭ": "I",
      "Ɨ": "I",
      "Ⓙ": "J",
      "Ｊ": "J",
      "Ĵ": "J",
      "Ɉ": "J",
      "Ⓚ": "K",
      "Ｋ": "K",
      "Ḱ": "K",
      "Ǩ": "K",
      "Ḳ": "K",
      "Ķ": "K",
      "Ḵ": "K",
      "Ƙ": "K",
      "Ⱪ": "K",
      "Ꝁ": "K",
      "Ꝃ": "K",
      "Ꝅ": "K",
      "Ꞣ": "K",
      "Ⓛ": "L",
      "Ｌ": "L",
      "Ŀ": "L",
      "Ĺ": "L",
      "Ľ": "L",
      "Ḷ": "L",
      "Ḹ": "L",
      "Ļ": "L",
      "Ḽ": "L",
      "Ḻ": "L",
      "Ł": "L",
      "Ƚ": "L",
      "Ɫ": "L",
      "Ⱡ": "L",
      "Ꝉ": "L",
      "Ꝇ": "L",
      "Ꞁ": "L",
      "Ǉ": "LJ",
      "ǈ": "Lj",
      "Ⓜ": "M",
      "Ｍ": "M",
      "Ḿ": "M",
      "Ṁ": "M",
      "Ṃ": "M",
      "Ɱ": "M",
      "Ɯ": "M",
      "Ⓝ": "N",
      "Ｎ": "N",
      "Ǹ": "N",
      "Ń": "N",
      "Ñ": "N",
      "Ṅ": "N",
      "Ň": "N",
      "Ṇ": "N",
      "Ņ": "N",
      "Ṋ": "N",
      "Ṉ": "N",
      "Ƞ": "N",
      "Ɲ": "N",
      "Ꞑ": "N",
      "Ꞥ": "N",
      "Ǌ": "NJ",
      "ǋ": "Nj",
      "Ⓞ": "O",
      "Ｏ": "O",
      "Ò": "O",
      "Ó": "O",
      "Ô": "O",
      "Ồ": "O",
      "Ố": "O",
      "Ỗ": "O",
      "Ổ": "O",
      "Õ": "O",
      "Ṍ": "O",
      "Ȭ": "O",
      "Ṏ": "O",
      "Ō": "O",
      "Ṑ": "O",
      "Ṓ": "O",
      "Ŏ": "O",
      "Ȯ": "O",
      "Ȱ": "O",
      "Ö": "O",
      "Ȫ": "O",
      "Ỏ": "O",
      "Ő": "O",
      "Ǒ": "O",
      "Ȍ": "O",
      "Ȏ": "O",
      "Ơ": "O",
      "Ờ": "O",
      "Ớ": "O",
      "Ỡ": "O",
      "Ở": "O",
      "Ợ": "O",
      "Ọ": "O",
      "Ộ": "O",
      "Ǫ": "O",
      "Ǭ": "O",
      "Ø": "O",
      "Ǿ": "O",
      "Ɔ": "O",
      "Ɵ": "O",
      "Ꝋ": "O",
      "Ꝍ": "O",
      "Ƣ": "OI",
      "Ꝏ": "OO",
      "Ȣ": "OU",
      "Ⓟ": "P",
      "Ｐ": "P",
      "Ṕ": "P",
      "Ṗ": "P",
      "Ƥ": "P",
      "Ᵽ": "P",
      "Ꝑ": "P",
      "Ꝓ": "P",
      "Ꝕ": "P",
      "Ⓠ": "Q",
      "Ｑ": "Q",
      "Ꝗ": "Q",
      "Ꝙ": "Q",
      "Ɋ": "Q",
      "Ⓡ": "R",
      "Ｒ": "R",
      "Ŕ": "R",
      "Ṙ": "R",
      "Ř": "R",
      "Ȑ": "R",
      "Ȓ": "R",
      "Ṛ": "R",
      "Ṝ": "R",
      "Ŗ": "R",
      "Ṟ": "R",
      "Ɍ": "R",
      "Ɽ": "R",
      "Ꝛ": "R",
      "Ꞧ": "R",
      "Ꞃ": "R",
      "Ⓢ": "S",
      "Ｓ": "S",
      "ẞ": "S",
      "Ś": "S",
      "Ṥ": "S",
      "Ŝ": "S",
      "Ṡ": "S",
      "Š": "S",
      "Ṧ": "S",
      "Ṣ": "S",
      "Ṩ": "S",
      "Ș": "S",
      "Ş": "S",
      "Ȿ": "S",
      "Ꞩ": "S",
      "Ꞅ": "S",
      "Ⓣ": "T",
      "Ｔ": "T",
      "Ṫ": "T",
      "Ť": "T",
      "Ṭ": "T",
      "Ț": "T",
      "Ţ": "T",
      "Ṱ": "T",
      "Ṯ": "T",
      "Ŧ": "T",
      "Ƭ": "T",
      "Ʈ": "T",
      "Ⱦ": "T",
      "Ꞇ": "T",
      "Ꜩ": "TZ",
      "Ⓤ": "U",
      "Ｕ": "U",
      "Ù": "U",
      "Ú": "U",
      "Û": "U",
      "Ũ": "U",
      "Ṹ": "U",
      "Ū": "U",
      "Ṻ": "U",
      "Ŭ": "U",
      "Ü": "U",
      "Ǜ": "U",
      "Ǘ": "U",
      "Ǖ": "U",
      "Ǚ": "U",
      "Ủ": "U",
      "Ů": "U",
      "Ű": "U",
      "Ǔ": "U",
      "Ȕ": "U",
      "Ȗ": "U",
      "Ư": "U",
      "Ừ": "U",
      "Ứ": "U",
      "Ữ": "U",
      "Ử": "U",
      "Ự": "U",
      "Ụ": "U",
      "Ṳ": "U",
      "Ų": "U",
      "Ṷ": "U",
      "Ṵ": "U",
      "Ʉ": "U",
      "Ⓥ": "V",
      "Ｖ": "V",
      "Ṽ": "V",
      "Ṿ": "V",
      "Ʋ": "V",
      "Ꝟ": "V",
      "Ʌ": "V",
      "Ꝡ": "VY",
      "Ⓦ": "W",
      "Ｗ": "W",
      "Ẁ": "W",
      "Ẃ": "W",
      "Ŵ": "W",
      "Ẇ": "W",
      "Ẅ": "W",
      "Ẉ": "W",
      "Ⱳ": "W",
      "Ⓧ": "X",
      "Ｘ": "X",
      "Ẋ": "X",
      "Ẍ": "X",
      "Ⓨ": "Y",
      "Ｙ": "Y",
      "Ỳ": "Y",
      "Ý": "Y",
      "Ŷ": "Y",
      "Ỹ": "Y",
      "Ȳ": "Y",
      "Ẏ": "Y",
      "Ÿ": "Y",
      "Ỷ": "Y",
      "Ỵ": "Y",
      "Ƴ": "Y",
      "Ɏ": "Y",
      "Ỿ": "Y",
      "Ⓩ": "Z",
      "Ｚ": "Z",
      "Ź": "Z",
      "Ẑ": "Z",
      "Ż": "Z",
      "Ž": "Z",
      "Ẓ": "Z",
      "Ẕ": "Z",
      "Ƶ": "Z",
      "Ȥ": "Z",
      "Ɀ": "Z",
      "Ⱬ": "Z",
      "Ꝣ": "Z",
      "ⓐ": "a",
      "ａ": "a",
      "ẚ": "a",
      "à": "a",
      "á": "a",
      "â": "a",
      "ầ": "a",
      "ấ": "a",
      "ẫ": "a",
      "ẩ": "a",
      "ã": "a",
      "ā": "a",
      "ă": "a",
      "ằ": "a",
      "ắ": "a",
      "ẵ": "a",
      "ẳ": "a",
      "ȧ": "a",
      "ǡ": "a",
      "ä": "a",
      "ǟ": "a",
      "ả": "a",
      "å": "a",
      "ǻ": "a",
      "ǎ": "a",
      "ȁ": "a",
      "ȃ": "a",
      "ạ": "a",
      "ậ": "a",
      "ặ": "a",
      "ḁ": "a",
      "ą": "a",
      "ⱥ": "a",
      "ɐ": "a",
      "ꜳ": "aa",
      "æ": "ae",
      "ǽ": "ae",
      "ǣ": "ae",
      "ꜵ": "ao",
      "ꜷ": "au",
      "ꜹ": "av",
      "ꜻ": "av",
      "ꜽ": "ay",
      "ⓑ": "b",
      "ｂ": "b",
      "ḃ": "b",
      "ḅ": "b",
      "ḇ": "b",
      "ƀ": "b",
      "ƃ": "b",
      "ɓ": "b",
      "ⓒ": "c",
      "ｃ": "c",
      "ć": "c",
      "ĉ": "c",
      "ċ": "c",
      "č": "c",
      "ç": "c",
      "ḉ": "c",
      "ƈ": "c",
      "ȼ": "c",
      "ꜿ": "c",
      "ↄ": "c",
      "ⓓ": "d",
      "ｄ": "d",
      "ḋ": "d",
      "ď": "d",
      "ḍ": "d",
      "ḑ": "d",
      "ḓ": "d",
      "ḏ": "d",
      "đ": "d",
      "ƌ": "d",
      "ɖ": "d",
      "ɗ": "d",
      "ꝺ": "d",
      "ǳ": "dz",
      "ǆ": "dz",
      "ⓔ": "e",
      "ｅ": "e",
      "è": "e",
      "é": "e",
      "ê": "e",
      "ề": "e",
      "ế": "e",
      "ễ": "e",
      "ể": "e",
      "ẽ": "e",
      "ē": "e",
      "ḕ": "e",
      "ḗ": "e",
      "ĕ": "e",
      "ė": "e",
      "ë": "e",
      "ẻ": "e",
      "ě": "e",
      "ȅ": "e",
      "ȇ": "e",
      "ẹ": "e",
      "ệ": "e",
      "ȩ": "e",
      "ḝ": "e",
      "ę": "e",
      "ḙ": "e",
      "ḛ": "e",
      "ɇ": "e",
      "ɛ": "e",
      "ǝ": "e",
      "ⓕ": "f",
      "ｆ": "f",
      "ḟ": "f",
      "ƒ": "f",
      "ꝼ": "f",
      "ⓖ": "g",
      "ｇ": "g",
      "ǵ": "g",
      "ĝ": "g",
      "ḡ": "g",
      "ğ": "g",
      "ġ": "g",
      "ǧ": "g",
      "ģ": "g",
      "ǥ": "g",
      "ɠ": "g",
      "ꞡ": "g",
      "ᵹ": "g",
      "ꝿ": "g",
      "ⓗ": "h",
      "ｈ": "h",
      "ĥ": "h",
      "ḣ": "h",
      "ḧ": "h",
      "ȟ": "h",
      "ḥ": "h",
      "ḩ": "h",
      "ḫ": "h",
      "ẖ": "h",
      "ħ": "h",
      "ⱨ": "h",
      "ⱶ": "h",
      "ɥ": "h",
      "ƕ": "hv",
      "ⓘ": "i",
      "ｉ": "i",
      "ì": "i",
      "í": "i",
      "î": "i",
      "ĩ": "i",
      "ī": "i",
      "ĭ": "i",
      "ï": "i",
      "ḯ": "i",
      "ỉ": "i",
      "ǐ": "i",
      "ȉ": "i",
      "ȋ": "i",
      "ị": "i",
      "į": "i",
      "ḭ": "i",
      "ɨ": "i",
      "ı": "i",
      "ⓙ": "j",
      "ｊ": "j",
      "ĵ": "j",
      "ǰ": "j",
      "ɉ": "j",
      "ⓚ": "k",
      "ｋ": "k",
      "ḱ": "k",
      "ǩ": "k",
      "ḳ": "k",
      "ķ": "k",
      "ḵ": "k",
      "ƙ": "k",
      "ⱪ": "k",
      "ꝁ": "k",
      "ꝃ": "k",
      "ꝅ": "k",
      "ꞣ": "k",
      "ⓛ": "l",
      "ｌ": "l",
      "ŀ": "l",
      "ĺ": "l",
      "ľ": "l",
      "ḷ": "l",
      "ḹ": "l",
      "ļ": "l",
      "ḽ": "l",
      "ḻ": "l",
      "ſ": "l",
      "ł": "l",
      "ƚ": "l",
      "ɫ": "l",
      "ⱡ": "l",
      "ꝉ": "l",
      "ꞁ": "l",
      "ꝇ": "l",
      "ǉ": "lj",
      "ⓜ": "m",
      "ｍ": "m",
      "ḿ": "m",
      "ṁ": "m",
      "ṃ": "m",
      "ɱ": "m",
      "ɯ": "m",
      "ⓝ": "n",
      "ｎ": "n",
      "ǹ": "n",
      "ń": "n",
      "ñ": "n",
      "ṅ": "n",
      "ň": "n",
      "ṇ": "n",
      "ņ": "n",
      "ṋ": "n",
      "ṉ": "n",
      "ƞ": "n",
      "ɲ": "n",
      "ŉ": "n",
      "ꞑ": "n",
      "ꞥ": "n",
      "ǌ": "nj",
      "ⓞ": "o",
      "ｏ": "o",
      "ò": "o",
      "ó": "o",
      "ô": "o",
      "ồ": "o",
      "ố": "o",
      "ỗ": "o",
      "ổ": "o",
      "õ": "o",
      "ṍ": "o",
      "ȭ": "o",
      "ṏ": "o",
      "ō": "o",
      "ṑ": "o",
      "ṓ": "o",
      "ŏ": "o",
      "ȯ": "o",
      "ȱ": "o",
      "ö": "o",
      "ȫ": "o",
      "ỏ": "o",
      "ő": "o",
      "ǒ": "o",
      "ȍ": "o",
      "ȏ": "o",
      "ơ": "o",
      "ờ": "o",
      "ớ": "o",
      "ỡ": "o",
      "ở": "o",
      "ợ": "o",
      "ọ": "o",
      "ộ": "o",
      "ǫ": "o",
      "ǭ": "o",
      "ø": "o",
      "ǿ": "o",
      "ɔ": "o",
      "ꝋ": "o",
      "ꝍ": "o",
      "ɵ": "o",
      "ƣ": "oi",
      "ȣ": "ou",
      "ꝏ": "oo",
      "ⓟ": "p",
      "ｐ": "p",
      "ṕ": "p",
      "ṗ": "p",
      "ƥ": "p",
      "ᵽ": "p",
      "ꝑ": "p",
      "ꝓ": "p",
      "ꝕ": "p",
      "ⓠ": "q",
      "ｑ": "q",
      "ɋ": "q",
      "ꝗ": "q",
      "ꝙ": "q",
      "ⓡ": "r",
      "ｒ": "r",
      "ŕ": "r",
      "ṙ": "r",
      "ř": "r",
      "ȑ": "r",
      "ȓ": "r",
      "ṛ": "r",
      "ṝ": "r",
      "ŗ": "r",
      "ṟ": "r",
      "ɍ": "r",
      "ɽ": "r",
      "ꝛ": "r",
      "ꞧ": "r",
      "ꞃ": "r",
      "ⓢ": "s",
      "ｓ": "s",
      "ß": "s",
      "ś": "s",
      "ṥ": "s",
      "ŝ": "s",
      "ṡ": "s",
      "š": "s",
      "ṧ": "s",
      "ṣ": "s",
      "ṩ": "s",
      "ș": "s",
      "ş": "s",
      "ȿ": "s",
      "ꞩ": "s",
      "ꞅ": "s",
      "ẛ": "s",
      "ⓣ": "t",
      "ｔ": "t",
      "ṫ": "t",
      "ẗ": "t",
      "ť": "t",
      "ṭ": "t",
      "ț": "t",
      "ţ": "t",
      "ṱ": "t",
      "ṯ": "t",
      "ŧ": "t",
      "ƭ": "t",
      "ʈ": "t",
      "ⱦ": "t",
      "ꞇ": "t",
      "ꜩ": "tz",
      "ⓤ": "u",
      "ｕ": "u",
      "ù": "u",
      "ú": "u",
      "û": "u",
      "ũ": "u",
      "ṹ": "u",
      "ū": "u",
      "ṻ": "u",
      "ŭ": "u",
      "ü": "u",
      "ǜ": "u",
      "ǘ": "u",
      "ǖ": "u",
      "ǚ": "u",
      "ủ": "u",
      "ů": "u",
      "ű": "u",
      "ǔ": "u",
      "ȕ": "u",
      "ȗ": "u",
      "ư": "u",
      "ừ": "u",
      "ứ": "u",
      "ữ": "u",
      "ử": "u",
      "ự": "u",
      "ụ": "u",
      "ṳ": "u",
      "ų": "u",
      "ṷ": "u",
      "ṵ": "u",
      "ʉ": "u",
      "ⓥ": "v",
      "ｖ": "v",
      "ṽ": "v",
      "ṿ": "v",
      "ʋ": "v",
      "ꝟ": "v",
      "ʌ": "v",
      "ꝡ": "vy",
      "ⓦ": "w",
      "ｗ": "w",
      "ẁ": "w",
      "ẃ": "w",
      "ŵ": "w",
      "ẇ": "w",
      "ẅ": "w",
      "ẘ": "w",
      "ẉ": "w",
      "ⱳ": "w",
      "ⓧ": "x",
      "ｘ": "x",
      "ẋ": "x",
      "ẍ": "x",
      "ⓨ": "y",
      "ｙ": "y",
      "ỳ": "y",
      "ý": "y",
      "ŷ": "y",
      "ỹ": "y",
      "ȳ": "y",
      "ẏ": "y",
      "ÿ": "y",
      "ỷ": "y",
      "ẙ": "y",
      "ỵ": "y",
      "ƴ": "y",
      "ɏ": "y",
      "ỿ": "y",
      "ⓩ": "z",
      "ｚ": "z",
      "ź": "z",
      "ẑ": "z",
      "ż": "z",
      "ž": "z",
      "ẓ": "z",
      "ẕ": "z",
      "ƶ": "z",
      "ȥ": "z",
      "ɀ": "z",
      "ⱬ": "z",
      "ꝣ": "z",
      "Ά": "Α",
      "Έ": "Ε",
      "Ή": "Η",
      "Ί": "Ι",
      "Ϊ": "Ι",
      "Ό": "Ο",
      "Ύ": "Υ",
      "Ϋ": "Υ",
      "Ώ": "Ω",
      "ά": "α",
      "έ": "ε",
      "ή": "η",
      "ί": "ι",
      "ϊ": "ι",
      "ΐ": "ι",
      "ό": "ο",
      "ύ": "υ",
      "ϋ": "υ",
      "ΰ": "υ",
      "ω": "ω",
      "ς": "σ"
    };
  }
  /**
   * Chuyển chuỗi có dấu sang không dấu ví dụ: á,ă,â... => a
   * @param text : chuỗi có dấu
   * @returns chuỗi không dấu
   */
  static stripSpecialChars(text) {
    const match = (a) => {
      return this.getDiacritics()[a] || a;
    };
    return this.compoundUnicode(text).replace(/[^\u0000-\u007E]/g, match);
  }
  /**
   * Chuyển chuỗi Unicode Tổ Hợp sang Unicode Dựng Sẵn
   * @param text : Unicode Tổ Hợp
   * @returns Unicode Dựng Sẵn
   */
  static compoundUnicode(unicode_str) {
    unicode_str = unicode_str.replace(/\u0065\u0309/g, "ẻ");
    unicode_str = unicode_str.replace(/\u0065\u0301/g, "é");
    unicode_str = unicode_str.replace(/\u0065\u0300/g, "è");
    unicode_str = unicode_str.replace(/\u0065\u0323/g, "ẹ");
    unicode_str = unicode_str.replace(/\u0065\u0303/g, "ẽ");
    unicode_str = unicode_str.replace(/\u00EA\u0309/g, "ể");
    unicode_str = unicode_str.replace(/\u00EA\u0301/g, "ế");
    unicode_str = unicode_str.replace(/\u00EA\u0300/g, "ề");
    unicode_str = unicode_str.replace(/\u00EA\u0323/g, "ệ");
    unicode_str = unicode_str.replace(/\u00EA\u0303/g, "ễ");
    unicode_str = unicode_str.replace(/\u0079\u0309/g, "ỷ");
    unicode_str = unicode_str.replace(/\u0079\u0301/g, "ý");
    unicode_str = unicode_str.replace(/\u0079\u0300/g, "ỳ");
    unicode_str = unicode_str.replace(/\u0079\u0323/g, "ỵ");
    unicode_str = unicode_str.replace(/\u0079\u0303/g, "ỹ");
    unicode_str = unicode_str.replace(/\u0075\u0309/g, "ủ");
    unicode_str = unicode_str.replace(/\u0075\u0301/g, "ú");
    unicode_str = unicode_str.replace(/\u0075\u0300/g, "ù");
    unicode_str = unicode_str.replace(/\u0075\u0323/g, "ụ");
    unicode_str = unicode_str.replace(/\u0075\u0303/g, "ũ");
    unicode_str = unicode_str.replace(/\u01B0\u0309/g, "ử");
    unicode_str = unicode_str.replace(/\u01B0\u0301/g, "ứ");
    unicode_str = unicode_str.replace(/\u01B0\u0300/g, "ừ");
    unicode_str = unicode_str.replace(/\u01B0\u0323/g, "ự");
    unicode_str = unicode_str.replace(/\u01B0\u0303/g, "ữ");
    unicode_str = unicode_str.replace(/\u0069\u0309/g, "ỉ");
    unicode_str = unicode_str.replace(/\u0069\u0301/g, "í");
    unicode_str = unicode_str.replace(/\u0069\u0300/g, "ì");
    unicode_str = unicode_str.replace(/\u0069\u0323/g, "ị");
    unicode_str = unicode_str.replace(/\u0069\u0303/g, "ĩ");
    unicode_str = unicode_str.replace(/\u006F\u0309/g, "ỏ");
    unicode_str = unicode_str.replace(/\u006F\u0301/g, "ó");
    unicode_str = unicode_str.replace(/\u006F\u0300/g, "ò");
    unicode_str = unicode_str.replace(/\u006F\u0323/g, "ọ");
    unicode_str = unicode_str.replace(/\u006F\u0303/g, "õ");
    unicode_str = unicode_str.replace(/\u01A1\u0309/g, "ở");
    unicode_str = unicode_str.replace(/\u01A1\u0301/g, "ớ");
    unicode_str = unicode_str.replace(/\u01A1\u0300/g, "ờ");
    unicode_str = unicode_str.replace(/\u01A1\u0323/g, "ợ");
    unicode_str = unicode_str.replace(/\u01A1\u0303/g, "ỡ");
    unicode_str = unicode_str.replace(/\u00F4\u0309/g, "ổ");
    unicode_str = unicode_str.replace(/\u00F4\u0301/g, "ố");
    unicode_str = unicode_str.replace(/\u00F4\u0300/g, "ồ");
    unicode_str = unicode_str.replace(/\u00F4\u0323/g, "ộ");
    unicode_str = unicode_str.replace(/\u00F4\u0303/g, "ỗ");
    unicode_str = unicode_str.replace(/\u0061\u0309/g, "ả");
    unicode_str = unicode_str.replace(/\u0061\u0301/g, "á");
    unicode_str = unicode_str.replace(/\u0061\u0300/g, "à");
    unicode_str = unicode_str.replace(/\u0061\u0323/g, "ạ");
    unicode_str = unicode_str.replace(/\u0061\u0303/g, "ã");
    unicode_str = unicode_str.replace(/\u0103\u0309/g, "ẳ");
    unicode_str = unicode_str.replace(/\u0103\u0301/g, "ắ");
    unicode_str = unicode_str.replace(/\u0103\u0300/g, "ằ");
    unicode_str = unicode_str.replace(/\u0103\u0323/g, "ặ");
    unicode_str = unicode_str.replace(/\u0103\u0303/g, "ẵ");
    unicode_str = unicode_str.replace(/\u00E2\u0309/g, "ẩ");
    unicode_str = unicode_str.replace(/\u00E2\u0301/g, "ấ");
    unicode_str = unicode_str.replace(/\u00E2\u0300/g, "ầ");
    unicode_str = unicode_str.replace(/\u00E2\u0323/g, "ậ");
    unicode_str = unicode_str.replace(/\u00E2\u0303/g, "ẫ");
    unicode_str = unicode_str.replace(/\u0045\u0309/g, "Ẻ");
    unicode_str = unicode_str.replace(/\u0045\u0301/g, "É");
    unicode_str = unicode_str.replace(/\u0045\u0300/g, "È");
    unicode_str = unicode_str.replace(/\u0045\u0323/g, "Ẹ");
    unicode_str = unicode_str.replace(/\u0045\u0303/g, "Ẽ");
    unicode_str = unicode_str.replace(/\u00CA\u0309/g, "Ể");
    unicode_str = unicode_str.replace(/\u00CA\u0301/g, "Ế");
    unicode_str = unicode_str.replace(/\u00CA\u0300/g, "Ề");
    unicode_str = unicode_str.replace(/\u00CA\u0323/g, "Ệ");
    unicode_str = unicode_str.replace(/\u00CA\u0303/g, "Ễ");
    unicode_str = unicode_str.replace(/\u0059\u0309/g, "Ỷ");
    unicode_str = unicode_str.replace(/\u0059\u0301/g, "Ý");
    unicode_str = unicode_str.replace(/\u0059\u0300/g, "Ỳ");
    unicode_str = unicode_str.replace(/\u0059\u0323/g, "Ỵ");
    unicode_str = unicode_str.replace(/\u0059\u0303/g, "Ỹ");
    unicode_str = unicode_str.replace(/\u0055\u0309/g, "Ủ");
    unicode_str = unicode_str.replace(/\u0055\u0301/g, "Ú");
    unicode_str = unicode_str.replace(/\u0055\u0300/g, "Ù");
    unicode_str = unicode_str.replace(/\u0055\u0323/g, "Ụ");
    unicode_str = unicode_str.replace(/\u0055\u0303/g, "Ũ");
    unicode_str = unicode_str.replace(/\u01AF\u0309/g, "Ử");
    unicode_str = unicode_str.replace(/\u01AF\u0301/g, "Ứ");
    unicode_str = unicode_str.replace(/\u01AF\u0300/g, "Ừ");
    unicode_str = unicode_str.replace(/\u01AF\u0323/g, "Ự");
    unicode_str = unicode_str.replace(/\u01AF\u0303/g, "Ữ");
    unicode_str = unicode_str.replace(/\u0049\u0309/g, "Ỉ");
    unicode_str = unicode_str.replace(/\u0049\u0301/g, "Í");
    unicode_str = unicode_str.replace(/\u0049\u0300/g, "Ì");
    unicode_str = unicode_str.replace(/\u0049\u0323/g, "Ị");
    unicode_str = unicode_str.replace(/\u0049\u0303/g, "Ĩ");
    unicode_str = unicode_str.replace(/\u004F\u0309/g, "Ỏ");
    unicode_str = unicode_str.replace(/\u004F\u0301/g, "Ó");
    unicode_str = unicode_str.replace(/\u004F\u0300/g, "Ò");
    unicode_str = unicode_str.replace(/\u004F\u0323/g, "Ọ");
    unicode_str = unicode_str.replace(/\u004F\u0303/g, "Õ");
    unicode_str = unicode_str.replace(/\u01A0\u0309/g, "Ở");
    unicode_str = unicode_str.replace(/\u01A0\u0301/g, "Ớ");
    unicode_str = unicode_str.replace(/\u01A0\u0300/g, "Ờ");
    unicode_str = unicode_str.replace(/\u01A0\u0323/g, "Ợ");
    unicode_str = unicode_str.replace(/\u01A0\u0303/g, "Ỡ");
    unicode_str = unicode_str.replace(/\u00D4\u0309/g, "Ổ");
    unicode_str = unicode_str.replace(/\u00D4\u0301/g, "Ố");
    unicode_str = unicode_str.replace(/\u00D4\u0300/g, "Ồ");
    unicode_str = unicode_str.replace(/\u00D4\u0323/g, "Ộ");
    unicode_str = unicode_str.replace(/\u00D4\u0303/g, "Ỗ");
    unicode_str = unicode_str.replace(/\u0041\u0309/g, "Ả");
    unicode_str = unicode_str.replace(/\u0041\u0301/g, "Á");
    unicode_str = unicode_str.replace(/\u0041\u0300/g, "À");
    unicode_str = unicode_str.replace(/\u0041\u0323/g, "Ạ");
    unicode_str = unicode_str.replace(/\u0041\u0303/g, "Ã");
    unicode_str = unicode_str.replace(/\u0102\u0309/g, "Ẳ");
    unicode_str = unicode_str.replace(/\u0102\u0301/g, "Ắ");
    unicode_str = unicode_str.replace(/\u0102\u0300/g, "Ằ");
    unicode_str = unicode_str.replace(/\u0102\u0323/g, "Ặ");
    unicode_str = unicode_str.replace(/\u0102\u0303/g, "Ẵ");
    unicode_str = unicode_str.replace(/\u00C2\u0309/g, "Ẩ");
    unicode_str = unicode_str.replace(/\u00C2\u0301/g, "Ấ");
    unicode_str = unicode_str.replace(/\u00C2\u0300/g, "Ầ");
    unicode_str = unicode_str.replace(/\u00C2\u0323/g, "Ậ");
    unicode_str = unicode_str.replace(/\u00C2\u0303/g, "Ẫ");
    return unicode_str;
  }
};
function toBoolean(value) {
  return coerceBooleanProperty(value);
}
function toNumber(value, fallbackValue = 0) {
  return _isNumberValue(value) ? Number(value) : fallbackValue;
}
function toCssPixel(value) {
  return coerceCssPixelValue(value);
}
function propDecoratorFactory(name, fallback) {
  function propDecorator(target, propName, originalDescriptor) {
    const privatePropName = `$$__${propName}`;
    if (Object.prototype.hasOwnProperty.call(target, privatePropName)) {
      console.log(`The prop "${privatePropName}" is already exist, it will be overrided by ${name} decorator.`);
    }
    Object.defineProperty(target, privatePropName, {
      configurable: true,
      writable: true
    });
    return {
      get() {
        return originalDescriptor && originalDescriptor.get ? originalDescriptor.get.bind(this)() : this[privatePropName];
      },
      set(value) {
        if (originalDescriptor && originalDescriptor.set) {
          originalDescriptor.set.bind(this)(fallback(value));
        }
        this[privatePropName] = fallback(value);
      }
    };
  }
  return propDecorator;
}
function InputBoolean() {
  return propDecoratorFactory("InputBoolean", toBoolean);
}
function InputNumber(fallbackValue) {
  return propDecoratorFactory("InputNumber", (value) => toNumber(value, fallbackValue));
}
function inNextTick() {
  const timer = new import_rxjs.Subject();
  Promise.resolve().then(() => timer.next());
  return timer.pipe((0, import_operators.take)(1));
}

export {
  TDSHelperArray,
  TDSHelperObject,
  TDSHelperString,
  toBoolean,
  toNumber,
  toCssPixel,
  InputBoolean,
  InputNumber,
  inNextTick
};
//# sourceMappingURL=chunk-VVZCKIK2.js.map
