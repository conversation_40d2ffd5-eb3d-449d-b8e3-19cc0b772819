export interface Call {
  id: string;
  agentId: string;
  agentName: string;
  customerId: string;
  customerName: string;
  customerPhone: string;
  startTime: Date;
  endTime: Date;
  duration: number; // in seconds
  status: CallStatus;
  direction: CallDirection;
  recordingUrl?: string;
  transcriptId?: string;
  qaScore?: number;
  qaStatus: QAStatus;
  tags: string[];
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

export enum CallStatus {
  COMPLETED = 'completed',
  MISSED = 'missed',
  ABANDONED = 'abandoned',
  TRANSFERRED = 'transferred'
}

export enum CallDirection {
  INBOUND = 'inbound',
  OUTBOUND = 'outbound'
}

export enum QAStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  FAILED = 'failed'
}

export interface CallFilter {
  dateFrom?: Date;
  dateTo?: Date;
  agentIds?: string[];
  status?: CallStatus[];
  direction?: CallDirection[];
  qaStatus?: QAStatus[];
  minDuration?: number;
  maxDuration?: number;
  searchTerm?: string;
}

export interface CallListResponse {
  calls: Call[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}
