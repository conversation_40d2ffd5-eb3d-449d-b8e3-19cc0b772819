export interface Agent {
  id: string;
  employeeId: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  department: string;
  team: string;
  supervisor: string;
  hireDate: Date;
  status: AgentStatus;
  skills: AgentSkill[];
  performance: AgentPerformanceMetrics;
  createdAt: Date;
  updatedAt: Date;
}

export enum AgentStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  ON_BREAK = 'on_break',
  IN_TRAINING = 'in_training',
  TERMINATED = 'terminated'
}

export interface AgentSkill {
  skillId: string;
  skillName: string;
  level: SkillLevel;
  certifiedDate?: Date;
}

export enum SkillLevel {
  BEGINNER = 'beginner',
  INTERMEDIATE = 'intermediate',
  ADVANCED = 'advanced',
  EXPERT = 'expert'
}

export interface AgentPerformanceMetrics {
  totalCalls: number;
  averageCallDuration: number;
  averageQAScore: number;
  customerSatisfactionScore: number;
  firstCallResolutionRate: number;
  adherenceToSchedule: number;
  lastUpdated: Date;
}

export interface Team {
  id: string;
  name: string;
  description: string;
  supervisorId: string;
  supervisorName: string;
  agents: Agent[];
  createdAt: Date;
  updatedAt: Date;
}

export interface Department {
  id: string;
  name: string;
  description: string;
  managerId: string;
  managerName: string;
  teams: Team[];
  createdAt: Date;
  updatedAt: Date;
}
