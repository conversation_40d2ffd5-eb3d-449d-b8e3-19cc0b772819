{"version": 3, "sources": ["../../../../../../node_modules/@angular/cdk/fesm2022/a11y.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/coercion/private.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/observers.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/drag-drop.mjs"], "sourcesContent": ["import { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, APP_ID, Injectable, Inject, signal, QueryList, isSignal, effect, InjectionToken, afterNextRender, Injector, booleanAttribute, Directive, Input, Optional, EventEmitter, Output, NgModule } from '@angular/core';\nimport * as i1 from '@angular/cdk/platform';\nimport { Platform, _getFocusedElementPierceShadowDom, normalizePassiveListenerOptions, _getEventTarget, _getShadowRoot } from '@angular/cdk/platform';\nimport { A, Z, ZERO, NINE, hasModifierKey, PAGE_DOWN, PAGE_UP, END, HOME, LEFT_ARROW, RIGHT_ARROW, UP_ARROW, DOWN_ARROW, TAB, ALT, CONTROL, MAC_META, META, SHIFT } from '@angular/cdk/keycodes';\nimport { Subject, Subscription, isObservable, of, BehaviorSubject } from 'rxjs';\nimport { tap, debounceTime, filter, map, take, skip, distinctUntilChanged, takeUntil } from 'rxjs/operators';\nimport { coerceObservable } from '@angular/cdk/coercion/private';\nimport * as i1$1 from '@angular/cdk/observers';\nimport { ObserversModule } from '@angular/cdk/observers';\nimport { coerceElement } from '@angular/cdk/coercion';\nimport { BreakpointObserver } from '@angular/cdk/layout';\n\n/** IDs are delimited by an empty space, as per the spec. */\nconst ID_DELIMITER = ' ';\n/**\n * Adds the given ID to the specified ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nfunction addAriaReferencedId(el, attr, id) {\n  const ids = getAriaReferenceIds(el, attr);\n  id = id.trim();\n  if (ids.some(existingId => existingId.trim() === id)) {\n    return;\n  }\n  ids.push(id);\n  el.setAttribute(attr, ids.join(ID_DELIMITER));\n}\n/**\n * Removes the given ID from the specified ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nfunction removeAriaReferencedId(el, attr, id) {\n  const ids = getAriaReferenceIds(el, attr);\n  id = id.trim();\n  const filteredIds = ids.filter(val => val !== id);\n  if (filteredIds.length) {\n    el.setAttribute(attr, filteredIds.join(ID_DELIMITER));\n  } else {\n    el.removeAttribute(attr);\n  }\n}\n/**\n * Gets the list of IDs referenced by the given ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nfunction getAriaReferenceIds(el, attr) {\n  // Get string array of all individual ids (whitespace delimited) in the attribute value\n  const attrValue = el.getAttribute(attr);\n  return attrValue?.match(/\\S+/g) ?? [];\n}\n\n/**\n * ID used for the body container where all messages are appended.\n * @deprecated No longer being used. To be removed.\n * @breaking-change 14.0.0\n */\nconst MESSAGES_CONTAINER_ID = 'cdk-describedby-message-container';\n/**\n * ID prefix used for each created message element.\n * @deprecated To be turned into a private variable.\n * @breaking-change 14.0.0\n */\nconst CDK_DESCRIBEDBY_ID_PREFIX = 'cdk-describedby-message';\n/**\n * Attribute given to each host element that is described by a message element.\n * @deprecated To be turned into a private variable.\n * @breaking-change 14.0.0\n */\nconst CDK_DESCRIBEDBY_HOST_ATTRIBUTE = 'cdk-describedby-host';\n/** Global incremental identifier for each registered message element. */\nlet nextId = 0;\n/**\n * Utility that creates visually hidden elements with a message content. Useful for elements that\n * want to use aria-describedby to further describe themselves without adding additional visual\n * content.\n */\nclass AriaDescriber {\n  constructor(_document,\n  /**\n   * @deprecated To be turned into a required parameter.\n   * @breaking-change 14.0.0\n   */\n  _platform) {\n    this._platform = _platform;\n    /** Map of all registered message elements that have been placed into the document. */\n    this._messageRegistry = new Map();\n    /** Container for all registered messages. */\n    this._messagesContainer = null;\n    /** Unique ID for the service. */\n    this._id = `${nextId++}`;\n    this._document = _document;\n    this._id = inject(APP_ID) + '-' + nextId++;\n  }\n  describe(hostElement, message, role) {\n    if (!this._canBeDescribed(hostElement, message)) {\n      return;\n    }\n    const key = getKey(message, role);\n    if (typeof message !== 'string') {\n      // We need to ensure that the element has an ID.\n      setMessageId(message, this._id);\n      this._messageRegistry.set(key, {\n        messageElement: message,\n        referenceCount: 0\n      });\n    } else if (!this._messageRegistry.has(key)) {\n      this._createMessageElement(message, role);\n    }\n    if (!this._isElementDescribedByMessage(hostElement, key)) {\n      this._addMessageReference(hostElement, key);\n    }\n  }\n  removeDescription(hostElement, message, role) {\n    if (!message || !this._isElementNode(hostElement)) {\n      return;\n    }\n    const key = getKey(message, role);\n    if (this._isElementDescribedByMessage(hostElement, key)) {\n      this._removeMessageReference(hostElement, key);\n    }\n    // If the message is a string, it means that it's one that we created for the\n    // consumer so we can remove it safely, otherwise we should leave it in place.\n    if (typeof message === 'string') {\n      const registeredMessage = this._messageRegistry.get(key);\n      if (registeredMessage && registeredMessage.referenceCount === 0) {\n        this._deleteMessageElement(key);\n      }\n    }\n    if (this._messagesContainer?.childNodes.length === 0) {\n      this._messagesContainer.remove();\n      this._messagesContainer = null;\n    }\n  }\n  /** Unregisters all created message elements and removes the message container. */\n  ngOnDestroy() {\n    const describedElements = this._document.querySelectorAll(`[${CDK_DESCRIBEDBY_HOST_ATTRIBUTE}=\"${this._id}\"]`);\n    for (let i = 0; i < describedElements.length; i++) {\n      this._removeCdkDescribedByReferenceIds(describedElements[i]);\n      describedElements[i].removeAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE);\n    }\n    this._messagesContainer?.remove();\n    this._messagesContainer = null;\n    this._messageRegistry.clear();\n  }\n  /**\n   * Creates a new element in the visually hidden message container element with the message\n   * as its content and adds it to the message registry.\n   */\n  _createMessageElement(message, role) {\n    const messageElement = this._document.createElement('div');\n    setMessageId(messageElement, this._id);\n    messageElement.textContent = message;\n    if (role) {\n      messageElement.setAttribute('role', role);\n    }\n    this._createMessagesContainer();\n    this._messagesContainer.appendChild(messageElement);\n    this._messageRegistry.set(getKey(message, role), {\n      messageElement,\n      referenceCount: 0\n    });\n  }\n  /** Deletes the message element from the global messages container. */\n  _deleteMessageElement(key) {\n    this._messageRegistry.get(key)?.messageElement?.remove();\n    this._messageRegistry.delete(key);\n  }\n  /** Creates the global container for all aria-describedby messages. */\n  _createMessagesContainer() {\n    if (this._messagesContainer) {\n      return;\n    }\n    const containerClassName = 'cdk-describedby-message-container';\n    const serverContainers = this._document.querySelectorAll(`.${containerClassName}[platform=\"server\"]`);\n    for (let i = 0; i < serverContainers.length; i++) {\n      // When going from the server to the client, we may end up in a situation where there's\n      // already a container on the page, but we don't have a reference to it. Clear the\n      // old container so we don't get duplicates. Doing this, instead of emptying the previous\n      // container, should be slightly faster.\n      serverContainers[i].remove();\n    }\n    const messagesContainer = this._document.createElement('div');\n    // We add `visibility: hidden` in order to prevent text in this container from\n    // being searchable by the browser's Ctrl + F functionality.\n    // Screen-readers will still read the description for elements with aria-describedby even\n    // when the description element is not visible.\n    messagesContainer.style.visibility = 'hidden';\n    // Even though we use `visibility: hidden`, we still apply `cdk-visually-hidden` so that\n    // the description element doesn't impact page layout.\n    messagesContainer.classList.add(containerClassName);\n    messagesContainer.classList.add('cdk-visually-hidden');\n    // @breaking-change 14.0.0 Remove null check for `_platform`.\n    if (this._platform && !this._platform.isBrowser) {\n      messagesContainer.setAttribute('platform', 'server');\n    }\n    this._document.body.appendChild(messagesContainer);\n    this._messagesContainer = messagesContainer;\n  }\n  /** Removes all cdk-describedby messages that are hosted through the element. */\n  _removeCdkDescribedByReferenceIds(element) {\n    // Remove all aria-describedby reference IDs that are prefixed by CDK_DESCRIBEDBY_ID_PREFIX\n    const originalReferenceIds = getAriaReferenceIds(element, 'aria-describedby').filter(id => id.indexOf(CDK_DESCRIBEDBY_ID_PREFIX) != 0);\n    element.setAttribute('aria-describedby', originalReferenceIds.join(' '));\n  }\n  /**\n   * Adds a message reference to the element using aria-describedby and increments the registered\n   * message's reference count.\n   */\n  _addMessageReference(element, key) {\n    const registeredMessage = this._messageRegistry.get(key);\n    // Add the aria-describedby reference and set the\n    // describedby_host attribute to mark the element.\n    addAriaReferencedId(element, 'aria-describedby', registeredMessage.messageElement.id);\n    element.setAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE, this._id);\n    registeredMessage.referenceCount++;\n  }\n  /**\n   * Removes a message reference from the element using aria-describedby\n   * and decrements the registered message's reference count.\n   */\n  _removeMessageReference(element, key) {\n    const registeredMessage = this._messageRegistry.get(key);\n    registeredMessage.referenceCount--;\n    removeAriaReferencedId(element, 'aria-describedby', registeredMessage.messageElement.id);\n    element.removeAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE);\n  }\n  /** Returns true if the element has been described by the provided message ID. */\n  _isElementDescribedByMessage(element, key) {\n    const referenceIds = getAriaReferenceIds(element, 'aria-describedby');\n    const registeredMessage = this._messageRegistry.get(key);\n    const messageId = registeredMessage && registeredMessage.messageElement.id;\n    return !!messageId && referenceIds.indexOf(messageId) != -1;\n  }\n  /** Determines whether a message can be described on a particular element. */\n  _canBeDescribed(element, message) {\n    if (!this._isElementNode(element)) {\n      return false;\n    }\n    if (message && typeof message === 'object') {\n      // We'd have to make some assumptions about the description element's text, if the consumer\n      // passed in an element. Assume that if an element is passed in, the consumer has verified\n      // that it can be used as a description.\n      return true;\n    }\n    const trimmedMessage = message == null ? '' : `${message}`.trim();\n    const ariaLabel = element.getAttribute('aria-label');\n    // We shouldn't set descriptions if they're exactly the same as the `aria-label` of the\n    // element, because screen readers will end up reading out the same text twice in a row.\n    return trimmedMessage ? !ariaLabel || ariaLabel.trim() !== trimmedMessage : false;\n  }\n  /** Checks whether a node is an Element node. */\n  _isElementNode(element) {\n    return element.nodeType === this._document.ELEMENT_NODE;\n  }\n  static {\n    this.ɵfac = function AriaDescriber_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AriaDescriber)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i1.Platform));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: AriaDescriber,\n      factory: AriaDescriber.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AriaDescriber, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: i1.Platform\n  }], null);\n})();\n/** Gets a key that can be used to look messages up in the registry. */\nfunction getKey(message, role) {\n  return typeof message === 'string' ? `${role || ''}/${message}` : message;\n}\n/** Assigns a unique ID to an element, if it doesn't have one already. */\nfunction setMessageId(element, serviceId) {\n  if (!element.id) {\n    element.id = `${CDK_DESCRIBEDBY_ID_PREFIX}-${serviceId}-${nextId++}`;\n  }\n}\nconst DEFAULT_TYPEAHEAD_DEBOUNCE_INTERVAL_MS = 200;\n/**\n * Selects items based on keyboard inputs. Implements the typeahead functionality of\n * `role=\"listbox\"` or `role=\"tree\"` and other related roles.\n */\nclass Typeahead {\n  constructor(initialItems, config) {\n    this._letterKeyStream = new Subject();\n    this._items = [];\n    this._selectedItemIndex = -1;\n    /** Buffer for the letters that the user has pressed */\n    this._pressedLetters = [];\n    this._selectedItem = new Subject();\n    this.selectedItem = this._selectedItem;\n    const typeAheadInterval = typeof config?.debounceInterval === 'number' ? config.debounceInterval : DEFAULT_TYPEAHEAD_DEBOUNCE_INTERVAL_MS;\n    if (config?.skipPredicate) {\n      this._skipPredicateFn = config.skipPredicate;\n    }\n    if ((typeof ngDevMode === 'undefined' || ngDevMode) && initialItems.length && initialItems.some(item => typeof item.getLabel !== 'function')) {\n      throw new Error('KeyManager items in typeahead mode must implement the `getLabel` method.');\n    }\n    this.setItems(initialItems);\n    this._setupKeyHandler(typeAheadInterval);\n  }\n  destroy() {\n    this._pressedLetters = [];\n    this._letterKeyStream.complete();\n    this._selectedItem.complete();\n  }\n  setCurrentSelectedItemIndex(index) {\n    this._selectedItemIndex = index;\n  }\n  setItems(items) {\n    this._items = items;\n  }\n  handleKey(event) {\n    const keyCode = event.keyCode;\n    // Attempt to use the `event.key` which also maps it to the user's keyboard language,\n    // otherwise fall back to resolving alphanumeric characters via the keyCode.\n    if (event.key && event.key.length === 1) {\n      this._letterKeyStream.next(event.key.toLocaleUpperCase());\n    } else if (keyCode >= A && keyCode <= Z || keyCode >= ZERO && keyCode <= NINE) {\n      this._letterKeyStream.next(String.fromCharCode(keyCode));\n    }\n  }\n  /** Gets whether the user is currently typing into the manager using the typeahead feature. */\n  isTyping() {\n    return this._pressedLetters.length > 0;\n  }\n  /** Resets the currently stored sequence of typed letters. */\n  reset() {\n    this._pressedLetters = [];\n  }\n  _setupKeyHandler(typeAheadInterval) {\n    // Debounce the presses of non-navigational keys, collect the ones that correspond to letters\n    // and convert those letters back into a string. Afterwards find the first item that starts\n    // with that string and select it.\n    this._letterKeyStream.pipe(tap(letter => this._pressedLetters.push(letter)), debounceTime(typeAheadInterval), filter(() => this._pressedLetters.length > 0), map(() => this._pressedLetters.join('').toLocaleUpperCase())).subscribe(inputString => {\n      // Start at 1 because we want to start searching at the item immediately\n      // following the current active item.\n      for (let i = 1; i < this._items.length + 1; i++) {\n        const index = (this._selectedItemIndex + i) % this._items.length;\n        const item = this._items[index];\n        if (!this._skipPredicateFn?.(item) && item.getLabel?.().toLocaleUpperCase().trim().indexOf(inputString) === 0) {\n          this._selectedItem.next(item);\n          break;\n        }\n      }\n      this._pressedLetters = [];\n    });\n  }\n}\n\n/**\n * This class manages keyboard events for selectable lists. If you pass it a query list\n * of items, it will set the active item correctly when arrow events occur.\n */\nclass ListKeyManager {\n  constructor(_items, injector) {\n    this._items = _items;\n    this._activeItemIndex = -1;\n    this._activeItem = signal(null);\n    this._wrap = false;\n    this._typeaheadSubscription = Subscription.EMPTY;\n    this._vertical = true;\n    this._allowedModifierKeys = [];\n    this._homeAndEnd = false;\n    this._pageUpAndDown = {\n      enabled: false,\n      delta: 10\n    };\n    /**\n     * Predicate function that can be used to check whether an item should be skipped\n     * by the key manager. By default, disabled items are skipped.\n     */\n    this._skipPredicateFn = item => item.disabled;\n    /**\n     * Stream that emits any time the TAB key is pressed, so components can react\n     * when focus is shifted off of the list.\n     */\n    this.tabOut = new Subject();\n    /** Stream that emits whenever the active item of the list manager changes. */\n    this.change = new Subject();\n    // We allow for the items to be an array because, in some cases, the consumer may\n    // not have access to a QueryList of the items they want to manage (e.g. when the\n    // items aren't being collected via `ViewChildren` or `ContentChildren`).\n    if (_items instanceof QueryList) {\n      this._itemChangesSubscription = _items.changes.subscribe(newItems => this._itemsChanged(newItems.toArray()));\n    } else if (isSignal(_items)) {\n      if (!injector && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw new Error('ListKeyManager constructed with a signal must receive an injector');\n      }\n      this._effectRef = effect(() => this._itemsChanged(_items()), {\n        injector\n      });\n    }\n  }\n  /**\n   * Sets the predicate function that determines which items should be skipped by the\n   * list key manager.\n   * @param predicate Function that determines whether the given item should be skipped.\n   */\n  skipPredicate(predicate) {\n    this._skipPredicateFn = predicate;\n    return this;\n  }\n  /**\n   * Configures wrapping mode, which determines whether the active item will wrap to\n   * the other end of list when there are no more items in the given direction.\n   * @param shouldWrap Whether the list should wrap when reaching the end.\n   */\n  withWrap(shouldWrap = true) {\n    this._wrap = shouldWrap;\n    return this;\n  }\n  /**\n   * Configures whether the key manager should be able to move the selection vertically.\n   * @param enabled Whether vertical selection should be enabled.\n   */\n  withVerticalOrientation(enabled = true) {\n    this._vertical = enabled;\n    return this;\n  }\n  /**\n   * Configures the key manager to move the selection horizontally.\n   * Passing in `null` will disable horizontal movement.\n   * @param direction Direction in which the selection can be moved.\n   */\n  withHorizontalOrientation(direction) {\n    this._horizontal = direction;\n    return this;\n  }\n  /**\n   * Modifier keys which are allowed to be held down and whose default actions will be prevented\n   * as the user is pressing the arrow keys. Defaults to not allowing any modifier keys.\n   */\n  withAllowedModifierKeys(keys) {\n    this._allowedModifierKeys = keys;\n    return this;\n  }\n  /**\n   * Turns on typeahead mode which allows users to set the active item by typing.\n   * @param debounceInterval Time to wait after the last keystroke before setting the active item.\n   */\n  withTypeAhead(debounceInterval = 200) {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      const items = this._getItemsArray();\n      if (items.length > 0 && items.some(item => typeof item.getLabel !== 'function')) {\n        throw Error('ListKeyManager items in typeahead mode must implement the `getLabel` method.');\n      }\n    }\n    this._typeaheadSubscription.unsubscribe();\n    const items = this._getItemsArray();\n    this._typeahead = new Typeahead(items, {\n      debounceInterval: typeof debounceInterval === 'number' ? debounceInterval : undefined,\n      skipPredicate: item => this._skipPredicateFn(item)\n    });\n    this._typeaheadSubscription = this._typeahead.selectedItem.subscribe(item => {\n      this.setActiveItem(item);\n    });\n    return this;\n  }\n  /** Cancels the current typeahead sequence. */\n  cancelTypeahead() {\n    this._typeahead?.reset();\n    return this;\n  }\n  /**\n   * Configures the key manager to activate the first and last items\n   * respectively when the Home or End key is pressed.\n   * @param enabled Whether pressing the Home or End key activates the first/last item.\n   */\n  withHomeAndEnd(enabled = true) {\n    this._homeAndEnd = enabled;\n    return this;\n  }\n  /**\n   * Configures the key manager to activate every 10th, configured or first/last element in up/down direction\n   * respectively when the Page-Up or Page-Down key is pressed.\n   * @param enabled Whether pressing the Page-Up or Page-Down key activates the first/last item.\n   * @param delta Whether pressing the Home or End key activates the first/last item.\n   */\n  withPageUpDown(enabled = true, delta = 10) {\n    this._pageUpAndDown = {\n      enabled,\n      delta\n    };\n    return this;\n  }\n  setActiveItem(item) {\n    const previousActiveItem = this._activeItem();\n    this.updateActiveItem(item);\n    if (this._activeItem() !== previousActiveItem) {\n      this.change.next(this._activeItemIndex);\n    }\n  }\n  /**\n   * Sets the active item depending on the key event passed in.\n   * @param event Keyboard event to be used for determining which element should be active.\n   */\n  onKeydown(event) {\n    const keyCode = event.keyCode;\n    const modifiers = ['altKey', 'ctrlKey', 'metaKey', 'shiftKey'];\n    const isModifierAllowed = modifiers.every(modifier => {\n      return !event[modifier] || this._allowedModifierKeys.indexOf(modifier) > -1;\n    });\n    switch (keyCode) {\n      case TAB:\n        this.tabOut.next();\n        return;\n      case DOWN_ARROW:\n        if (this._vertical && isModifierAllowed) {\n          this.setNextItemActive();\n          break;\n        } else {\n          return;\n        }\n      case UP_ARROW:\n        if (this._vertical && isModifierAllowed) {\n          this.setPreviousItemActive();\n          break;\n        } else {\n          return;\n        }\n      case RIGHT_ARROW:\n        if (this._horizontal && isModifierAllowed) {\n          this._horizontal === 'rtl' ? this.setPreviousItemActive() : this.setNextItemActive();\n          break;\n        } else {\n          return;\n        }\n      case LEFT_ARROW:\n        if (this._horizontal && isModifierAllowed) {\n          this._horizontal === 'rtl' ? this.setNextItemActive() : this.setPreviousItemActive();\n          break;\n        } else {\n          return;\n        }\n      case HOME:\n        if (this._homeAndEnd && isModifierAllowed) {\n          this.setFirstItemActive();\n          break;\n        } else {\n          return;\n        }\n      case END:\n        if (this._homeAndEnd && isModifierAllowed) {\n          this.setLastItemActive();\n          break;\n        } else {\n          return;\n        }\n      case PAGE_UP:\n        if (this._pageUpAndDown.enabled && isModifierAllowed) {\n          const targetIndex = this._activeItemIndex - this._pageUpAndDown.delta;\n          this._setActiveItemByIndex(targetIndex > 0 ? targetIndex : 0, 1);\n          break;\n        } else {\n          return;\n        }\n      case PAGE_DOWN:\n        if (this._pageUpAndDown.enabled && isModifierAllowed) {\n          const targetIndex = this._activeItemIndex + this._pageUpAndDown.delta;\n          const itemsLength = this._getItemsArray().length;\n          this._setActiveItemByIndex(targetIndex < itemsLength ? targetIndex : itemsLength - 1, -1);\n          break;\n        } else {\n          return;\n        }\n      default:\n        if (isModifierAllowed || hasModifierKey(event, 'shiftKey')) {\n          this._typeahead?.handleKey(event);\n        }\n        // Note that we return here, in order to avoid preventing\n        // the default action of non-navigational keys.\n        return;\n    }\n    this._typeahead?.reset();\n    event.preventDefault();\n  }\n  /** Index of the currently active item. */\n  get activeItemIndex() {\n    return this._activeItemIndex;\n  }\n  /** The active item. */\n  get activeItem() {\n    return this._activeItem();\n  }\n  /** Gets whether the user is currently typing into the manager using the typeahead feature. */\n  isTyping() {\n    return !!this._typeahead && this._typeahead.isTyping();\n  }\n  /** Sets the active item to the first enabled item in the list. */\n  setFirstItemActive() {\n    this._setActiveItemByIndex(0, 1);\n  }\n  /** Sets the active item to the last enabled item in the list. */\n  setLastItemActive() {\n    this._setActiveItemByIndex(this._getItemsArray().length - 1, -1);\n  }\n  /** Sets the active item to the next enabled item in the list. */\n  setNextItemActive() {\n    this._activeItemIndex < 0 ? this.setFirstItemActive() : this._setActiveItemByDelta(1);\n  }\n  /** Sets the active item to a previous enabled item in the list. */\n  setPreviousItemActive() {\n    this._activeItemIndex < 0 && this._wrap ? this.setLastItemActive() : this._setActiveItemByDelta(-1);\n  }\n  updateActiveItem(item) {\n    const itemArray = this._getItemsArray();\n    const index = typeof item === 'number' ? item : itemArray.indexOf(item);\n    const activeItem = itemArray[index];\n    // Explicitly check for `null` and `undefined` because other falsy values are valid.\n    this._activeItem.set(activeItem == null ? null : activeItem);\n    this._activeItemIndex = index;\n    this._typeahead?.setCurrentSelectedItemIndex(index);\n  }\n  /** Cleans up the key manager. */\n  destroy() {\n    this._typeaheadSubscription.unsubscribe();\n    this._itemChangesSubscription?.unsubscribe();\n    this._effectRef?.destroy();\n    this._typeahead?.destroy();\n    this.tabOut.complete();\n    this.change.complete();\n  }\n  /**\n   * This method sets the active item, given a list of items and the delta between the\n   * currently active item and the new active item. It will calculate differently\n   * depending on whether wrap mode is turned on.\n   */\n  _setActiveItemByDelta(delta) {\n    this._wrap ? this._setActiveInWrapMode(delta) : this._setActiveInDefaultMode(delta);\n  }\n  /**\n   * Sets the active item properly given \"wrap\" mode. In other words, it will continue to move\n   * down the list until it finds an item that is not disabled, and it will wrap if it\n   * encounters either end of the list.\n   */\n  _setActiveInWrapMode(delta) {\n    const items = this._getItemsArray();\n    for (let i = 1; i <= items.length; i++) {\n      const index = (this._activeItemIndex + delta * i + items.length) % items.length;\n      const item = items[index];\n      if (!this._skipPredicateFn(item)) {\n        this.setActiveItem(index);\n        return;\n      }\n    }\n  }\n  /**\n   * Sets the active item properly given the default mode. In other words, it will\n   * continue to move down the list until it finds an item that is not disabled. If\n   * it encounters either end of the list, it will stop and not wrap.\n   */\n  _setActiveInDefaultMode(delta) {\n    this._setActiveItemByIndex(this._activeItemIndex + delta, delta);\n  }\n  /**\n   * Sets the active item to the first enabled item starting at the index specified. If the\n   * item is disabled, it will move in the fallbackDelta direction until it either\n   * finds an enabled item or encounters the end of the list.\n   */\n  _setActiveItemByIndex(index, fallbackDelta) {\n    const items = this._getItemsArray();\n    if (!items[index]) {\n      return;\n    }\n    while (this._skipPredicateFn(items[index])) {\n      index += fallbackDelta;\n      if (!items[index]) {\n        return;\n      }\n    }\n    this.setActiveItem(index);\n  }\n  /** Returns the items as an array. */\n  _getItemsArray() {\n    if (isSignal(this._items)) {\n      return this._items();\n    }\n    return this._items instanceof QueryList ? this._items.toArray() : this._items;\n  }\n  /** Callback for when the items have changed. */\n  _itemsChanged(newItems) {\n    this._typeahead?.setItems(newItems);\n    const activeItem = this._activeItem();\n    if (activeItem) {\n      const newIndex = newItems.indexOf(activeItem);\n      if (newIndex > -1 && newIndex !== this._activeItemIndex) {\n        this._activeItemIndex = newIndex;\n        this._typeahead?.setCurrentSelectedItemIndex(newIndex);\n      }\n    }\n  }\n}\nclass ActiveDescendantKeyManager extends ListKeyManager {\n  setActiveItem(index) {\n    if (this.activeItem) {\n      this.activeItem.setInactiveStyles();\n    }\n    super.setActiveItem(index);\n    if (this.activeItem) {\n      this.activeItem.setActiveStyles();\n    }\n  }\n}\nclass FocusKeyManager extends ListKeyManager {\n  constructor() {\n    super(...arguments);\n    this._origin = 'program';\n  }\n  /**\n   * Sets the focus origin that will be passed in to the items for any subsequent `focus` calls.\n   * @param origin Focus origin to be used when focusing items.\n   */\n  setFocusOrigin(origin) {\n    this._origin = origin;\n    return this;\n  }\n  setActiveItem(item) {\n    super.setActiveItem(item);\n    if (this.activeItem) {\n      this.activeItem.focus(this._origin);\n    }\n  }\n}\n\n/**\n * This class manages keyboard events for trees. If you pass it a QueryList or other list of tree\n * items, it will set the active item, focus, handle expansion and typeahead correctly when\n * keyboard events occur.\n */\nclass TreeKeyManager {\n  _initializeFocus() {\n    if (this._hasInitialFocused || this._items.length === 0) {\n      return;\n    }\n    let activeIndex = 0;\n    for (let i = 0; i < this._items.length; i++) {\n      if (!this._skipPredicateFn(this._items[i]) && !this._isItemDisabled(this._items[i])) {\n        activeIndex = i;\n        break;\n      }\n    }\n    const activeItem = this._items[activeIndex];\n    // Use `makeFocusable` here, because we want the item to just be focusable, not actually\n    // capture the focus since the user isn't interacting with it. See #29628.\n    if (activeItem.makeFocusable) {\n      this._activeItem?.unfocus();\n      this._activeItemIndex = activeIndex;\n      this._activeItem = activeItem;\n      this._typeahead?.setCurrentSelectedItemIndex(activeIndex);\n      activeItem.makeFocusable();\n    } else {\n      // Backwards compatibility for items that don't implement `makeFocusable`.\n      this.focusItem(activeIndex);\n    }\n    this._hasInitialFocused = true;\n  }\n  /**\n   *\n   * @param items List of TreeKeyManager options. Can be synchronous or asynchronous.\n   * @param config Optional configuration options. By default, use 'ltr' horizontal orientation. By\n   * default, do not skip any nodes. By default, key manager only calls `focus` method when items\n   * are focused and does not call `activate`. If `typeaheadDefaultInterval` is `true`, use a\n   * default interval of 200ms.\n   */\n  constructor(items, config) {\n    /** The index of the currently active (focused) item. */\n    this._activeItemIndex = -1;\n    /** The currently active (focused) item. */\n    this._activeItem = null;\n    /** Whether or not we activate the item when it's focused. */\n    this._shouldActivationFollowFocus = false;\n    /**\n     * The orientation that the tree is laid out in. In `rtl` mode, the behavior of Left and\n     * Right arrow are switched.\n     */\n    this._horizontalOrientation = 'ltr';\n    /**\n     * Predicate function that can be used to check whether an item should be skipped\n     * by the key manager.\n     *\n     * The default value for this doesn't skip any elements in order to keep tree items focusable\n     * when disabled. This aligns with ARIA guidelines:\n     * https://www.w3.org/WAI/ARIA/apg/practices/keyboard-interface/#focusabilityofdisabledcontrols.\n     */\n    this._skipPredicateFn = _item => false;\n    /** Function to determine equivalent items. */\n    this._trackByFn = item => item;\n    /** Synchronous cache of the items to manage. */\n    this._items = [];\n    this._typeaheadSubscription = Subscription.EMPTY;\n    this._hasInitialFocused = false;\n    /** Stream that emits any time the focused item changes. */\n    this.change = new Subject();\n    // We allow for the items to be an array or Observable because, in some cases, the consumer may\n    // not have access to a QueryList of the items they want to manage (e.g. when the\n    // items aren't being collected via `ViewChildren` or `ContentChildren`).\n    if (items instanceof QueryList) {\n      this._items = items.toArray();\n      items.changes.subscribe(newItems => {\n        this._items = newItems.toArray();\n        this._typeahead?.setItems(this._items);\n        this._updateActiveItemIndex(this._items);\n        this._initializeFocus();\n      });\n    } else if (isObservable(items)) {\n      items.subscribe(newItems => {\n        this._items = newItems;\n        this._typeahead?.setItems(newItems);\n        this._updateActiveItemIndex(newItems);\n        this._initializeFocus();\n      });\n    } else {\n      this._items = items;\n      this._initializeFocus();\n    }\n    if (typeof config.shouldActivationFollowFocus === 'boolean') {\n      this._shouldActivationFollowFocus = config.shouldActivationFollowFocus;\n    }\n    if (config.horizontalOrientation) {\n      this._horizontalOrientation = config.horizontalOrientation;\n    }\n    if (config.skipPredicate) {\n      this._skipPredicateFn = config.skipPredicate;\n    }\n    if (config.trackBy) {\n      this._trackByFn = config.trackBy;\n    }\n    if (typeof config.typeAheadDebounceInterval !== 'undefined') {\n      this._setTypeAhead(config.typeAheadDebounceInterval);\n    }\n  }\n  /** Cleans up the key manager. */\n  destroy() {\n    this._typeaheadSubscription.unsubscribe();\n    this._typeahead?.destroy();\n    this.change.complete();\n  }\n  /**\n   * Handles a keyboard event on the tree.\n   * @param event Keyboard event that represents the user interaction with the tree.\n   */\n  onKeydown(event) {\n    const key = event.key;\n    switch (key) {\n      case 'Tab':\n        // Return early here, in order to allow Tab to actually tab out of the tree\n        return;\n      case 'ArrowDown':\n        this._focusNextItem();\n        break;\n      case 'ArrowUp':\n        this._focusPreviousItem();\n        break;\n      case 'ArrowRight':\n        this._horizontalOrientation === 'rtl' ? this._collapseCurrentItem() : this._expandCurrentItem();\n        break;\n      case 'ArrowLeft':\n        this._horizontalOrientation === 'rtl' ? this._expandCurrentItem() : this._collapseCurrentItem();\n        break;\n      case 'Home':\n        this._focusFirstItem();\n        break;\n      case 'End':\n        this._focusLastItem();\n        break;\n      case 'Enter':\n      case ' ':\n        this._activateCurrentItem();\n        break;\n      default:\n        if (event.key === '*') {\n          this._expandAllItemsAtCurrentItemLevel();\n          break;\n        }\n        this._typeahead?.handleKey(event);\n        // Return here, in order to avoid preventing the default action of non-navigational\n        // keys or resetting the buffer of pressed letters.\n        return;\n    }\n    // Reset the typeahead since the user has used a navigational key.\n    this._typeahead?.reset();\n    event.preventDefault();\n  }\n  /** Index of the currently active item. */\n  getActiveItemIndex() {\n    return this._activeItemIndex;\n  }\n  /** The currently active item. */\n  getActiveItem() {\n    return this._activeItem;\n  }\n  /** Focus the first available item. */\n  _focusFirstItem() {\n    this.focusItem(this._findNextAvailableItemIndex(-1));\n  }\n  /** Focus the last available item. */\n  _focusLastItem() {\n    this.focusItem(this._findPreviousAvailableItemIndex(this._items.length));\n  }\n  /** Focus the next available item. */\n  _focusNextItem() {\n    this.focusItem(this._findNextAvailableItemIndex(this._activeItemIndex));\n  }\n  /** Focus the previous available item. */\n  _focusPreviousItem() {\n    this.focusItem(this._findPreviousAvailableItemIndex(this._activeItemIndex));\n  }\n  focusItem(itemOrIndex, options = {}) {\n    // Set default options\n    options.emitChangeEvent ??= true;\n    let index = typeof itemOrIndex === 'number' ? itemOrIndex : this._items.findIndex(item => this._trackByFn(item) === this._trackByFn(itemOrIndex));\n    if (index < 0 || index >= this._items.length) {\n      return;\n    }\n    const activeItem = this._items[index];\n    // If we're just setting the same item, don't re-call activate or focus\n    if (this._activeItem !== null && this._trackByFn(activeItem) === this._trackByFn(this._activeItem)) {\n      return;\n    }\n    const previousActiveItem = this._activeItem;\n    this._activeItem = activeItem ?? null;\n    this._activeItemIndex = index;\n    this._typeahead?.setCurrentSelectedItemIndex(index);\n    this._activeItem?.focus();\n    previousActiveItem?.unfocus();\n    if (options.emitChangeEvent) {\n      this.change.next(this._activeItem);\n    }\n    if (this._shouldActivationFollowFocus) {\n      this._activateCurrentItem();\n    }\n  }\n  _updateActiveItemIndex(newItems) {\n    const activeItem = this._activeItem;\n    if (!activeItem) {\n      return;\n    }\n    const newIndex = newItems.findIndex(item => this._trackByFn(item) === this._trackByFn(activeItem));\n    if (newIndex > -1 && newIndex !== this._activeItemIndex) {\n      this._activeItemIndex = newIndex;\n      this._typeahead?.setCurrentSelectedItemIndex(newIndex);\n    }\n  }\n  _setTypeAhead(debounceInterval) {\n    this._typeahead = new Typeahead(this._items, {\n      debounceInterval: typeof debounceInterval === 'number' ? debounceInterval : undefined,\n      skipPredicate: item => this._skipPredicateFn(item)\n    });\n    this._typeaheadSubscription = this._typeahead.selectedItem.subscribe(item => {\n      this.focusItem(item);\n    });\n  }\n  _findNextAvailableItemIndex(startingIndex) {\n    for (let i = startingIndex + 1; i < this._items.length; i++) {\n      if (!this._skipPredicateFn(this._items[i])) {\n        return i;\n      }\n    }\n    return startingIndex;\n  }\n  _findPreviousAvailableItemIndex(startingIndex) {\n    for (let i = startingIndex - 1; i >= 0; i--) {\n      if (!this._skipPredicateFn(this._items[i])) {\n        return i;\n      }\n    }\n    return startingIndex;\n  }\n  /**\n   * If the item is already expanded, we collapse the item. Otherwise, we will focus the parent.\n   */\n  _collapseCurrentItem() {\n    if (!this._activeItem) {\n      return;\n    }\n    if (this._isCurrentItemExpanded()) {\n      this._activeItem.collapse();\n    } else {\n      const parent = this._activeItem.getParent();\n      if (!parent || this._skipPredicateFn(parent)) {\n        return;\n      }\n      this.focusItem(parent);\n    }\n  }\n  /**\n   * If the item is already collapsed, we expand the item. Otherwise, we will focus the first child.\n   */\n  _expandCurrentItem() {\n    if (!this._activeItem) {\n      return;\n    }\n    if (!this._isCurrentItemExpanded()) {\n      this._activeItem.expand();\n    } else {\n      coerceObservable(this._activeItem.getChildren()).pipe(take(1)).subscribe(children => {\n        const firstChild = children.find(child => !this._skipPredicateFn(child));\n        if (!firstChild) {\n          return;\n        }\n        this.focusItem(firstChild);\n      });\n    }\n  }\n  _isCurrentItemExpanded() {\n    if (!this._activeItem) {\n      return false;\n    }\n    return typeof this._activeItem.isExpanded === 'boolean' ? this._activeItem.isExpanded : this._activeItem.isExpanded();\n  }\n  _isItemDisabled(item) {\n    return typeof item.isDisabled === 'boolean' ? item.isDisabled : item.isDisabled?.();\n  }\n  /** For all items that are the same level as the current item, we expand those items. */\n  _expandAllItemsAtCurrentItemLevel() {\n    if (!this._activeItem) {\n      return;\n    }\n    const parent = this._activeItem.getParent();\n    let itemsToExpand;\n    if (!parent) {\n      itemsToExpand = of(this._items.filter(item => item.getParent() === null));\n    } else {\n      itemsToExpand = coerceObservable(parent.getChildren());\n    }\n    itemsToExpand.pipe(take(1)).subscribe(items => {\n      for (const item of items) {\n        item.expand();\n      }\n    });\n  }\n  _activateCurrentItem() {\n    this._activeItem?.activate();\n  }\n}\n/** @docs-private */\nfunction TREE_KEY_MANAGER_FACTORY() {\n  return (items, options) => new TreeKeyManager(items, options);\n}\n/** Injection token that determines the key manager to use. */\nconst TREE_KEY_MANAGER = new InjectionToken('tree-key-manager', {\n  providedIn: 'root',\n  factory: TREE_KEY_MANAGER_FACTORY\n});\n/** @docs-private */\nconst TREE_KEY_MANAGER_FACTORY_PROVIDER = {\n  provide: TREE_KEY_MANAGER,\n  useFactory: TREE_KEY_MANAGER_FACTORY\n};\n\n// NoopTreeKeyManager is a \"noop\" implementation of TreeKeyMangerStrategy. Methods are noops. Does\n// not emit to streams.\n//\n// Used for applications built before TreeKeyManager to opt-out of TreeKeyManager and revert to\n// legacy behavior.\n/**\n * @docs-private\n *\n * Opt-out of Tree of key manager behavior.\n *\n * When provided, Tree has same focus management behavior as before TreeKeyManager was introduced.\n *  - Tree does not respond to keyboard interaction\n *  - Tree node allows tabindex to be set by Input binding\n *  - Tree node allows tabindex to be set by attribute binding\n *\n * @deprecated NoopTreeKeyManager deprecated. Use TreeKeyManager or inject a\n * TreeKeyManagerStrategy instead. To be removed in a future version.\n *\n * @breaking-change 21.0.0\n */\nclass NoopTreeKeyManager {\n  constructor() {\n    this._isNoopTreeKeyManager = true;\n    // Provide change as required by TreeKeyManagerStrategy. NoopTreeKeyManager is a \"noop\"\n    // implementation that does not emit to streams.\n    this.change = new Subject();\n  }\n  destroy() {\n    this.change.complete();\n  }\n  onKeydown() {\n    // noop\n  }\n  getActiveItemIndex() {\n    // Always return null. NoopTreeKeyManager is a \"noop\" implementation that does not maintain\n    // the active item.\n    return null;\n  }\n  getActiveItem() {\n    // Always return null. NoopTreeKeyManager is a \"noop\" implementation that does not maintain\n    // the active item.\n    return null;\n  }\n  focusItem() {\n    // noop\n  }\n}\n/**\n * @docs-private\n *\n * Opt-out of Tree of key manager behavior.\n *\n * When provided, Tree has same focus management behavior as before TreeKeyManager was introduced.\n *  - Tree does not respond to keyboard interaction\n *  - Tree node allows tabindex to be set by Input binding\n *  - Tree node allows tabindex to be set by attribute binding\n *\n * @deprecated NoopTreeKeyManager deprecated. Use TreeKeyManager or inject a\n * TreeKeyManagerStrategy instead. To be removed in a future version.\n *\n * @breaking-change 21.0.0\n */\nfunction NOOP_TREE_KEY_MANAGER_FACTORY() {\n  return () => new NoopTreeKeyManager();\n}\n/**\n * @docs-private\n *\n * Opt-out of Tree of key manager behavior.\n *\n * When provided, Tree has same focus management behavior as before TreeKeyManager was introduced.\n *  - Tree does not respond to keyboard interaction\n *  - Tree node allows tabindex to be set by Input binding\n *  - Tree node allows tabindex to be set by attribute binding\n *\n * @deprecated NoopTreeKeyManager deprecated. Use TreeKeyManager or inject a\n * TreeKeyManagerStrategy instead. To be removed in a future version.\n *\n * @breaking-change 21.0.0\n */\nconst NOOP_TREE_KEY_MANAGER_FACTORY_PROVIDER = {\n  provide: TREE_KEY_MANAGER,\n  useFactory: NOOP_TREE_KEY_MANAGER_FACTORY\n};\n\n/**\n * Configuration for the isFocusable method.\n */\nclass IsFocusableConfig {\n  constructor() {\n    /**\n     * Whether to count an element as focusable even if it is not currently visible.\n     */\n    this.ignoreVisibility = false;\n  }\n}\n// The InteractivityChecker leans heavily on the ally.js accessibility utilities.\n// Methods like `isTabbable` are only covering specific edge-cases for the browsers which are\n// supported.\n/**\n * Utility for checking the interactivity of an element, such as whether it is focusable or\n * tabbable.\n */\nclass InteractivityChecker {\n  constructor(_platform) {\n    this._platform = _platform;\n  }\n  /**\n   * Gets whether an element is disabled.\n   *\n   * @param element Element to be checked.\n   * @returns Whether the element is disabled.\n   */\n  isDisabled(element) {\n    // This does not capture some cases, such as a non-form control with a disabled attribute or\n    // a form control inside of a disabled form, but should capture the most common cases.\n    return element.hasAttribute('disabled');\n  }\n  /**\n   * Gets whether an element is visible for the purposes of interactivity.\n   *\n   * This will capture states like `display: none` and `visibility: hidden`, but not things like\n   * being clipped by an `overflow: hidden` parent or being outside the viewport.\n   *\n   * @returns Whether the element is visible.\n   */\n  isVisible(element) {\n    return hasGeometry(element) && getComputedStyle(element).visibility === 'visible';\n  }\n  /**\n   * Gets whether an element can be reached via Tab key.\n   * Assumes that the element has already been checked with isFocusable.\n   *\n   * @param element Element to be checked.\n   * @returns Whether the element is tabbable.\n   */\n  isTabbable(element) {\n    // Nothing is tabbable on the server 😎\n    if (!this._platform.isBrowser) {\n      return false;\n    }\n    const frameElement = getFrameElement(getWindow(element));\n    if (frameElement) {\n      // Frame elements inherit their tabindex onto all child elements.\n      if (getTabIndexValue(frameElement) === -1) {\n        return false;\n      }\n      // Browsers disable tabbing to an element inside of an invisible frame.\n      if (!this.isVisible(frameElement)) {\n        return false;\n      }\n    }\n    let nodeName = element.nodeName.toLowerCase();\n    let tabIndexValue = getTabIndexValue(element);\n    if (element.hasAttribute('contenteditable')) {\n      return tabIndexValue !== -1;\n    }\n    if (nodeName === 'iframe' || nodeName === 'object') {\n      // The frame or object's content may be tabbable depending on the content, but it's\n      // not possibly to reliably detect the content of the frames. We always consider such\n      // elements as non-tabbable.\n      return false;\n    }\n    // In iOS, the browser only considers some specific elements as tabbable.\n    if (this._platform.WEBKIT && this._platform.IOS && !isPotentiallyTabbableIOS(element)) {\n      return false;\n    }\n    if (nodeName === 'audio') {\n      // Audio elements without controls enabled are never tabbable, regardless\n      // of the tabindex attribute explicitly being set.\n      if (!element.hasAttribute('controls')) {\n        return false;\n      }\n      // Audio elements with controls are by default tabbable unless the\n      // tabindex attribute is set to `-1` explicitly.\n      return tabIndexValue !== -1;\n    }\n    if (nodeName === 'video') {\n      // For all video elements, if the tabindex attribute is set to `-1`, the video\n      // is not tabbable. Note: We cannot rely on the default `HTMLElement.tabIndex`\n      // property as that one is set to `-1` in Chrome, Edge and Safari v13.1. The\n      // tabindex attribute is the source of truth here.\n      if (tabIndexValue === -1) {\n        return false;\n      }\n      // If the tabindex is explicitly set, and not `-1` (as per check before), the\n      // video element is always tabbable (regardless of whether it has controls or not).\n      if (tabIndexValue !== null) {\n        return true;\n      }\n      // Otherwise (when no explicit tabindex is set), a video is only tabbable if it\n      // has controls enabled. Firefox is special as videos are always tabbable regardless\n      // of whether there are controls or not.\n      return this._platform.FIREFOX || element.hasAttribute('controls');\n    }\n    return element.tabIndex >= 0;\n  }\n  /**\n   * Gets whether an element can be focused by the user.\n   *\n   * @param element Element to be checked.\n   * @param config The config object with options to customize this method's behavior\n   * @returns Whether the element is focusable.\n   */\n  isFocusable(element, config) {\n    // Perform checks in order of left to most expensive.\n    // Again, naive approach that does not capture many edge cases and browser quirks.\n    return isPotentiallyFocusable(element) && !this.isDisabled(element) && (config?.ignoreVisibility || this.isVisible(element));\n  }\n  static {\n    this.ɵfac = function InteractivityChecker_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || InteractivityChecker)(i0.ɵɵinject(i1.Platform));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: InteractivityChecker,\n      factory: InteractivityChecker.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InteractivityChecker, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i1.Platform\n  }], null);\n})();\n/**\n * Returns the frame element from a window object. Since browsers like MS Edge throw errors if\n * the frameElement property is being accessed from a different host address, this property\n * should be accessed carefully.\n */\nfunction getFrameElement(window) {\n  try {\n    return window.frameElement;\n  } catch {\n    return null;\n  }\n}\n/** Checks whether the specified element has any geometry / rectangles. */\nfunction hasGeometry(element) {\n  // Use logic from jQuery to check for an invisible element.\n  // See https://github.com/jquery/jquery/blob/master/src/css/hiddenVisibleSelectors.js#L12\n  return !!(element.offsetWidth || element.offsetHeight || typeof element.getClientRects === 'function' && element.getClientRects().length);\n}\n/** Gets whether an element's  */\nfunction isNativeFormElement(element) {\n  let nodeName = element.nodeName.toLowerCase();\n  return nodeName === 'input' || nodeName === 'select' || nodeName === 'button' || nodeName === 'textarea';\n}\n/** Gets whether an element is an `<input type=\"hidden\">`. */\nfunction isHiddenInput(element) {\n  return isInputElement(element) && element.type == 'hidden';\n}\n/** Gets whether an element is an anchor that has an href attribute. */\nfunction isAnchorWithHref(element) {\n  return isAnchorElement(element) && element.hasAttribute('href');\n}\n/** Gets whether an element is an input element. */\nfunction isInputElement(element) {\n  return element.nodeName.toLowerCase() == 'input';\n}\n/** Gets whether an element is an anchor element. */\nfunction isAnchorElement(element) {\n  return element.nodeName.toLowerCase() == 'a';\n}\n/** Gets whether an element has a valid tabindex. */\nfunction hasValidTabIndex(element) {\n  if (!element.hasAttribute('tabindex') || element.tabIndex === undefined) {\n    return false;\n  }\n  let tabIndex = element.getAttribute('tabindex');\n  return !!(tabIndex && !isNaN(parseInt(tabIndex, 10)));\n}\n/**\n * Returns the parsed tabindex from the element attributes instead of returning the\n * evaluated tabindex from the browsers defaults.\n */\nfunction getTabIndexValue(element) {\n  if (!hasValidTabIndex(element)) {\n    return null;\n  }\n  // See browser issue in Gecko https://bugzilla.mozilla.org/show_bug.cgi?id=1128054\n  const tabIndex = parseInt(element.getAttribute('tabindex') || '', 10);\n  return isNaN(tabIndex) ? -1 : tabIndex;\n}\n/** Checks whether the specified element is potentially tabbable on iOS */\nfunction isPotentiallyTabbableIOS(element) {\n  let nodeName = element.nodeName.toLowerCase();\n  let inputType = nodeName === 'input' && element.type;\n  return inputType === 'text' || inputType === 'password' || nodeName === 'select' || nodeName === 'textarea';\n}\n/**\n * Gets whether an element is potentially focusable without taking current visible/disabled state\n * into account.\n */\nfunction isPotentiallyFocusable(element) {\n  // Inputs are potentially focusable *unless* they're type=\"hidden\".\n  if (isHiddenInput(element)) {\n    return false;\n  }\n  return isNativeFormElement(element) || isAnchorWithHref(element) || element.hasAttribute('contenteditable') || hasValidTabIndex(element);\n}\n/** Gets the parent window of a DOM node with regards of being inside of an iframe. */\nfunction getWindow(node) {\n  // ownerDocument is null if `node` itself *is* a document.\n  return node.ownerDocument && node.ownerDocument.defaultView || window;\n}\n\n/**\n * Class that allows for trapping focus within a DOM element.\n *\n * This class currently uses a relatively simple approach to focus trapping.\n * It assumes that the tab order is the same as DOM order, which is not necessarily true.\n * Things like `tabIndex > 0`, flex `order`, and shadow roots can cause the two to be misaligned.\n */\nclass FocusTrap {\n  /** Whether the focus trap is active. */\n  get enabled() {\n    return this._enabled;\n  }\n  set enabled(value) {\n    this._enabled = value;\n    if (this._startAnchor && this._endAnchor) {\n      this._toggleAnchorTabIndex(value, this._startAnchor);\n      this._toggleAnchorTabIndex(value, this._endAnchor);\n    }\n  }\n  constructor(_element, _checker, _ngZone, _document, deferAnchors = false, /** @breaking-change 20.0.0 param to become required */\n  _injector) {\n    this._element = _element;\n    this._checker = _checker;\n    this._ngZone = _ngZone;\n    this._document = _document;\n    this._injector = _injector;\n    this._hasAttached = false;\n    // Event listeners for the anchors. Need to be regular functions so that we can unbind them later.\n    this.startAnchorListener = () => this.focusLastTabbableElement();\n    this.endAnchorListener = () => this.focusFirstTabbableElement();\n    this._enabled = true;\n    if (!deferAnchors) {\n      this.attachAnchors();\n    }\n  }\n  /** Destroys the focus trap by cleaning up the anchors. */\n  destroy() {\n    const startAnchor = this._startAnchor;\n    const endAnchor = this._endAnchor;\n    if (startAnchor) {\n      startAnchor.removeEventListener('focus', this.startAnchorListener);\n      startAnchor.remove();\n    }\n    if (endAnchor) {\n      endAnchor.removeEventListener('focus', this.endAnchorListener);\n      endAnchor.remove();\n    }\n    this._startAnchor = this._endAnchor = null;\n    this._hasAttached = false;\n  }\n  /**\n   * Inserts the anchors into the DOM. This is usually done automatically\n   * in the constructor, but can be deferred for cases like directives with `*ngIf`.\n   * @returns Whether the focus trap managed to attach successfully. This may not be the case\n   * if the target element isn't currently in the DOM.\n   */\n  attachAnchors() {\n    // If we're not on the browser, there can be no focus to trap.\n    if (this._hasAttached) {\n      return true;\n    }\n    this._ngZone.runOutsideAngular(() => {\n      if (!this._startAnchor) {\n        this._startAnchor = this._createAnchor();\n        this._startAnchor.addEventListener('focus', this.startAnchorListener);\n      }\n      if (!this._endAnchor) {\n        this._endAnchor = this._createAnchor();\n        this._endAnchor.addEventListener('focus', this.endAnchorListener);\n      }\n    });\n    if (this._element.parentNode) {\n      this._element.parentNode.insertBefore(this._startAnchor, this._element);\n      this._element.parentNode.insertBefore(this._endAnchor, this._element.nextSibling);\n      this._hasAttached = true;\n    }\n    return this._hasAttached;\n  }\n  /**\n   * Waits for the zone to stabilize, then focuses the first tabbable element.\n   * @returns Returns a promise that resolves with a boolean, depending\n   * on whether focus was moved successfully.\n   */\n  focusInitialElementWhenReady(options) {\n    return new Promise(resolve => {\n      this._executeOnStable(() => resolve(this.focusInitialElement(options)));\n    });\n  }\n  /**\n   * Waits for the zone to stabilize, then focuses\n   * the first tabbable element within the focus trap region.\n   * @returns Returns a promise that resolves with a boolean, depending\n   * on whether focus was moved successfully.\n   */\n  focusFirstTabbableElementWhenReady(options) {\n    return new Promise(resolve => {\n      this._executeOnStable(() => resolve(this.focusFirstTabbableElement(options)));\n    });\n  }\n  /**\n   * Waits for the zone to stabilize, then focuses\n   * the last tabbable element within the focus trap region.\n   * @returns Returns a promise that resolves with a boolean, depending\n   * on whether focus was moved successfully.\n   */\n  focusLastTabbableElementWhenReady(options) {\n    return new Promise(resolve => {\n      this._executeOnStable(() => resolve(this.focusLastTabbableElement(options)));\n    });\n  }\n  /**\n   * Get the specified boundary element of the trapped region.\n   * @param bound The boundary to get (start or end of trapped region).\n   * @returns The boundary element.\n   */\n  _getRegionBoundary(bound) {\n    // Contains the deprecated version of selector, for temporary backwards comparability.\n    const markers = this._element.querySelectorAll(`[cdk-focus-region-${bound}], ` + `[cdkFocusRegion${bound}], ` + `[cdk-focus-${bound}]`);\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      for (let i = 0; i < markers.length; i++) {\n        // @breaking-change 8.0.0\n        if (markers[i].hasAttribute(`cdk-focus-${bound}`)) {\n          console.warn(`Found use of deprecated attribute 'cdk-focus-${bound}', ` + `use 'cdkFocusRegion${bound}' instead. The deprecated ` + `attribute will be removed in 8.0.0.`, markers[i]);\n        } else if (markers[i].hasAttribute(`cdk-focus-region-${bound}`)) {\n          console.warn(`Found use of deprecated attribute 'cdk-focus-region-${bound}', ` + `use 'cdkFocusRegion${bound}' instead. The deprecated attribute ` + `will be removed in 8.0.0.`, markers[i]);\n        }\n      }\n    }\n    if (bound == 'start') {\n      return markers.length ? markers[0] : this._getFirstTabbableElement(this._element);\n    }\n    return markers.length ? markers[markers.length - 1] : this._getLastTabbableElement(this._element);\n  }\n  /**\n   * Focuses the element that should be focused when the focus trap is initialized.\n   * @returns Whether focus was moved successfully.\n   */\n  focusInitialElement(options) {\n    // Contains the deprecated version of selector, for temporary backwards comparability.\n    const redirectToElement = this._element.querySelector(`[cdk-focus-initial], ` + `[cdkFocusInitial]`);\n    if (redirectToElement) {\n      // @breaking-change 8.0.0\n      if ((typeof ngDevMode === 'undefined' || ngDevMode) && redirectToElement.hasAttribute(`cdk-focus-initial`)) {\n        console.warn(`Found use of deprecated attribute 'cdk-focus-initial', ` + `use 'cdkFocusInitial' instead. The deprecated attribute ` + `will be removed in 8.0.0`, redirectToElement);\n      }\n      // Warn the consumer if the element they've pointed to\n      // isn't focusable, when not in production mode.\n      if ((typeof ngDevMode === 'undefined' || ngDevMode) && !this._checker.isFocusable(redirectToElement)) {\n        console.warn(`Element matching '[cdkFocusInitial]' is not focusable.`, redirectToElement);\n      }\n      if (!this._checker.isFocusable(redirectToElement)) {\n        const focusableChild = this._getFirstTabbableElement(redirectToElement);\n        focusableChild?.focus(options);\n        return !!focusableChild;\n      }\n      redirectToElement.focus(options);\n      return true;\n    }\n    return this.focusFirstTabbableElement(options);\n  }\n  /**\n   * Focuses the first tabbable element within the focus trap region.\n   * @returns Whether focus was moved successfully.\n   */\n  focusFirstTabbableElement(options) {\n    const redirectToElement = this._getRegionBoundary('start');\n    if (redirectToElement) {\n      redirectToElement.focus(options);\n    }\n    return !!redirectToElement;\n  }\n  /**\n   * Focuses the last tabbable element within the focus trap region.\n   * @returns Whether focus was moved successfully.\n   */\n  focusLastTabbableElement(options) {\n    const redirectToElement = this._getRegionBoundary('end');\n    if (redirectToElement) {\n      redirectToElement.focus(options);\n    }\n    return !!redirectToElement;\n  }\n  /**\n   * Checks whether the focus trap has successfully been attached.\n   */\n  hasAttached() {\n    return this._hasAttached;\n  }\n  /** Get the first tabbable element from a DOM subtree (inclusive). */\n  _getFirstTabbableElement(root) {\n    if (this._checker.isFocusable(root) && this._checker.isTabbable(root)) {\n      return root;\n    }\n    const children = root.children;\n    for (let i = 0; i < children.length; i++) {\n      const tabbableChild = children[i].nodeType === this._document.ELEMENT_NODE ? this._getFirstTabbableElement(children[i]) : null;\n      if (tabbableChild) {\n        return tabbableChild;\n      }\n    }\n    return null;\n  }\n  /** Get the last tabbable element from a DOM subtree (inclusive). */\n  _getLastTabbableElement(root) {\n    if (this._checker.isFocusable(root) && this._checker.isTabbable(root)) {\n      return root;\n    }\n    // Iterate in reverse DOM order.\n    const children = root.children;\n    for (let i = children.length - 1; i >= 0; i--) {\n      const tabbableChild = children[i].nodeType === this._document.ELEMENT_NODE ? this._getLastTabbableElement(children[i]) : null;\n      if (tabbableChild) {\n        return tabbableChild;\n      }\n    }\n    return null;\n  }\n  /** Creates an anchor element. */\n  _createAnchor() {\n    const anchor = this._document.createElement('div');\n    this._toggleAnchorTabIndex(this._enabled, anchor);\n    anchor.classList.add('cdk-visually-hidden');\n    anchor.classList.add('cdk-focus-trap-anchor');\n    anchor.setAttribute('aria-hidden', 'true');\n    return anchor;\n  }\n  /**\n   * Toggles the `tabindex` of an anchor, based on the enabled state of the focus trap.\n   * @param isEnabled Whether the focus trap is enabled.\n   * @param anchor Anchor on which to toggle the tabindex.\n   */\n  _toggleAnchorTabIndex(isEnabled, anchor) {\n    // Remove the tabindex completely, rather than setting it to -1, because if the\n    // element has a tabindex, the user might still hit it when navigating with the arrow keys.\n    isEnabled ? anchor.setAttribute('tabindex', '0') : anchor.removeAttribute('tabindex');\n  }\n  /**\n   * Toggles the`tabindex` of both anchors to either trap Tab focus or allow it to escape.\n   * @param enabled: Whether the anchors should trap Tab.\n   */\n  toggleAnchors(enabled) {\n    if (this._startAnchor && this._endAnchor) {\n      this._toggleAnchorTabIndex(enabled, this._startAnchor);\n      this._toggleAnchorTabIndex(enabled, this._endAnchor);\n    }\n  }\n  /** Executes a function when the zone is stable. */\n  _executeOnStable(fn) {\n    // TODO: remove this conditional when injector is required in the constructor.\n    if (this._injector) {\n      afterNextRender(fn, {\n        injector: this._injector\n      });\n    } else {\n      setTimeout(fn);\n    }\n  }\n}\n/**\n * Factory that allows easy instantiation of focus traps.\n */\nclass FocusTrapFactory {\n  constructor(_checker, _ngZone, _document) {\n    this._checker = _checker;\n    this._ngZone = _ngZone;\n    this._injector = inject(Injector);\n    this._document = _document;\n  }\n  /**\n   * Creates a focus-trapped region around the given element.\n   * @param element The element around which focus will be trapped.\n   * @param deferCaptureElements Defers the creation of focus-capturing elements to be done\n   *     manually by the user.\n   * @returns The created focus trap instance.\n   */\n  create(element, deferCaptureElements = false) {\n    return new FocusTrap(element, this._checker, this._ngZone, this._document, deferCaptureElements, this._injector);\n  }\n  static {\n    this.ɵfac = function FocusTrapFactory_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FocusTrapFactory)(i0.ɵɵinject(InteractivityChecker), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(DOCUMENT));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: FocusTrapFactory,\n      factory: FocusTrapFactory.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FocusTrapFactory, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: InteractivityChecker\n  }, {\n    type: i0.NgZone\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\n/** Directive for trapping focus within a region. */\nclass CdkTrapFocus {\n  /** Whether the focus trap is active. */\n  get enabled() {\n    return this.focusTrap?.enabled || false;\n  }\n  set enabled(value) {\n    if (this.focusTrap) {\n      this.focusTrap.enabled = value;\n    }\n  }\n  constructor(_elementRef, _focusTrapFactory,\n  /**\n   * @deprecated No longer being used. To be removed.\n   * @breaking-change 13.0.0\n   */\n  _document) {\n    this._elementRef = _elementRef;\n    this._focusTrapFactory = _focusTrapFactory;\n    /** Previously focused element to restore focus to upon destroy when using autoCapture. */\n    this._previouslyFocusedElement = null;\n    const platform = inject(Platform);\n    if (platform.isBrowser) {\n      this.focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement, true);\n    }\n  }\n  ngOnDestroy() {\n    this.focusTrap?.destroy();\n    // If we stored a previously focused element when using autoCapture, return focus to that\n    // element now that the trapped region is being destroyed.\n    if (this._previouslyFocusedElement) {\n      this._previouslyFocusedElement.focus();\n      this._previouslyFocusedElement = null;\n    }\n  }\n  ngAfterContentInit() {\n    this.focusTrap?.attachAnchors();\n    if (this.autoCapture) {\n      this._captureFocus();\n    }\n  }\n  ngDoCheck() {\n    if (this.focusTrap && !this.focusTrap.hasAttached()) {\n      this.focusTrap.attachAnchors();\n    }\n  }\n  ngOnChanges(changes) {\n    const autoCaptureChange = changes['autoCapture'];\n    if (autoCaptureChange && !autoCaptureChange.firstChange && this.autoCapture && this.focusTrap?.hasAttached()) {\n      this._captureFocus();\n    }\n  }\n  _captureFocus() {\n    this._previouslyFocusedElement = _getFocusedElementPierceShadowDom();\n    this.focusTrap?.focusInitialElementWhenReady();\n  }\n  static {\n    this.ɵfac = function CdkTrapFocus_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkTrapFocus)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(FocusTrapFactory), i0.ɵɵdirectiveInject(DOCUMENT));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkTrapFocus,\n      selectors: [[\"\", \"cdkTrapFocus\", \"\"]],\n      inputs: {\n        enabled: [2, \"cdkTrapFocus\", \"enabled\", booleanAttribute],\n        autoCapture: [2, \"cdkTrapFocusAutoCapture\", \"autoCapture\", booleanAttribute]\n      },\n      exportAs: [\"cdkTrapFocus\"],\n      standalone: true,\n      features: [i0.ɵɵInputTransformsFeature, i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTrapFocus, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkTrapFocus]',\n      exportAs: 'cdkTrapFocus',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: FocusTrapFactory\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], {\n    enabled: [{\n      type: Input,\n      args: [{\n        alias: 'cdkTrapFocus',\n        transform: booleanAttribute\n      }]\n    }],\n    autoCapture: [{\n      type: Input,\n      args: [{\n        alias: 'cdkTrapFocusAutoCapture',\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n\n/**\n * Class that allows for trapping focus within a DOM element.\n *\n * This class uses a strategy pattern that determines how it traps focus.\n * See FocusTrapInertStrategy.\n */\nclass ConfigurableFocusTrap extends FocusTrap {\n  /** Whether the FocusTrap is enabled. */\n  get enabled() {\n    return this._enabled;\n  }\n  set enabled(value) {\n    this._enabled = value;\n    if (this._enabled) {\n      this._focusTrapManager.register(this);\n    } else {\n      this._focusTrapManager.deregister(this);\n    }\n  }\n  constructor(_element, _checker, _ngZone, _document, _focusTrapManager, _inertStrategy, config, injector) {\n    super(_element, _checker, _ngZone, _document, config.defer, injector);\n    this._focusTrapManager = _focusTrapManager;\n    this._inertStrategy = _inertStrategy;\n    this._focusTrapManager.register(this);\n  }\n  /** Notifies the FocusTrapManager that this FocusTrap will be destroyed. */\n  destroy() {\n    this._focusTrapManager.deregister(this);\n    super.destroy();\n  }\n  /** @docs-private Implemented as part of ManagedFocusTrap. */\n  _enable() {\n    this._inertStrategy.preventFocus(this);\n    this.toggleAnchors(true);\n  }\n  /** @docs-private Implemented as part of ManagedFocusTrap. */\n  _disable() {\n    this._inertStrategy.allowFocus(this);\n    this.toggleAnchors(false);\n  }\n}\n\n/**\n * Lightweight FocusTrapInertStrategy that adds a document focus event\n * listener to redirect focus back inside the FocusTrap.\n */\nclass EventListenerFocusTrapInertStrategy {\n  constructor() {\n    /** Focus event handler. */\n    this._listener = null;\n  }\n  /** Adds a document event listener that keeps focus inside the FocusTrap. */\n  preventFocus(focusTrap) {\n    // Ensure there's only one listener per document\n    if (this._listener) {\n      focusTrap._document.removeEventListener('focus', this._listener, true);\n    }\n    this._listener = e => this._trapFocus(focusTrap, e);\n    focusTrap._ngZone.runOutsideAngular(() => {\n      focusTrap._document.addEventListener('focus', this._listener, true);\n    });\n  }\n  /** Removes the event listener added in preventFocus. */\n  allowFocus(focusTrap) {\n    if (!this._listener) {\n      return;\n    }\n    focusTrap._document.removeEventListener('focus', this._listener, true);\n    this._listener = null;\n  }\n  /**\n   * Refocuses the first element in the FocusTrap if the focus event target was outside\n   * the FocusTrap.\n   *\n   * This is an event listener callback. The event listener is added in runOutsideAngular,\n   * so all this code runs outside Angular as well.\n   */\n  _trapFocus(focusTrap, event) {\n    const target = event.target;\n    const focusTrapRoot = focusTrap._element;\n    // Don't refocus if target was in an overlay, because the overlay might be associated\n    // with an element inside the FocusTrap, ex. mat-select.\n    if (target && !focusTrapRoot.contains(target) && !target.closest?.('div.cdk-overlay-pane')) {\n      // Some legacy FocusTrap usages have logic that focuses some element on the page\n      // just before FocusTrap is destroyed. For backwards compatibility, wait\n      // to be sure FocusTrap is still enabled before refocusing.\n      setTimeout(() => {\n        // Check whether focus wasn't put back into the focus trap while the timeout was pending.\n        if (focusTrap.enabled && !focusTrapRoot.contains(focusTrap._document.activeElement)) {\n          focusTrap.focusFirstTabbableElement();\n        }\n      });\n    }\n  }\n}\n\n/** The injection token used to specify the inert strategy. */\nconst FOCUS_TRAP_INERT_STRATEGY = new InjectionToken('FOCUS_TRAP_INERT_STRATEGY');\n\n/** Injectable that ensures only the most recently enabled FocusTrap is active. */\nclass FocusTrapManager {\n  constructor() {\n    // A stack of the FocusTraps on the page. Only the FocusTrap at the\n    // top of the stack is active.\n    this._focusTrapStack = [];\n  }\n  /**\n   * Disables the FocusTrap at the top of the stack, and then pushes\n   * the new FocusTrap onto the stack.\n   */\n  register(focusTrap) {\n    // Dedupe focusTraps that register multiple times.\n    this._focusTrapStack = this._focusTrapStack.filter(ft => ft !== focusTrap);\n    let stack = this._focusTrapStack;\n    if (stack.length) {\n      stack[stack.length - 1]._disable();\n    }\n    stack.push(focusTrap);\n    focusTrap._enable();\n  }\n  /**\n   * Removes the FocusTrap from the stack, and activates the\n   * FocusTrap that is the new top of the stack.\n   */\n  deregister(focusTrap) {\n    focusTrap._disable();\n    const stack = this._focusTrapStack;\n    const i = stack.indexOf(focusTrap);\n    if (i !== -1) {\n      stack.splice(i, 1);\n      if (stack.length) {\n        stack[stack.length - 1]._enable();\n      }\n    }\n  }\n  static {\n    this.ɵfac = function FocusTrapManager_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FocusTrapManager)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: FocusTrapManager,\n      factory: FocusTrapManager.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FocusTrapManager, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n\n/** Factory that allows easy instantiation of configurable focus traps. */\nclass ConfigurableFocusTrapFactory {\n  constructor(_checker, _ngZone, _focusTrapManager, _document, _inertStrategy) {\n    this._checker = _checker;\n    this._ngZone = _ngZone;\n    this._focusTrapManager = _focusTrapManager;\n    this._injector = inject(Injector);\n    this._document = _document;\n    // TODO split up the strategies into different modules, similar to DateAdapter.\n    this._inertStrategy = _inertStrategy || new EventListenerFocusTrapInertStrategy();\n  }\n  create(element, config = {\n    defer: false\n  }) {\n    let configObject;\n    if (typeof config === 'boolean') {\n      configObject = {\n        defer: config\n      };\n    } else {\n      configObject = config;\n    }\n    return new ConfigurableFocusTrap(element, this._checker, this._ngZone, this._document, this._focusTrapManager, this._inertStrategy, configObject, this._injector);\n  }\n  static {\n    this.ɵfac = function ConfigurableFocusTrapFactory_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ConfigurableFocusTrapFactory)(i0.ɵɵinject(InteractivityChecker), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(FocusTrapManager), i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(FOCUS_TRAP_INERT_STRATEGY, 8));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ConfigurableFocusTrapFactory,\n      factory: ConfigurableFocusTrapFactory.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ConfigurableFocusTrapFactory, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: InteractivityChecker\n  }, {\n    type: i0.NgZone\n  }, {\n    type: FocusTrapManager\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [FOCUS_TRAP_INERT_STRATEGY]\n    }]\n  }], null);\n})();\n\n/** Gets whether an event could be a faked `mousedown` event dispatched by a screen reader. */\nfunction isFakeMousedownFromScreenReader(event) {\n  // Some screen readers will dispatch a fake `mousedown` event when pressing enter or space on\n  // a clickable element. We can distinguish these events when `event.buttons` is zero, or\n  // `event.detail` is zero depending on the browser:\n  // - `event.buttons` works on Firefox, but fails on Chrome.\n  // - `detail` works on Chrome, but fails on Firefox.\n  return event.buttons === 0 || event.detail === 0;\n}\n/** Gets whether an event could be a faked `touchstart` event dispatched by a screen reader. */\nfunction isFakeTouchstartFromScreenReader(event) {\n  const touch = event.touches && event.touches[0] || event.changedTouches && event.changedTouches[0];\n  // A fake `touchstart` can be distinguished from a real one by looking at the `identifier`\n  // which is typically >= 0 on a real device versus -1 from a screen reader. Just to be safe,\n  // we can also look at `radiusX` and `radiusY`. This behavior was observed against a Windows 10\n  // device with a touch screen running NVDA v2020.4 and Firefox 85 or Chrome 88.\n  return !!touch && touch.identifier === -1 && (touch.radiusX == null || touch.radiusX === 1) && (touch.radiusY == null || touch.radiusY === 1);\n}\n\n/**\n * Injectable options for the InputModalityDetector. These are shallowly merged with the default\n * options.\n */\nconst INPUT_MODALITY_DETECTOR_OPTIONS = new InjectionToken('cdk-input-modality-detector-options');\n/**\n * Default options for the InputModalityDetector.\n *\n * Modifier keys are ignored by default (i.e. when pressed won't cause the service to detect\n * keyboard input modality) for two reasons:\n *\n * 1. Modifier keys are commonly used with mouse to perform actions such as 'right click' or 'open\n *    in new tab', and are thus less representative of actual keyboard interaction.\n * 2. VoiceOver triggers some keyboard events when linearly navigating with Control + Option (but\n *    confusingly not with Caps Lock). Thus, to have parity with other screen readers, we ignore\n *    these keys so as to not update the input modality.\n *\n * Note that we do not by default ignore the right Meta key on Safari because it has the same key\n * code as the ContextMenu key on other browsers. When we switch to using event.key, we can\n * distinguish between the two.\n */\nconst INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS = {\n  ignoreKeys: [ALT, CONTROL, MAC_META, META, SHIFT]\n};\n/**\n * The amount of time needed to pass after a touchstart event in order for a subsequent mousedown\n * event to be attributed as mouse and not touch.\n *\n * This is the value used by AngularJS Material. Through trial and error (on iPhone 6S) they found\n * that a value of around 650ms seems appropriate.\n */\nconst TOUCH_BUFFER_MS = 650;\n/**\n * Event listener options that enable capturing and also mark the listener as passive if the browser\n * supports it.\n */\nconst modalityEventListenerOptions = normalizePassiveListenerOptions({\n  passive: true,\n  capture: true\n});\n/**\n * Service that detects the user's input modality.\n *\n * This service does not update the input modality when a user navigates with a screen reader\n * (e.g. linear navigation with VoiceOver, object navigation / browse mode with NVDA, virtual PC\n * cursor mode with JAWS). This is in part due to technical limitations (i.e. keyboard events do not\n * fire as expected in these modes) but is also arguably the correct behavior. Navigating with a\n * screen reader is akin to visually scanning a page, and should not be interpreted as actual user\n * input interaction.\n *\n * When a user is not navigating but *interacting* with a screen reader, this service attempts to\n * update the input modality to keyboard, but in general this service's behavior is largely\n * undefined.\n */\nclass InputModalityDetector {\n  /** The most recently detected input modality. */\n  get mostRecentModality() {\n    return this._modality.value;\n  }\n  constructor(_platform, ngZone, document, options) {\n    this._platform = _platform;\n    /**\n     * The most recently detected input modality event target. Is null if no input modality has been\n     * detected or if the associated event target is null for some unknown reason.\n     */\n    this._mostRecentTarget = null;\n    /** The underlying BehaviorSubject that emits whenever an input modality is detected. */\n    this._modality = new BehaviorSubject(null);\n    /**\n     * The timestamp of the last touch input modality. Used to determine whether mousedown events\n     * should be attributed to mouse or touch.\n     */\n    this._lastTouchMs = 0;\n    /**\n     * Handles keydown events. Must be an arrow function in order to preserve the context when it gets\n     * bound.\n     */\n    this._onKeydown = event => {\n      // If this is one of the keys we should ignore, then ignore it and don't update the input\n      // modality to keyboard.\n      if (this._options?.ignoreKeys?.some(keyCode => keyCode === event.keyCode)) {\n        return;\n      }\n      this._modality.next('keyboard');\n      this._mostRecentTarget = _getEventTarget(event);\n    };\n    /**\n     * Handles mousedown events. Must be an arrow function in order to preserve the context when it\n     * gets bound.\n     */\n    this._onMousedown = event => {\n      // Touches trigger both touch and mouse events, so we need to distinguish between mouse events\n      // that were triggered via mouse vs touch. To do so, check if the mouse event occurs closely\n      // after the previous touch event.\n      if (Date.now() - this._lastTouchMs < TOUCH_BUFFER_MS) {\n        return;\n      }\n      // Fake mousedown events are fired by some screen readers when controls are activated by the\n      // screen reader. Attribute them to keyboard input modality.\n      this._modality.next(isFakeMousedownFromScreenReader(event) ? 'keyboard' : 'mouse');\n      this._mostRecentTarget = _getEventTarget(event);\n    };\n    /**\n     * Handles touchstart events. Must be an arrow function in order to preserve the context when it\n     * gets bound.\n     */\n    this._onTouchstart = event => {\n      // Same scenario as mentioned in _onMousedown, but on touch screen devices, fake touchstart\n      // events are fired. Again, attribute to keyboard input modality.\n      if (isFakeTouchstartFromScreenReader(event)) {\n        this._modality.next('keyboard');\n        return;\n      }\n      // Store the timestamp of this touch event, as it's used to distinguish between mouse events\n      // triggered via mouse vs touch.\n      this._lastTouchMs = Date.now();\n      this._modality.next('touch');\n      this._mostRecentTarget = _getEventTarget(event);\n    };\n    this._options = {\n      ...INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS,\n      ...options\n    };\n    // Skip the first emission as it's null.\n    this.modalityDetected = this._modality.pipe(skip(1));\n    this.modalityChanged = this.modalityDetected.pipe(distinctUntilChanged());\n    // If we're not in a browser, this service should do nothing, as there's no relevant input\n    // modality to detect.\n    if (_platform.isBrowser) {\n      ngZone.runOutsideAngular(() => {\n        document.addEventListener('keydown', this._onKeydown, modalityEventListenerOptions);\n        document.addEventListener('mousedown', this._onMousedown, modalityEventListenerOptions);\n        document.addEventListener('touchstart', this._onTouchstart, modalityEventListenerOptions);\n      });\n    }\n  }\n  ngOnDestroy() {\n    this._modality.complete();\n    if (this._platform.isBrowser) {\n      document.removeEventListener('keydown', this._onKeydown, modalityEventListenerOptions);\n      document.removeEventListener('mousedown', this._onMousedown, modalityEventListenerOptions);\n      document.removeEventListener('touchstart', this._onTouchstart, modalityEventListenerOptions);\n    }\n  }\n  static {\n    this.ɵfac = function InputModalityDetector_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || InputModalityDetector)(i0.ɵɵinject(i1.Platform), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(INPUT_MODALITY_DETECTOR_OPTIONS, 8));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: InputModalityDetector,\n      factory: InputModalityDetector.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InputModalityDetector, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i1.Platform\n  }, {\n    type: i0.NgZone\n  }, {\n    type: Document,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [INPUT_MODALITY_DETECTOR_OPTIONS]\n    }]\n  }], null);\n})();\nconst LIVE_ANNOUNCER_ELEMENT_TOKEN = new InjectionToken('liveAnnouncerElement', {\n  providedIn: 'root',\n  factory: LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY\n});\n/** @docs-private */\nfunction LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY() {\n  return null;\n}\n/** Injection token that can be used to configure the default options for the LiveAnnouncer. */\nconst LIVE_ANNOUNCER_DEFAULT_OPTIONS = new InjectionToken('LIVE_ANNOUNCER_DEFAULT_OPTIONS');\nlet uniqueIds = 0;\nclass LiveAnnouncer {\n  constructor(elementToken, _ngZone, _document, _defaultOptions) {\n    this._ngZone = _ngZone;\n    this._defaultOptions = _defaultOptions;\n    // We inject the live element and document as `any` because the constructor signature cannot\n    // reference browser globals (HTMLElement, Document) on non-browser environments, since having\n    // a class decorator causes TypeScript to preserve the constructor signature types.\n    this._document = _document;\n    this._liveElement = elementToken || this._createLiveElement();\n  }\n  announce(message, ...args) {\n    const defaultOptions = this._defaultOptions;\n    let politeness;\n    let duration;\n    if (args.length === 1 && typeof args[0] === 'number') {\n      duration = args[0];\n    } else {\n      [politeness, duration] = args;\n    }\n    this.clear();\n    clearTimeout(this._previousTimeout);\n    if (!politeness) {\n      politeness = defaultOptions && defaultOptions.politeness ? defaultOptions.politeness : 'polite';\n    }\n    if (duration == null && defaultOptions) {\n      duration = defaultOptions.duration;\n    }\n    // TODO: ensure changing the politeness works on all environments we support.\n    this._liveElement.setAttribute('aria-live', politeness);\n    if (this._liveElement.id) {\n      this._exposeAnnouncerToModals(this._liveElement.id);\n    }\n    // This 100ms timeout is necessary for some browser + screen-reader combinations:\n    // - Both JAWS and NVDA over IE11 will not announce anything without a non-zero timeout.\n    // - With Chrome and IE11 with NVDA or JAWS, a repeated (identical) message won't be read a\n    //   second time without clearing and then using a non-zero delay.\n    // (using JAWS 17 at time of this writing).\n    return this._ngZone.runOutsideAngular(() => {\n      if (!this._currentPromise) {\n        this._currentPromise = new Promise(resolve => this._currentResolve = resolve);\n      }\n      clearTimeout(this._previousTimeout);\n      this._previousTimeout = setTimeout(() => {\n        this._liveElement.textContent = message;\n        if (typeof duration === 'number') {\n          this._previousTimeout = setTimeout(() => this.clear(), duration);\n        }\n        // For some reason in tests this can be undefined\n        // Probably related to ZoneJS and every other thing that patches browser APIs in tests\n        this._currentResolve?.();\n        this._currentPromise = this._currentResolve = undefined;\n      }, 100);\n      return this._currentPromise;\n    });\n  }\n  /**\n   * Clears the current text from the announcer element. Can be used to prevent\n   * screen readers from reading the text out again while the user is going\n   * through the page landmarks.\n   */\n  clear() {\n    if (this._liveElement) {\n      this._liveElement.textContent = '';\n    }\n  }\n  ngOnDestroy() {\n    clearTimeout(this._previousTimeout);\n    this._liveElement?.remove();\n    this._liveElement = null;\n    this._currentResolve?.();\n    this._currentPromise = this._currentResolve = undefined;\n  }\n  _createLiveElement() {\n    const elementClass = 'cdk-live-announcer-element';\n    const previousElements = this._document.getElementsByClassName(elementClass);\n    const liveEl = this._document.createElement('div');\n    // Remove any old containers. This can happen when coming in from a server-side-rendered page.\n    for (let i = 0; i < previousElements.length; i++) {\n      previousElements[i].remove();\n    }\n    liveEl.classList.add(elementClass);\n    liveEl.classList.add('cdk-visually-hidden');\n    liveEl.setAttribute('aria-atomic', 'true');\n    liveEl.setAttribute('aria-live', 'polite');\n    liveEl.id = `cdk-live-announcer-${uniqueIds++}`;\n    this._document.body.appendChild(liveEl);\n    return liveEl;\n  }\n  /**\n   * Some browsers won't expose the accessibility node of the live announcer element if there is an\n   * `aria-modal` and the live announcer is outside of it. This method works around the issue by\n   * pointing the `aria-owns` of all modals to the live announcer element.\n   */\n  _exposeAnnouncerToModals(id) {\n    // TODO(http://github.com/angular/components/issues/26853): consider de-duplicating this with\n    // the `SnakBarContainer` and other usages.\n    //\n    // Note that the selector here is limited to CDK overlays at the moment in order to reduce the\n    // section of the DOM we need to look through. This should cover all the cases we support, but\n    // the selector can be expanded if it turns out to be too narrow.\n    const modals = this._document.querySelectorAll('body > .cdk-overlay-container [aria-modal=\"true\"]');\n    for (let i = 0; i < modals.length; i++) {\n      const modal = modals[i];\n      const ariaOwns = modal.getAttribute('aria-owns');\n      if (!ariaOwns) {\n        modal.setAttribute('aria-owns', id);\n      } else if (ariaOwns.indexOf(id) === -1) {\n        modal.setAttribute('aria-owns', ariaOwns + ' ' + id);\n      }\n    }\n  }\n  static {\n    this.ɵfac = function LiveAnnouncer_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LiveAnnouncer)(i0.ɵɵinject(LIVE_ANNOUNCER_ELEMENT_TOKEN, 8), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(LIVE_ANNOUNCER_DEFAULT_OPTIONS, 8));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: LiveAnnouncer,\n      factory: LiveAnnouncer.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LiveAnnouncer, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [LIVE_ANNOUNCER_ELEMENT_TOKEN]\n    }]\n  }, {\n    type: i0.NgZone\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [LIVE_ANNOUNCER_DEFAULT_OPTIONS]\n    }]\n  }], null);\n})();\n/**\n * A directive that works similarly to aria-live, but uses the LiveAnnouncer to ensure compatibility\n * with a wider range of browsers and screen readers.\n */\nclass CdkAriaLive {\n  /** The aria-live politeness level to use when announcing messages. */\n  get politeness() {\n    return this._politeness;\n  }\n  set politeness(value) {\n    this._politeness = value === 'off' || value === 'assertive' ? value : 'polite';\n    if (this._politeness === 'off') {\n      if (this._subscription) {\n        this._subscription.unsubscribe();\n        this._subscription = null;\n      }\n    } else if (!this._subscription) {\n      this._subscription = this._ngZone.runOutsideAngular(() => {\n        return this._contentObserver.observe(this._elementRef).subscribe(() => {\n          // Note that we use textContent here, rather than innerText, in order to avoid a reflow.\n          const elementText = this._elementRef.nativeElement.textContent;\n          // The `MutationObserver` fires also for attribute\n          // changes which we don't want to announce.\n          if (elementText !== this._previousAnnouncedText) {\n            this._liveAnnouncer.announce(elementText, this._politeness, this.duration);\n            this._previousAnnouncedText = elementText;\n          }\n        });\n      });\n    }\n  }\n  constructor(_elementRef, _liveAnnouncer, _contentObserver, _ngZone) {\n    this._elementRef = _elementRef;\n    this._liveAnnouncer = _liveAnnouncer;\n    this._contentObserver = _contentObserver;\n    this._ngZone = _ngZone;\n    this._politeness = 'polite';\n  }\n  ngOnDestroy() {\n    if (this._subscription) {\n      this._subscription.unsubscribe();\n    }\n  }\n  static {\n    this.ɵfac = function CdkAriaLive_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkAriaLive)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(LiveAnnouncer), i0.ɵɵdirectiveInject(i1$1.ContentObserver), i0.ɵɵdirectiveInject(i0.NgZone));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkAriaLive,\n      selectors: [[\"\", \"cdkAriaLive\", \"\"]],\n      inputs: {\n        politeness: [0, \"cdkAriaLive\", \"politeness\"],\n        duration: [0, \"cdkAriaLiveDuration\", \"duration\"]\n      },\n      exportAs: [\"cdkAriaLive\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkAriaLive, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkAriaLive]',\n      exportAs: 'cdkAriaLive',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: LiveAnnouncer\n  }, {\n    type: i1$1.ContentObserver\n  }, {\n    type: i0.NgZone\n  }], {\n    politeness: [{\n      type: Input,\n      args: ['cdkAriaLive']\n    }],\n    duration: [{\n      type: Input,\n      args: ['cdkAriaLiveDuration']\n    }]\n  });\n})();\n\n/** Detection mode used for attributing the origin of a focus event. */\nvar FocusMonitorDetectionMode;\n(function (FocusMonitorDetectionMode) {\n  /**\n   * Any mousedown, keydown, or touchstart event that happened in the previous\n   * tick or the current tick will be used to assign a focus event's origin (to\n   * either mouse, keyboard, or touch). This is the default option.\n   */\n  FocusMonitorDetectionMode[FocusMonitorDetectionMode[\"IMMEDIATE\"] = 0] = \"IMMEDIATE\";\n  /**\n   * A focus event's origin is always attributed to the last corresponding\n   * mousedown, keydown, or touchstart event, no matter how long ago it occurred.\n   */\n  FocusMonitorDetectionMode[FocusMonitorDetectionMode[\"EVENTUAL\"] = 1] = \"EVENTUAL\";\n})(FocusMonitorDetectionMode || (FocusMonitorDetectionMode = {}));\n/** InjectionToken for FocusMonitorOptions. */\nconst FOCUS_MONITOR_DEFAULT_OPTIONS = new InjectionToken('cdk-focus-monitor-default-options');\n/**\n * Event listener options that enable capturing and also\n * mark the listener as passive if the browser supports it.\n */\nconst captureEventListenerOptions = normalizePassiveListenerOptions({\n  passive: true,\n  capture: true\n});\n/** Monitors mouse and keyboard events to determine the cause of focus events. */\nclass FocusMonitor {\n  constructor(_ngZone, _platform, _inputModalityDetector, /** @breaking-change 11.0.0 make document required */\n  document, options) {\n    this._ngZone = _ngZone;\n    this._platform = _platform;\n    this._inputModalityDetector = _inputModalityDetector;\n    /** The focus origin that the next focus event is a result of. */\n    this._origin = null;\n    /** Whether the window has just been focused. */\n    this._windowFocused = false;\n    /**\n     * Whether the origin was determined via a touch interaction. Necessary as properly attributing\n     * focus events to touch interactions requires special logic.\n     */\n    this._originFromTouchInteraction = false;\n    /** Map of elements being monitored to their info. */\n    this._elementInfo = new Map();\n    /** The number of elements currently being monitored. */\n    this._monitoredElementCount = 0;\n    /**\n     * Keeps track of the root nodes to which we've currently bound a focus/blur handler,\n     * as well as the number of monitored elements that they contain. We have to treat focus/blur\n     * handlers differently from the rest of the events, because the browser won't emit events\n     * to the document when focus moves inside of a shadow root.\n     */\n    this._rootNodeFocusListenerCount = new Map();\n    /**\n     * Event listener for `focus` events on the window.\n     * Needs to be an arrow function in order to preserve the context when it gets bound.\n     */\n    this._windowFocusListener = () => {\n      // Make a note of when the window regains focus, so we can\n      // restore the origin info for the focused element.\n      this._windowFocused = true;\n      this._windowFocusTimeoutId = window.setTimeout(() => this._windowFocused = false);\n    };\n    /** Subject for stopping our InputModalityDetector subscription. */\n    this._stopInputModalityDetector = new Subject();\n    /**\n     * Event listener for `focus` and 'blur' events on the document.\n     * Needs to be an arrow function in order to preserve the context when it gets bound.\n     */\n    this._rootNodeFocusAndBlurListener = event => {\n      const target = _getEventTarget(event);\n      // We need to walk up the ancestor chain in order to support `checkChildren`.\n      for (let element = target; element; element = element.parentElement) {\n        if (event.type === 'focus') {\n          this._onFocus(event, element);\n        } else {\n          this._onBlur(event, element);\n        }\n      }\n    };\n    this._document = document;\n    this._detectionMode = options?.detectionMode || FocusMonitorDetectionMode.IMMEDIATE;\n  }\n  monitor(element, checkChildren = false) {\n    const nativeElement = coerceElement(element);\n    // Do nothing if we're not on the browser platform or the passed in node isn't an element.\n    if (!this._platform.isBrowser || nativeElement.nodeType !== 1) {\n      // Note: we don't want the observable to emit at all so we don't pass any parameters.\n      return of();\n    }\n    // If the element is inside the shadow DOM, we need to bind our focus/blur listeners to\n    // the shadow root, rather than the `document`, because the browser won't emit focus events\n    // to the `document`, if focus is moving within the same shadow root.\n    const rootNode = _getShadowRoot(nativeElement) || this._getDocument();\n    const cachedInfo = this._elementInfo.get(nativeElement);\n    // Check if we're already monitoring this element.\n    if (cachedInfo) {\n      if (checkChildren) {\n        // TODO(COMP-318): this can be problematic, because it'll turn all non-checkChildren\n        // observers into ones that behave as if `checkChildren` was turned on. We need a more\n        // robust solution.\n        cachedInfo.checkChildren = true;\n      }\n      return cachedInfo.subject;\n    }\n    // Create monitored element info.\n    const info = {\n      checkChildren: checkChildren,\n      subject: new Subject(),\n      rootNode\n    };\n    this._elementInfo.set(nativeElement, info);\n    this._registerGlobalListeners(info);\n    return info.subject;\n  }\n  stopMonitoring(element) {\n    const nativeElement = coerceElement(element);\n    const elementInfo = this._elementInfo.get(nativeElement);\n    if (elementInfo) {\n      elementInfo.subject.complete();\n      this._setClasses(nativeElement);\n      this._elementInfo.delete(nativeElement);\n      this._removeGlobalListeners(elementInfo);\n    }\n  }\n  focusVia(element, origin, options) {\n    const nativeElement = coerceElement(element);\n    const focusedElement = this._getDocument().activeElement;\n    // If the element is focused already, calling `focus` again won't trigger the event listener\n    // which means that the focus classes won't be updated. If that's the case, update the classes\n    // directly without waiting for an event.\n    if (nativeElement === focusedElement) {\n      this._getClosestElementsInfo(nativeElement).forEach(([currentElement, info]) => this._originChanged(currentElement, origin, info));\n    } else {\n      this._setOrigin(origin);\n      // `focus` isn't available on the server\n      if (typeof nativeElement.focus === 'function') {\n        nativeElement.focus(options);\n      }\n    }\n  }\n  ngOnDestroy() {\n    this._elementInfo.forEach((_info, element) => this.stopMonitoring(element));\n  }\n  /** Access injected document if available or fallback to global document reference */\n  _getDocument() {\n    return this._document || document;\n  }\n  /** Use defaultView of injected document if available or fallback to global window reference */\n  _getWindow() {\n    const doc = this._getDocument();\n    return doc.defaultView || window;\n  }\n  _getFocusOrigin(focusEventTarget) {\n    if (this._origin) {\n      // If the origin was realized via a touch interaction, we need to perform additional checks\n      // to determine whether the focus origin should be attributed to touch or program.\n      if (this._originFromTouchInteraction) {\n        return this._shouldBeAttributedToTouch(focusEventTarget) ? 'touch' : 'program';\n      } else {\n        return this._origin;\n      }\n    }\n    // If the window has just regained focus, we can restore the most recent origin from before the\n    // window blurred. Otherwise, we've reached the point where we can't identify the source of the\n    // focus. This typically means one of two things happened:\n    //\n    // 1) The element was programmatically focused, or\n    // 2) The element was focused via screen reader navigation (which generally doesn't fire\n    //    events).\n    //\n    // Because we can't distinguish between these two cases, we default to setting `program`.\n    if (this._windowFocused && this._lastFocusOrigin) {\n      return this._lastFocusOrigin;\n    }\n    // If the interaction is coming from an input label, we consider it a mouse interactions.\n    // This is a special case where focus moves on `click`, rather than `mousedown` which breaks\n    // our detection, because all our assumptions are for `mousedown`. We need to handle this\n    // special case, because it's very common for checkboxes and radio buttons.\n    if (focusEventTarget && this._isLastInteractionFromInputLabel(focusEventTarget)) {\n      return 'mouse';\n    }\n    return 'program';\n  }\n  /**\n   * Returns whether the focus event should be attributed to touch. Recall that in IMMEDIATE mode, a\n   * touch origin isn't immediately reset at the next tick (see _setOrigin). This means that when we\n   * handle a focus event following a touch interaction, we need to determine whether (1) the focus\n   * event was directly caused by the touch interaction or (2) the focus event was caused by a\n   * subsequent programmatic focus call triggered by the touch interaction.\n   * @param focusEventTarget The target of the focus event under examination.\n   */\n  _shouldBeAttributedToTouch(focusEventTarget) {\n    // Please note that this check is not perfect. Consider the following edge case:\n    //\n    // <div #parent tabindex=\"0\">\n    //   <div #child tabindex=\"0\" (click)=\"#parent.focus()\"></div>\n    // </div>\n    //\n    // Suppose there is a FocusMonitor in IMMEDIATE mode attached to #parent. When the user touches\n    // #child, #parent is programmatically focused. This code will attribute the focus to touch\n    // instead of program. This is a relatively minor edge-case that can be worked around by using\n    // focusVia(parent, 'program') to focus #parent.\n    return this._detectionMode === FocusMonitorDetectionMode.EVENTUAL || !!focusEventTarget?.contains(this._inputModalityDetector._mostRecentTarget);\n  }\n  /**\n   * Sets the focus classes on the element based on the given focus origin.\n   * @param element The element to update the classes on.\n   * @param origin The focus origin.\n   */\n  _setClasses(element, origin) {\n    element.classList.toggle('cdk-focused', !!origin);\n    element.classList.toggle('cdk-touch-focused', origin === 'touch');\n    element.classList.toggle('cdk-keyboard-focused', origin === 'keyboard');\n    element.classList.toggle('cdk-mouse-focused', origin === 'mouse');\n    element.classList.toggle('cdk-program-focused', origin === 'program');\n  }\n  /**\n   * Updates the focus origin. If we're using immediate detection mode, we schedule an async\n   * function to clear the origin at the end of a timeout. The duration of the timeout depends on\n   * the origin being set.\n   * @param origin The origin to set.\n   * @param isFromInteraction Whether we are setting the origin from an interaction event.\n   */\n  _setOrigin(origin, isFromInteraction = false) {\n    this._ngZone.runOutsideAngular(() => {\n      this._origin = origin;\n      this._originFromTouchInteraction = origin === 'touch' && isFromInteraction;\n      // If we're in IMMEDIATE mode, reset the origin at the next tick (or in `TOUCH_BUFFER_MS` ms\n      // for a touch event). We reset the origin at the next tick because Firefox focuses one tick\n      // after the interaction event. We wait `TOUCH_BUFFER_MS` ms before resetting the origin for\n      // a touch event because when a touch event is fired, the associated focus event isn't yet in\n      // the event queue. Before doing so, clear any pending timeouts.\n      if (this._detectionMode === FocusMonitorDetectionMode.IMMEDIATE) {\n        clearTimeout(this._originTimeoutId);\n        const ms = this._originFromTouchInteraction ? TOUCH_BUFFER_MS : 1;\n        this._originTimeoutId = setTimeout(() => this._origin = null, ms);\n      }\n    });\n  }\n  /**\n   * Handles focus events on a registered element.\n   * @param event The focus event.\n   * @param element The monitored element.\n   */\n  _onFocus(event, element) {\n    // NOTE(mmalerba): We currently set the classes based on the focus origin of the most recent\n    // focus event affecting the monitored element. If we want to use the origin of the first event\n    // instead we should check for the cdk-focused class here and return if the element already has\n    // it. (This only matters for elements that have includesChildren = true).\n    // If we are not counting child-element-focus as focused, make sure that the event target is the\n    // monitored element itself.\n    const elementInfo = this._elementInfo.get(element);\n    const focusEventTarget = _getEventTarget(event);\n    if (!elementInfo || !elementInfo.checkChildren && element !== focusEventTarget) {\n      return;\n    }\n    this._originChanged(element, this._getFocusOrigin(focusEventTarget), elementInfo);\n  }\n  /**\n   * Handles blur events on a registered element.\n   * @param event The blur event.\n   * @param element The monitored element.\n   */\n  _onBlur(event, element) {\n    // If we are counting child-element-focus as focused, make sure that we aren't just blurring in\n    // order to focus another child of the monitored element.\n    const elementInfo = this._elementInfo.get(element);\n    if (!elementInfo || elementInfo.checkChildren && event.relatedTarget instanceof Node && element.contains(event.relatedTarget)) {\n      return;\n    }\n    this._setClasses(element);\n    this._emitOrigin(elementInfo, null);\n  }\n  _emitOrigin(info, origin) {\n    if (info.subject.observers.length) {\n      this._ngZone.run(() => info.subject.next(origin));\n    }\n  }\n  _registerGlobalListeners(elementInfo) {\n    if (!this._platform.isBrowser) {\n      return;\n    }\n    const rootNode = elementInfo.rootNode;\n    const rootNodeFocusListeners = this._rootNodeFocusListenerCount.get(rootNode) || 0;\n    if (!rootNodeFocusListeners) {\n      this._ngZone.runOutsideAngular(() => {\n        rootNode.addEventListener('focus', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n        rootNode.addEventListener('blur', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n      });\n    }\n    this._rootNodeFocusListenerCount.set(rootNode, rootNodeFocusListeners + 1);\n    // Register global listeners when first element is monitored.\n    if (++this._monitoredElementCount === 1) {\n      // Note: we listen to events in the capture phase so we\n      // can detect them even if the user stops propagation.\n      this._ngZone.runOutsideAngular(() => {\n        const window = this._getWindow();\n        window.addEventListener('focus', this._windowFocusListener);\n      });\n      // The InputModalityDetector is also just a collection of global listeners.\n      this._inputModalityDetector.modalityDetected.pipe(takeUntil(this._stopInputModalityDetector)).subscribe(modality => {\n        this._setOrigin(modality, true /* isFromInteraction */);\n      });\n    }\n  }\n  _removeGlobalListeners(elementInfo) {\n    const rootNode = elementInfo.rootNode;\n    if (this._rootNodeFocusListenerCount.has(rootNode)) {\n      const rootNodeFocusListeners = this._rootNodeFocusListenerCount.get(rootNode);\n      if (rootNodeFocusListeners > 1) {\n        this._rootNodeFocusListenerCount.set(rootNode, rootNodeFocusListeners - 1);\n      } else {\n        rootNode.removeEventListener('focus', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n        rootNode.removeEventListener('blur', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n        this._rootNodeFocusListenerCount.delete(rootNode);\n      }\n    }\n    // Unregister global listeners when last element is unmonitored.\n    if (! --this._monitoredElementCount) {\n      const window = this._getWindow();\n      window.removeEventListener('focus', this._windowFocusListener);\n      // Equivalently, stop our InputModalityDetector subscription.\n      this._stopInputModalityDetector.next();\n      // Clear timeouts for all potentially pending timeouts to prevent the leaks.\n      clearTimeout(this._windowFocusTimeoutId);\n      clearTimeout(this._originTimeoutId);\n    }\n  }\n  /** Updates all the state on an element once its focus origin has changed. */\n  _originChanged(element, origin, elementInfo) {\n    this._setClasses(element, origin);\n    this._emitOrigin(elementInfo, origin);\n    this._lastFocusOrigin = origin;\n  }\n  /**\n   * Collects the `MonitoredElementInfo` of a particular element and\n   * all of its ancestors that have enabled `checkChildren`.\n   * @param element Element from which to start the search.\n   */\n  _getClosestElementsInfo(element) {\n    const results = [];\n    this._elementInfo.forEach((info, currentElement) => {\n      if (currentElement === element || info.checkChildren && currentElement.contains(element)) {\n        results.push([currentElement, info]);\n      }\n    });\n    return results;\n  }\n  /**\n   * Returns whether an interaction is likely to have come from the user clicking the `label` of\n   * an `input` or `textarea` in order to focus it.\n   * @param focusEventTarget Target currently receiving focus.\n   */\n  _isLastInteractionFromInputLabel(focusEventTarget) {\n    const {\n      _mostRecentTarget: mostRecentTarget,\n      mostRecentModality\n    } = this._inputModalityDetector;\n    // If the last interaction used the mouse on an element contained by one of the labels\n    // of an `input`/`textarea` that is currently focused, it is very likely that the\n    // user redirected focus using the label.\n    if (mostRecentModality !== 'mouse' || !mostRecentTarget || mostRecentTarget === focusEventTarget || focusEventTarget.nodeName !== 'INPUT' && focusEventTarget.nodeName !== 'TEXTAREA' || focusEventTarget.disabled) {\n      return false;\n    }\n    const labels = focusEventTarget.labels;\n    if (labels) {\n      for (let i = 0; i < labels.length; i++) {\n        if (labels[i].contains(mostRecentTarget)) {\n          return true;\n        }\n      }\n    }\n    return false;\n  }\n  static {\n    this.ɵfac = function FocusMonitor_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FocusMonitor)(i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(i1.Platform), i0.ɵɵinject(InputModalityDetector), i0.ɵɵinject(DOCUMENT, 8), i0.ɵɵinject(FOCUS_MONITOR_DEFAULT_OPTIONS, 8));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: FocusMonitor,\n      factory: FocusMonitor.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FocusMonitor, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i0.NgZone\n  }, {\n    type: i1.Platform\n  }, {\n    type: InputModalityDetector\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [FOCUS_MONITOR_DEFAULT_OPTIONS]\n    }]\n  }], null);\n})();\n/**\n * Directive that determines how a particular element was focused (via keyboard, mouse, touch, or\n * programmatically) and adds corresponding classes to the element.\n *\n * There are two variants of this directive:\n * 1) cdkMonitorElementFocus: does not consider an element to be focused if one of its children is\n *    focused.\n * 2) cdkMonitorSubtreeFocus: considers an element focused if it or any of its children are focused.\n */\nclass CdkMonitorFocus {\n  constructor(_elementRef, _focusMonitor) {\n    this._elementRef = _elementRef;\n    this._focusMonitor = _focusMonitor;\n    this._focusOrigin = null;\n    this.cdkFocusChange = new EventEmitter();\n  }\n  get focusOrigin() {\n    return this._focusOrigin;\n  }\n  ngAfterViewInit() {\n    const element = this._elementRef.nativeElement;\n    this._monitorSubscription = this._focusMonitor.monitor(element, element.nodeType === 1 && element.hasAttribute('cdkMonitorSubtreeFocus')).subscribe(origin => {\n      this._focusOrigin = origin;\n      this.cdkFocusChange.emit(origin);\n    });\n  }\n  ngOnDestroy() {\n    this._focusMonitor.stopMonitoring(this._elementRef);\n    if (this._monitorSubscription) {\n      this._monitorSubscription.unsubscribe();\n    }\n  }\n  static {\n    this.ɵfac = function CdkMonitorFocus_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkMonitorFocus)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(FocusMonitor));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkMonitorFocus,\n      selectors: [[\"\", \"cdkMonitorElementFocus\", \"\"], [\"\", \"cdkMonitorSubtreeFocus\", \"\"]],\n      outputs: {\n        cdkFocusChange: \"cdkFocusChange\"\n      },\n      exportAs: [\"cdkMonitorFocus\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkMonitorFocus, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkMonitorElementFocus], [cdkMonitorSubtreeFocus]',\n      exportAs: 'cdkMonitorFocus',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: FocusMonitor\n  }], {\n    cdkFocusChange: [{\n      type: Output\n    }]\n  });\n})();\n\n/** Set of possible high-contrast mode backgrounds. */\nvar HighContrastMode;\n(function (HighContrastMode) {\n  HighContrastMode[HighContrastMode[\"NONE\"] = 0] = \"NONE\";\n  HighContrastMode[HighContrastMode[\"BLACK_ON_WHITE\"] = 1] = \"BLACK_ON_WHITE\";\n  HighContrastMode[HighContrastMode[\"WHITE_ON_BLACK\"] = 2] = \"WHITE_ON_BLACK\";\n})(HighContrastMode || (HighContrastMode = {}));\n/** CSS class applied to the document body when in black-on-white high-contrast mode. */\nconst BLACK_ON_WHITE_CSS_CLASS = 'cdk-high-contrast-black-on-white';\n/** CSS class applied to the document body when in white-on-black high-contrast mode. */\nconst WHITE_ON_BLACK_CSS_CLASS = 'cdk-high-contrast-white-on-black';\n/** CSS class applied to the document body when in high-contrast mode. */\nconst HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS = 'cdk-high-contrast-active';\n/**\n * Service to determine whether the browser is currently in a high-contrast-mode environment.\n *\n * Microsoft Windows supports an accessibility feature called \"High Contrast Mode\". This mode\n * changes the appearance of all applications, including web applications, to dramatically increase\n * contrast.\n *\n * IE, Edge, and Firefox currently support this mode. Chrome does not support Windows High Contrast\n * Mode. This service does not detect high-contrast mode as added by the Chrome \"High Contrast\"\n * browser extension.\n */\nclass HighContrastModeDetector {\n  constructor(_platform, document) {\n    this._platform = _platform;\n    this._document = document;\n    this._breakpointSubscription = inject(BreakpointObserver).observe('(forced-colors: active)').subscribe(() => {\n      if (this._hasCheckedHighContrastMode) {\n        this._hasCheckedHighContrastMode = false;\n        this._applyBodyHighContrastModeCssClasses();\n      }\n    });\n  }\n  /** Gets the current high-contrast-mode for the page. */\n  getHighContrastMode() {\n    if (!this._platform.isBrowser) {\n      return HighContrastMode.NONE;\n    }\n    // Create a test element with an arbitrary background-color that is neither black nor\n    // white; high-contrast mode will coerce the color to either black or white. Also ensure that\n    // appending the test element to the DOM does not affect layout by absolutely positioning it\n    const testElement = this._document.createElement('div');\n    testElement.style.backgroundColor = 'rgb(1,2,3)';\n    testElement.style.position = 'absolute';\n    this._document.body.appendChild(testElement);\n    // Get the computed style for the background color, collapsing spaces to normalize between\n    // browsers. Once we get this color, we no longer need the test element. Access the `window`\n    // via the document so we can fake it in tests. Note that we have extra null checks, because\n    // this logic will likely run during app bootstrap and throwing can break the entire app.\n    const documentWindow = this._document.defaultView || window;\n    const computedStyle = documentWindow && documentWindow.getComputedStyle ? documentWindow.getComputedStyle(testElement) : null;\n    const computedColor = (computedStyle && computedStyle.backgroundColor || '').replace(/ /g, '');\n    testElement.remove();\n    switch (computedColor) {\n      // Pre Windows 11 dark theme.\n      case 'rgb(0,0,0)':\n      // Windows 11 dark themes.\n      case 'rgb(45,50,54)':\n      case 'rgb(32,32,32)':\n        return HighContrastMode.WHITE_ON_BLACK;\n      // Pre Windows 11 light theme.\n      case 'rgb(255,255,255)':\n      // Windows 11 light theme.\n      case 'rgb(255,250,239)':\n        return HighContrastMode.BLACK_ON_WHITE;\n    }\n    return HighContrastMode.NONE;\n  }\n  ngOnDestroy() {\n    this._breakpointSubscription.unsubscribe();\n  }\n  /** Applies CSS classes indicating high-contrast mode to document body (browser-only). */\n  _applyBodyHighContrastModeCssClasses() {\n    if (!this._hasCheckedHighContrastMode && this._platform.isBrowser && this._document.body) {\n      const bodyClasses = this._document.body.classList;\n      bodyClasses.remove(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS, BLACK_ON_WHITE_CSS_CLASS, WHITE_ON_BLACK_CSS_CLASS);\n      this._hasCheckedHighContrastMode = true;\n      const mode = this.getHighContrastMode();\n      if (mode === HighContrastMode.BLACK_ON_WHITE) {\n        bodyClasses.add(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS, BLACK_ON_WHITE_CSS_CLASS);\n      } else if (mode === HighContrastMode.WHITE_ON_BLACK) {\n        bodyClasses.add(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS, WHITE_ON_BLACK_CSS_CLASS);\n      }\n    }\n  }\n  static {\n    this.ɵfac = function HighContrastModeDetector_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || HighContrastModeDetector)(i0.ɵɵinject(i1.Platform), i0.ɵɵinject(DOCUMENT));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: HighContrastModeDetector,\n      factory: HighContrastModeDetector.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HighContrastModeDetector, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i1.Platform\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\nclass A11yModule {\n  constructor(highContrastModeDetector) {\n    highContrastModeDetector._applyBodyHighContrastModeCssClasses();\n  }\n  static {\n    this.ɵfac = function A11yModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || A11yModule)(i0.ɵɵinject(HighContrastModeDetector));\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: A11yModule,\n      imports: [ObserversModule, CdkAriaLive, CdkTrapFocus, CdkMonitorFocus],\n      exports: [CdkAriaLive, CdkTrapFocus, CdkMonitorFocus]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [ObserversModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(A11yModule, [{\n    type: NgModule,\n    args: [{\n      imports: [ObserversModule, CdkAriaLive, CdkTrapFocus, CdkMonitorFocus],\n      exports: [CdkAriaLive, CdkTrapFocus, CdkMonitorFocus]\n    }]\n  }], () => [{\n    type: HighContrastModeDetector\n  }], null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { A11yModule, ActiveDescendantKeyManager, AriaDescriber, CDK_DESCRIBEDBY_HOST_ATTRIBUTE, CDK_DESCRIBEDBY_ID_PREFIX, CdkAriaLive, CdkMonitorFocus, CdkTrapFocus, ConfigurableFocusTrap, ConfigurableFocusTrapFactory, EventListenerFocusTrapInertStrategy, FOCUS_MONITOR_DEFAULT_OPTIONS, FOCUS_TRAP_INERT_STRATEGY, FocusKeyManager, FocusMonitor, FocusMonitorDetectionMode, FocusTrap, FocusTrapFactory, HighContrastMode, HighContrastModeDetector, INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS, INPUT_MODALITY_DETECTOR_OPTIONS, InputModalityDetector, InteractivityChecker, IsFocusableConfig, LIVE_ANNOUNCER_DEFAULT_OPTIONS, LIVE_ANNOUNCER_ELEMENT_TOKEN, LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY, ListKeyManager, LiveAnnouncer, MESSAGES_CONTAINER_ID, NOOP_TREE_KEY_MANAGER_FACTORY, NOOP_TREE_KEY_MANAGER_FACTORY_PROVIDER, NoopTreeKeyManager, TREE_KEY_MANAGER, TREE_KEY_MANAGER_FACTORY, TREE_KEY_MANAGER_FACTORY_PROVIDER, TreeKeyManager, addAriaReferencedId, getAriaReferenceIds, isFakeMousedownFromScreenReader, isFakeTouchstartFromScreenReader, removeAriaReferencedId };\n", "import { isObservable, of } from 'rxjs';\n\n/**\n * Given either an Observable or non-Observable value, returns either the original\n * Observable, or wraps it in an Observable that emits the non-Observable value.\n */\nfunction coerceObservable(data) {\n  if (!isObservable(data)) {\n    return of(data);\n  }\n  return data;\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { coerceObservable };\n", "import { coerceElement, coerceNumberProperty } from '@angular/cdk/coercion';\nimport * as i0 from '@angular/core';\nimport { Injectable, inject, NgZone, EventEmitter, booleanAttribute, Directive, Output, Input, NgModule } from '@angular/core';\nimport { Observable, Subject } from 'rxjs';\nimport { map, filter, debounceTime } from 'rxjs/operators';\n\n// Angular may add, remove, or edit comment nodes during change detection. We don't care about\n// these changes because they don't affect the user-preceived content, and worse it can cause\n// infinite change detection cycles where the change detection updates a comment, triggering the\n// MutationObserver, triggering another change detection and kicking the cycle off again.\nfunction shouldIgnoreRecord(record) {\n  // Ignore changes to comment text.\n  if (record.type === 'characterData' && record.target instanceof Comment) {\n    return true;\n  }\n  // Ignore addition / removal of comments.\n  if (record.type === 'childList') {\n    for (let i = 0; i < record.addedNodes.length; i++) {\n      if (!(record.addedNodes[i] instanceof Comment)) {\n        return false;\n      }\n    }\n    for (let i = 0; i < record.removedNodes.length; i++) {\n      if (!(record.removedNodes[i] instanceof Comment)) {\n        return false;\n      }\n    }\n    return true;\n  }\n  // Observe everything else.\n  return false;\n}\n/**\n * Factory that creates a new MutationObserver and allows us to stub it out in unit tests.\n * @docs-private\n */\nclass MutationObserverFactory {\n  create(callback) {\n    return typeof MutationObserver === 'undefined' ? null : new MutationObserver(callback);\n  }\n  static {\n    this.ɵfac = function MutationObserverFactory_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MutationObserverFactory)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: MutationObserverFactory,\n      factory: MutationObserverFactory.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MutationObserverFactory, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n/** An injectable service that allows watching elements for changes to their content. */\nclass ContentObserver {\n  constructor(_mutationObserverFactory) {\n    this._mutationObserverFactory = _mutationObserverFactory;\n    /** Keeps track of the existing MutationObservers so they can be reused. */\n    this._observedElements = new Map();\n    this._ngZone = inject(NgZone);\n  }\n  ngOnDestroy() {\n    this._observedElements.forEach((_, element) => this._cleanupObserver(element));\n  }\n  observe(elementOrRef) {\n    const element = coerceElement(elementOrRef);\n    return new Observable(observer => {\n      const stream = this._observeElement(element);\n      const subscription = stream.pipe(map(records => records.filter(record => !shouldIgnoreRecord(record))), filter(records => !!records.length)).subscribe(records => {\n        this._ngZone.run(() => {\n          observer.next(records);\n        });\n      });\n      return () => {\n        subscription.unsubscribe();\n        this._unobserveElement(element);\n      };\n    });\n  }\n  /**\n   * Observes the given element by using the existing MutationObserver if available, or creating a\n   * new one if not.\n   */\n  _observeElement(element) {\n    return this._ngZone.runOutsideAngular(() => {\n      if (!this._observedElements.has(element)) {\n        const stream = new Subject();\n        const observer = this._mutationObserverFactory.create(mutations => stream.next(mutations));\n        if (observer) {\n          observer.observe(element, {\n            characterData: true,\n            childList: true,\n            subtree: true\n          });\n        }\n        this._observedElements.set(element, {\n          observer,\n          stream,\n          count: 1\n        });\n      } else {\n        this._observedElements.get(element).count++;\n      }\n      return this._observedElements.get(element).stream;\n    });\n  }\n  /**\n   * Un-observes the given element and cleans up the underlying MutationObserver if nobody else is\n   * observing this element.\n   */\n  _unobserveElement(element) {\n    if (this._observedElements.has(element)) {\n      this._observedElements.get(element).count--;\n      if (!this._observedElements.get(element).count) {\n        this._cleanupObserver(element);\n      }\n    }\n  }\n  /** Clean up the underlying MutationObserver for the specified element. */\n  _cleanupObserver(element) {\n    if (this._observedElements.has(element)) {\n      const {\n        observer,\n        stream\n      } = this._observedElements.get(element);\n      if (observer) {\n        observer.disconnect();\n      }\n      stream.complete();\n      this._observedElements.delete(element);\n    }\n  }\n  static {\n    this.ɵfac = function ContentObserver_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ContentObserver)(i0.ɵɵinject(MutationObserverFactory));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ContentObserver,\n      factory: ContentObserver.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ContentObserver, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: MutationObserverFactory\n  }], null);\n})();\n/**\n * Directive that triggers a callback whenever the content of\n * its associated element has changed.\n */\nclass CdkObserveContent {\n  /**\n   * Whether observing content is disabled. This option can be used\n   * to disconnect the underlying MutationObserver until it is needed.\n   */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value) {\n    this._disabled = value;\n    this._disabled ? this._unsubscribe() : this._subscribe();\n  }\n  /** Debounce interval for emitting the changes. */\n  get debounce() {\n    return this._debounce;\n  }\n  set debounce(value) {\n    this._debounce = coerceNumberProperty(value);\n    this._subscribe();\n  }\n  constructor(_contentObserver, _elementRef) {\n    this._contentObserver = _contentObserver;\n    this._elementRef = _elementRef;\n    /** Event emitted for each change in the element's content. */\n    this.event = new EventEmitter();\n    this._disabled = false;\n    this._currentSubscription = null;\n  }\n  ngAfterContentInit() {\n    if (!this._currentSubscription && !this.disabled) {\n      this._subscribe();\n    }\n  }\n  ngOnDestroy() {\n    this._unsubscribe();\n  }\n  _subscribe() {\n    this._unsubscribe();\n    const stream = this._contentObserver.observe(this._elementRef);\n    this._currentSubscription = (this.debounce ? stream.pipe(debounceTime(this.debounce)) : stream).subscribe(this.event);\n  }\n  _unsubscribe() {\n    this._currentSubscription?.unsubscribe();\n  }\n  static {\n    this.ɵfac = function CdkObserveContent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkObserveContent)(i0.ɵɵdirectiveInject(ContentObserver), i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkObserveContent,\n      selectors: [[\"\", \"cdkObserveContent\", \"\"]],\n      inputs: {\n        disabled: [2, \"cdkObserveContentDisabled\", \"disabled\", booleanAttribute],\n        debounce: \"debounce\"\n      },\n      outputs: {\n        event: \"cdkObserveContent\"\n      },\n      exportAs: [\"cdkObserveContent\"],\n      standalone: true,\n      features: [i0.ɵɵInputTransformsFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkObserveContent, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkObserveContent]',\n      exportAs: 'cdkObserveContent',\n      standalone: true\n    }]\n  }], () => [{\n    type: ContentObserver\n  }, {\n    type: i0.ElementRef\n  }], {\n    event: [{\n      type: Output,\n      args: ['cdkObserveContent']\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        alias: 'cdkObserveContentDisabled',\n        transform: booleanAttribute\n      }]\n    }],\n    debounce: [{\n      type: Input\n    }]\n  });\n})();\nclass ObserversModule {\n  static {\n    this.ɵfac = function ObserversModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ObserversModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: ObserversModule,\n      imports: [CdkObserveContent],\n      exports: [CdkObserveContent]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [MutationObserverFactory]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ObserversModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CdkObserveContent],\n      exports: [CdkObserveContent],\n      providers: [MutationObserverFactory]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CdkObserveContent, ContentObserver, MutationObserverFactory, ObserversModule };\n", "import * as i0 from '@angular/core';\nimport { signal, Component, ViewEncapsulation, ChangeDetectionStrategy, inject, ApplicationRef, EnvironmentInjector, createComponent, Injectable, Inject, InjectionToken, booleanAttribute, Directive, Optional, SkipSelf, Input, EventEmitter, Injector, afterNextRender, numberAttribute, Self, Output, NgModule } from '@angular/core';\nimport { DOCUMENT } from '@angular/common';\nimport * as i1 from '@angular/cdk/scrolling';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\nimport { isFakeTouchstartFromScreenReader, isFakeMousedownFromScreenReader } from '@angular/cdk/a11y';\nimport { coerceElement, coerceNumberProperty, coerceArray } from '@angular/cdk/coercion';\nimport { _getEventTarget, normalizePassiveListenerOptions, _getShadowRoot } from '@angular/cdk/platform';\nimport { Subject, Subscription, interval, animationFrameScheduler, Observable, merge, BehaviorSubject } from 'rxjs';\nimport { takeUntil, map, take, tap, switchMap, startWith } from 'rxjs/operators';\nimport * as i1$1 from '@angular/cdk/bidi';\n\n/** Creates a deep clone of an element. */\nfunction deepCloneNode(node) {\n  const clone = node.cloneNode(true);\n  const descendantsWithId = clone.querySelectorAll('[id]');\n  const nodeName = node.nodeName.toLowerCase();\n  // Remove the `id` to avoid having multiple elements with the same id on the page.\n  clone.removeAttribute('id');\n  for (let i = 0; i < descendantsWithId.length; i++) {\n    descendantsWithId[i].removeAttribute('id');\n  }\n  if (nodeName === 'canvas') {\n    transferCanvasData(node, clone);\n  } else if (nodeName === 'input' || nodeName === 'select' || nodeName === 'textarea') {\n    transferInputData(node, clone);\n  }\n  transferData('canvas', node, clone, transferCanvasData);\n  transferData('input, textarea, select', node, clone, transferInputData);\n  return clone;\n}\n/** Matches elements between an element and its clone and allows for their data to be cloned. */\nfunction transferData(selector, node, clone, callback) {\n  const descendantElements = node.querySelectorAll(selector);\n  if (descendantElements.length) {\n    const cloneElements = clone.querySelectorAll(selector);\n    for (let i = 0; i < descendantElements.length; i++) {\n      callback(descendantElements[i], cloneElements[i]);\n    }\n  }\n}\n// Counter for unique cloned radio button names.\nlet cloneUniqueId = 0;\n/** Transfers the data of one input element to another. */\nfunction transferInputData(source, clone) {\n  // Browsers throw an error when assigning the value of a file input programmatically.\n  if (clone.type !== 'file') {\n    clone.value = source.value;\n  }\n  // Radio button `name` attributes must be unique for radio button groups\n  // otherwise original radio buttons can lose their checked state\n  // once the clone is inserted in the DOM.\n  if (clone.type === 'radio' && clone.name) {\n    clone.name = `mat-clone-${clone.name}-${cloneUniqueId++}`;\n  }\n}\n/** Transfers the data of one canvas element to another. */\nfunction transferCanvasData(source, clone) {\n  const context = clone.getContext('2d');\n  if (context) {\n    // In some cases `drawImage` can throw (e.g. if the canvas size is 0x0).\n    // We can't do much about it so just ignore the error.\n    try {\n      context.drawImage(source, 0, 0);\n    } catch {}\n  }\n}\n\n/** Gets a mutable version of an element's bounding `DOMRect`. */\nfunction getMutableClientRect(element) {\n  const rect = element.getBoundingClientRect();\n  // We need to clone the `clientRect` here, because all the values on it are readonly\n  // and we need to be able to update them. Also we can't use a spread here, because\n  // the values on a `DOMRect` aren't own properties. See:\n  // https://developer.mozilla.org/en-US/docs/Web/API/Element/getBoundingClientRect#Notes\n  return {\n    top: rect.top,\n    right: rect.right,\n    bottom: rect.bottom,\n    left: rect.left,\n    width: rect.width,\n    height: rect.height,\n    x: rect.x,\n    y: rect.y\n  };\n}\n/**\n * Checks whether some coordinates are within a `DOMRect`.\n * @param clientRect DOMRect that is being checked.\n * @param x Coordinates along the X axis.\n * @param y Coordinates along the Y axis.\n */\nfunction isInsideClientRect(clientRect, x, y) {\n  const {\n    top,\n    bottom,\n    left,\n    right\n  } = clientRect;\n  return y >= top && y <= bottom && x >= left && x <= right;\n}\n/**\n * Updates the top/left positions of a `DOMRect`, as well as their bottom/right counterparts.\n * @param domRect `DOMRect` that should be updated.\n * @param top Amount to add to the `top` position.\n * @param left Amount to add to the `left` position.\n */\nfunction adjustDomRect(domRect, top, left) {\n  domRect.top += top;\n  domRect.bottom = domRect.top + domRect.height;\n  domRect.left += left;\n  domRect.right = domRect.left + domRect.width;\n}\n/**\n * Checks whether the pointer coordinates are close to a DOMRect.\n * @param rect DOMRect to check against.\n * @param threshold Threshold around the DOMRect.\n * @param pointerX Coordinates along the X axis.\n * @param pointerY Coordinates along the Y axis.\n */\nfunction isPointerNearDomRect(rect, threshold, pointerX, pointerY) {\n  const {\n    top,\n    right,\n    bottom,\n    left,\n    width,\n    height\n  } = rect;\n  const xThreshold = width * threshold;\n  const yThreshold = height * threshold;\n  return pointerY > top - yThreshold && pointerY < bottom + yThreshold && pointerX > left - xThreshold && pointerX < right + xThreshold;\n}\n\n/** Keeps track of the scroll position and dimensions of the parents of an element. */\nclass ParentPositionTracker {\n  constructor(_document) {\n    this._document = _document;\n    /** Cached positions of the scrollable parent elements. */\n    this.positions = new Map();\n  }\n  /** Clears the cached positions. */\n  clear() {\n    this.positions.clear();\n  }\n  /** Caches the positions. Should be called at the beginning of a drag sequence. */\n  cache(elements) {\n    this.clear();\n    this.positions.set(this._document, {\n      scrollPosition: this.getViewportScrollPosition()\n    });\n    elements.forEach(element => {\n      this.positions.set(element, {\n        scrollPosition: {\n          top: element.scrollTop,\n          left: element.scrollLeft\n        },\n        clientRect: getMutableClientRect(element)\n      });\n    });\n  }\n  /** Handles scrolling while a drag is taking place. */\n  handleScroll(event) {\n    const target = _getEventTarget(event);\n    const cachedPosition = this.positions.get(target);\n    if (!cachedPosition) {\n      return null;\n    }\n    const scrollPosition = cachedPosition.scrollPosition;\n    let newTop;\n    let newLeft;\n    if (target === this._document) {\n      const viewportScrollPosition = this.getViewportScrollPosition();\n      newTop = viewportScrollPosition.top;\n      newLeft = viewportScrollPosition.left;\n    } else {\n      newTop = target.scrollTop;\n      newLeft = target.scrollLeft;\n    }\n    const topDifference = scrollPosition.top - newTop;\n    const leftDifference = scrollPosition.left - newLeft;\n    // Go through and update the cached positions of the scroll\n    // parents that are inside the element that was scrolled.\n    this.positions.forEach((position, node) => {\n      if (position.clientRect && target !== node && target.contains(node)) {\n        adjustDomRect(position.clientRect, topDifference, leftDifference);\n      }\n    });\n    scrollPosition.top = newTop;\n    scrollPosition.left = newLeft;\n    return {\n      top: topDifference,\n      left: leftDifference\n    };\n  }\n  /**\n   * Gets the scroll position of the viewport. Note that we use the scrollX and scrollY directly,\n   * instead of going through the `ViewportRuler`, because the first value the ruler looks at is\n   * the top/left offset of the `document.documentElement` which works for most cases, but breaks\n   * if the element is offset by something like the `BlockScrollStrategy`.\n   */\n  getViewportScrollPosition() {\n    return {\n      top: window.scrollY,\n      left: window.scrollX\n    };\n  }\n}\n\n/**\n * Gets the root HTML element of an embedded view.\n * If the root is not an HTML element it gets wrapped in one.\n */\nfunction getRootNode(viewRef, _document) {\n  const rootNodes = viewRef.rootNodes;\n  if (rootNodes.length === 1 && rootNodes[0].nodeType === _document.ELEMENT_NODE) {\n    return rootNodes[0];\n  }\n  const wrapper = _document.createElement('div');\n  rootNodes.forEach(node => wrapper.appendChild(node));\n  return wrapper;\n}\n\n/**\n * Shallow-extends a stylesheet object with another stylesheet-like object.\n * Note that the keys in `source` have to be dash-cased.\n * @docs-private\n */\nfunction extendStyles(dest, source, importantProperties) {\n  for (let key in source) {\n    if (source.hasOwnProperty(key)) {\n      const value = source[key];\n      if (value) {\n        dest.setProperty(key, value, importantProperties?.has(key) ? 'important' : '');\n      } else {\n        dest.removeProperty(key);\n      }\n    }\n  }\n  return dest;\n}\n/**\n * Toggles whether the native drag interactions should be enabled for an element.\n * @param element Element on which to toggle the drag interactions.\n * @param enable Whether the drag interactions should be enabled.\n * @docs-private\n */\nfunction toggleNativeDragInteractions(element, enable) {\n  const userSelect = enable ? '' : 'none';\n  extendStyles(element.style, {\n    'touch-action': enable ? '' : 'none',\n    '-webkit-user-drag': enable ? '' : 'none',\n    '-webkit-tap-highlight-color': enable ? '' : 'transparent',\n    'user-select': userSelect,\n    '-ms-user-select': userSelect,\n    '-webkit-user-select': userSelect,\n    '-moz-user-select': userSelect\n  });\n}\n/**\n * Toggles whether an element is visible while preserving its dimensions.\n * @param element Element whose visibility to toggle\n * @param enable Whether the element should be visible.\n * @param importantProperties Properties to be set as `!important`.\n * @docs-private\n */\nfunction toggleVisibility(element, enable, importantProperties) {\n  extendStyles(element.style, {\n    position: enable ? '' : 'fixed',\n    top: enable ? '' : '0',\n    opacity: enable ? '' : '0',\n    left: enable ? '' : '-999em'\n  }, importantProperties);\n}\n/**\n * Combines a transform string with an optional other transform\n * that exited before the base transform was applied.\n */\nfunction combineTransforms(transform, initialTransform) {\n  return initialTransform && initialTransform != 'none' ? transform + ' ' + initialTransform : transform;\n}\n/**\n * Matches the target element's size to the source's size.\n * @param target Element that needs to be resized.\n * @param sourceRect Dimensions of the source element.\n */\nfunction matchElementSize(target, sourceRect) {\n  target.style.width = `${sourceRect.width}px`;\n  target.style.height = `${sourceRect.height}px`;\n  target.style.transform = getTransform(sourceRect.left, sourceRect.top);\n}\n/**\n * Gets a 3d `transform` that can be applied to an element.\n * @param x Desired position of the element along the X axis.\n * @param y Desired position of the element along the Y axis.\n */\nfunction getTransform(x, y) {\n  // Round the transforms since some browsers will\n  // blur the elements for sub-pixel transforms.\n  return `translate3d(${Math.round(x)}px, ${Math.round(y)}px, 0)`;\n}\n\n/** Parses a CSS time value to milliseconds. */\nfunction parseCssTimeUnitsToMs(value) {\n  // Some browsers will return it in seconds, whereas others will return milliseconds.\n  const multiplier = value.toLowerCase().indexOf('ms') > -1 ? 1 : 1000;\n  return parseFloat(value) * multiplier;\n}\n/** Gets the transform transition duration, including the delay, of an element in milliseconds. */\nfunction getTransformTransitionDurationInMs(element) {\n  const computedStyle = getComputedStyle(element);\n  const transitionedProperties = parseCssPropertyValue(computedStyle, 'transition-property');\n  const property = transitionedProperties.find(prop => prop === 'transform' || prop === 'all');\n  // If there's no transition for `all` or `transform`, we shouldn't do anything.\n  if (!property) {\n    return 0;\n  }\n  // Get the index of the property that we're interested in and match\n  // it up to the same index in `transition-delay` and `transition-duration`.\n  const propertyIndex = transitionedProperties.indexOf(property);\n  const rawDurations = parseCssPropertyValue(computedStyle, 'transition-duration');\n  const rawDelays = parseCssPropertyValue(computedStyle, 'transition-delay');\n  return parseCssTimeUnitsToMs(rawDurations[propertyIndex]) + parseCssTimeUnitsToMs(rawDelays[propertyIndex]);\n}\n/** Parses out multiple values from a computed style into an array. */\nfunction parseCssPropertyValue(computedStyle, name) {\n  const value = computedStyle.getPropertyValue(name);\n  return value.split(',').map(part => part.trim());\n}\n\n/** Inline styles to be set as `!important` while dragging. */\nconst importantProperties = new Set([\n// Needs to be important, because some `mat-table` sets `position: sticky !important`. See #22781.\n'position']);\nclass PreviewRef {\n  get element() {\n    return this._preview;\n  }\n  constructor(_document, _rootElement, _direction, _initialDomRect, _previewTemplate, _previewClass, _pickupPositionOnPage, _initialTransform, _zIndex) {\n    this._document = _document;\n    this._rootElement = _rootElement;\n    this._direction = _direction;\n    this._initialDomRect = _initialDomRect;\n    this._previewTemplate = _previewTemplate;\n    this._previewClass = _previewClass;\n    this._pickupPositionOnPage = _pickupPositionOnPage;\n    this._initialTransform = _initialTransform;\n    this._zIndex = _zIndex;\n  }\n  attach(parent) {\n    this._preview = this._createPreview();\n    parent.appendChild(this._preview);\n    // The null check is necessary for browsers that don't support the popover API.\n    // Note that we use a string access for compatibility with Closure.\n    if (supportsPopover(this._preview)) {\n      this._preview['showPopover']();\n    }\n  }\n  destroy() {\n    this._preview.remove();\n    this._previewEmbeddedView?.destroy();\n    this._preview = this._previewEmbeddedView = null;\n  }\n  setTransform(value) {\n    this._preview.style.transform = value;\n  }\n  getBoundingClientRect() {\n    return this._preview.getBoundingClientRect();\n  }\n  addClass(className) {\n    this._preview.classList.add(className);\n  }\n  getTransitionDuration() {\n    return getTransformTransitionDurationInMs(this._preview);\n  }\n  addEventListener(name, handler) {\n    this._preview.addEventListener(name, handler);\n  }\n  removeEventListener(name, handler) {\n    this._preview.removeEventListener(name, handler);\n  }\n  _createPreview() {\n    const previewConfig = this._previewTemplate;\n    const previewClass = this._previewClass;\n    const previewTemplate = previewConfig ? previewConfig.template : null;\n    let preview;\n    if (previewTemplate && previewConfig) {\n      // Measure the element before we've inserted the preview\n      // since the insertion could throw off the measurement.\n      const rootRect = previewConfig.matchSize ? this._initialDomRect : null;\n      const viewRef = previewConfig.viewContainer.createEmbeddedView(previewTemplate, previewConfig.context);\n      viewRef.detectChanges();\n      preview = getRootNode(viewRef, this._document);\n      this._previewEmbeddedView = viewRef;\n      if (previewConfig.matchSize) {\n        matchElementSize(preview, rootRect);\n      } else {\n        preview.style.transform = getTransform(this._pickupPositionOnPage.x, this._pickupPositionOnPage.y);\n      }\n    } else {\n      preview = deepCloneNode(this._rootElement);\n      matchElementSize(preview, this._initialDomRect);\n      if (this._initialTransform) {\n        preview.style.transform = this._initialTransform;\n      }\n    }\n    extendStyles(preview.style, {\n      // It's important that we disable the pointer events on the preview, because\n      // it can throw off the `document.elementFromPoint` calls in the `CdkDropList`.\n      'pointer-events': 'none',\n      // If the preview has a margin, it can throw off our positioning so we reset it. The reset\n      // value for `margin-right` needs to be `auto` when opened as a popover, because our\n      // positioning is always top/left based, but native popover seems to position itself\n      // to the top/right if `<html>` or `<body>` have `dir=\"rtl\"` (see #29604). Setting it\n      // to `auto` pushed it to the top/left corner in RTL and is a noop in LTR.\n      'margin': supportsPopover(preview) ? '0 auto 0 0' : '0',\n      'position': 'fixed',\n      'top': '0',\n      'left': '0',\n      'z-index': this._zIndex + ''\n    }, importantProperties);\n    toggleNativeDragInteractions(preview, false);\n    preview.classList.add('cdk-drag-preview');\n    preview.setAttribute('popover', 'manual');\n    preview.setAttribute('dir', this._direction);\n    if (previewClass) {\n      if (Array.isArray(previewClass)) {\n        previewClass.forEach(className => preview.classList.add(className));\n      } else {\n        preview.classList.add(previewClass);\n      }\n    }\n    return preview;\n  }\n}\n/** Checks whether a specific element supports the popover API. */\nfunction supportsPopover(element) {\n  return 'showPopover' in element;\n}\n\n/** Options that can be used to bind a passive event listener. */\nconst passiveEventListenerOptions = normalizePassiveListenerOptions({\n  passive: true\n});\n/** Options that can be used to bind an active event listener. */\nconst activeEventListenerOptions = normalizePassiveListenerOptions({\n  passive: false\n});\n/** Event options that can be used to bind an active, capturing event. */\nconst activeCapturingEventOptions$1 = normalizePassiveListenerOptions({\n  passive: false,\n  capture: true\n});\n/**\n * Time in milliseconds for which to ignore mouse events, after\n * receiving a touch event. Used to avoid doing double work for\n * touch devices where the browser fires fake mouse events, in\n * addition to touch events.\n */\nconst MOUSE_EVENT_IGNORE_TIME = 800;\n/** Inline styles to be set as `!important` while dragging. */\nconst dragImportantProperties = new Set([\n// Needs to be important, because some `mat-table` sets `position: sticky !important`. See #22781.\n'position']);\n/**\n * Reference to a draggable item. Used to manipulate or dispose of the item.\n */\nclass DragRef {\n  /** Whether starting to drag this element is disabled. */\n  get disabled() {\n    return this._disabled || !!(this._dropContainer && this._dropContainer.disabled);\n  }\n  set disabled(value) {\n    if (value !== this._disabled) {\n      this._disabled = value;\n      this._toggleNativeDragInteractions();\n      this._handles.forEach(handle => toggleNativeDragInteractions(handle, value));\n    }\n  }\n  constructor(element, _config, _document, _ngZone, _viewportRuler, _dragDropRegistry) {\n    this._config = _config;\n    this._document = _document;\n    this._ngZone = _ngZone;\n    this._viewportRuler = _viewportRuler;\n    this._dragDropRegistry = _dragDropRegistry;\n    /**\n     * CSS `transform` applied to the element when it isn't being dragged. We need a\n     * passive transform in order for the dragged element to retain its new position\n     * after the user has stopped dragging and because we need to know the relative\n     * position in case they start dragging again. This corresponds to `element.style.transform`.\n     */\n    this._passiveTransform = {\n      x: 0,\n      y: 0\n    };\n    /** CSS `transform` that is applied to the element while it's being dragged. */\n    this._activeTransform = {\n      x: 0,\n      y: 0\n    };\n    /**\n     * Whether the dragging sequence has been started. Doesn't\n     * necessarily mean that the element has been moved.\n     */\n    this._hasStartedDragging = signal(false);\n    /** Emits when the item is being moved. */\n    this._moveEvents = new Subject();\n    /** Subscription to pointer movement events. */\n    this._pointerMoveSubscription = Subscription.EMPTY;\n    /** Subscription to the event that is dispatched when the user lifts their pointer. */\n    this._pointerUpSubscription = Subscription.EMPTY;\n    /** Subscription to the viewport being scrolled. */\n    this._scrollSubscription = Subscription.EMPTY;\n    /** Subscription to the viewport being resized. */\n    this._resizeSubscription = Subscription.EMPTY;\n    /** Cached reference to the boundary element. */\n    this._boundaryElement = null;\n    /** Whether the native dragging interactions have been enabled on the root element. */\n    this._nativeInteractionsEnabled = true;\n    /** Elements that can be used to drag the draggable item. */\n    this._handles = [];\n    /** Registered handles that are currently disabled. */\n    this._disabledHandles = new Set();\n    /** Layout direction of the item. */\n    this._direction = 'ltr';\n    /**\n     * Amount of milliseconds to wait after the user has put their\n     * pointer down before starting to drag the element.\n     */\n    this.dragStartDelay = 0;\n    /**\n     * If the parent of the dragged element has a `scale` transform, it can throw off the\n     * positioning when the user starts dragging. Use this input to notify the CDK of the scale.\n     */\n    this.scale = 1;\n    this._disabled = false;\n    /** Emits as the drag sequence is being prepared. */\n    this.beforeStarted = new Subject();\n    /** Emits when the user starts dragging the item. */\n    this.started = new Subject();\n    /** Emits when the user has released a drag item, before any animations have started. */\n    this.released = new Subject();\n    /** Emits when the user stops dragging an item in the container. */\n    this.ended = new Subject();\n    /** Emits when the user has moved the item into a new container. */\n    this.entered = new Subject();\n    /** Emits when the user removes the item its container by dragging it into another container. */\n    this.exited = new Subject();\n    /** Emits when the user drops the item inside a container. */\n    this.dropped = new Subject();\n    /**\n     * Emits as the user is dragging the item. Use with caution,\n     * because this event will fire for every pixel that the user has dragged.\n     */\n    this.moved = this._moveEvents;\n    /** Handler for the `mousedown`/`touchstart` events. */\n    this._pointerDown = event => {\n      this.beforeStarted.next();\n      // Delegate the event based on whether it started from a handle or the element itself.\n      if (this._handles.length) {\n        const targetHandle = this._getTargetHandle(event);\n        if (targetHandle && !this._disabledHandles.has(targetHandle) && !this.disabled) {\n          this._initializeDragSequence(targetHandle, event);\n        }\n      } else if (!this.disabled) {\n        this._initializeDragSequence(this._rootElement, event);\n      }\n    };\n    /** Handler that is invoked when the user moves their pointer after they've initiated a drag. */\n    this._pointerMove = event => {\n      const pointerPosition = this._getPointerPositionOnPage(event);\n      if (!this._hasStartedDragging()) {\n        const distanceX = Math.abs(pointerPosition.x - this._pickupPositionOnPage.x);\n        const distanceY = Math.abs(pointerPosition.y - this._pickupPositionOnPage.y);\n        const isOverThreshold = distanceX + distanceY >= this._config.dragStartThreshold;\n        // Only start dragging after the user has moved more than the minimum distance in either\n        // direction. Note that this is preferable over doing something like `skip(minimumDistance)`\n        // in the `pointerMove` subscription, because we're not guaranteed to have one move event\n        // per pixel of movement (e.g. if the user moves their pointer quickly).\n        if (isOverThreshold) {\n          const isDelayElapsed = Date.now() >= this._dragStartTime + this._getDragStartDelay(event);\n          const container = this._dropContainer;\n          if (!isDelayElapsed) {\n            this._endDragSequence(event);\n            return;\n          }\n          // Prevent other drag sequences from starting while something in the container is still\n          // being dragged. This can happen while we're waiting for the drop animation to finish\n          // and can cause errors, because some elements might still be moving around.\n          if (!container || !container.isDragging() && !container.isReceiving()) {\n            // Prevent the default action as soon as the dragging sequence is considered as\n            // \"started\" since waiting for the next event can allow the device to begin scrolling.\n            if (event.cancelable) {\n              event.preventDefault();\n            }\n            this._hasStartedDragging.set(true);\n            this._ngZone.run(() => this._startDragSequence(event));\n          }\n        }\n        return;\n      }\n      // We prevent the default action down here so that we know that dragging has started. This is\n      // important for touch devices where doing this too early can unnecessarily block scrolling,\n      // if there's a dragging delay.\n      if (event.cancelable) {\n        event.preventDefault();\n      }\n      const constrainedPointerPosition = this._getConstrainedPointerPosition(pointerPosition);\n      this._hasMoved = true;\n      this._lastKnownPointerPosition = pointerPosition;\n      this._updatePointerDirectionDelta(constrainedPointerPosition);\n      if (this._dropContainer) {\n        this._updateActiveDropContainer(constrainedPointerPosition, pointerPosition);\n      } else {\n        // If there's a position constraint function, we want the element's top/left to be at the\n        // specific position on the page. Use the initial position as a reference if that's the case.\n        const offset = this.constrainPosition ? this._initialDomRect : this._pickupPositionOnPage;\n        const activeTransform = this._activeTransform;\n        activeTransform.x = constrainedPointerPosition.x - offset.x + this._passiveTransform.x;\n        activeTransform.y = constrainedPointerPosition.y - offset.y + this._passiveTransform.y;\n        this._applyRootElementTransform(activeTransform.x, activeTransform.y);\n      }\n      // Since this event gets fired for every pixel while dragging, we only\n      // want to fire it if the consumer opted into it. Also we have to\n      // re-enter the zone because we run all of the events on the outside.\n      if (this._moveEvents.observers.length) {\n        this._ngZone.run(() => {\n          this._moveEvents.next({\n            source: this,\n            pointerPosition: constrainedPointerPosition,\n            event,\n            distance: this._getDragDistance(constrainedPointerPosition),\n            delta: this._pointerDirectionDelta\n          });\n        });\n      }\n    };\n    /** Handler that is invoked when the user lifts their pointer up, after initiating a drag. */\n    this._pointerUp = event => {\n      this._endDragSequence(event);\n    };\n    /** Handles a native `dragstart` event. */\n    this._nativeDragStart = event => {\n      if (this._handles.length) {\n        const targetHandle = this._getTargetHandle(event);\n        if (targetHandle && !this._disabledHandles.has(targetHandle) && !this.disabled) {\n          event.preventDefault();\n        }\n      } else if (!this.disabled) {\n        // Usually this isn't necessary since the we prevent the default action in `pointerDown`,\n        // but some cases like dragging of links can slip through (see #24403).\n        event.preventDefault();\n      }\n    };\n    this.withRootElement(element).withParent(_config.parentDragRef || null);\n    this._parentPositions = new ParentPositionTracker(_document);\n    _dragDropRegistry.registerDragItem(this);\n  }\n  /**\n   * Returns the element that is being used as a placeholder\n   * while the current element is being dragged.\n   */\n  getPlaceholderElement() {\n    return this._placeholder;\n  }\n  /** Returns the root draggable element. */\n  getRootElement() {\n    return this._rootElement;\n  }\n  /**\n   * Gets the currently-visible element that represents the drag item.\n   * While dragging this is the placeholder, otherwise it's the root element.\n   */\n  getVisibleElement() {\n    return this.isDragging() ? this.getPlaceholderElement() : this.getRootElement();\n  }\n  /** Registers the handles that can be used to drag the element. */\n  withHandles(handles) {\n    this._handles = handles.map(handle => coerceElement(handle));\n    this._handles.forEach(handle => toggleNativeDragInteractions(handle, this.disabled));\n    this._toggleNativeDragInteractions();\n    // Delete any lingering disabled handles that may have been destroyed. Note that we re-create\n    // the set, rather than iterate over it and filter out the destroyed handles, because while\n    // the ES spec allows for sets to be modified while they're being iterated over, some polyfills\n    // use an array internally which may throw an error.\n    const disabledHandles = new Set();\n    this._disabledHandles.forEach(handle => {\n      if (this._handles.indexOf(handle) > -1) {\n        disabledHandles.add(handle);\n      }\n    });\n    this._disabledHandles = disabledHandles;\n    return this;\n  }\n  /**\n   * Registers the template that should be used for the drag preview.\n   * @param template Template that from which to stamp out the preview.\n   */\n  withPreviewTemplate(template) {\n    this._previewTemplate = template;\n    return this;\n  }\n  /**\n   * Registers the template that should be used for the drag placeholder.\n   * @param template Template that from which to stamp out the placeholder.\n   */\n  withPlaceholderTemplate(template) {\n    this._placeholderTemplate = template;\n    return this;\n  }\n  /**\n   * Sets an alternate drag root element. The root element is the element that will be moved as\n   * the user is dragging. Passing an alternate root element is useful when trying to enable\n   * dragging on an element that you might not have access to.\n   */\n  withRootElement(rootElement) {\n    const element = coerceElement(rootElement);\n    if (element !== this._rootElement) {\n      if (this._rootElement) {\n        this._removeRootElementListeners(this._rootElement);\n      }\n      this._ngZone.runOutsideAngular(() => {\n        element.addEventListener('mousedown', this._pointerDown, activeEventListenerOptions);\n        element.addEventListener('touchstart', this._pointerDown, passiveEventListenerOptions);\n        element.addEventListener('dragstart', this._nativeDragStart, activeEventListenerOptions);\n      });\n      this._initialTransform = undefined;\n      this._rootElement = element;\n    }\n    if (typeof SVGElement !== 'undefined' && this._rootElement instanceof SVGElement) {\n      this._ownerSVGElement = this._rootElement.ownerSVGElement;\n    }\n    return this;\n  }\n  /**\n   * Element to which the draggable's position will be constrained.\n   */\n  withBoundaryElement(boundaryElement) {\n    this._boundaryElement = boundaryElement ? coerceElement(boundaryElement) : null;\n    this._resizeSubscription.unsubscribe();\n    if (boundaryElement) {\n      this._resizeSubscription = this._viewportRuler.change(10).subscribe(() => this._containInsideBoundaryOnResize());\n    }\n    return this;\n  }\n  /** Sets the parent ref that the ref is nested in.  */\n  withParent(parent) {\n    this._parentDragRef = parent;\n    return this;\n  }\n  /** Removes the dragging functionality from the DOM element. */\n  dispose() {\n    this._removeRootElementListeners(this._rootElement);\n    // Do this check before removing from the registry since it'll\n    // stop being considered as dragged once it is removed.\n    if (this.isDragging()) {\n      // Since we move out the element to the end of the body while it's being\n      // dragged, we have to make sure that it's removed if it gets destroyed.\n      this._rootElement?.remove();\n    }\n    this._anchor?.remove();\n    this._destroyPreview();\n    this._destroyPlaceholder();\n    this._dragDropRegistry.removeDragItem(this);\n    this._removeListeners();\n    this.beforeStarted.complete();\n    this.started.complete();\n    this.released.complete();\n    this.ended.complete();\n    this.entered.complete();\n    this.exited.complete();\n    this.dropped.complete();\n    this._moveEvents.complete();\n    this._handles = [];\n    this._disabledHandles.clear();\n    this._dropContainer = undefined;\n    this._resizeSubscription.unsubscribe();\n    this._parentPositions.clear();\n    this._boundaryElement = this._rootElement = this._ownerSVGElement = this._placeholderTemplate = this._previewTemplate = this._anchor = this._parentDragRef = null;\n  }\n  /** Checks whether the element is currently being dragged. */\n  isDragging() {\n    return this._hasStartedDragging() && this._dragDropRegistry.isDragging(this);\n  }\n  /** Resets a standalone drag item to its initial position. */\n  reset() {\n    this._rootElement.style.transform = this._initialTransform || '';\n    this._activeTransform = {\n      x: 0,\n      y: 0\n    };\n    this._passiveTransform = {\n      x: 0,\n      y: 0\n    };\n  }\n  /**\n   * Sets a handle as disabled. While a handle is disabled, it'll capture and interrupt dragging.\n   * @param handle Handle element that should be disabled.\n   */\n  disableHandle(handle) {\n    if (!this._disabledHandles.has(handle) && this._handles.indexOf(handle) > -1) {\n      this._disabledHandles.add(handle);\n      toggleNativeDragInteractions(handle, true);\n    }\n  }\n  /**\n   * Enables a handle, if it has been disabled.\n   * @param handle Handle element to be enabled.\n   */\n  enableHandle(handle) {\n    if (this._disabledHandles.has(handle)) {\n      this._disabledHandles.delete(handle);\n      toggleNativeDragInteractions(handle, this.disabled);\n    }\n  }\n  /** Sets the layout direction of the draggable item. */\n  withDirection(direction) {\n    this._direction = direction;\n    return this;\n  }\n  /** Sets the container that the item is part of. */\n  _withDropContainer(container) {\n    this._dropContainer = container;\n  }\n  /**\n   * Gets the current position in pixels the draggable outside of a drop container.\n   */\n  getFreeDragPosition() {\n    const position = this.isDragging() ? this._activeTransform : this._passiveTransform;\n    return {\n      x: position.x,\n      y: position.y\n    };\n  }\n  /**\n   * Sets the current position in pixels the draggable outside of a drop container.\n   * @param value New position to be set.\n   */\n  setFreeDragPosition(value) {\n    this._activeTransform = {\n      x: 0,\n      y: 0\n    };\n    this._passiveTransform.x = value.x;\n    this._passiveTransform.y = value.y;\n    if (!this._dropContainer) {\n      this._applyRootElementTransform(value.x, value.y);\n    }\n    return this;\n  }\n  /**\n   * Sets the container into which to insert the preview element.\n   * @param value Container into which to insert the preview.\n   */\n  withPreviewContainer(value) {\n    this._previewContainer = value;\n    return this;\n  }\n  /** Updates the item's sort order based on the last-known pointer position. */\n  _sortFromLastPointerPosition() {\n    const position = this._lastKnownPointerPosition;\n    if (position && this._dropContainer) {\n      this._updateActiveDropContainer(this._getConstrainedPointerPosition(position), position);\n    }\n  }\n  /** Unsubscribes from the global subscriptions. */\n  _removeListeners() {\n    this._pointerMoveSubscription.unsubscribe();\n    this._pointerUpSubscription.unsubscribe();\n    this._scrollSubscription.unsubscribe();\n    this._getShadowRoot()?.removeEventListener('selectstart', shadowDomSelectStart, activeCapturingEventOptions$1);\n  }\n  /** Destroys the preview element and its ViewRef. */\n  _destroyPreview() {\n    this._preview?.destroy();\n    this._preview = null;\n  }\n  /** Destroys the placeholder element and its ViewRef. */\n  _destroyPlaceholder() {\n    this._placeholder?.remove();\n    this._placeholderRef?.destroy();\n    this._placeholder = this._placeholderRef = null;\n  }\n  /**\n   * Clears subscriptions and stops the dragging sequence.\n   * @param event Browser event object that ended the sequence.\n   */\n  _endDragSequence(event) {\n    // Note that here we use `isDragging` from the service, rather than from `this`.\n    // The difference is that the one from the service reflects whether a dragging sequence\n    // has been initiated, whereas the one on `this` includes whether the user has passed\n    // the minimum dragging threshold.\n    if (!this._dragDropRegistry.isDragging(this)) {\n      return;\n    }\n    this._removeListeners();\n    this._dragDropRegistry.stopDragging(this);\n    this._toggleNativeDragInteractions();\n    if (this._handles) {\n      this._rootElement.style.webkitTapHighlightColor = this._rootElementTapHighlight;\n    }\n    if (!this._hasStartedDragging()) {\n      return;\n    }\n    this.released.next({\n      source: this,\n      event\n    });\n    if (this._dropContainer) {\n      // Stop scrolling immediately, instead of waiting for the animation to finish.\n      this._dropContainer._stopScrolling();\n      this._animatePreviewToPlaceholder().then(() => {\n        this._cleanupDragArtifacts(event);\n        this._cleanupCachedDimensions();\n        this._dragDropRegistry.stopDragging(this);\n      });\n    } else {\n      // Convert the active transform into a passive one. This means that next time\n      // the user starts dragging the item, its position will be calculated relatively\n      // to the new passive transform.\n      this._passiveTransform.x = this._activeTransform.x;\n      const pointerPosition = this._getPointerPositionOnPage(event);\n      this._passiveTransform.y = this._activeTransform.y;\n      this._ngZone.run(() => {\n        this.ended.next({\n          source: this,\n          distance: this._getDragDistance(pointerPosition),\n          dropPoint: pointerPosition,\n          event\n        });\n      });\n      this._cleanupCachedDimensions();\n      this._dragDropRegistry.stopDragging(this);\n    }\n  }\n  /** Starts the dragging sequence. */\n  _startDragSequence(event) {\n    if (isTouchEvent(event)) {\n      this._lastTouchEventTime = Date.now();\n    }\n    this._toggleNativeDragInteractions();\n    // Needs to happen before the root element is moved.\n    const shadowRoot = this._getShadowRoot();\n    const dropContainer = this._dropContainer;\n    if (shadowRoot) {\n      // In some browsers the global `selectstart` that we maintain in the `DragDropRegistry`\n      // doesn't cross the shadow boundary so we have to prevent it at the shadow root (see #28792).\n      this._ngZone.runOutsideAngular(() => {\n        shadowRoot.addEventListener('selectstart', shadowDomSelectStart, activeCapturingEventOptions$1);\n      });\n    }\n    if (dropContainer) {\n      const element = this._rootElement;\n      const parent = element.parentNode;\n      const placeholder = this._placeholder = this._createPlaceholderElement();\n      const anchor = this._anchor = this._anchor || this._document.createComment(typeof ngDevMode === 'undefined' || ngDevMode ? 'cdk-drag-anchor' : '');\n      // Insert an anchor node so that we can restore the element's position in the DOM.\n      parent.insertBefore(anchor, element);\n      // There's no risk of transforms stacking when inside a drop container so\n      // we can keep the initial transform up to date any time dragging starts.\n      this._initialTransform = element.style.transform || '';\n      // Create the preview after the initial transform has\n      // been cached, because it can be affected by the transform.\n      this._preview = new PreviewRef(this._document, this._rootElement, this._direction, this._initialDomRect, this._previewTemplate || null, this.previewClass || null, this._pickupPositionOnPage, this._initialTransform, this._config.zIndex || 1000);\n      this._preview.attach(this._getPreviewInsertionPoint(parent, shadowRoot));\n      // We move the element out at the end of the body and we make it hidden, because keeping it in\n      // place will throw off the consumer's `:last-child` selectors. We can't remove the element\n      // from the DOM completely, because iOS will stop firing all subsequent events in the chain.\n      toggleVisibility(element, false, dragImportantProperties);\n      this._document.body.appendChild(parent.replaceChild(placeholder, element));\n      this.started.next({\n        source: this,\n        event\n      }); // Emit before notifying the container.\n      dropContainer.start();\n      this._initialContainer = dropContainer;\n      this._initialIndex = dropContainer.getItemIndex(this);\n    } else {\n      this.started.next({\n        source: this,\n        event\n      });\n      this._initialContainer = this._initialIndex = undefined;\n    }\n    // Important to run after we've called `start` on the parent container\n    // so that it has had time to resolve its scrollable parents.\n    this._parentPositions.cache(dropContainer ? dropContainer.getScrollableParents() : []);\n  }\n  /**\n   * Sets up the different variables and subscriptions\n   * that will be necessary for the dragging sequence.\n   * @param referenceElement Element that started the drag sequence.\n   * @param event Browser event object that started the sequence.\n   */\n  _initializeDragSequence(referenceElement, event) {\n    // Stop propagation if the item is inside another\n    // draggable so we don't start multiple drag sequences.\n    if (this._parentDragRef) {\n      event.stopPropagation();\n    }\n    const isDragging = this.isDragging();\n    const isTouchSequence = isTouchEvent(event);\n    const isAuxiliaryMouseButton = !isTouchSequence && event.button !== 0;\n    const rootElement = this._rootElement;\n    const target = _getEventTarget(event);\n    const isSyntheticEvent = !isTouchSequence && this._lastTouchEventTime && this._lastTouchEventTime + MOUSE_EVENT_IGNORE_TIME > Date.now();\n    const isFakeEvent = isTouchSequence ? isFakeTouchstartFromScreenReader(event) : isFakeMousedownFromScreenReader(event);\n    // If the event started from an element with the native HTML drag&drop, it'll interfere\n    // with our own dragging (e.g. `img` tags do it by default). Prevent the default action\n    // to stop it from happening. Note that preventing on `dragstart` also seems to work, but\n    // it's flaky and it fails if the user drags it away quickly. Also note that we only want\n    // to do this for `mousedown` since doing the same for `touchstart` will stop any `click`\n    // events from firing on touch devices.\n    if (target && target.draggable && event.type === 'mousedown') {\n      event.preventDefault();\n    }\n    // Abort if the user is already dragging or is using a mouse button other than the primary one.\n    if (isDragging || isAuxiliaryMouseButton || isSyntheticEvent || isFakeEvent) {\n      return;\n    }\n    // If we've got handles, we need to disable the tap highlight on the entire root element,\n    // otherwise iOS will still add it, even though all the drag interactions on the handle\n    // are disabled.\n    if (this._handles.length) {\n      const rootStyles = rootElement.style;\n      this._rootElementTapHighlight = rootStyles.webkitTapHighlightColor || '';\n      rootStyles.webkitTapHighlightColor = 'transparent';\n    }\n    this._hasMoved = false;\n    this._hasStartedDragging.set(this._hasMoved);\n    // Avoid multiple subscriptions and memory leaks when multi touch\n    // (isDragging check above isn't enough because of possible temporal and/or dimensional delays)\n    this._removeListeners();\n    this._initialDomRect = this._rootElement.getBoundingClientRect();\n    this._pointerMoveSubscription = this._dragDropRegistry.pointerMove.subscribe(this._pointerMove);\n    this._pointerUpSubscription = this._dragDropRegistry.pointerUp.subscribe(this._pointerUp);\n    this._scrollSubscription = this._dragDropRegistry.scrolled(this._getShadowRoot()).subscribe(scrollEvent => this._updateOnScroll(scrollEvent));\n    if (this._boundaryElement) {\n      this._boundaryRect = getMutableClientRect(this._boundaryElement);\n    }\n    // If we have a custom preview we can't know ahead of time how large it'll be so we position\n    // it next to the cursor. The exception is when the consumer has opted into making the preview\n    // the same size as the root element, in which case we do know the size.\n    const previewTemplate = this._previewTemplate;\n    this._pickupPositionInElement = previewTemplate && previewTemplate.template && !previewTemplate.matchSize ? {\n      x: 0,\n      y: 0\n    } : this._getPointerPositionInElement(this._initialDomRect, referenceElement, event);\n    const pointerPosition = this._pickupPositionOnPage = this._lastKnownPointerPosition = this._getPointerPositionOnPage(event);\n    this._pointerDirectionDelta = {\n      x: 0,\n      y: 0\n    };\n    this._pointerPositionAtLastDirectionChange = {\n      x: pointerPosition.x,\n      y: pointerPosition.y\n    };\n    this._dragStartTime = Date.now();\n    this._dragDropRegistry.startDragging(this, event);\n  }\n  /** Cleans up the DOM artifacts that were added to facilitate the element being dragged. */\n  _cleanupDragArtifacts(event) {\n    // Restore the element's visibility and insert it at its old position in the DOM.\n    // It's important that we maintain the position, because moving the element around in the DOM\n    // can throw off `NgFor` which does smart diffing and re-creates elements only when necessary,\n    // while moving the existing elements in all other cases.\n    toggleVisibility(this._rootElement, true, dragImportantProperties);\n    this._anchor.parentNode.replaceChild(this._rootElement, this._anchor);\n    this._destroyPreview();\n    this._destroyPlaceholder();\n    this._initialDomRect = this._boundaryRect = this._previewRect = this._initialTransform = undefined;\n    // Re-enter the NgZone since we bound `document` events on the outside.\n    this._ngZone.run(() => {\n      const container = this._dropContainer;\n      const currentIndex = container.getItemIndex(this);\n      const pointerPosition = this._getPointerPositionOnPage(event);\n      const distance = this._getDragDistance(pointerPosition);\n      const isPointerOverContainer = container._isOverContainer(pointerPosition.x, pointerPosition.y);\n      this.ended.next({\n        source: this,\n        distance,\n        dropPoint: pointerPosition,\n        event\n      });\n      this.dropped.next({\n        item: this,\n        currentIndex,\n        previousIndex: this._initialIndex,\n        container: container,\n        previousContainer: this._initialContainer,\n        isPointerOverContainer,\n        distance,\n        dropPoint: pointerPosition,\n        event\n      });\n      container.drop(this, currentIndex, this._initialIndex, this._initialContainer, isPointerOverContainer, distance, pointerPosition, event);\n      this._dropContainer = this._initialContainer;\n    });\n  }\n  /**\n   * Updates the item's position in its drop container, or moves it\n   * into a new one, depending on its current drag position.\n   */\n  _updateActiveDropContainer({\n    x,\n    y\n  }, {\n    x: rawX,\n    y: rawY\n  }) {\n    // Drop container that draggable has been moved into.\n    let newContainer = this._initialContainer._getSiblingContainerFromPosition(this, x, y);\n    // If we couldn't find a new container to move the item into, and the item has left its\n    // initial container, check whether the it's over the initial container. This handles the\n    // case where two containers are connected one way and the user tries to undo dragging an\n    // item into a new container.\n    if (!newContainer && this._dropContainer !== this._initialContainer && this._initialContainer._isOverContainer(x, y)) {\n      newContainer = this._initialContainer;\n    }\n    if (newContainer && newContainer !== this._dropContainer) {\n      this._ngZone.run(() => {\n        // Notify the old container that the item has left.\n        this.exited.next({\n          item: this,\n          container: this._dropContainer\n        });\n        this._dropContainer.exit(this);\n        // Notify the new container that the item has entered.\n        this._dropContainer = newContainer;\n        this._dropContainer.enter(this, x, y, newContainer === this._initialContainer &&\n        // If we're re-entering the initial container and sorting is disabled,\n        // put item the into its starting index to begin with.\n        newContainer.sortingDisabled ? this._initialIndex : undefined);\n        this.entered.next({\n          item: this,\n          container: newContainer,\n          currentIndex: newContainer.getItemIndex(this)\n        });\n      });\n    }\n    // Dragging may have been interrupted as a result of the events above.\n    if (this.isDragging()) {\n      this._dropContainer._startScrollingIfNecessary(rawX, rawY);\n      this._dropContainer._sortItem(this, x, y, this._pointerDirectionDelta);\n      if (this.constrainPosition) {\n        this._applyPreviewTransform(x, y);\n      } else {\n        this._applyPreviewTransform(x - this._pickupPositionInElement.x, y - this._pickupPositionInElement.y);\n      }\n    }\n  }\n  /**\n   * Animates the preview element from its current position to the location of the drop placeholder.\n   * @returns Promise that resolves when the animation completes.\n   */\n  _animatePreviewToPlaceholder() {\n    // If the user hasn't moved yet, the transitionend event won't fire.\n    if (!this._hasMoved) {\n      return Promise.resolve();\n    }\n    const placeholderRect = this._placeholder.getBoundingClientRect();\n    // Apply the class that adds a transition to the preview.\n    this._preview.addClass('cdk-drag-animating');\n    // Move the preview to the placeholder position.\n    this._applyPreviewTransform(placeholderRect.left, placeholderRect.top);\n    // If the element doesn't have a `transition`, the `transitionend` event won't fire. Since\n    // we need to trigger a style recalculation in order for the `cdk-drag-animating` class to\n    // apply its style, we take advantage of the available info to figure out whether we need to\n    // bind the event in the first place.\n    const duration = this._preview.getTransitionDuration();\n    if (duration === 0) {\n      return Promise.resolve();\n    }\n    return this._ngZone.runOutsideAngular(() => {\n      return new Promise(resolve => {\n        const handler = event => {\n          if (!event || this._preview && _getEventTarget(event) === this._preview.element && event.propertyName === 'transform') {\n            this._preview?.removeEventListener('transitionend', handler);\n            resolve();\n            clearTimeout(timeout);\n          }\n        };\n        // If a transition is short enough, the browser might not fire the `transitionend` event.\n        // Since we know how long it's supposed to take, add a timeout with a 50% buffer that'll\n        // fire if the transition hasn't completed when it was supposed to.\n        const timeout = setTimeout(handler, duration * 1.5);\n        this._preview.addEventListener('transitionend', handler);\n      });\n    });\n  }\n  /** Creates an element that will be shown instead of the current element while dragging. */\n  _createPlaceholderElement() {\n    const placeholderConfig = this._placeholderTemplate;\n    const placeholderTemplate = placeholderConfig ? placeholderConfig.template : null;\n    let placeholder;\n    if (placeholderTemplate) {\n      this._placeholderRef = placeholderConfig.viewContainer.createEmbeddedView(placeholderTemplate, placeholderConfig.context);\n      this._placeholderRef.detectChanges();\n      placeholder = getRootNode(this._placeholderRef, this._document);\n    } else {\n      placeholder = deepCloneNode(this._rootElement);\n    }\n    // Stop pointer events on the preview so the user can't\n    // interact with it while the preview is animating.\n    placeholder.style.pointerEvents = 'none';\n    placeholder.classList.add('cdk-drag-placeholder');\n    return placeholder;\n  }\n  /**\n   * Figures out the coordinates at which an element was picked up.\n   * @param referenceElement Element that initiated the dragging.\n   * @param event Event that initiated the dragging.\n   */\n  _getPointerPositionInElement(elementRect, referenceElement, event) {\n    const handleElement = referenceElement === this._rootElement ? null : referenceElement;\n    const referenceRect = handleElement ? handleElement.getBoundingClientRect() : elementRect;\n    const point = isTouchEvent(event) ? event.targetTouches[0] : event;\n    const scrollPosition = this._getViewportScrollPosition();\n    const x = point.pageX - referenceRect.left - scrollPosition.left;\n    const y = point.pageY - referenceRect.top - scrollPosition.top;\n    return {\n      x: referenceRect.left - elementRect.left + x,\n      y: referenceRect.top - elementRect.top + y\n    };\n  }\n  /** Determines the point of the page that was touched by the user. */\n  _getPointerPositionOnPage(event) {\n    const scrollPosition = this._getViewportScrollPosition();\n    const point = isTouchEvent(event) ?\n    // `touches` will be empty for start/end events so we have to fall back to `changedTouches`.\n    // Also note that on real devices we're guaranteed for either `touches` or `changedTouches`\n    // to have a value, but Firefox in device emulation mode has a bug where both can be empty\n    // for `touchstart` and `touchend` so we fall back to a dummy object in order to avoid\n    // throwing an error. The value returned here will be incorrect, but since this only\n    // breaks inside a developer tool and the value is only used for secondary information,\n    // we can get away with it. See https://bugzilla.mozilla.org/show_bug.cgi?id=1615824.\n    event.touches[0] || event.changedTouches[0] || {\n      pageX: 0,\n      pageY: 0\n    } : event;\n    const x = point.pageX - scrollPosition.left;\n    const y = point.pageY - scrollPosition.top;\n    // if dragging SVG element, try to convert from the screen coordinate system to the SVG\n    // coordinate system\n    if (this._ownerSVGElement) {\n      const svgMatrix = this._ownerSVGElement.getScreenCTM();\n      if (svgMatrix) {\n        const svgPoint = this._ownerSVGElement.createSVGPoint();\n        svgPoint.x = x;\n        svgPoint.y = y;\n        return svgPoint.matrixTransform(svgMatrix.inverse());\n      }\n    }\n    return {\n      x,\n      y\n    };\n  }\n  /** Gets the pointer position on the page, accounting for any position constraints. */\n  _getConstrainedPointerPosition(point) {\n    const dropContainerLock = this._dropContainer ? this._dropContainer.lockAxis : null;\n    let {\n      x,\n      y\n    } = this.constrainPosition ? this.constrainPosition(point, this, this._initialDomRect, this._pickupPositionInElement) : point;\n    if (this.lockAxis === 'x' || dropContainerLock === 'x') {\n      y = this._pickupPositionOnPage.y - (this.constrainPosition ? this._pickupPositionInElement.y : 0);\n    } else if (this.lockAxis === 'y' || dropContainerLock === 'y') {\n      x = this._pickupPositionOnPage.x - (this.constrainPosition ? this._pickupPositionInElement.x : 0);\n    }\n    if (this._boundaryRect) {\n      // If not using a custom constrain we need to account for the pickup position in the element\n      // otherwise we do not need to do this, as it has already been accounted for\n      const {\n        x: pickupX,\n        y: pickupY\n      } = !this.constrainPosition ? this._pickupPositionInElement : {\n        x: 0,\n        y: 0\n      };\n      const boundaryRect = this._boundaryRect;\n      const {\n        width: previewWidth,\n        height: previewHeight\n      } = this._getPreviewRect();\n      const minY = boundaryRect.top + pickupY;\n      const maxY = boundaryRect.bottom - (previewHeight - pickupY);\n      const minX = boundaryRect.left + pickupX;\n      const maxX = boundaryRect.right - (previewWidth - pickupX);\n      x = clamp$1(x, minX, maxX);\n      y = clamp$1(y, minY, maxY);\n    }\n    return {\n      x,\n      y\n    };\n  }\n  /** Updates the current drag delta, based on the user's current pointer position on the page. */\n  _updatePointerDirectionDelta(pointerPositionOnPage) {\n    const {\n      x,\n      y\n    } = pointerPositionOnPage;\n    const delta = this._pointerDirectionDelta;\n    const positionSinceLastChange = this._pointerPositionAtLastDirectionChange;\n    // Amount of pixels the user has dragged since the last time the direction changed.\n    const changeX = Math.abs(x - positionSinceLastChange.x);\n    const changeY = Math.abs(y - positionSinceLastChange.y);\n    // Because we handle pointer events on a per-pixel basis, we don't want the delta\n    // to change for every pixel, otherwise anything that depends on it can look erratic.\n    // To make the delta more consistent, we track how much the user has moved since the last\n    // delta change and we only update it after it has reached a certain threshold.\n    if (changeX > this._config.pointerDirectionChangeThreshold) {\n      delta.x = x > positionSinceLastChange.x ? 1 : -1;\n      positionSinceLastChange.x = x;\n    }\n    if (changeY > this._config.pointerDirectionChangeThreshold) {\n      delta.y = y > positionSinceLastChange.y ? 1 : -1;\n      positionSinceLastChange.y = y;\n    }\n    return delta;\n  }\n  /** Toggles the native drag interactions, based on how many handles are registered. */\n  _toggleNativeDragInteractions() {\n    if (!this._rootElement || !this._handles) {\n      return;\n    }\n    const shouldEnable = this._handles.length > 0 || !this.isDragging();\n    if (shouldEnable !== this._nativeInteractionsEnabled) {\n      this._nativeInteractionsEnabled = shouldEnable;\n      toggleNativeDragInteractions(this._rootElement, shouldEnable);\n    }\n  }\n  /** Removes the manually-added event listeners from the root element. */\n  _removeRootElementListeners(element) {\n    element.removeEventListener('mousedown', this._pointerDown, activeEventListenerOptions);\n    element.removeEventListener('touchstart', this._pointerDown, passiveEventListenerOptions);\n    element.removeEventListener('dragstart', this._nativeDragStart, activeEventListenerOptions);\n  }\n  /**\n   * Applies a `transform` to the root element, taking into account any existing transforms on it.\n   * @param x New transform value along the X axis.\n   * @param y New transform value along the Y axis.\n   */\n  _applyRootElementTransform(x, y) {\n    const scale = 1 / this.scale;\n    const transform = getTransform(x * scale, y * scale);\n    const styles = this._rootElement.style;\n    // Cache the previous transform amount only after the first drag sequence, because\n    // we don't want our own transforms to stack on top of each other.\n    // Should be excluded none because none + translate3d(x, y, x) is invalid css\n    if (this._initialTransform == null) {\n      this._initialTransform = styles.transform && styles.transform != 'none' ? styles.transform : '';\n    }\n    // Preserve the previous `transform` value, if there was one. Note that we apply our own\n    // transform before the user's, because things like rotation can affect which direction\n    // the element will be translated towards.\n    styles.transform = combineTransforms(transform, this._initialTransform);\n  }\n  /**\n   * Applies a `transform` to the preview, taking into account any existing transforms on it.\n   * @param x New transform value along the X axis.\n   * @param y New transform value along the Y axis.\n   */\n  _applyPreviewTransform(x, y) {\n    // Only apply the initial transform if the preview is a clone of the original element, otherwise\n    // it could be completely different and the transform might not make sense anymore.\n    const initialTransform = this._previewTemplate?.template ? undefined : this._initialTransform;\n    const transform = getTransform(x, y);\n    this._preview.setTransform(combineTransforms(transform, initialTransform));\n  }\n  /**\n   * Gets the distance that the user has dragged during the current drag sequence.\n   * @param currentPosition Current position of the user's pointer.\n   */\n  _getDragDistance(currentPosition) {\n    const pickupPosition = this._pickupPositionOnPage;\n    if (pickupPosition) {\n      return {\n        x: currentPosition.x - pickupPosition.x,\n        y: currentPosition.y - pickupPosition.y\n      };\n    }\n    return {\n      x: 0,\n      y: 0\n    };\n  }\n  /** Cleans up any cached element dimensions that we don't need after dragging has stopped. */\n  _cleanupCachedDimensions() {\n    this._boundaryRect = this._previewRect = undefined;\n    this._parentPositions.clear();\n  }\n  /**\n   * Checks whether the element is still inside its boundary after the viewport has been resized.\n   * If not, the position is adjusted so that the element fits again.\n   */\n  _containInsideBoundaryOnResize() {\n    let {\n      x,\n      y\n    } = this._passiveTransform;\n    if (x === 0 && y === 0 || this.isDragging() || !this._boundaryElement) {\n      return;\n    }\n    // Note: don't use `_clientRectAtStart` here, because we want the latest position.\n    const elementRect = this._rootElement.getBoundingClientRect();\n    const boundaryRect = this._boundaryElement.getBoundingClientRect();\n    // It's possible that the element got hidden away after dragging (e.g. by switching to a\n    // different tab). Don't do anything in this case so we don't clear the user's position.\n    if (boundaryRect.width === 0 && boundaryRect.height === 0 || elementRect.width === 0 && elementRect.height === 0) {\n      return;\n    }\n    const leftOverflow = boundaryRect.left - elementRect.left;\n    const rightOverflow = elementRect.right - boundaryRect.right;\n    const topOverflow = boundaryRect.top - elementRect.top;\n    const bottomOverflow = elementRect.bottom - boundaryRect.bottom;\n    // If the element has become wider than the boundary, we can't\n    // do much to make it fit so we just anchor it to the left.\n    if (boundaryRect.width > elementRect.width) {\n      if (leftOverflow > 0) {\n        x += leftOverflow;\n      }\n      if (rightOverflow > 0) {\n        x -= rightOverflow;\n      }\n    } else {\n      x = 0;\n    }\n    // If the element has become taller than the boundary, we can't\n    // do much to make it fit so we just anchor it to the top.\n    if (boundaryRect.height > elementRect.height) {\n      if (topOverflow > 0) {\n        y += topOverflow;\n      }\n      if (bottomOverflow > 0) {\n        y -= bottomOverflow;\n      }\n    } else {\n      y = 0;\n    }\n    if (x !== this._passiveTransform.x || y !== this._passiveTransform.y) {\n      this.setFreeDragPosition({\n        y,\n        x\n      });\n    }\n  }\n  /** Gets the drag start delay, based on the event type. */\n  _getDragStartDelay(event) {\n    const value = this.dragStartDelay;\n    if (typeof value === 'number') {\n      return value;\n    } else if (isTouchEvent(event)) {\n      return value.touch;\n    }\n    return value ? value.mouse : 0;\n  }\n  /** Updates the internal state of the draggable element when scrolling has occurred. */\n  _updateOnScroll(event) {\n    const scrollDifference = this._parentPositions.handleScroll(event);\n    if (scrollDifference) {\n      const target = _getEventTarget(event);\n      // DOMRect dimensions are based on the scroll position of the page and its parent\n      // node so we have to update the cached boundary DOMRect if the user has scrolled.\n      if (this._boundaryRect && target !== this._boundaryElement && target.contains(this._boundaryElement)) {\n        adjustDomRect(this._boundaryRect, scrollDifference.top, scrollDifference.left);\n      }\n      this._pickupPositionOnPage.x += scrollDifference.left;\n      this._pickupPositionOnPage.y += scrollDifference.top;\n      // If we're in free drag mode, we have to update the active transform, because\n      // it isn't relative to the viewport like the preview inside a drop list.\n      if (!this._dropContainer) {\n        this._activeTransform.x -= scrollDifference.left;\n        this._activeTransform.y -= scrollDifference.top;\n        this._applyRootElementTransform(this._activeTransform.x, this._activeTransform.y);\n      }\n    }\n  }\n  /** Gets the scroll position of the viewport. */\n  _getViewportScrollPosition() {\n    return this._parentPositions.positions.get(this._document)?.scrollPosition || this._parentPositions.getViewportScrollPosition();\n  }\n  /**\n   * Lazily resolves and returns the shadow root of the element. We do this in a function, rather\n   * than saving it in property directly on init, because we want to resolve it as late as possible\n   * in order to ensure that the element has been moved into the shadow DOM. Doing it inside the\n   * constructor might be too early if the element is inside of something like `ngFor` or `ngIf`.\n   */\n  _getShadowRoot() {\n    if (this._cachedShadowRoot === undefined) {\n      this._cachedShadowRoot = _getShadowRoot(this._rootElement);\n    }\n    return this._cachedShadowRoot;\n  }\n  /** Gets the element into which the drag preview should be inserted. */\n  _getPreviewInsertionPoint(initialParent, shadowRoot) {\n    const previewContainer = this._previewContainer || 'global';\n    if (previewContainer === 'parent') {\n      return initialParent;\n    }\n    if (previewContainer === 'global') {\n      const documentRef = this._document;\n      // We can't use the body if the user is in fullscreen mode,\n      // because the preview will render under the fullscreen element.\n      // TODO(crisbeto): dedupe this with the `FullscreenOverlayContainer` eventually.\n      return shadowRoot || documentRef.fullscreenElement || documentRef.webkitFullscreenElement || documentRef.mozFullScreenElement || documentRef.msFullscreenElement || documentRef.body;\n    }\n    return coerceElement(previewContainer);\n  }\n  /** Lazily resolves and returns the dimensions of the preview. */\n  _getPreviewRect() {\n    // Cache the preview element rect if we haven't cached it already or if\n    // we cached it too early before the element dimensions were computed.\n    if (!this._previewRect || !this._previewRect.width && !this._previewRect.height) {\n      this._previewRect = this._preview ? this._preview.getBoundingClientRect() : this._initialDomRect;\n    }\n    return this._previewRect;\n  }\n  /** Gets a handle that is the target of an event. */\n  _getTargetHandle(event) {\n    return this._handles.find(handle => {\n      return event.target && (event.target === handle || handle.contains(event.target));\n    });\n  }\n}\n/** Clamps a value between a minimum and a maximum. */\nfunction clamp$1(value, min, max) {\n  return Math.max(min, Math.min(max, value));\n}\n/** Determines whether an event is a touch event. */\nfunction isTouchEvent(event) {\n  // This function is called for every pixel that the user has dragged so we need it to be\n  // as fast as possible. Since we only bind mouse events and touch events, we can assume\n  // that if the event's name starts with `t`, it's a touch event.\n  return event.type[0] === 't';\n}\n/** Callback invoked for `selectstart` events inside the shadow DOM. */\nfunction shadowDomSelectStart(event) {\n  event.preventDefault();\n}\n\n/**\n * Moves an item one index in an array to another.\n * @param array Array in which to move the item.\n * @param fromIndex Starting index of the item.\n * @param toIndex Index to which the item should be moved.\n */\nfunction moveItemInArray(array, fromIndex, toIndex) {\n  const from = clamp(fromIndex, array.length - 1);\n  const to = clamp(toIndex, array.length - 1);\n  if (from === to) {\n    return;\n  }\n  const target = array[from];\n  const delta = to < from ? -1 : 1;\n  for (let i = from; i !== to; i += delta) {\n    array[i] = array[i + delta];\n  }\n  array[to] = target;\n}\n/**\n * Moves an item from one array to another.\n * @param currentArray Array from which to transfer the item.\n * @param targetArray Array into which to put the item.\n * @param currentIndex Index of the item in its current array.\n * @param targetIndex Index at which to insert the item.\n */\nfunction transferArrayItem(currentArray, targetArray, currentIndex, targetIndex) {\n  const from = clamp(currentIndex, currentArray.length - 1);\n  const to = clamp(targetIndex, targetArray.length);\n  if (currentArray.length) {\n    targetArray.splice(to, 0, currentArray.splice(from, 1)[0]);\n  }\n}\n/**\n * Copies an item from one array to another, leaving it in its\n * original position in current array.\n * @param currentArray Array from which to copy the item.\n * @param targetArray Array into which is copy the item.\n * @param currentIndex Index of the item in its current array.\n * @param targetIndex Index at which to insert the item.\n *\n */\nfunction copyArrayItem(currentArray, targetArray, currentIndex, targetIndex) {\n  const to = clamp(targetIndex, targetArray.length);\n  if (currentArray.length) {\n    targetArray.splice(to, 0, currentArray[currentIndex]);\n  }\n}\n/** Clamps a number between zero and a maximum. */\nfunction clamp(value, max) {\n  return Math.max(0, Math.min(max, value));\n}\n\n/**\n * Strategy that only supports sorting along a single axis.\n * Items are reordered using CSS transforms which allows for sorting to be animated.\n * @docs-private\n */\nclass SingleAxisSortStrategy {\n  constructor(_dragDropRegistry) {\n    this._dragDropRegistry = _dragDropRegistry;\n    /** Cache of the dimensions of all the items inside the container. */\n    this._itemPositions = [];\n    /** Direction in which the list is oriented. */\n    this.orientation = 'vertical';\n    /**\n     * Keeps track of the item that was last swapped with the dragged item, as well as what direction\n     * the pointer was moving in when the swap occurred and whether the user's pointer continued to\n     * overlap with the swapped item after the swapping occurred.\n     */\n    this._previousSwap = {\n      drag: null,\n      delta: 0,\n      overlaps: false\n    };\n  }\n  /**\n   * To be called when the drag sequence starts.\n   * @param items Items that are currently in the list.\n   */\n  start(items) {\n    this.withItems(items);\n  }\n  /**\n   * To be called when an item is being sorted.\n   * @param item Item to be sorted.\n   * @param pointerX Position of the item along the X axis.\n   * @param pointerY Position of the item along the Y axis.\n   * @param pointerDelta Direction in which the pointer is moving along each axis.\n   */\n  sort(item, pointerX, pointerY, pointerDelta) {\n    const siblings = this._itemPositions;\n    const newIndex = this._getItemIndexFromPointerPosition(item, pointerX, pointerY, pointerDelta);\n    if (newIndex === -1 && siblings.length > 0) {\n      return null;\n    }\n    const isHorizontal = this.orientation === 'horizontal';\n    const currentIndex = siblings.findIndex(currentItem => currentItem.drag === item);\n    const siblingAtNewPosition = siblings[newIndex];\n    const currentPosition = siblings[currentIndex].clientRect;\n    const newPosition = siblingAtNewPosition.clientRect;\n    const delta = currentIndex > newIndex ? 1 : -1;\n    // How many pixels the item's placeholder should be offset.\n    const itemOffset = this._getItemOffsetPx(currentPosition, newPosition, delta);\n    // How many pixels all the other items should be offset.\n    const siblingOffset = this._getSiblingOffsetPx(currentIndex, siblings, delta);\n    // Save the previous order of the items before moving the item to its new index.\n    // We use this to check whether an item has been moved as a result of the sorting.\n    const oldOrder = siblings.slice();\n    // Shuffle the array in place.\n    moveItemInArray(siblings, currentIndex, newIndex);\n    siblings.forEach((sibling, index) => {\n      // Don't do anything if the position hasn't changed.\n      if (oldOrder[index] === sibling) {\n        return;\n      }\n      const isDraggedItem = sibling.drag === item;\n      const offset = isDraggedItem ? itemOffset : siblingOffset;\n      const elementToOffset = isDraggedItem ? item.getPlaceholderElement() : sibling.drag.getRootElement();\n      // Update the offset to reflect the new position.\n      sibling.offset += offset;\n      const transformAmount = Math.round(sibling.offset * (1 / sibling.drag.scale));\n      // Since we're moving the items with a `transform`, we need to adjust their cached\n      // client rects to reflect their new position, as well as swap their positions in the cache.\n      // Note that we shouldn't use `getBoundingClientRect` here to update the cache, because the\n      // elements may be mid-animation which will give us a wrong result.\n      if (isHorizontal) {\n        // Round the transforms since some browsers will\n        // blur the elements, for sub-pixel transforms.\n        elementToOffset.style.transform = combineTransforms(`translate3d(${transformAmount}px, 0, 0)`, sibling.initialTransform);\n        adjustDomRect(sibling.clientRect, 0, offset);\n      } else {\n        elementToOffset.style.transform = combineTransforms(`translate3d(0, ${transformAmount}px, 0)`, sibling.initialTransform);\n        adjustDomRect(sibling.clientRect, offset, 0);\n      }\n    });\n    // Note that it's important that we do this after the client rects have been adjusted.\n    this._previousSwap.overlaps = isInsideClientRect(newPosition, pointerX, pointerY);\n    this._previousSwap.drag = siblingAtNewPosition.drag;\n    this._previousSwap.delta = isHorizontal ? pointerDelta.x : pointerDelta.y;\n    return {\n      previousIndex: currentIndex,\n      currentIndex: newIndex\n    };\n  }\n  /**\n   * Called when an item is being moved into the container.\n   * @param item Item that was moved into the container.\n   * @param pointerX Position of the item along the X axis.\n   * @param pointerY Position of the item along the Y axis.\n   * @param index Index at which the item entered. If omitted, the container will try to figure it\n   *   out automatically.\n   */\n  enter(item, pointerX, pointerY, index) {\n    const newIndex = index == null || index < 0 ?\n    // We use the coordinates of where the item entered the drop\n    // zone to figure out at which index it should be inserted.\n    this._getItemIndexFromPointerPosition(item, pointerX, pointerY) : index;\n    const activeDraggables = this._activeDraggables;\n    const currentIndex = activeDraggables.indexOf(item);\n    const placeholder = item.getPlaceholderElement();\n    let newPositionReference = activeDraggables[newIndex];\n    // If the item at the new position is the same as the item that is being dragged,\n    // it means that we're trying to restore the item to its initial position. In this\n    // case we should use the next item from the list as the reference.\n    if (newPositionReference === item) {\n      newPositionReference = activeDraggables[newIndex + 1];\n    }\n    // If we didn't find a new position reference, it means that either the item didn't start off\n    // in this container, or that the item requested to be inserted at the end of the list.\n    if (!newPositionReference && (newIndex == null || newIndex === -1 || newIndex < activeDraggables.length - 1) && this._shouldEnterAsFirstChild(pointerX, pointerY)) {\n      newPositionReference = activeDraggables[0];\n    }\n    // Since the item may be in the `activeDraggables` already (e.g. if the user dragged it\n    // into another container and back again), we have to ensure that it isn't duplicated.\n    if (currentIndex > -1) {\n      activeDraggables.splice(currentIndex, 1);\n    }\n    // Don't use items that are being dragged as a reference, because\n    // their element has been moved down to the bottom of the body.\n    if (newPositionReference && !this._dragDropRegistry.isDragging(newPositionReference)) {\n      const element = newPositionReference.getRootElement();\n      element.parentElement.insertBefore(placeholder, element);\n      activeDraggables.splice(newIndex, 0, item);\n    } else {\n      this._element.appendChild(placeholder);\n      activeDraggables.push(item);\n    }\n    // The transform needs to be cleared so it doesn't throw off the measurements.\n    placeholder.style.transform = '';\n    // Note that usually `start` is called together with `enter` when an item goes into a new\n    // container. This will cache item positions, but we need to refresh them since the amount\n    // of items has changed.\n    this._cacheItemPositions();\n  }\n  /** Sets the items that are currently part of the list. */\n  withItems(items) {\n    this._activeDraggables = items.slice();\n    this._cacheItemPositions();\n  }\n  /** Assigns a sort predicate to the strategy. */\n  withSortPredicate(predicate) {\n    this._sortPredicate = predicate;\n  }\n  /** Resets the strategy to its initial state before dragging was started. */\n  reset() {\n    // TODO(crisbeto): may have to wait for the animations to finish.\n    this._activeDraggables?.forEach(item => {\n      const rootElement = item.getRootElement();\n      if (rootElement) {\n        const initialTransform = this._itemPositions.find(p => p.drag === item)?.initialTransform;\n        rootElement.style.transform = initialTransform || '';\n      }\n    });\n    this._itemPositions = [];\n    this._activeDraggables = [];\n    this._previousSwap.drag = null;\n    this._previousSwap.delta = 0;\n    this._previousSwap.overlaps = false;\n  }\n  /**\n   * Gets a snapshot of items currently in the list.\n   * Can include items that we dragged in from another list.\n   */\n  getActiveItemsSnapshot() {\n    return this._activeDraggables;\n  }\n  /** Gets the index of a specific item. */\n  getItemIndex(item) {\n    // Items are sorted always by top/left in the cache, however they flow differently in RTL.\n    // The rest of the logic still stands no matter what orientation we're in, however\n    // we need to invert the array when determining the index.\n    const items = this.orientation === 'horizontal' && this.direction === 'rtl' ? this._itemPositions.slice().reverse() : this._itemPositions;\n    return items.findIndex(currentItem => currentItem.drag === item);\n  }\n  /** Used to notify the strategy that the scroll position has changed. */\n  updateOnScroll(topDifference, leftDifference) {\n    // Since we know the amount that the user has scrolled we can shift all of the\n    // client rectangles ourselves. This is cheaper than re-measuring everything and\n    // we can avoid inconsistent behavior where we might be measuring the element before\n    // its position has changed.\n    this._itemPositions.forEach(({\n      clientRect\n    }) => {\n      adjustDomRect(clientRect, topDifference, leftDifference);\n    });\n    // We need two loops for this, because we want all of the cached\n    // positions to be up-to-date before we re-sort the item.\n    this._itemPositions.forEach(({\n      drag\n    }) => {\n      if (this._dragDropRegistry.isDragging(drag)) {\n        // We need to re-sort the item manually, because the pointer move\n        // events won't be dispatched while the user is scrolling.\n        drag._sortFromLastPointerPosition();\n      }\n    });\n  }\n  withElementContainer(container) {\n    this._element = container;\n  }\n  /** Refreshes the position cache of the items and sibling containers. */\n  _cacheItemPositions() {\n    const isHorizontal = this.orientation === 'horizontal';\n    this._itemPositions = this._activeDraggables.map(drag => {\n      const elementToMeasure = drag.getVisibleElement();\n      return {\n        drag,\n        offset: 0,\n        initialTransform: elementToMeasure.style.transform || '',\n        clientRect: getMutableClientRect(elementToMeasure)\n      };\n    }).sort((a, b) => {\n      return isHorizontal ? a.clientRect.left - b.clientRect.left : a.clientRect.top - b.clientRect.top;\n    });\n  }\n  /**\n   * Gets the offset in pixels by which the item that is being dragged should be moved.\n   * @param currentPosition Current position of the item.\n   * @param newPosition Position of the item where the current item should be moved.\n   * @param delta Direction in which the user is moving.\n   */\n  _getItemOffsetPx(currentPosition, newPosition, delta) {\n    const isHorizontal = this.orientation === 'horizontal';\n    let itemOffset = isHorizontal ? newPosition.left - currentPosition.left : newPosition.top - currentPosition.top;\n    // Account for differences in the item width/height.\n    if (delta === -1) {\n      itemOffset += isHorizontal ? newPosition.width - currentPosition.width : newPosition.height - currentPosition.height;\n    }\n    return itemOffset;\n  }\n  /**\n   * Gets the offset in pixels by which the items that aren't being dragged should be moved.\n   * @param currentIndex Index of the item currently being dragged.\n   * @param siblings All of the items in the list.\n   * @param delta Direction in which the user is moving.\n   */\n  _getSiblingOffsetPx(currentIndex, siblings, delta) {\n    const isHorizontal = this.orientation === 'horizontal';\n    const currentPosition = siblings[currentIndex].clientRect;\n    const immediateSibling = siblings[currentIndex + delta * -1];\n    let siblingOffset = currentPosition[isHorizontal ? 'width' : 'height'] * delta;\n    if (immediateSibling) {\n      const start = isHorizontal ? 'left' : 'top';\n      const end = isHorizontal ? 'right' : 'bottom';\n      // Get the spacing between the start of the current item and the end of the one immediately\n      // after it in the direction in which the user is dragging, or vice versa. We add it to the\n      // offset in order to push the element to where it will be when it's inline and is influenced\n      // by the `margin` of its siblings.\n      if (delta === -1) {\n        siblingOffset -= immediateSibling.clientRect[start] - currentPosition[end];\n      } else {\n        siblingOffset += currentPosition[start] - immediateSibling.clientRect[end];\n      }\n    }\n    return siblingOffset;\n  }\n  /**\n   * Checks if pointer is entering in the first position\n   * @param pointerX Position of the user's pointer along the X axis.\n   * @param pointerY Position of the user's pointer along the Y axis.\n   */\n  _shouldEnterAsFirstChild(pointerX, pointerY) {\n    if (!this._activeDraggables.length) {\n      return false;\n    }\n    const itemPositions = this._itemPositions;\n    const isHorizontal = this.orientation === 'horizontal';\n    // `itemPositions` are sorted by position while `activeDraggables` are sorted by child index\n    // check if container is using some sort of \"reverse\" ordering (eg: flex-direction: row-reverse)\n    const reversed = itemPositions[0].drag !== this._activeDraggables[0];\n    if (reversed) {\n      const lastItemRect = itemPositions[itemPositions.length - 1].clientRect;\n      return isHorizontal ? pointerX >= lastItemRect.right : pointerY >= lastItemRect.bottom;\n    } else {\n      const firstItemRect = itemPositions[0].clientRect;\n      return isHorizontal ? pointerX <= firstItemRect.left : pointerY <= firstItemRect.top;\n    }\n  }\n  /**\n   * Gets the index of an item in the drop container, based on the position of the user's pointer.\n   * @param item Item that is being sorted.\n   * @param pointerX Position of the user's pointer along the X axis.\n   * @param pointerY Position of the user's pointer along the Y axis.\n   * @param delta Direction in which the user is moving their pointer.\n   */\n  _getItemIndexFromPointerPosition(item, pointerX, pointerY, delta) {\n    const isHorizontal = this.orientation === 'horizontal';\n    const index = this._itemPositions.findIndex(({\n      drag,\n      clientRect\n    }) => {\n      // Skip the item itself.\n      if (drag === item) {\n        return false;\n      }\n      if (delta) {\n        const direction = isHorizontal ? delta.x : delta.y;\n        // If the user is still hovering over the same item as last time, their cursor hasn't left\n        // the item after we made the swap, and they didn't change the direction in which they're\n        // dragging, we don't consider it a direction swap.\n        if (drag === this._previousSwap.drag && this._previousSwap.overlaps && direction === this._previousSwap.delta) {\n          return false;\n        }\n      }\n      return isHorizontal ?\n      // Round these down since most browsers report client rects with\n      // sub-pixel precision, whereas the pointer coordinates are rounded to pixels.\n      pointerX >= Math.floor(clientRect.left) && pointerX < Math.floor(clientRect.right) : pointerY >= Math.floor(clientRect.top) && pointerY < Math.floor(clientRect.bottom);\n    });\n    return index === -1 || !this._sortPredicate(index, item) ? -1 : index;\n  }\n}\n\n/**\n * Strategy that only supports sorting on a list that might wrap.\n * Items are reordered by moving their DOM nodes around.\n * @docs-private\n */\nclass MixedSortStrategy {\n  constructor(_document, _dragDropRegistry) {\n    this._document = _document;\n    this._dragDropRegistry = _dragDropRegistry;\n    /**\n     * Keeps track of the item that was last swapped with the dragged item, as well as what direction\n     * the pointer was moving in when the swap occurred and whether the user's pointer continued to\n     * overlap with the swapped item after the swapping occurred.\n     */\n    this._previousSwap = {\n      drag: null,\n      deltaX: 0,\n      deltaY: 0,\n      overlaps: false\n    };\n    /**\n     * Keeps track of the relationship between a node and its next sibling. This information\n     * is used to restore the DOM to the order it was in before dragging started.\n     */\n    this._relatedNodes = [];\n  }\n  /**\n   * To be called when the drag sequence starts.\n   * @param items Items that are currently in the list.\n   */\n  start(items) {\n    const childNodes = this._element.childNodes;\n    this._relatedNodes = [];\n    for (let i = 0; i < childNodes.length; i++) {\n      const node = childNodes[i];\n      this._relatedNodes.push([node, node.nextSibling]);\n    }\n    this.withItems(items);\n  }\n  /**\n   * To be called when an item is being sorted.\n   * @param item Item to be sorted.\n   * @param pointerX Position of the item along the X axis.\n   * @param pointerY Position of the item along the Y axis.\n   * @param pointerDelta Direction in which the pointer is moving along each axis.\n   */\n  sort(item, pointerX, pointerY, pointerDelta) {\n    const newIndex = this._getItemIndexFromPointerPosition(item, pointerX, pointerY);\n    const previousSwap = this._previousSwap;\n    if (newIndex === -1 || this._activeItems[newIndex] === item) {\n      return null;\n    }\n    const toSwapWith = this._activeItems[newIndex];\n    // Prevent too many swaps over the same item.\n    if (previousSwap.drag === toSwapWith && previousSwap.overlaps && previousSwap.deltaX === pointerDelta.x && previousSwap.deltaY === pointerDelta.y) {\n      return null;\n    }\n    const previousIndex = this.getItemIndex(item);\n    const current = item.getPlaceholderElement();\n    const overlapElement = toSwapWith.getRootElement();\n    if (newIndex > previousIndex) {\n      overlapElement.after(current);\n    } else {\n      overlapElement.before(current);\n    }\n    moveItemInArray(this._activeItems, previousIndex, newIndex);\n    const newOverlapElement = this._getRootNode().elementFromPoint(pointerX, pointerY);\n    // Note: it's tempting to save the entire `pointerDelta` object here, however that'll\n    // break this functionality, because the same object is passed for all `sort` calls.\n    previousSwap.deltaX = pointerDelta.x;\n    previousSwap.deltaY = pointerDelta.y;\n    previousSwap.drag = toSwapWith;\n    previousSwap.overlaps = overlapElement === newOverlapElement || overlapElement.contains(newOverlapElement);\n    return {\n      previousIndex,\n      currentIndex: newIndex\n    };\n  }\n  /**\n   * Called when an item is being moved into the container.\n   * @param item Item that was moved into the container.\n   * @param pointerX Position of the item along the X axis.\n   * @param pointerY Position of the item along the Y axis.\n   * @param index Index at which the item entered. If omitted, the container will try to figure it\n   *   out automatically.\n   */\n  enter(item, pointerX, pointerY, index) {\n    let enterIndex = index == null || index < 0 ? this._getItemIndexFromPointerPosition(item, pointerX, pointerY) : index;\n    // In some cases (e.g. when the container has padding) we might not be able to figure\n    // out which item to insert the dragged item next to, because the pointer didn't overlap\n    // with anything. In that case we find the item that's closest to the pointer.\n    if (enterIndex === -1) {\n      enterIndex = this._getClosestItemIndexToPointer(item, pointerX, pointerY);\n    }\n    const targetItem = this._activeItems[enterIndex];\n    const currentIndex = this._activeItems.indexOf(item);\n    if (currentIndex > -1) {\n      this._activeItems.splice(currentIndex, 1);\n    }\n    if (targetItem && !this._dragDropRegistry.isDragging(targetItem)) {\n      this._activeItems.splice(enterIndex, 0, item);\n      targetItem.getRootElement().before(item.getPlaceholderElement());\n    } else {\n      this._activeItems.push(item);\n      this._element.appendChild(item.getPlaceholderElement());\n    }\n  }\n  /** Sets the items that are currently part of the list. */\n  withItems(items) {\n    this._activeItems = items.slice();\n  }\n  /** Assigns a sort predicate to the strategy. */\n  withSortPredicate(predicate) {\n    this._sortPredicate = predicate;\n  }\n  /** Resets the strategy to its initial state before dragging was started. */\n  reset() {\n    const root = this._element;\n    const previousSwap = this._previousSwap;\n    // Moving elements around in the DOM can break things like the `@for` loop, because it\n    // uses comment nodes to know where to insert elements. To avoid such issues, we restore\n    // the DOM nodes in the list to their original order when the list is reset.\n    // Note that this could be simpler if we just saved all the nodes, cleared the root\n    // and then appended them in the original order. We don't do it, because it can break\n    // down depending on when the snapshot was taken. E.g. we may end up snapshotting the\n    // placeholder element which is removed after dragging.\n    for (let i = this._relatedNodes.length - 1; i > -1; i--) {\n      const [node, nextSibling] = this._relatedNodes[i];\n      if (node.parentNode === root && node.nextSibling !== nextSibling) {\n        if (nextSibling === null) {\n          root.appendChild(node);\n        } else if (nextSibling.parentNode === root) {\n          root.insertBefore(node, nextSibling);\n        }\n      }\n    }\n    this._relatedNodes = [];\n    this._activeItems = [];\n    previousSwap.drag = null;\n    previousSwap.deltaX = previousSwap.deltaY = 0;\n    previousSwap.overlaps = false;\n  }\n  /**\n   * Gets a snapshot of items currently in the list.\n   * Can include items that we dragged in from another list.\n   */\n  getActiveItemsSnapshot() {\n    return this._activeItems;\n  }\n  /** Gets the index of a specific item. */\n  getItemIndex(item) {\n    return this._activeItems.indexOf(item);\n  }\n  /** Used to notify the strategy that the scroll position has changed. */\n  updateOnScroll() {\n    this._activeItems.forEach(item => {\n      if (this._dragDropRegistry.isDragging(item)) {\n        // We need to re-sort the item manually, because the pointer move\n        // events won't be dispatched while the user is scrolling.\n        item._sortFromLastPointerPosition();\n      }\n    });\n  }\n  withElementContainer(container) {\n    if (container !== this._element) {\n      this._element = container;\n      this._rootNode = undefined;\n    }\n  }\n  /**\n   * Gets the index of an item in the drop container, based on the position of the user's pointer.\n   * @param item Item that is being sorted.\n   * @param pointerX Position of the user's pointer along the X axis.\n   * @param pointerY Position of the user's pointer along the Y axis.\n   * @param delta Direction in which the user is moving their pointer.\n   */\n  _getItemIndexFromPointerPosition(item, pointerX, pointerY) {\n    const elementAtPoint = this._getRootNode().elementFromPoint(Math.floor(pointerX), Math.floor(pointerY));\n    const index = elementAtPoint ? this._activeItems.findIndex(item => {\n      const root = item.getRootElement();\n      return elementAtPoint === root || root.contains(elementAtPoint);\n    }) : -1;\n    return index === -1 || !this._sortPredicate(index, item) ? -1 : index;\n  }\n  /** Lazily resolves the list's root node. */\n  _getRootNode() {\n    // Resolve the root node lazily to ensure that the drop list is in its final place in the DOM.\n    if (!this._rootNode) {\n      this._rootNode = _getShadowRoot(this._element) || this._document;\n    }\n    return this._rootNode;\n  }\n  /**\n   * Finds the index of the item that's closest to the item being dragged.\n   * @param item Item being dragged.\n   * @param pointerX Position of the user's pointer along the X axis.\n   * @param pointerY Position of the user's pointer along the Y axis.\n   */\n  _getClosestItemIndexToPointer(item, pointerX, pointerY) {\n    if (this._activeItems.length === 0) {\n      return -1;\n    }\n    if (this._activeItems.length === 1) {\n      return 0;\n    }\n    let minDistance = Infinity;\n    let minIndex = -1;\n    // Find the Euclidean distance (https://en.wikipedia.org/wiki/Euclidean_distance) between each\n    // item and the pointer, and return the smallest one. Note that this is a bit flawed in that DOM\n    // nodes are rectangles, not points, so we use the top/left coordinates. It should be enough\n    // for our purposes.\n    for (let i = 0; i < this._activeItems.length; i++) {\n      const current = this._activeItems[i];\n      if (current !== item) {\n        const {\n          x,\n          y\n        } = current.getRootElement().getBoundingClientRect();\n        const distance = Math.hypot(pointerX - x, pointerY - y);\n        if (distance < minDistance) {\n          minDistance = distance;\n          minIndex = i;\n        }\n      }\n    }\n    return minIndex;\n  }\n}\n\n/**\n * Proximity, as a ratio to width/height, at which a\n * dragged item will affect the drop container.\n */\nconst DROP_PROXIMITY_THRESHOLD = 0.05;\n/**\n * Proximity, as a ratio to width/height at which to start auto-scrolling the drop list or the\n * viewport. The value comes from trying it out manually until it feels right.\n */\nconst SCROLL_PROXIMITY_THRESHOLD = 0.05;\n/** Vertical direction in which we can auto-scroll. */\nvar AutoScrollVerticalDirection;\n(function (AutoScrollVerticalDirection) {\n  AutoScrollVerticalDirection[AutoScrollVerticalDirection[\"NONE\"] = 0] = \"NONE\";\n  AutoScrollVerticalDirection[AutoScrollVerticalDirection[\"UP\"] = 1] = \"UP\";\n  AutoScrollVerticalDirection[AutoScrollVerticalDirection[\"DOWN\"] = 2] = \"DOWN\";\n})(AutoScrollVerticalDirection || (AutoScrollVerticalDirection = {}));\n/** Horizontal direction in which we can auto-scroll. */\nvar AutoScrollHorizontalDirection;\n(function (AutoScrollHorizontalDirection) {\n  AutoScrollHorizontalDirection[AutoScrollHorizontalDirection[\"NONE\"] = 0] = \"NONE\";\n  AutoScrollHorizontalDirection[AutoScrollHorizontalDirection[\"LEFT\"] = 1] = \"LEFT\";\n  AutoScrollHorizontalDirection[AutoScrollHorizontalDirection[\"RIGHT\"] = 2] = \"RIGHT\";\n})(AutoScrollHorizontalDirection || (AutoScrollHorizontalDirection = {}));\n/**\n * Reference to a drop list. Used to manipulate or dispose of the container.\n */\nclass DropListRef {\n  constructor(element, _dragDropRegistry, _document, _ngZone, _viewportRuler) {\n    this._dragDropRegistry = _dragDropRegistry;\n    this._ngZone = _ngZone;\n    this._viewportRuler = _viewportRuler;\n    /** Whether starting a dragging sequence from this container is disabled. */\n    this.disabled = false;\n    /** Whether sorting items within the list is disabled. */\n    this.sortingDisabled = false;\n    /**\n     * Whether auto-scrolling the view when the user\n     * moves their pointer close to the edges is disabled.\n     */\n    this.autoScrollDisabled = false;\n    /** Number of pixels to scroll for each frame when auto-scrolling an element. */\n    this.autoScrollStep = 2;\n    /**\n     * Function that is used to determine whether an item\n     * is allowed to be moved into a drop container.\n     */\n    this.enterPredicate = () => true;\n    /** Function that is used to determine whether an item can be sorted into a particular index. */\n    this.sortPredicate = () => true;\n    /** Emits right before dragging has started. */\n    this.beforeStarted = new Subject();\n    /**\n     * Emits when the user has moved a new drag item into this container.\n     */\n    this.entered = new Subject();\n    /**\n     * Emits when the user removes an item from the container\n     * by dragging it into another container.\n     */\n    this.exited = new Subject();\n    /** Emits when the user drops an item inside the container. */\n    this.dropped = new Subject();\n    /** Emits as the user is swapping items while actively dragging. */\n    this.sorted = new Subject();\n    /** Emits when a dragging sequence is started in a list connected to the current one. */\n    this.receivingStarted = new Subject();\n    /** Emits when a dragging sequence is stopped from a list connected to the current one. */\n    this.receivingStopped = new Subject();\n    /** Whether an item in the list is being dragged. */\n    this._isDragging = false;\n    /** Draggable items in the container. */\n    this._draggables = [];\n    /** Drop lists that are connected to the current one. */\n    this._siblings = [];\n    /** Connected siblings that currently have a dragged item. */\n    this._activeSiblings = new Set();\n    /** Subscription to the window being scrolled. */\n    this._viewportScrollSubscription = Subscription.EMPTY;\n    /** Vertical direction in which the list is currently scrolling. */\n    this._verticalScrollDirection = AutoScrollVerticalDirection.NONE;\n    /** Horizontal direction in which the list is currently scrolling. */\n    this._horizontalScrollDirection = AutoScrollHorizontalDirection.NONE;\n    /** Used to signal to the current auto-scroll sequence when to stop. */\n    this._stopScrollTimers = new Subject();\n    /** Shadow root of the current element. Necessary for `elementFromPoint` to resolve correctly. */\n    this._cachedShadowRoot = null;\n    /** Elements that can be scrolled while the user is dragging. */\n    this._scrollableElements = [];\n    /** Direction of the list's layout. */\n    this._direction = 'ltr';\n    /** Starts the interval that'll auto-scroll the element. */\n    this._startScrollInterval = () => {\n      this._stopScrolling();\n      interval(0, animationFrameScheduler).pipe(takeUntil(this._stopScrollTimers)).subscribe(() => {\n        const node = this._scrollNode;\n        const scrollStep = this.autoScrollStep;\n        if (this._verticalScrollDirection === AutoScrollVerticalDirection.UP) {\n          node.scrollBy(0, -scrollStep);\n        } else if (this._verticalScrollDirection === AutoScrollVerticalDirection.DOWN) {\n          node.scrollBy(0, scrollStep);\n        }\n        if (this._horizontalScrollDirection === AutoScrollHorizontalDirection.LEFT) {\n          node.scrollBy(-scrollStep, 0);\n        } else if (this._horizontalScrollDirection === AutoScrollHorizontalDirection.RIGHT) {\n          node.scrollBy(scrollStep, 0);\n        }\n      });\n    };\n    const coercedElement = this.element = coerceElement(element);\n    this._document = _document;\n    this.withOrientation('vertical').withElementContainer(coercedElement);\n    _dragDropRegistry.registerDropContainer(this);\n    this._parentPositions = new ParentPositionTracker(_document);\n  }\n  /** Removes the drop list functionality from the DOM element. */\n  dispose() {\n    this._stopScrolling();\n    this._stopScrollTimers.complete();\n    this._viewportScrollSubscription.unsubscribe();\n    this.beforeStarted.complete();\n    this.entered.complete();\n    this.exited.complete();\n    this.dropped.complete();\n    this.sorted.complete();\n    this.receivingStarted.complete();\n    this.receivingStopped.complete();\n    this._activeSiblings.clear();\n    this._scrollNode = null;\n    this._parentPositions.clear();\n    this._dragDropRegistry.removeDropContainer(this);\n  }\n  /** Whether an item from this list is currently being dragged. */\n  isDragging() {\n    return this._isDragging;\n  }\n  /** Starts dragging an item. */\n  start() {\n    this._draggingStarted();\n    this._notifyReceivingSiblings();\n  }\n  /**\n   * Attempts to move an item into the container.\n   * @param item Item that was moved into the container.\n   * @param pointerX Position of the item along the X axis.\n   * @param pointerY Position of the item along the Y axis.\n   * @param index Index at which the item entered. If omitted, the container will try to figure it\n   *   out automatically.\n   */\n  enter(item, pointerX, pointerY, index) {\n    this._draggingStarted();\n    // If sorting is disabled, we want the item to return to its starting\n    // position if the user is returning it to its initial container.\n    if (index == null && this.sortingDisabled) {\n      index = this._draggables.indexOf(item);\n    }\n    this._sortStrategy.enter(item, pointerX, pointerY, index);\n    // Note that this usually happens inside `_draggingStarted` as well, but the dimensions\n    // can change when the sort strategy moves the item around inside `enter`.\n    this._cacheParentPositions();\n    // Notify siblings at the end so that the item has been inserted into the `activeDraggables`.\n    this._notifyReceivingSiblings();\n    this.entered.next({\n      item,\n      container: this,\n      currentIndex: this.getItemIndex(item)\n    });\n  }\n  /**\n   * Removes an item from the container after it was dragged into another container by the user.\n   * @param item Item that was dragged out.\n   */\n  exit(item) {\n    this._reset();\n    this.exited.next({\n      item,\n      container: this\n    });\n  }\n  /**\n   * Drops an item into this container.\n   * @param item Item being dropped into the container.\n   * @param currentIndex Index at which the item should be inserted.\n   * @param previousIndex Index of the item when dragging started.\n   * @param previousContainer Container from which the item got dragged in.\n   * @param isPointerOverContainer Whether the user's pointer was over the\n   *    container when the item was dropped.\n   * @param distance Distance the user has dragged since the start of the dragging sequence.\n   * @param event Event that triggered the dropping sequence.\n   *\n   * @breaking-change 15.0.0 `previousIndex` and `event` parameters to become required.\n   */\n  drop(item, currentIndex, previousIndex, previousContainer, isPointerOverContainer, distance, dropPoint, event = {}) {\n    this._reset();\n    this.dropped.next({\n      item,\n      currentIndex,\n      previousIndex,\n      container: this,\n      previousContainer,\n      isPointerOverContainer,\n      distance,\n      dropPoint,\n      event\n    });\n  }\n  /**\n   * Sets the draggable items that are a part of this list.\n   * @param items Items that are a part of this list.\n   */\n  withItems(items) {\n    const previousItems = this._draggables;\n    this._draggables = items;\n    items.forEach(item => item._withDropContainer(this));\n    if (this.isDragging()) {\n      const draggedItems = previousItems.filter(item => item.isDragging());\n      // If all of the items being dragged were removed\n      // from the list, abort the current drag sequence.\n      if (draggedItems.every(item => items.indexOf(item) === -1)) {\n        this._reset();\n      } else {\n        this._sortStrategy.withItems(this._draggables);\n      }\n    }\n    return this;\n  }\n  /** Sets the layout direction of the drop list. */\n  withDirection(direction) {\n    this._direction = direction;\n    if (this._sortStrategy instanceof SingleAxisSortStrategy) {\n      this._sortStrategy.direction = direction;\n    }\n    return this;\n  }\n  /**\n   * Sets the containers that are connected to this one. When two or more containers are\n   * connected, the user will be allowed to transfer items between them.\n   * @param connectedTo Other containers that the current containers should be connected to.\n   */\n  connectedTo(connectedTo) {\n    this._siblings = connectedTo.slice();\n    return this;\n  }\n  /**\n   * Sets the orientation of the container.\n   * @param orientation New orientation for the container.\n   */\n  withOrientation(orientation) {\n    if (orientation === 'mixed') {\n      this._sortStrategy = new MixedSortStrategy(this._document, this._dragDropRegistry);\n    } else {\n      const strategy = new SingleAxisSortStrategy(this._dragDropRegistry);\n      strategy.direction = this._direction;\n      strategy.orientation = orientation;\n      this._sortStrategy = strategy;\n    }\n    this._sortStrategy.withElementContainer(this._container);\n    this._sortStrategy.withSortPredicate((index, item) => this.sortPredicate(index, item, this));\n    return this;\n  }\n  /**\n   * Sets which parent elements are can be scrolled while the user is dragging.\n   * @param elements Elements that can be scrolled.\n   */\n  withScrollableParents(elements) {\n    const element = this._container;\n    // We always allow the current element to be scrollable\n    // so we need to ensure that it's in the array.\n    this._scrollableElements = elements.indexOf(element) === -1 ? [element, ...elements] : elements.slice();\n    return this;\n  }\n  /**\n   * Configures the drop list so that a different element is used as the container for the\n   * dragged items. This is useful for the cases when one might not have control over the\n   * full DOM that sets up the dragging.\n   * Note that the alternate container needs to be a descendant of the drop list.\n   * @param container New element container to be assigned.\n   */\n  withElementContainer(container) {\n    if (container === this._container) {\n      return this;\n    }\n    const element = coerceElement(this.element);\n    if ((typeof ngDevMode === 'undefined' || ngDevMode) && container !== element && !element.contains(container)) {\n      throw new Error('Invalid DOM structure for drop list. Alternate container element must be a descendant of the drop list.');\n    }\n    const oldContainerIndex = this._scrollableElements.indexOf(this._container);\n    const newContainerIndex = this._scrollableElements.indexOf(container);\n    if (oldContainerIndex > -1) {\n      this._scrollableElements.splice(oldContainerIndex, 1);\n    }\n    if (newContainerIndex > -1) {\n      this._scrollableElements.splice(newContainerIndex, 1);\n    }\n    if (this._sortStrategy) {\n      this._sortStrategy.withElementContainer(container);\n    }\n    this._cachedShadowRoot = null;\n    this._scrollableElements.unshift(container);\n    this._container = container;\n    return this;\n  }\n  /** Gets the scrollable parents that are registered with this drop container. */\n  getScrollableParents() {\n    return this._scrollableElements;\n  }\n  /**\n   * Figures out the index of an item in the container.\n   * @param item Item whose index should be determined.\n   */\n  getItemIndex(item) {\n    return this._isDragging ? this._sortStrategy.getItemIndex(item) : this._draggables.indexOf(item);\n  }\n  /**\n   * Whether the list is able to receive the item that\n   * is currently being dragged inside a connected drop list.\n   */\n  isReceiving() {\n    return this._activeSiblings.size > 0;\n  }\n  /**\n   * Sorts an item inside the container based on its position.\n   * @param item Item to be sorted.\n   * @param pointerX Position of the item along the X axis.\n   * @param pointerY Position of the item along the Y axis.\n   * @param pointerDelta Direction in which the pointer is moving along each axis.\n   */\n  _sortItem(item, pointerX, pointerY, pointerDelta) {\n    // Don't sort the item if sorting is disabled or it's out of range.\n    if (this.sortingDisabled || !this._domRect || !isPointerNearDomRect(this._domRect, DROP_PROXIMITY_THRESHOLD, pointerX, pointerY)) {\n      return;\n    }\n    const result = this._sortStrategy.sort(item, pointerX, pointerY, pointerDelta);\n    if (result) {\n      this.sorted.next({\n        previousIndex: result.previousIndex,\n        currentIndex: result.currentIndex,\n        container: this,\n        item\n      });\n    }\n  }\n  /**\n   * Checks whether the user's pointer is close to the edges of either the\n   * viewport or the drop list and starts the auto-scroll sequence.\n   * @param pointerX User's pointer position along the x axis.\n   * @param pointerY User's pointer position along the y axis.\n   */\n  _startScrollingIfNecessary(pointerX, pointerY) {\n    if (this.autoScrollDisabled) {\n      return;\n    }\n    let scrollNode;\n    let verticalScrollDirection = AutoScrollVerticalDirection.NONE;\n    let horizontalScrollDirection = AutoScrollHorizontalDirection.NONE;\n    // Check whether we should start scrolling any of the parent containers.\n    this._parentPositions.positions.forEach((position, element) => {\n      // We have special handling for the `document` below. Also this would be\n      // nicer with a  for...of loop, but it requires changing a compiler flag.\n      if (element === this._document || !position.clientRect || scrollNode) {\n        return;\n      }\n      if (isPointerNearDomRect(position.clientRect, DROP_PROXIMITY_THRESHOLD, pointerX, pointerY)) {\n        [verticalScrollDirection, horizontalScrollDirection] = getElementScrollDirections(element, position.clientRect, this._direction, pointerX, pointerY);\n        if (verticalScrollDirection || horizontalScrollDirection) {\n          scrollNode = element;\n        }\n      }\n    });\n    // Otherwise check if we can start scrolling the viewport.\n    if (!verticalScrollDirection && !horizontalScrollDirection) {\n      const {\n        width,\n        height\n      } = this._viewportRuler.getViewportSize();\n      const domRect = {\n        width,\n        height,\n        top: 0,\n        right: width,\n        bottom: height,\n        left: 0\n      };\n      verticalScrollDirection = getVerticalScrollDirection(domRect, pointerY);\n      horizontalScrollDirection = getHorizontalScrollDirection(domRect, pointerX);\n      scrollNode = window;\n    }\n    if (scrollNode && (verticalScrollDirection !== this._verticalScrollDirection || horizontalScrollDirection !== this._horizontalScrollDirection || scrollNode !== this._scrollNode)) {\n      this._verticalScrollDirection = verticalScrollDirection;\n      this._horizontalScrollDirection = horizontalScrollDirection;\n      this._scrollNode = scrollNode;\n      if ((verticalScrollDirection || horizontalScrollDirection) && scrollNode) {\n        this._ngZone.runOutsideAngular(this._startScrollInterval);\n      } else {\n        this._stopScrolling();\n      }\n    }\n  }\n  /** Stops any currently-running auto-scroll sequences. */\n  _stopScrolling() {\n    this._stopScrollTimers.next();\n  }\n  /** Starts the dragging sequence within the list. */\n  _draggingStarted() {\n    const styles = this._container.style;\n    this.beforeStarted.next();\n    this._isDragging = true;\n    if ((typeof ngDevMode === 'undefined' || ngDevMode) &&\n    // Prevent the check from running on apps not using an alternate container. Ideally we\n    // would always run it, but introducing it at this stage would be a breaking change.\n    this._container !== coerceElement(this.element)) {\n      for (const drag of this._draggables) {\n        if (!drag.isDragging() && drag.getVisibleElement().parentNode !== this._container) {\n          throw new Error('Invalid DOM structure for drop list. All items must be placed directly inside of the element container.');\n        }\n      }\n    }\n    // We need to disable scroll snapping while the user is dragging, because it breaks automatic\n    // scrolling. The browser seems to round the value based on the snapping points which means\n    // that we can't increment/decrement the scroll position.\n    this._initialScrollSnap = styles.msScrollSnapType || styles.scrollSnapType || '';\n    styles.scrollSnapType = styles.msScrollSnapType = 'none';\n    this._sortStrategy.start(this._draggables);\n    this._cacheParentPositions();\n    this._viewportScrollSubscription.unsubscribe();\n    this._listenToScrollEvents();\n  }\n  /** Caches the positions of the configured scrollable parents. */\n  _cacheParentPositions() {\n    this._parentPositions.cache(this._scrollableElements);\n    // The list element is always in the `scrollableElements`\n    // so we can take advantage of the cached `DOMRect`.\n    this._domRect = this._parentPositions.positions.get(this._container).clientRect;\n  }\n  /** Resets the container to its initial state. */\n  _reset() {\n    this._isDragging = false;\n    const styles = this._container.style;\n    styles.scrollSnapType = styles.msScrollSnapType = this._initialScrollSnap;\n    this._siblings.forEach(sibling => sibling._stopReceiving(this));\n    this._sortStrategy.reset();\n    this._stopScrolling();\n    this._viewportScrollSubscription.unsubscribe();\n    this._parentPositions.clear();\n  }\n  /**\n   * Checks whether the user's pointer is positioned over the container.\n   * @param x Pointer position along the X axis.\n   * @param y Pointer position along the Y axis.\n   */\n  _isOverContainer(x, y) {\n    return this._domRect != null && isInsideClientRect(this._domRect, x, y);\n  }\n  /**\n   * Figures out whether an item should be moved into a sibling\n   * drop container, based on its current position.\n   * @param item Drag item that is being moved.\n   * @param x Position of the item along the X axis.\n   * @param y Position of the item along the Y axis.\n   */\n  _getSiblingContainerFromPosition(item, x, y) {\n    return this._siblings.find(sibling => sibling._canReceive(item, x, y));\n  }\n  /**\n   * Checks whether the drop list can receive the passed-in item.\n   * @param item Item that is being dragged into the list.\n   * @param x Position of the item along the X axis.\n   * @param y Position of the item along the Y axis.\n   */\n  _canReceive(item, x, y) {\n    if (!this._domRect || !isInsideClientRect(this._domRect, x, y) || !this.enterPredicate(item, this)) {\n      return false;\n    }\n    const elementFromPoint = this._getShadowRoot().elementFromPoint(x, y);\n    // If there's no element at the pointer position, then\n    // the client rect is probably scrolled out of the view.\n    if (!elementFromPoint) {\n      return false;\n    }\n    // The `DOMRect`, that we're using to find the container over which the user is\n    // hovering, doesn't give us any information on whether the element has been scrolled\n    // out of the view or whether it's overlapping with other containers. This means that\n    // we could end up transferring the item into a container that's invisible or is positioned\n    // below another one. We use the result from `elementFromPoint` to get the top-most element\n    // at the pointer position and to find whether it's one of the intersecting drop containers.\n    return elementFromPoint === this._container || this._container.contains(elementFromPoint);\n  }\n  /**\n   * Called by one of the connected drop lists when a dragging sequence has started.\n   * @param sibling Sibling in which dragging has started.\n   */\n  _startReceiving(sibling, items) {\n    const activeSiblings = this._activeSiblings;\n    if (!activeSiblings.has(sibling) && items.every(item => {\n      // Note that we have to add an exception to the `enterPredicate` for items that started off\n      // in this drop list. The drag ref has logic that allows an item to return to its initial\n      // container, if it has left the initial container and none of the connected containers\n      // allow it to enter. See `DragRef._updateActiveDropContainer` for more context.\n      return this.enterPredicate(item, this) || this._draggables.indexOf(item) > -1;\n    })) {\n      activeSiblings.add(sibling);\n      this._cacheParentPositions();\n      this._listenToScrollEvents();\n      this.receivingStarted.next({\n        initiator: sibling,\n        receiver: this,\n        items\n      });\n    }\n  }\n  /**\n   * Called by a connected drop list when dragging has stopped.\n   * @param sibling Sibling whose dragging has stopped.\n   */\n  _stopReceiving(sibling) {\n    this._activeSiblings.delete(sibling);\n    this._viewportScrollSubscription.unsubscribe();\n    this.receivingStopped.next({\n      initiator: sibling,\n      receiver: this\n    });\n  }\n  /**\n   * Starts listening to scroll events on the viewport.\n   * Used for updating the internal state of the list.\n   */\n  _listenToScrollEvents() {\n    this._viewportScrollSubscription = this._dragDropRegistry.scrolled(this._getShadowRoot()).subscribe(event => {\n      if (this.isDragging()) {\n        const scrollDifference = this._parentPositions.handleScroll(event);\n        if (scrollDifference) {\n          this._sortStrategy.updateOnScroll(scrollDifference.top, scrollDifference.left);\n        }\n      } else if (this.isReceiving()) {\n        this._cacheParentPositions();\n      }\n    });\n  }\n  /**\n   * Lazily resolves and returns the shadow root of the element. We do this in a function, rather\n   * than saving it in property directly on init, because we want to resolve it as late as possible\n   * in order to ensure that the element has been moved into the shadow DOM. Doing it inside the\n   * constructor might be too early if the element is inside of something like `ngFor` or `ngIf`.\n   */\n  _getShadowRoot() {\n    if (!this._cachedShadowRoot) {\n      const shadowRoot = _getShadowRoot(this._container);\n      this._cachedShadowRoot = shadowRoot || this._document;\n    }\n    return this._cachedShadowRoot;\n  }\n  /** Notifies any siblings that may potentially receive the item. */\n  _notifyReceivingSiblings() {\n    const draggedItems = this._sortStrategy.getActiveItemsSnapshot().filter(item => item.isDragging());\n    this._siblings.forEach(sibling => sibling._startReceiving(this, draggedItems));\n  }\n}\n/**\n * Gets whether the vertical auto-scroll direction of a node.\n * @param clientRect Dimensions of the node.\n * @param pointerY Position of the user's pointer along the y axis.\n */\nfunction getVerticalScrollDirection(clientRect, pointerY) {\n  const {\n    top,\n    bottom,\n    height\n  } = clientRect;\n  const yThreshold = height * SCROLL_PROXIMITY_THRESHOLD;\n  if (pointerY >= top - yThreshold && pointerY <= top + yThreshold) {\n    return AutoScrollVerticalDirection.UP;\n  } else if (pointerY >= bottom - yThreshold && pointerY <= bottom + yThreshold) {\n    return AutoScrollVerticalDirection.DOWN;\n  }\n  return AutoScrollVerticalDirection.NONE;\n}\n/**\n * Gets whether the horizontal auto-scroll direction of a node.\n * @param clientRect Dimensions of the node.\n * @param pointerX Position of the user's pointer along the x axis.\n */\nfunction getHorizontalScrollDirection(clientRect, pointerX) {\n  const {\n    left,\n    right,\n    width\n  } = clientRect;\n  const xThreshold = width * SCROLL_PROXIMITY_THRESHOLD;\n  if (pointerX >= left - xThreshold && pointerX <= left + xThreshold) {\n    return AutoScrollHorizontalDirection.LEFT;\n  } else if (pointerX >= right - xThreshold && pointerX <= right + xThreshold) {\n    return AutoScrollHorizontalDirection.RIGHT;\n  }\n  return AutoScrollHorizontalDirection.NONE;\n}\n/**\n * Gets the directions in which an element node should be scrolled,\n * assuming that the user's pointer is already within it scrollable region.\n * @param element Element for which we should calculate the scroll direction.\n * @param clientRect Bounding client rectangle of the element.\n * @param direction Layout direction of the drop list.\n * @param pointerX Position of the user's pointer along the x axis.\n * @param pointerY Position of the user's pointer along the y axis.\n */\nfunction getElementScrollDirections(element, clientRect, direction, pointerX, pointerY) {\n  const computedVertical = getVerticalScrollDirection(clientRect, pointerY);\n  const computedHorizontal = getHorizontalScrollDirection(clientRect, pointerX);\n  let verticalScrollDirection = AutoScrollVerticalDirection.NONE;\n  let horizontalScrollDirection = AutoScrollHorizontalDirection.NONE;\n  // Note that we here we do some extra checks for whether the element is actually scrollable in\n  // a certain direction and we only assign the scroll direction if it is. We do this so that we\n  // can allow other elements to be scrolled, if the current element can't be scrolled anymore.\n  // This allows us to handle cases where the scroll regions of two scrollable elements overlap.\n  if (computedVertical) {\n    const scrollTop = element.scrollTop;\n    if (computedVertical === AutoScrollVerticalDirection.UP) {\n      if (scrollTop > 0) {\n        verticalScrollDirection = AutoScrollVerticalDirection.UP;\n      }\n    } else if (element.scrollHeight - scrollTop > element.clientHeight) {\n      verticalScrollDirection = AutoScrollVerticalDirection.DOWN;\n    }\n  }\n  if (computedHorizontal) {\n    const scrollLeft = element.scrollLeft;\n    if (direction === 'rtl') {\n      if (computedHorizontal === AutoScrollHorizontalDirection.RIGHT) {\n        // In RTL `scrollLeft` will be negative when scrolled.\n        if (scrollLeft < 0) {\n          horizontalScrollDirection = AutoScrollHorizontalDirection.RIGHT;\n        }\n      } else if (element.scrollWidth + scrollLeft > element.clientWidth) {\n        horizontalScrollDirection = AutoScrollHorizontalDirection.LEFT;\n      }\n    } else {\n      if (computedHorizontal === AutoScrollHorizontalDirection.LEFT) {\n        if (scrollLeft > 0) {\n          horizontalScrollDirection = AutoScrollHorizontalDirection.LEFT;\n        }\n      } else if (element.scrollWidth - scrollLeft > element.clientWidth) {\n        horizontalScrollDirection = AutoScrollHorizontalDirection.RIGHT;\n      }\n    }\n  }\n  return [verticalScrollDirection, horizontalScrollDirection];\n}\n\n/** Event options that can be used to bind an active, capturing event. */\nconst activeCapturingEventOptions = normalizePassiveListenerOptions({\n  passive: false,\n  capture: true\n});\n/** Keeps track of the apps currently containing drag items. */\nconst activeApps = new Set();\n/**\n * Component used to load the drag&drop reset styles.\n * @docs-private\n */\nclass _ResetsLoader {\n  static {\n    this.ɵfac = function _ResetsLoader_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || _ResetsLoader)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: _ResetsLoader,\n      selectors: [[\"ng-component\"]],\n      hostAttrs: [\"cdk-drag-resets-container\", \"\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 0,\n      vars: 0,\n      template: function _ResetsLoader_Template(rf, ctx) {},\n      styles: [\"@layer cdk-resets{.cdk-drag-preview{background:none;border:none;padding:0;color:inherit;inset:auto}}.cdk-drag-placeholder *,.cdk-drag-preview *{pointer-events:none !important}\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_ResetsLoader, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      encapsulation: ViewEncapsulation.None,\n      template: '',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        'cdk-drag-resets-container': ''\n      },\n      styles: [\"@layer cdk-resets{.cdk-drag-preview{background:none;border:none;padding:0;color:inherit;inset:auto}}.cdk-drag-placeholder *,.cdk-drag-preview *{pointer-events:none !important}\"]\n    }]\n  }], null, null);\n})();\n// TODO(crisbeto): remove generics when making breaking changes.\n/**\n * Service that keeps track of all the drag item and drop container\n * instances, and manages global event listeners on the `document`.\n * @docs-private\n */\nclass DragDropRegistry {\n  constructor(_ngZone, _document) {\n    this._ngZone = _ngZone;\n    this._appRef = inject(ApplicationRef);\n    this._environmentInjector = inject(EnvironmentInjector);\n    /** Registered drop container instances. */\n    this._dropInstances = new Set();\n    /** Registered drag item instances. */\n    this._dragInstances = new Set();\n    /** Drag item instances that are currently being dragged. */\n    this._activeDragInstances = signal([]);\n    /** Keeps track of the event listeners that we've bound to the `document`. */\n    this._globalListeners = new Map();\n    /**\n     * Predicate function to check if an item is being dragged.  Moved out into a property,\n     * because it'll be called a lot and we don't want to create a new function every time.\n     */\n    this._draggingPredicate = item => item.isDragging();\n    /**\n     * Emits the `touchmove` or `mousemove` events that are dispatched\n     * while the user is dragging a drag item instance.\n     */\n    this.pointerMove = new Subject();\n    /**\n     * Emits the `touchend` or `mouseup` events that are dispatched\n     * while the user is dragging a drag item instance.\n     */\n    this.pointerUp = new Subject();\n    /**\n     * Emits when the viewport has been scrolled while the user is dragging an item.\n     * @deprecated To be turned into a private member. Use the `scrolled` method instead.\n     * @breaking-change 13.0.0\n     */\n    this.scroll = new Subject();\n    /**\n     * Event listener that will prevent the default browser action while the user is dragging.\n     * @param event Event whose default action should be prevented.\n     */\n    this._preventDefaultWhileDragging = event => {\n      if (this._activeDragInstances().length > 0) {\n        event.preventDefault();\n      }\n    };\n    /** Event listener for `touchmove` that is bound even if no dragging is happening. */\n    this._persistentTouchmoveListener = event => {\n      if (this._activeDragInstances().length > 0) {\n        // Note that we only want to prevent the default action after dragging has actually started.\n        // Usually this is the same time at which the item is added to the `_activeDragInstances`,\n        // but it could be pushed back if the user has set up a drag delay or threshold.\n        if (this._activeDragInstances().some(this._draggingPredicate)) {\n          event.preventDefault();\n        }\n        this.pointerMove.next(event);\n      }\n    };\n    this._document = _document;\n  }\n  /** Adds a drop container to the registry. */\n  registerDropContainer(drop) {\n    if (!this._dropInstances.has(drop)) {\n      this._dropInstances.add(drop);\n    }\n  }\n  /** Adds a drag item instance to the registry. */\n  registerDragItem(drag) {\n    this._dragInstances.add(drag);\n    // The `touchmove` event gets bound once, ahead of time, because WebKit\n    // won't preventDefault on a dynamically-added `touchmove` listener.\n    // See https://bugs.webkit.org/show_bug.cgi?id=184250.\n    if (this._dragInstances.size === 1) {\n      this._ngZone.runOutsideAngular(() => {\n        // The event handler has to be explicitly active,\n        // because newer browsers make it passive by default.\n        this._document.addEventListener('touchmove', this._persistentTouchmoveListener, activeCapturingEventOptions);\n      });\n    }\n  }\n  /** Removes a drop container from the registry. */\n  removeDropContainer(drop) {\n    this._dropInstances.delete(drop);\n  }\n  /** Removes a drag item instance from the registry. */\n  removeDragItem(drag) {\n    this._dragInstances.delete(drag);\n    this.stopDragging(drag);\n    if (this._dragInstances.size === 0) {\n      this._document.removeEventListener('touchmove', this._persistentTouchmoveListener, activeCapturingEventOptions);\n    }\n  }\n  /**\n   * Starts the dragging sequence for a drag instance.\n   * @param drag Drag instance which is being dragged.\n   * @param event Event that initiated the dragging.\n   */\n  startDragging(drag, event) {\n    // Do not process the same drag twice to avoid memory leaks and redundant listeners\n    if (this._activeDragInstances().indexOf(drag) > -1) {\n      return;\n    }\n    this._loadResets();\n    this._activeDragInstances.update(instances => [...instances, drag]);\n    if (this._activeDragInstances().length === 1) {\n      const isTouchEvent = event.type.startsWith('touch');\n      // We explicitly bind __active__ listeners here, because newer browsers will default to\n      // passive ones for `mousemove` and `touchmove`. The events need to be active, because we\n      // use `preventDefault` to prevent the page from scrolling while the user is dragging.\n      this._globalListeners.set(isTouchEvent ? 'touchend' : 'mouseup', {\n        handler: e => this.pointerUp.next(e),\n        options: true\n      }).set('scroll', {\n        handler: e => this.scroll.next(e),\n        // Use capturing so that we pick up scroll changes in any scrollable nodes that aren't\n        // the document. See https://github.com/angular/components/issues/17144.\n        options: true\n      })\n      // Preventing the default action on `mousemove` isn't enough to disable text selection\n      // on Safari so we need to prevent the selection event as well. Alternatively this can\n      // be done by setting `user-select: none` on the `body`, however it has causes a style\n      // recalculation which can be expensive on pages with a lot of elements.\n      .set('selectstart', {\n        handler: this._preventDefaultWhileDragging,\n        options: activeCapturingEventOptions\n      });\n      // We don't have to bind a move event for touch drag sequences, because\n      // we already have a persistent global one bound from `registerDragItem`.\n      if (!isTouchEvent) {\n        this._globalListeners.set('mousemove', {\n          handler: e => this.pointerMove.next(e),\n          options: activeCapturingEventOptions\n        });\n      }\n      this._ngZone.runOutsideAngular(() => {\n        this._globalListeners.forEach((config, name) => {\n          this._document.addEventListener(name, config.handler, config.options);\n        });\n      });\n    }\n  }\n  /** Stops dragging a drag item instance. */\n  stopDragging(drag) {\n    this._activeDragInstances.update(instances => {\n      const index = instances.indexOf(drag);\n      if (index > -1) {\n        instances.splice(index, 1);\n        return [...instances];\n      }\n      return instances;\n    });\n    if (this._activeDragInstances().length === 0) {\n      this._clearGlobalListeners();\n    }\n  }\n  /** Gets whether a drag item instance is currently being dragged. */\n  isDragging(drag) {\n    return this._activeDragInstances().indexOf(drag) > -1;\n  }\n  /**\n   * Gets a stream that will emit when any element on the page is scrolled while an item is being\n   * dragged.\n   * @param shadowRoot Optional shadow root that the current dragging sequence started from.\n   *   Top-level listeners won't pick up events coming from the shadow DOM so this parameter can\n   *   be used to include an additional top-level listener at the shadow root level.\n   */\n  scrolled(shadowRoot) {\n    const streams = [this.scroll];\n    if (shadowRoot && shadowRoot !== this._document) {\n      // Note that this is basically the same as `fromEvent` from rxjs, but we do it ourselves,\n      // because we want to guarantee that the event is bound outside of the `NgZone`. With\n      // `fromEvent` it'll only happen if the subscription is outside the `NgZone`.\n      streams.push(new Observable(observer => {\n        return this._ngZone.runOutsideAngular(() => {\n          const eventOptions = true;\n          const callback = event => {\n            if (this._activeDragInstances().length) {\n              observer.next(event);\n            }\n          };\n          shadowRoot.addEventListener('scroll', callback, eventOptions);\n          return () => {\n            shadowRoot.removeEventListener('scroll', callback, eventOptions);\n          };\n        });\n      }));\n    }\n    return merge(...streams);\n  }\n  ngOnDestroy() {\n    this._dragInstances.forEach(instance => this.removeDragItem(instance));\n    this._dropInstances.forEach(instance => this.removeDropContainer(instance));\n    this._clearGlobalListeners();\n    this.pointerMove.complete();\n    this.pointerUp.complete();\n  }\n  /** Clears out the global event listeners from the `document`. */\n  _clearGlobalListeners() {\n    this._globalListeners.forEach((config, name) => {\n      this._document.removeEventListener(name, config.handler, config.options);\n    });\n    this._globalListeners.clear();\n  }\n  // TODO(crisbeto): abstract this away into something reusable.\n  /** Loads the CSS resets needed for the module to work correctly. */\n  _loadResets() {\n    if (!activeApps.has(this._appRef)) {\n      activeApps.add(this._appRef);\n      const componentRef = createComponent(_ResetsLoader, {\n        environmentInjector: this._environmentInjector\n      });\n      this._appRef.onDestroy(() => {\n        activeApps.delete(this._appRef);\n        if (activeApps.size === 0) {\n          componentRef.destroy();\n        }\n      });\n    }\n  }\n  static {\n    this.ɵfac = function DragDropRegistry_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DragDropRegistry)(i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(DOCUMENT));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: DragDropRegistry,\n      factory: DragDropRegistry.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DragDropRegistry, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i0.NgZone\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\n\n/** Default configuration to be used when creating a `DragRef`. */\nconst DEFAULT_CONFIG = {\n  dragStartThreshold: 5,\n  pointerDirectionChangeThreshold: 5\n};\n/**\n * Service that allows for drag-and-drop functionality to be attached to DOM elements.\n */\nclass DragDrop {\n  constructor(_document, _ngZone, _viewportRuler, _dragDropRegistry) {\n    this._document = _document;\n    this._ngZone = _ngZone;\n    this._viewportRuler = _viewportRuler;\n    this._dragDropRegistry = _dragDropRegistry;\n  }\n  /**\n   * Turns an element into a draggable item.\n   * @param element Element to which to attach the dragging functionality.\n   * @param config Object used to configure the dragging behavior.\n   */\n  createDrag(element, config = DEFAULT_CONFIG) {\n    return new DragRef(element, config, this._document, this._ngZone, this._viewportRuler, this._dragDropRegistry);\n  }\n  /**\n   * Turns an element into a drop list.\n   * @param element Element to which to attach the drop list functionality.\n   */\n  createDropList(element) {\n    return new DropListRef(element, this._dragDropRegistry, this._document, this._ngZone, this._viewportRuler);\n  }\n  static {\n    this.ɵfac = function DragDrop_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DragDrop)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(i1.ViewportRuler), i0.ɵɵinject(DragDropRegistry));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: DragDrop,\n      factory: DragDrop.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DragDrop, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: i0.NgZone\n  }, {\n    type: i1.ViewportRuler\n  }, {\n    type: DragDropRegistry\n  }], null);\n})();\n\n/**\n * Injection token that can be used for a `CdkDrag` to provide itself as a parent to the\n * drag-specific child directive (`CdkDragHandle`, `CdkDragPreview` etc.). Used primarily\n * to avoid circular imports.\n * @docs-private\n */\nconst CDK_DRAG_PARENT = new InjectionToken('CDK_DRAG_PARENT');\n\n/**\n * Asserts that a particular node is an element.\n * @param node Node to be checked.\n * @param name Name to attach to the error message.\n */\nfunction assertElementNode(node, name) {\n  if (node.nodeType !== 1) {\n    throw Error(`${name} must be attached to an element node. ` + `Currently attached to \"${node.nodeName}\".`);\n  }\n}\n\n/**\n * Injection token that can be used to reference instances of `CdkDragHandle`. It serves as\n * alternative token to the actual `CdkDragHandle` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst CDK_DRAG_HANDLE = new InjectionToken('CdkDragHandle');\n/** Handle that can be used to drag a CdkDrag instance. */\nclass CdkDragHandle {\n  /** Whether starting to drag through this handle is disabled. */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value) {\n    this._disabled = value;\n    this._stateChanges.next(this);\n  }\n  constructor(element, _parentDrag) {\n    this.element = element;\n    this._parentDrag = _parentDrag;\n    /** Emits when the state of the handle has changed. */\n    this._stateChanges = new Subject();\n    this._disabled = false;\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      assertElementNode(element.nativeElement, 'cdkDragHandle');\n    }\n    _parentDrag?._addHandle(this);\n  }\n  ngOnDestroy() {\n    this._parentDrag?._removeHandle(this);\n    this._stateChanges.complete();\n  }\n  static {\n    this.ɵfac = function CdkDragHandle_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkDragHandle)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(CDK_DRAG_PARENT, 12));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkDragHandle,\n      selectors: [[\"\", \"cdkDragHandle\", \"\"]],\n      hostAttrs: [1, \"cdk-drag-handle\"],\n      inputs: {\n        disabled: [2, \"cdkDragHandleDisabled\", \"disabled\", booleanAttribute]\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CDK_DRAG_HANDLE,\n        useExisting: CdkDragHandle\n      }]), i0.ɵɵInputTransformsFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkDragHandle, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkDragHandle]',\n      standalone: true,\n      host: {\n        'class': 'cdk-drag-handle'\n      },\n      providers: [{\n        provide: CDK_DRAG_HANDLE,\n        useExisting: CdkDragHandle\n      }]\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [CDK_DRAG_PARENT]\n    }, {\n      type: Optional\n    }, {\n      type: SkipSelf\n    }]\n  }], {\n    disabled: [{\n      type: Input,\n      args: [{\n        alias: 'cdkDragHandleDisabled',\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n\n/**\n * Injection token that can be used to configure the\n * behavior of the drag&drop-related components.\n */\nconst CDK_DRAG_CONFIG = new InjectionToken('CDK_DRAG_CONFIG');\nconst DRAG_HOST_CLASS = 'cdk-drag';\n/**\n * Injection token that can be used to reference instances of `CdkDropList`. It serves as\n * alternative token to the actual `CdkDropList` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst CDK_DROP_LIST = new InjectionToken('CdkDropList');\n/** Element that can be moved inside a CdkDropList container. */\nclass CdkDrag {\n  static {\n    this._dragInstances = [];\n  }\n  /** Whether starting to drag this element is disabled. */\n  get disabled() {\n    return this._disabled || this.dropContainer && this.dropContainer.disabled;\n  }\n  set disabled(value) {\n    this._disabled = value;\n    this._dragRef.disabled = this._disabled;\n  }\n  constructor(/** Element that the draggable is attached to. */\n  element, /** Droppable container that the draggable is a part of. */\n  dropContainer,\n  /**\n   * @deprecated `_document` parameter no longer being used and will be removed.\n   * @breaking-change 12.0.0\n   */\n  _document, _ngZone, _viewContainerRef, config, _dir, dragDrop, _changeDetectorRef, _selfHandle, _parentDrag) {\n    this.element = element;\n    this.dropContainer = dropContainer;\n    this._ngZone = _ngZone;\n    this._viewContainerRef = _viewContainerRef;\n    this._dir = _dir;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._selfHandle = _selfHandle;\n    this._parentDrag = _parentDrag;\n    this._destroyed = new Subject();\n    this._handles = new BehaviorSubject([]);\n    /**\n     * If the parent of the dragged element has a `scale` transform, it can throw off the\n     * positioning when the user starts dragging. Use this input to notify the CDK of the scale.\n     */\n    this.scale = 1;\n    /** Emits when the user starts dragging the item. */\n    this.started = new EventEmitter();\n    /** Emits when the user has released a drag item, before any animations have started. */\n    this.released = new EventEmitter();\n    /** Emits when the user stops dragging an item in the container. */\n    this.ended = new EventEmitter();\n    /** Emits when the user has moved the item into a new container. */\n    this.entered = new EventEmitter();\n    /** Emits when the user removes the item its container by dragging it into another container. */\n    this.exited = new EventEmitter();\n    /** Emits when the user drops the item inside a container. */\n    this.dropped = new EventEmitter();\n    /**\n     * Emits as the user is dragging the item. Use with caution,\n     * because this event will fire for every pixel that the user has dragged.\n     */\n    this.moved = new Observable(observer => {\n      const subscription = this._dragRef.moved.pipe(map(movedEvent => ({\n        source: this,\n        pointerPosition: movedEvent.pointerPosition,\n        event: movedEvent.event,\n        delta: movedEvent.delta,\n        distance: movedEvent.distance\n      }))).subscribe(observer);\n      return () => {\n        subscription.unsubscribe();\n      };\n    });\n    this._injector = inject(Injector);\n    this._dragRef = dragDrop.createDrag(element, {\n      dragStartThreshold: config && config.dragStartThreshold != null ? config.dragStartThreshold : 5,\n      pointerDirectionChangeThreshold: config && config.pointerDirectionChangeThreshold != null ? config.pointerDirectionChangeThreshold : 5,\n      zIndex: config?.zIndex\n    });\n    this._dragRef.data = this;\n    // We have to keep track of the drag instances in order to be able to match an element to\n    // a drag instance. We can't go through the global registry of `DragRef`, because the root\n    // element could be different.\n    CdkDrag._dragInstances.push(this);\n    if (config) {\n      this._assignDefaults(config);\n    }\n    // Note that usually the container is assigned when the drop list is picks up the item, but in\n    // some cases (mainly transplanted views with OnPush, see #18341) we may end up in a situation\n    // where there are no items on the first change detection pass, but the items get picked up as\n    // soon as the user triggers another pass by dragging. This is a problem, because the item would\n    // have to switch from standalone mode to drag mode in the middle of the dragging sequence which\n    // is too late since the two modes save different kinds of information. We work around it by\n    // assigning the drop container both from here and the list.\n    if (dropContainer) {\n      this._dragRef._withDropContainer(dropContainer._dropListRef);\n      dropContainer.addItem(this);\n      // The drop container reads this so we need to sync it here.\n      dropContainer._dropListRef.beforeStarted.pipe(takeUntil(this._destroyed)).subscribe(() => {\n        this._dragRef.scale = this.scale;\n      });\n    }\n    this._syncInputs(this._dragRef);\n    this._handleEvents(this._dragRef);\n  }\n  /**\n   * Returns the element that is being used as a placeholder\n   * while the current element is being dragged.\n   */\n  getPlaceholderElement() {\n    return this._dragRef.getPlaceholderElement();\n  }\n  /** Returns the root draggable element. */\n  getRootElement() {\n    return this._dragRef.getRootElement();\n  }\n  /** Resets a standalone drag item to its initial position. */\n  reset() {\n    this._dragRef.reset();\n  }\n  /**\n   * Gets the pixel coordinates of the draggable outside of a drop container.\n   */\n  getFreeDragPosition() {\n    return this._dragRef.getFreeDragPosition();\n  }\n  /**\n   * Sets the current position in pixels the draggable outside of a drop container.\n   * @param value New position to be set.\n   */\n  setFreeDragPosition(value) {\n    this._dragRef.setFreeDragPosition(value);\n  }\n  ngAfterViewInit() {\n    // We need to wait until after render, in order for the reference\n    // element to be in the proper place in the DOM. This is mostly relevant\n    // for draggable elements inside portals since they get stamped out in\n    // their original DOM position, and then they get transferred to the portal.\n    afterNextRender(() => {\n      this._updateRootElement();\n      this._setupHandlesListener();\n      this._dragRef.scale = this.scale;\n      if (this.freeDragPosition) {\n        this._dragRef.setFreeDragPosition(this.freeDragPosition);\n      }\n    }, {\n      injector: this._injector\n    });\n  }\n  ngOnChanges(changes) {\n    const rootSelectorChange = changes['rootElementSelector'];\n    const positionChange = changes['freeDragPosition'];\n    // We don't have to react to the first change since it's being\n    // handled in the `afterNextRender` queued up in the constructor.\n    if (rootSelectorChange && !rootSelectorChange.firstChange) {\n      this._updateRootElement();\n    }\n    // Scale affects the free drag position so we need to sync it up here.\n    this._dragRef.scale = this.scale;\n    // Skip the first change since it's being handled in the `afterNextRender` queued up in the\n    // constructor.\n    if (positionChange && !positionChange.firstChange && this.freeDragPosition) {\n      this._dragRef.setFreeDragPosition(this.freeDragPosition);\n    }\n  }\n  ngOnDestroy() {\n    if (this.dropContainer) {\n      this.dropContainer.removeItem(this);\n    }\n    const index = CdkDrag._dragInstances.indexOf(this);\n    if (index > -1) {\n      CdkDrag._dragInstances.splice(index, 1);\n    }\n    // Unnecessary in most cases, but used to avoid extra change detections with `zone-paths-rxjs`.\n    this._ngZone.runOutsideAngular(() => {\n      this._handles.complete();\n      this._destroyed.next();\n      this._destroyed.complete();\n      this._dragRef.dispose();\n    });\n  }\n  _addHandle(handle) {\n    const handles = this._handles.getValue();\n    handles.push(handle);\n    this._handles.next(handles);\n  }\n  _removeHandle(handle) {\n    const handles = this._handles.getValue();\n    const index = handles.indexOf(handle);\n    if (index > -1) {\n      handles.splice(index, 1);\n      this._handles.next(handles);\n    }\n  }\n  _setPreviewTemplate(preview) {\n    this._previewTemplate = preview;\n  }\n  _resetPreviewTemplate(preview) {\n    if (preview === this._previewTemplate) {\n      this._previewTemplate = null;\n    }\n  }\n  _setPlaceholderTemplate(placeholder) {\n    this._placeholderTemplate = placeholder;\n  }\n  _resetPlaceholderTemplate(placeholder) {\n    if (placeholder === this._placeholderTemplate) {\n      this._placeholderTemplate = null;\n    }\n  }\n  /** Syncs the root element with the `DragRef`. */\n  _updateRootElement() {\n    const element = this.element.nativeElement;\n    let rootElement = element;\n    if (this.rootElementSelector) {\n      rootElement = element.closest !== undefined ? element.closest(this.rootElementSelector) :\n      // Comment tag doesn't have closest method, so use parent's one.\n      element.parentElement?.closest(this.rootElementSelector);\n    }\n    if (rootElement && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      assertElementNode(rootElement, 'cdkDrag');\n    }\n    this._dragRef.withRootElement(rootElement || element);\n  }\n  /** Gets the boundary element, based on the `boundaryElement` value. */\n  _getBoundaryElement() {\n    const boundary = this.boundaryElement;\n    if (!boundary) {\n      return null;\n    }\n    if (typeof boundary === 'string') {\n      return this.element.nativeElement.closest(boundary);\n    }\n    return coerceElement(boundary);\n  }\n  /** Syncs the inputs of the CdkDrag with the options of the underlying DragRef. */\n  _syncInputs(ref) {\n    ref.beforeStarted.subscribe(() => {\n      if (!ref.isDragging()) {\n        const dir = this._dir;\n        const dragStartDelay = this.dragStartDelay;\n        const placeholder = this._placeholderTemplate ? {\n          template: this._placeholderTemplate.templateRef,\n          context: this._placeholderTemplate.data,\n          viewContainer: this._viewContainerRef\n        } : null;\n        const preview = this._previewTemplate ? {\n          template: this._previewTemplate.templateRef,\n          context: this._previewTemplate.data,\n          matchSize: this._previewTemplate.matchSize,\n          viewContainer: this._viewContainerRef\n        } : null;\n        ref.disabled = this.disabled;\n        ref.lockAxis = this.lockAxis;\n        ref.scale = this.scale;\n        ref.dragStartDelay = typeof dragStartDelay === 'object' && dragStartDelay ? dragStartDelay : coerceNumberProperty(dragStartDelay);\n        ref.constrainPosition = this.constrainPosition;\n        ref.previewClass = this.previewClass;\n        ref.withBoundaryElement(this._getBoundaryElement()).withPlaceholderTemplate(placeholder).withPreviewTemplate(preview).withPreviewContainer(this.previewContainer || 'global');\n        if (dir) {\n          ref.withDirection(dir.value);\n        }\n      }\n    });\n    // This only needs to be resolved once.\n    ref.beforeStarted.pipe(take(1)).subscribe(() => {\n      // If we managed to resolve a parent through DI, use it.\n      if (this._parentDrag) {\n        ref.withParent(this._parentDrag._dragRef);\n        return;\n      }\n      // Otherwise fall back to resolving the parent by looking up the DOM. This can happen if\n      // the item was projected into another item by something like `ngTemplateOutlet`.\n      let parent = this.element.nativeElement.parentElement;\n      while (parent) {\n        if (parent.classList.contains(DRAG_HOST_CLASS)) {\n          ref.withParent(CdkDrag._dragInstances.find(drag => {\n            return drag.element.nativeElement === parent;\n          })?._dragRef || null);\n          break;\n        }\n        parent = parent.parentElement;\n      }\n    });\n  }\n  /** Handles the events from the underlying `DragRef`. */\n  _handleEvents(ref) {\n    ref.started.subscribe(startEvent => {\n      this.started.emit({\n        source: this,\n        event: startEvent.event\n      });\n      // Since all of these events run outside of change detection,\n      // we need to ensure that everything is marked correctly.\n      this._changeDetectorRef.markForCheck();\n    });\n    ref.released.subscribe(releaseEvent => {\n      this.released.emit({\n        source: this,\n        event: releaseEvent.event\n      });\n    });\n    ref.ended.subscribe(endEvent => {\n      this.ended.emit({\n        source: this,\n        distance: endEvent.distance,\n        dropPoint: endEvent.dropPoint,\n        event: endEvent.event\n      });\n      // Since all of these events run outside of change detection,\n      // we need to ensure that everything is marked correctly.\n      this._changeDetectorRef.markForCheck();\n    });\n    ref.entered.subscribe(enterEvent => {\n      this.entered.emit({\n        container: enterEvent.container.data,\n        item: this,\n        currentIndex: enterEvent.currentIndex\n      });\n    });\n    ref.exited.subscribe(exitEvent => {\n      this.exited.emit({\n        container: exitEvent.container.data,\n        item: this\n      });\n    });\n    ref.dropped.subscribe(dropEvent => {\n      this.dropped.emit({\n        previousIndex: dropEvent.previousIndex,\n        currentIndex: dropEvent.currentIndex,\n        previousContainer: dropEvent.previousContainer.data,\n        container: dropEvent.container.data,\n        isPointerOverContainer: dropEvent.isPointerOverContainer,\n        item: this,\n        distance: dropEvent.distance,\n        dropPoint: dropEvent.dropPoint,\n        event: dropEvent.event\n      });\n    });\n  }\n  /** Assigns the default input values based on a provided config object. */\n  _assignDefaults(config) {\n    const {\n      lockAxis,\n      dragStartDelay,\n      constrainPosition,\n      previewClass,\n      boundaryElement,\n      draggingDisabled,\n      rootElementSelector,\n      previewContainer\n    } = config;\n    this.disabled = draggingDisabled == null ? false : draggingDisabled;\n    this.dragStartDelay = dragStartDelay || 0;\n    if (lockAxis) {\n      this.lockAxis = lockAxis;\n    }\n    if (constrainPosition) {\n      this.constrainPosition = constrainPosition;\n    }\n    if (previewClass) {\n      this.previewClass = previewClass;\n    }\n    if (boundaryElement) {\n      this.boundaryElement = boundaryElement;\n    }\n    if (rootElementSelector) {\n      this.rootElementSelector = rootElementSelector;\n    }\n    if (previewContainer) {\n      this.previewContainer = previewContainer;\n    }\n  }\n  /** Sets up the listener that syncs the handles with the drag ref. */\n  _setupHandlesListener() {\n    // Listen for any newly-added handles.\n    this._handles.pipe(\n    // Sync the new handles with the DragRef.\n    tap(handles => {\n      const handleElements = handles.map(handle => handle.element);\n      // Usually handles are only allowed to be a descendant of the drag element, but if\n      // the consumer defined a different drag root, we should allow the drag element\n      // itself to be a handle too.\n      if (this._selfHandle && this.rootElementSelector) {\n        handleElements.push(this.element);\n      }\n      this._dragRef.withHandles(handleElements);\n    }),\n    // Listen if the state of any of the handles changes.\n    switchMap(handles => {\n      return merge(...handles.map(item => item._stateChanges.pipe(startWith(item))));\n    }), takeUntil(this._destroyed)).subscribe(handleInstance => {\n      // Enabled/disable the handle that changed in the DragRef.\n      const dragRef = this._dragRef;\n      const handle = handleInstance.element.nativeElement;\n      handleInstance.disabled ? dragRef.disableHandle(handle) : dragRef.enableHandle(handle);\n    });\n  }\n  static {\n    this.ɵfac = function CdkDrag_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkDrag)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(CDK_DROP_LIST, 12), i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(CDK_DRAG_CONFIG, 8), i0.ɵɵdirectiveInject(i1$1.Directionality, 8), i0.ɵɵdirectiveInject(DragDrop), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(CDK_DRAG_HANDLE, 10), i0.ɵɵdirectiveInject(CDK_DRAG_PARENT, 12));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkDrag,\n      selectors: [[\"\", \"cdkDrag\", \"\"]],\n      hostAttrs: [1, \"cdk-drag\"],\n      hostVars: 4,\n      hostBindings: function CdkDrag_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"cdk-drag-disabled\", ctx.disabled)(\"cdk-drag-dragging\", ctx._dragRef.isDragging());\n        }\n      },\n      inputs: {\n        data: [0, \"cdkDragData\", \"data\"],\n        lockAxis: [0, \"cdkDragLockAxis\", \"lockAxis\"],\n        rootElementSelector: [0, \"cdkDragRootElement\", \"rootElementSelector\"],\n        boundaryElement: [0, \"cdkDragBoundary\", \"boundaryElement\"],\n        dragStartDelay: [0, \"cdkDragStartDelay\", \"dragStartDelay\"],\n        freeDragPosition: [0, \"cdkDragFreeDragPosition\", \"freeDragPosition\"],\n        disabled: [2, \"cdkDragDisabled\", \"disabled\", booleanAttribute],\n        constrainPosition: [0, \"cdkDragConstrainPosition\", \"constrainPosition\"],\n        previewClass: [0, \"cdkDragPreviewClass\", \"previewClass\"],\n        previewContainer: [0, \"cdkDragPreviewContainer\", \"previewContainer\"],\n        scale: [2, \"cdkDragScale\", \"scale\", numberAttribute]\n      },\n      outputs: {\n        started: \"cdkDragStarted\",\n        released: \"cdkDragReleased\",\n        ended: \"cdkDragEnded\",\n        entered: \"cdkDragEntered\",\n        exited: \"cdkDragExited\",\n        dropped: \"cdkDragDropped\",\n        moved: \"cdkDragMoved\"\n      },\n      exportAs: [\"cdkDrag\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CDK_DRAG_PARENT,\n        useExisting: CdkDrag\n      }]), i0.ɵɵInputTransformsFeature, i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkDrag, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkDrag]',\n      exportAs: 'cdkDrag',\n      standalone: true,\n      host: {\n        'class': DRAG_HOST_CLASS,\n        '[class.cdk-drag-disabled]': 'disabled',\n        '[class.cdk-drag-dragging]': '_dragRef.isDragging()'\n      },\n      providers: [{\n        provide: CDK_DRAG_PARENT,\n        useExisting: CdkDrag\n      }]\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [CDK_DROP_LIST]\n    }, {\n      type: Optional\n    }, {\n      type: SkipSelf\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: i0.NgZone\n  }, {\n    type: i0.ViewContainerRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [CDK_DRAG_CONFIG]\n    }]\n  }, {\n    type: i1$1.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: DragDrop\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: CdkDragHandle,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Self\n    }, {\n      type: Inject,\n      args: [CDK_DRAG_HANDLE]\n    }]\n  }, {\n    type: CdkDrag,\n    decorators: [{\n      type: Optional\n    }, {\n      type: SkipSelf\n    }, {\n      type: Inject,\n      args: [CDK_DRAG_PARENT]\n    }]\n  }], {\n    data: [{\n      type: Input,\n      args: ['cdkDragData']\n    }],\n    lockAxis: [{\n      type: Input,\n      args: ['cdkDragLockAxis']\n    }],\n    rootElementSelector: [{\n      type: Input,\n      args: ['cdkDragRootElement']\n    }],\n    boundaryElement: [{\n      type: Input,\n      args: ['cdkDragBoundary']\n    }],\n    dragStartDelay: [{\n      type: Input,\n      args: ['cdkDragStartDelay']\n    }],\n    freeDragPosition: [{\n      type: Input,\n      args: ['cdkDragFreeDragPosition']\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        alias: 'cdkDragDisabled',\n        transform: booleanAttribute\n      }]\n    }],\n    constrainPosition: [{\n      type: Input,\n      args: ['cdkDragConstrainPosition']\n    }],\n    previewClass: [{\n      type: Input,\n      args: ['cdkDragPreviewClass']\n    }],\n    previewContainer: [{\n      type: Input,\n      args: ['cdkDragPreviewContainer']\n    }],\n    scale: [{\n      type: Input,\n      args: [{\n        alias: 'cdkDragScale',\n        transform: numberAttribute\n      }]\n    }],\n    started: [{\n      type: Output,\n      args: ['cdkDragStarted']\n    }],\n    released: [{\n      type: Output,\n      args: ['cdkDragReleased']\n    }],\n    ended: [{\n      type: Output,\n      args: ['cdkDragEnded']\n    }],\n    entered: [{\n      type: Output,\n      args: ['cdkDragEntered']\n    }],\n    exited: [{\n      type: Output,\n      args: ['cdkDragExited']\n    }],\n    dropped: [{\n      type: Output,\n      args: ['cdkDragDropped']\n    }],\n    moved: [{\n      type: Output,\n      args: ['cdkDragMoved']\n    }]\n  });\n})();\n\n/**\n * Injection token that can be used to reference instances of `CdkDropListGroup`. It serves as\n * alternative token to the actual `CdkDropListGroup` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst CDK_DROP_LIST_GROUP = new InjectionToken('CdkDropListGroup');\n/**\n * Declaratively connects sibling `cdkDropList` instances together. All of the `cdkDropList`\n * elements that are placed inside a `cdkDropListGroup` will be connected to each other\n * automatically. Can be used as an alternative to the `cdkDropListConnectedTo` input\n * from `cdkDropList`.\n */\nclass CdkDropListGroup {\n  constructor() {\n    /** Drop lists registered inside the group. */\n    this._items = new Set();\n    /** Whether starting a dragging sequence from inside this group is disabled. */\n    this.disabled = false;\n  }\n  ngOnDestroy() {\n    this._items.clear();\n  }\n  static {\n    this.ɵfac = function CdkDropListGroup_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkDropListGroup)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkDropListGroup,\n      selectors: [[\"\", \"cdkDropListGroup\", \"\"]],\n      inputs: {\n        disabled: [2, \"cdkDropListGroupDisabled\", \"disabled\", booleanAttribute]\n      },\n      exportAs: [\"cdkDropListGroup\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CDK_DROP_LIST_GROUP,\n        useExisting: CdkDropListGroup\n      }]), i0.ɵɵInputTransformsFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkDropListGroup, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkDropListGroup]',\n      exportAs: 'cdkDropListGroup',\n      standalone: true,\n      providers: [{\n        provide: CDK_DROP_LIST_GROUP,\n        useExisting: CdkDropListGroup\n      }]\n    }]\n  }], null, {\n    disabled: [{\n      type: Input,\n      args: [{\n        alias: 'cdkDropListGroupDisabled',\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n\n/** Counter used to generate unique ids for drop zones. */\nlet _uniqueIdCounter = 0;\n/** Container that wraps a set of draggable items. */\nclass CdkDropList {\n  /** Keeps track of the drop lists that are currently on the page. */\n  static {\n    this._dropLists = [];\n  }\n  /** Whether starting a dragging sequence from this container is disabled. */\n  get disabled() {\n    return this._disabled || !!this._group && this._group.disabled;\n  }\n  set disabled(value) {\n    // Usually we sync the directive and ref state right before dragging starts, in order to have\n    // a single point of failure and to avoid having to use setters for everything. `disabled` is\n    // a special case, because it can prevent the `beforeStarted` event from firing, which can lock\n    // the user in a disabled state, so we also need to sync it as it's being set.\n    this._dropListRef.disabled = this._disabled = value;\n  }\n  constructor(/** Element that the drop list is attached to. */\n  element, dragDrop, _changeDetectorRef, _scrollDispatcher, _dir, _group, config) {\n    this.element = element;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._scrollDispatcher = _scrollDispatcher;\n    this._dir = _dir;\n    this._group = _group;\n    /** Emits when the list has been destroyed. */\n    this._destroyed = new Subject();\n    /**\n     * Other draggable containers that this container is connected to and into which the\n     * container's items can be transferred. Can either be references to other drop containers,\n     * or their unique IDs.\n     */\n    this.connectedTo = [];\n    /**\n     * Unique ID for the drop zone. Can be used as a reference\n     * in the `connectedTo` of another `CdkDropList`.\n     */\n    this.id = `cdk-drop-list-${_uniqueIdCounter++}`;\n    /**\n     * Function that is used to determine whether an item\n     * is allowed to be moved into a drop container.\n     */\n    this.enterPredicate = () => true;\n    /** Functions that is used to determine whether an item can be sorted into a particular index. */\n    this.sortPredicate = () => true;\n    /** Emits when the user drops an item inside the container. */\n    this.dropped = new EventEmitter();\n    /**\n     * Emits when the user has moved a new drag item into this container.\n     */\n    this.entered = new EventEmitter();\n    /**\n     * Emits when the user removes an item from the container\n     * by dragging it into another container.\n     */\n    this.exited = new EventEmitter();\n    /** Emits as the user is swapping items while actively dragging. */\n    this.sorted = new EventEmitter();\n    /**\n     * Keeps track of the items that are registered with this container. Historically we used to\n     * do this with a `ContentChildren` query, however queries don't handle transplanted views very\n     * well which means that we can't handle cases like dragging the headers of a `mat-table`\n     * correctly. What we do instead is to have the items register themselves with the container\n     * and then we sort them based on their position in the DOM.\n     */\n    this._unsortedItems = new Set();\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      assertElementNode(element.nativeElement, 'cdkDropList');\n    }\n    this._dropListRef = dragDrop.createDropList(element);\n    this._dropListRef.data = this;\n    if (config) {\n      this._assignDefaults(config);\n    }\n    this._dropListRef.enterPredicate = (drag, drop) => {\n      return this.enterPredicate(drag.data, drop.data);\n    };\n    this._dropListRef.sortPredicate = (index, drag, drop) => {\n      return this.sortPredicate(index, drag.data, drop.data);\n    };\n    this._setupInputSyncSubscription(this._dropListRef);\n    this._handleEvents(this._dropListRef);\n    CdkDropList._dropLists.push(this);\n    if (_group) {\n      _group._items.add(this);\n    }\n  }\n  /** Registers an items with the drop list. */\n  addItem(item) {\n    this._unsortedItems.add(item);\n    if (this._dropListRef.isDragging()) {\n      this._syncItemsWithRef();\n    }\n  }\n  /** Removes an item from the drop list. */\n  removeItem(item) {\n    this._unsortedItems.delete(item);\n    if (this._dropListRef.isDragging()) {\n      this._syncItemsWithRef();\n    }\n  }\n  /** Gets the registered items in the list, sorted by their position in the DOM. */\n  getSortedItems() {\n    return Array.from(this._unsortedItems).sort((a, b) => {\n      const documentPosition = a._dragRef.getVisibleElement().compareDocumentPosition(b._dragRef.getVisibleElement());\n      // `compareDocumentPosition` returns a bitmask so we have to use a bitwise operator.\n      // https://developer.mozilla.org/en-US/docs/Web/API/Node/compareDocumentPosition\n      // tslint:disable-next-line:no-bitwise\n      return documentPosition & Node.DOCUMENT_POSITION_FOLLOWING ? -1 : 1;\n    });\n  }\n  ngOnDestroy() {\n    const index = CdkDropList._dropLists.indexOf(this);\n    if (index > -1) {\n      CdkDropList._dropLists.splice(index, 1);\n    }\n    if (this._group) {\n      this._group._items.delete(this);\n    }\n    this._unsortedItems.clear();\n    this._dropListRef.dispose();\n    this._destroyed.next();\n    this._destroyed.complete();\n  }\n  /** Syncs the inputs of the CdkDropList with the options of the underlying DropListRef. */\n  _setupInputSyncSubscription(ref) {\n    if (this._dir) {\n      this._dir.change.pipe(startWith(this._dir.value), takeUntil(this._destroyed)).subscribe(value => ref.withDirection(value));\n    }\n    ref.beforeStarted.subscribe(() => {\n      const siblings = coerceArray(this.connectedTo).map(drop => {\n        if (typeof drop === 'string') {\n          const correspondingDropList = CdkDropList._dropLists.find(list => list.id === drop);\n          if (!correspondingDropList && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            console.warn(`CdkDropList could not find connected drop list with id \"${drop}\"`);\n          }\n          return correspondingDropList;\n        }\n        return drop;\n      });\n      if (this._group) {\n        this._group._items.forEach(drop => {\n          if (siblings.indexOf(drop) === -1) {\n            siblings.push(drop);\n          }\n        });\n      }\n      // Note that we resolve the scrollable parents here so that we delay the resolution\n      // as long as possible, ensuring that the element is in its final place in the DOM.\n      if (!this._scrollableParentsResolved) {\n        const scrollableParents = this._scrollDispatcher.getAncestorScrollContainers(this.element).map(scrollable => scrollable.getElementRef().nativeElement);\n        this._dropListRef.withScrollableParents(scrollableParents);\n        // Only do this once since it involves traversing the DOM and the parents\n        // shouldn't be able to change without the drop list being destroyed.\n        this._scrollableParentsResolved = true;\n      }\n      if (this.elementContainerSelector) {\n        const container = this.element.nativeElement.querySelector(this.elementContainerSelector);\n        if (!container && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n          throw new Error(`CdkDropList could not find an element container matching the selector \"${this.elementContainerSelector}\"`);\n        }\n        ref.withElementContainer(container);\n      }\n      ref.disabled = this.disabled;\n      ref.lockAxis = this.lockAxis;\n      ref.sortingDisabled = this.sortingDisabled;\n      ref.autoScrollDisabled = this.autoScrollDisabled;\n      ref.autoScrollStep = coerceNumberProperty(this.autoScrollStep, 2);\n      ref.connectedTo(siblings.filter(drop => drop && drop !== this).map(list => list._dropListRef)).withOrientation(this.orientation);\n    });\n  }\n  /** Handles events from the underlying DropListRef. */\n  _handleEvents(ref) {\n    ref.beforeStarted.subscribe(() => {\n      this._syncItemsWithRef();\n      this._changeDetectorRef.markForCheck();\n    });\n    ref.entered.subscribe(event => {\n      this.entered.emit({\n        container: this,\n        item: event.item.data,\n        currentIndex: event.currentIndex\n      });\n    });\n    ref.exited.subscribe(event => {\n      this.exited.emit({\n        container: this,\n        item: event.item.data\n      });\n      this._changeDetectorRef.markForCheck();\n    });\n    ref.sorted.subscribe(event => {\n      this.sorted.emit({\n        previousIndex: event.previousIndex,\n        currentIndex: event.currentIndex,\n        container: this,\n        item: event.item.data\n      });\n    });\n    ref.dropped.subscribe(dropEvent => {\n      this.dropped.emit({\n        previousIndex: dropEvent.previousIndex,\n        currentIndex: dropEvent.currentIndex,\n        previousContainer: dropEvent.previousContainer.data,\n        container: dropEvent.container.data,\n        item: dropEvent.item.data,\n        isPointerOverContainer: dropEvent.isPointerOverContainer,\n        distance: dropEvent.distance,\n        dropPoint: dropEvent.dropPoint,\n        event: dropEvent.event\n      });\n      // Mark for check since all of these events run outside of change\n      // detection and we're not guaranteed for something else to have triggered it.\n      this._changeDetectorRef.markForCheck();\n    });\n    merge(ref.receivingStarted, ref.receivingStopped).subscribe(() => this._changeDetectorRef.markForCheck());\n  }\n  /** Assigns the default input values based on a provided config object. */\n  _assignDefaults(config) {\n    const {\n      lockAxis,\n      draggingDisabled,\n      sortingDisabled,\n      listAutoScrollDisabled,\n      listOrientation\n    } = config;\n    this.disabled = draggingDisabled == null ? false : draggingDisabled;\n    this.sortingDisabled = sortingDisabled == null ? false : sortingDisabled;\n    this.autoScrollDisabled = listAutoScrollDisabled == null ? false : listAutoScrollDisabled;\n    this.orientation = listOrientation || 'vertical';\n    if (lockAxis) {\n      this.lockAxis = lockAxis;\n    }\n  }\n  /** Syncs up the registered drag items with underlying drop list ref. */\n  _syncItemsWithRef() {\n    this._dropListRef.withItems(this.getSortedItems().map(item => item._dragRef));\n  }\n  static {\n    this.ɵfac = function CdkDropList_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkDropList)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(DragDrop), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.ScrollDispatcher), i0.ɵɵdirectiveInject(i1$1.Directionality, 8), i0.ɵɵdirectiveInject(CDK_DROP_LIST_GROUP, 12), i0.ɵɵdirectiveInject(CDK_DRAG_CONFIG, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkDropList,\n      selectors: [[\"\", \"cdkDropList\", \"\"], [\"cdk-drop-list\"]],\n      hostAttrs: [1, \"cdk-drop-list\"],\n      hostVars: 7,\n      hostBindings: function CdkDropList_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"id\", ctx.id);\n          i0.ɵɵclassProp(\"cdk-drop-list-disabled\", ctx.disabled)(\"cdk-drop-list-dragging\", ctx._dropListRef.isDragging())(\"cdk-drop-list-receiving\", ctx._dropListRef.isReceiving());\n        }\n      },\n      inputs: {\n        connectedTo: [0, \"cdkDropListConnectedTo\", \"connectedTo\"],\n        data: [0, \"cdkDropListData\", \"data\"],\n        orientation: [0, \"cdkDropListOrientation\", \"orientation\"],\n        id: \"id\",\n        lockAxis: [0, \"cdkDropListLockAxis\", \"lockAxis\"],\n        disabled: [2, \"cdkDropListDisabled\", \"disabled\", booleanAttribute],\n        sortingDisabled: [2, \"cdkDropListSortingDisabled\", \"sortingDisabled\", booleanAttribute],\n        enterPredicate: [0, \"cdkDropListEnterPredicate\", \"enterPredicate\"],\n        sortPredicate: [0, \"cdkDropListSortPredicate\", \"sortPredicate\"],\n        autoScrollDisabled: [2, \"cdkDropListAutoScrollDisabled\", \"autoScrollDisabled\", booleanAttribute],\n        autoScrollStep: [0, \"cdkDropListAutoScrollStep\", \"autoScrollStep\"],\n        elementContainerSelector: [0, \"cdkDropListElementContainer\", \"elementContainerSelector\"]\n      },\n      outputs: {\n        dropped: \"cdkDropListDropped\",\n        entered: \"cdkDropListEntered\",\n        exited: \"cdkDropListExited\",\n        sorted: \"cdkDropListSorted\"\n      },\n      exportAs: [\"cdkDropList\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([\n      // Prevent child drop lists from picking up the same group as their parent.\n      {\n        provide: CDK_DROP_LIST_GROUP,\n        useValue: undefined\n      }, {\n        provide: CDK_DROP_LIST,\n        useExisting: CdkDropList\n      }]), i0.ɵɵInputTransformsFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkDropList, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkDropList], cdk-drop-list',\n      exportAs: 'cdkDropList',\n      standalone: true,\n      providers: [\n      // Prevent child drop lists from picking up the same group as their parent.\n      {\n        provide: CDK_DROP_LIST_GROUP,\n        useValue: undefined\n      }, {\n        provide: CDK_DROP_LIST,\n        useExisting: CdkDropList\n      }],\n      host: {\n        'class': 'cdk-drop-list',\n        '[attr.id]': 'id',\n        '[class.cdk-drop-list-disabled]': 'disabled',\n        '[class.cdk-drop-list-dragging]': '_dropListRef.isDragging()',\n        '[class.cdk-drop-list-receiving]': '_dropListRef.isReceiving()'\n      }\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: DragDrop\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i1.ScrollDispatcher\n  }, {\n    type: i1$1.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: CdkDropListGroup,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [CDK_DROP_LIST_GROUP]\n    }, {\n      type: SkipSelf\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [CDK_DRAG_CONFIG]\n    }]\n  }], {\n    connectedTo: [{\n      type: Input,\n      args: ['cdkDropListConnectedTo']\n    }],\n    data: [{\n      type: Input,\n      args: ['cdkDropListData']\n    }],\n    orientation: [{\n      type: Input,\n      args: ['cdkDropListOrientation']\n    }],\n    id: [{\n      type: Input\n    }],\n    lockAxis: [{\n      type: Input,\n      args: ['cdkDropListLockAxis']\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        alias: 'cdkDropListDisabled',\n        transform: booleanAttribute\n      }]\n    }],\n    sortingDisabled: [{\n      type: Input,\n      args: [{\n        alias: 'cdkDropListSortingDisabled',\n        transform: booleanAttribute\n      }]\n    }],\n    enterPredicate: [{\n      type: Input,\n      args: ['cdkDropListEnterPredicate']\n    }],\n    sortPredicate: [{\n      type: Input,\n      args: ['cdkDropListSortPredicate']\n    }],\n    autoScrollDisabled: [{\n      type: Input,\n      args: [{\n        alias: 'cdkDropListAutoScrollDisabled',\n        transform: booleanAttribute\n      }]\n    }],\n    autoScrollStep: [{\n      type: Input,\n      args: ['cdkDropListAutoScrollStep']\n    }],\n    elementContainerSelector: [{\n      type: Input,\n      args: ['cdkDropListElementContainer']\n    }],\n    dropped: [{\n      type: Output,\n      args: ['cdkDropListDropped']\n    }],\n    entered: [{\n      type: Output,\n      args: ['cdkDropListEntered']\n    }],\n    exited: [{\n      type: Output,\n      args: ['cdkDropListExited']\n    }],\n    sorted: [{\n      type: Output,\n      args: ['cdkDropListSorted']\n    }]\n  });\n})();\n\n/**\n * Injection token that can be used to reference instances of `CdkDragPreview`. It serves as\n * alternative token to the actual `CdkDragPreview` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst CDK_DRAG_PREVIEW = new InjectionToken('CdkDragPreview');\n/**\n * Element that will be used as a template for the preview\n * of a CdkDrag when it is being dragged.\n */\nclass CdkDragPreview {\n  constructor(templateRef) {\n    this.templateRef = templateRef;\n    this._drag = inject(CDK_DRAG_PARENT, {\n      optional: true\n    });\n    /** Whether the preview should preserve the same size as the item that is being dragged. */\n    this.matchSize = false;\n    this._drag?._setPreviewTemplate(this);\n  }\n  ngOnDestroy() {\n    this._drag?._resetPreviewTemplate(this);\n  }\n  static {\n    this.ɵfac = function CdkDragPreview_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkDragPreview)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkDragPreview,\n      selectors: [[\"ng-template\", \"cdkDragPreview\", \"\"]],\n      inputs: {\n        data: \"data\",\n        matchSize: [2, \"matchSize\", \"matchSize\", booleanAttribute]\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CDK_DRAG_PREVIEW,\n        useExisting: CdkDragPreview\n      }]), i0.ɵɵInputTransformsFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkDragPreview, [{\n    type: Directive,\n    args: [{\n      selector: 'ng-template[cdkDragPreview]',\n      standalone: true,\n      providers: [{\n        provide: CDK_DRAG_PREVIEW,\n        useExisting: CdkDragPreview\n      }]\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], {\n    data: [{\n      type: Input\n    }],\n    matchSize: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n\n/**\n * Injection token that can be used to reference instances of `CdkDragPlaceholder`. It serves as\n * alternative token to the actual `CdkDragPlaceholder` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst CDK_DRAG_PLACEHOLDER = new InjectionToken('CdkDragPlaceholder');\n/**\n * Element that will be used as a template for the placeholder of a CdkDrag when\n * it is being dragged. The placeholder is displayed in place of the element being dragged.\n */\nclass CdkDragPlaceholder {\n  constructor(templateRef) {\n    this.templateRef = templateRef;\n    this._drag = inject(CDK_DRAG_PARENT, {\n      optional: true\n    });\n    this._drag?._setPlaceholderTemplate(this);\n  }\n  ngOnDestroy() {\n    this._drag?._resetPlaceholderTemplate(this);\n  }\n  static {\n    this.ɵfac = function CdkDragPlaceholder_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkDragPlaceholder)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkDragPlaceholder,\n      selectors: [[\"ng-template\", \"cdkDragPlaceholder\", \"\"]],\n      inputs: {\n        data: \"data\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CDK_DRAG_PLACEHOLDER,\n        useExisting: CdkDragPlaceholder\n      }])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkDragPlaceholder, [{\n    type: Directive,\n    args: [{\n      selector: 'ng-template[cdkDragPlaceholder]',\n      standalone: true,\n      providers: [{\n        provide: CDK_DRAG_PLACEHOLDER,\n        useExisting: CdkDragPlaceholder\n      }]\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], {\n    data: [{\n      type: Input\n    }]\n  });\n})();\nconst DRAG_DROP_DIRECTIVES = [CdkDropList, CdkDropListGroup, CdkDrag, CdkDragHandle, CdkDragPreview, CdkDragPlaceholder];\nclass DragDropModule {\n  static {\n    this.ɵfac = function DragDropModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DragDropModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: DragDropModule,\n      imports: [CdkDropList, CdkDropListGroup, CdkDrag, CdkDragHandle, CdkDragPreview, CdkDragPlaceholder],\n      exports: [CdkScrollableModule, CdkDropList, CdkDropListGroup, CdkDrag, CdkDragHandle, CdkDragPreview, CdkDragPlaceholder]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [DragDrop],\n      imports: [CdkScrollableModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DragDropModule, [{\n    type: NgModule,\n    args: [{\n      imports: DRAG_DROP_DIRECTIVES,\n      exports: [CdkScrollableModule, ...DRAG_DROP_DIRECTIVES],\n      providers: [DragDrop]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CDK_DRAG_CONFIG, CDK_DRAG_HANDLE, CDK_DRAG_PARENT, CDK_DRAG_PLACEHOLDER, CDK_DRAG_PREVIEW, CDK_DROP_LIST, CDK_DROP_LIST_GROUP, CdkDrag, CdkDragHandle, CdkDragPlaceholder, CdkDragPreview, CdkDropList, CdkDropListGroup, DragDrop, DragDropModule, DragDropRegistry, DragRef, DropListRef, copyArrayItem, moveItemInArray, transferArrayItem };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA,IAAAA,eAAyE;AACzE,IAAAC,oBAA4F;;;ACP5F,kBAAiC;AAMjC,SAAS,iBAAiB,MAAM;AAC9B,MAAI,KAAC,0BAAa,IAAI,GAAG;AACvB,eAAO,gBAAG,IAAI;AAAA,EAChB;AACA,SAAO;AACT;;;ACRA,IAAAC,eAAoC;AACpC,uBAA0C;AAM1C,SAAS,mBAAmB,QAAQ;AAElC,MAAI,OAAO,SAAS,mBAAmB,OAAO,kBAAkB,SAAS;AACvE,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,SAAS,aAAa;AAC/B,aAAS,IAAI,GAAG,IAAI,OAAO,WAAW,QAAQ,KAAK;AACjD,UAAI,EAAE,OAAO,WAAW,CAAC,aAAa,UAAU;AAC9C,eAAO;AAAA,MACT;AAAA,IACF;AACA,aAAS,IAAI,GAAG,IAAI,OAAO,aAAa,QAAQ,KAAK;AACnD,UAAI,EAAE,OAAO,aAAa,CAAC,aAAa,UAAU;AAChD,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAKA,IAAM,0BAAN,MAAM,yBAAwB;AAAA,EAC5B,OAAO,UAAU;AACf,WAAO,OAAO,qBAAqB,cAAc,OAAO,IAAI,iBAAiB,QAAQ;AAAA,EACvF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,gCAAgC,mBAAmB;AACtE,aAAO,KAAK,qBAAqB,0BAAyB;AAAA,IAC5D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,yBAAwB;AAAA,MACjC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAEH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,YAAY,0BAA0B;AACpC,SAAK,2BAA2B;AAEhC,SAAK,oBAAoB,oBAAI,IAAI;AACjC,SAAK,UAAU,OAAO,MAAM;AAAA,EAC9B;AAAA,EACA,cAAc;AACZ,SAAK,kBAAkB,QAAQ,CAAC,GAAG,YAAY,KAAK,iBAAiB,OAAO,CAAC;AAAA,EAC/E;AAAA,EACA,QAAQ,cAAc;AACpB,UAAM,UAAU,cAAc,YAAY;AAC1C,WAAO,IAAI,wBAAW,cAAY;AAChC,YAAM,SAAS,KAAK,gBAAgB,OAAO;AAC3C,YAAM,eAAe,OAAO,SAAK,sBAAI,aAAW,QAAQ,OAAO,YAAU,CAAC,mBAAmB,MAAM,CAAC,CAAC,OAAG,yBAAO,aAAW,CAAC,CAAC,QAAQ,MAAM,CAAC,EAAE,UAAU,aAAW;AAChK,aAAK,QAAQ,IAAI,MAAM;AACrB,mBAAS,KAAK,OAAO;AAAA,QACvB,CAAC;AAAA,MACH,CAAC;AACD,aAAO,MAAM;AACX,qBAAa,YAAY;AACzB,aAAK,kBAAkB,OAAO;AAAA,MAChC;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB,SAAS;AACvB,WAAO,KAAK,QAAQ,kBAAkB,MAAM;AAC1C,UAAI,CAAC,KAAK,kBAAkB,IAAI,OAAO,GAAG;AACxC,cAAM,SAAS,IAAI,qBAAQ;AAC3B,cAAM,WAAW,KAAK,yBAAyB,OAAO,eAAa,OAAO,KAAK,SAAS,CAAC;AACzF,YAAI,UAAU;AACZ,mBAAS,QAAQ,SAAS;AAAA,YACxB,eAAe;AAAA,YACf,WAAW;AAAA,YACX,SAAS;AAAA,UACX,CAAC;AAAA,QACH;AACA,aAAK,kBAAkB,IAAI,SAAS;AAAA,UAClC;AAAA,UACA;AAAA,UACA,OAAO;AAAA,QACT,CAAC;AAAA,MACH,OAAO;AACL,aAAK,kBAAkB,IAAI,OAAO,EAAE;AAAA,MACtC;AACA,aAAO,KAAK,kBAAkB,IAAI,OAAO,EAAE;AAAA,IAC7C,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB,SAAS;AACzB,QAAI,KAAK,kBAAkB,IAAI,OAAO,GAAG;AACvC,WAAK,kBAAkB,IAAI,OAAO,EAAE;AACpC,UAAI,CAAC,KAAK,kBAAkB,IAAI,OAAO,EAAE,OAAO;AAC9C,aAAK,iBAAiB,OAAO;AAAA,MAC/B;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,iBAAiB,SAAS;AACxB,QAAI,KAAK,kBAAkB,IAAI,OAAO,GAAG;AACvC,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,KAAK,kBAAkB,IAAI,OAAO;AACtC,UAAI,UAAU;AACZ,iBAAS,WAAW;AAAA,MACtB;AACA,aAAO,SAAS;AAChB,WAAK,kBAAkB,OAAO,OAAO;AAAA,IACvC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,wBAAwB,mBAAmB;AAC9D,aAAO,KAAK,qBAAqB,kBAAoB,SAAS,uBAAuB,CAAC;AAAA,IACxF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,iBAAgB;AAAA,MACzB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,CAAC,GAAG,IAAI;AACV,GAAG;AAKH,IAAM,oBAAN,MAAM,mBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtB,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY;AACjB,SAAK,YAAY,KAAK,aAAa,IAAI,KAAK,WAAW;AAAA,EACzD;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY,qBAAqB,KAAK;AAC3C,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,YAAY,kBAAkB,aAAa;AACzC,SAAK,mBAAmB;AACxB,SAAK,cAAc;AAEnB,SAAK,QAAQ,IAAI,aAAa;AAC9B,SAAK,YAAY;AACjB,SAAK,uBAAuB;AAAA,EAC9B;AAAA,EACA,qBAAqB;AACnB,QAAI,CAAC,KAAK,wBAAwB,CAAC,KAAK,UAAU;AAChD,WAAK,WAAW;AAAA,IAClB;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,aAAa;AACX,SAAK,aAAa;AAClB,UAAM,SAAS,KAAK,iBAAiB,QAAQ,KAAK,WAAW;AAC7D,SAAK,wBAAwB,KAAK,WAAW,OAAO,SAAK,+BAAa,KAAK,QAAQ,CAAC,IAAI,QAAQ,UAAU,KAAK,KAAK;AAAA,EACtH;AAAA,EACA,eAAe;AACb,SAAK,sBAAsB,YAAY;AAAA,EACzC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,0BAA0B,mBAAmB;AAChE,aAAO,KAAK,qBAAqB,oBAAsB,kBAAkB,eAAe,GAAM,kBAAqB,UAAU,CAAC;AAAA,IAChI;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,qBAAqB,EAAE,CAAC;AAAA,MACzC,QAAQ;AAAA,QACN,UAAU,CAAC,GAAG,6BAA6B,YAAY,gBAAgB;AAAA,QACvE,UAAU;AAAA,MACZ;AAAA,MACA,SAAS;AAAA,QACP,OAAO;AAAA,MACT;AAAA,MACA,UAAU,CAAC,mBAAmB;AAAA,MAC9B,YAAY;AAAA,MACZ,UAAU,CAAI,wBAAwB;AAAA,IACxC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,OAAO;AACL,SAAK,OAAO,SAAS,wBAAwB,mBAAmB;AAC9D,aAAO,KAAK,qBAAqB,kBAAiB;AAAA,IACpD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,iBAAiB;AAAA,MAC3B,SAAS,CAAC,iBAAiB;AAAA,IAC7B,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,WAAW,CAAC,uBAAuB;AAAA,IACrC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,iBAAiB;AAAA,MAC3B,SAAS,CAAC,iBAAiB;AAAA,MAC3B,WAAW,CAAC,uBAAuB;AAAA,IACrC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;AFnRH,IAAM,eAAe;AAKrB,SAAS,oBAAoB,IAAI,MAAM,IAAI;AACzC,QAAM,MAAM,oBAAoB,IAAI,IAAI;AACxC,OAAK,GAAG,KAAK;AACb,MAAI,IAAI,KAAK,gBAAc,WAAW,KAAK,MAAM,EAAE,GAAG;AACpD;AAAA,EACF;AACA,MAAI,KAAK,EAAE;AACX,KAAG,aAAa,MAAM,IAAI,KAAK,YAAY,CAAC;AAC9C;AAKA,SAAS,uBAAuB,IAAI,MAAM,IAAI;AAC5C,QAAM,MAAM,oBAAoB,IAAI,IAAI;AACxC,OAAK,GAAG,KAAK;AACb,QAAM,cAAc,IAAI,OAAO,SAAO,QAAQ,EAAE;AAChD,MAAI,YAAY,QAAQ;AACtB,OAAG,aAAa,MAAM,YAAY,KAAK,YAAY,CAAC;AAAA,EACtD,OAAO;AACL,OAAG,gBAAgB,IAAI;AAAA,EACzB;AACF;AAKA,SAAS,oBAAoB,IAAI,MAAM;AAErC,QAAM,YAAY,GAAG,aAAa,IAAI;AACtC,SAAO,WAAW,MAAM,MAAM,KAAK,CAAC;AACtC;AAaA,IAAM,4BAA4B;AAMlC,IAAM,iCAAiC;AAEvC,IAAI,SAAS;AAMb,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,YAAY,WAKZ,WAAW;AACT,SAAK,YAAY;AAEjB,SAAK,mBAAmB,oBAAI,IAAI;AAEhC,SAAK,qBAAqB;AAE1B,SAAK,MAAM,GAAG,QAAQ;AACtB,SAAK,YAAY;AACjB,SAAK,MAAM,OAAO,MAAM,IAAI,MAAM;AAAA,EACpC;AAAA,EACA,SAAS,aAAa,SAAS,MAAM;AACnC,QAAI,CAAC,KAAK,gBAAgB,aAAa,OAAO,GAAG;AAC/C;AAAA,IACF;AACA,UAAM,MAAM,OAAO,SAAS,IAAI;AAChC,QAAI,OAAO,YAAY,UAAU;AAE/B,mBAAa,SAAS,KAAK,GAAG;AAC9B,WAAK,iBAAiB,IAAI,KAAK;AAAA,QAC7B,gBAAgB;AAAA,QAChB,gBAAgB;AAAA,MAClB,CAAC;AAAA,IACH,WAAW,CAAC,KAAK,iBAAiB,IAAI,GAAG,GAAG;AAC1C,WAAK,sBAAsB,SAAS,IAAI;AAAA,IAC1C;AACA,QAAI,CAAC,KAAK,6BAA6B,aAAa,GAAG,GAAG;AACxD,WAAK,qBAAqB,aAAa,GAAG;AAAA,IAC5C;AAAA,EACF;AAAA,EACA,kBAAkB,aAAa,SAAS,MAAM;AAC5C,QAAI,CAAC,WAAW,CAAC,KAAK,eAAe,WAAW,GAAG;AACjD;AAAA,IACF;AACA,UAAM,MAAM,OAAO,SAAS,IAAI;AAChC,QAAI,KAAK,6BAA6B,aAAa,GAAG,GAAG;AACvD,WAAK,wBAAwB,aAAa,GAAG;AAAA,IAC/C;AAGA,QAAI,OAAO,YAAY,UAAU;AAC/B,YAAM,oBAAoB,KAAK,iBAAiB,IAAI,GAAG;AACvD,UAAI,qBAAqB,kBAAkB,mBAAmB,GAAG;AAC/D,aAAK,sBAAsB,GAAG;AAAA,MAChC;AAAA,IACF;AACA,QAAI,KAAK,oBAAoB,WAAW,WAAW,GAAG;AACpD,WAAK,mBAAmB,OAAO;AAC/B,WAAK,qBAAqB;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA,EAEA,cAAc;AACZ,UAAM,oBAAoB,KAAK,UAAU,iBAAiB,IAAI,8BAA8B,KAAK,KAAK,GAAG,IAAI;AAC7G,aAAS,IAAI,GAAG,IAAI,kBAAkB,QAAQ,KAAK;AACjD,WAAK,kCAAkC,kBAAkB,CAAC,CAAC;AAC3D,wBAAkB,CAAC,EAAE,gBAAgB,8BAA8B;AAAA,IACrE;AACA,SAAK,oBAAoB,OAAO;AAChC,SAAK,qBAAqB;AAC1B,SAAK,iBAAiB,MAAM;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,sBAAsB,SAAS,MAAM;AACnC,UAAM,iBAAiB,KAAK,UAAU,cAAc,KAAK;AACzD,iBAAa,gBAAgB,KAAK,GAAG;AACrC,mBAAe,cAAc;AAC7B,QAAI,MAAM;AACR,qBAAe,aAAa,QAAQ,IAAI;AAAA,IAC1C;AACA,SAAK,yBAAyB;AAC9B,SAAK,mBAAmB,YAAY,cAAc;AAClD,SAAK,iBAAiB,IAAI,OAAO,SAAS,IAAI,GAAG;AAAA,MAC/C;AAAA,MACA,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,sBAAsB,KAAK;AACzB,SAAK,iBAAiB,IAAI,GAAG,GAAG,gBAAgB,OAAO;AACvD,SAAK,iBAAiB,OAAO,GAAG;AAAA,EAClC;AAAA;AAAA,EAEA,2BAA2B;AACzB,QAAI,KAAK,oBAAoB;AAC3B;AAAA,IACF;AACA,UAAM,qBAAqB;AAC3B,UAAM,mBAAmB,KAAK,UAAU,iBAAiB,IAAI,kBAAkB,qBAAqB;AACpG,aAAS,IAAI,GAAG,IAAI,iBAAiB,QAAQ,KAAK;AAKhD,uBAAiB,CAAC,EAAE,OAAO;AAAA,IAC7B;AACA,UAAM,oBAAoB,KAAK,UAAU,cAAc,KAAK;AAK5D,sBAAkB,MAAM,aAAa;AAGrC,sBAAkB,UAAU,IAAI,kBAAkB;AAClD,sBAAkB,UAAU,IAAI,qBAAqB;AAErD,QAAI,KAAK,aAAa,CAAC,KAAK,UAAU,WAAW;AAC/C,wBAAkB,aAAa,YAAY,QAAQ;AAAA,IACrD;AACA,SAAK,UAAU,KAAK,YAAY,iBAAiB;AACjD,SAAK,qBAAqB;AAAA,EAC5B;AAAA;AAAA,EAEA,kCAAkC,SAAS;AAEzC,UAAM,uBAAuB,oBAAoB,SAAS,kBAAkB,EAAE,OAAO,QAAM,GAAG,QAAQ,yBAAyB,KAAK,CAAC;AACrI,YAAQ,aAAa,oBAAoB,qBAAqB,KAAK,GAAG,CAAC;AAAA,EACzE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB,SAAS,KAAK;AACjC,UAAM,oBAAoB,KAAK,iBAAiB,IAAI,GAAG;AAGvD,wBAAoB,SAAS,oBAAoB,kBAAkB,eAAe,EAAE;AACpF,YAAQ,aAAa,gCAAgC,KAAK,GAAG;AAC7D,sBAAkB;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,wBAAwB,SAAS,KAAK;AACpC,UAAM,oBAAoB,KAAK,iBAAiB,IAAI,GAAG;AACvD,sBAAkB;AAClB,2BAAuB,SAAS,oBAAoB,kBAAkB,eAAe,EAAE;AACvF,YAAQ,gBAAgB,8BAA8B;AAAA,EACxD;AAAA;AAAA,EAEA,6BAA6B,SAAS,KAAK;AACzC,UAAM,eAAe,oBAAoB,SAAS,kBAAkB;AACpE,UAAM,oBAAoB,KAAK,iBAAiB,IAAI,GAAG;AACvD,UAAM,YAAY,qBAAqB,kBAAkB,eAAe;AACxE,WAAO,CAAC,CAAC,aAAa,aAAa,QAAQ,SAAS,KAAK;AAAA,EAC3D;AAAA;AAAA,EAEA,gBAAgB,SAAS,SAAS;AAChC,QAAI,CAAC,KAAK,eAAe,OAAO,GAAG;AACjC,aAAO;AAAA,IACT;AACA,QAAI,WAAW,OAAO,YAAY,UAAU;AAI1C,aAAO;AAAA,IACT;AACA,UAAM,iBAAiB,WAAW,OAAO,KAAK,GAAG,OAAO,GAAG,KAAK;AAChE,UAAM,YAAY,QAAQ,aAAa,YAAY;AAGnD,WAAO,iBAAiB,CAAC,aAAa,UAAU,KAAK,MAAM,iBAAiB;AAAA,EAC9E;AAAA;AAAA,EAEA,eAAe,SAAS;AACtB,WAAO,QAAQ,aAAa,KAAK,UAAU;AAAA,EAC7C;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,sBAAsB,mBAAmB;AAC5D,aAAO,KAAK,qBAAqB,gBAAkB,SAAS,QAAQ,GAAM,SAAY,QAAQ,CAAC;AAAA,IACjG;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,eAAc;AAAA,MACvB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AAEH,SAAS,OAAO,SAAS,MAAM;AAC7B,SAAO,OAAO,YAAY,WAAW,GAAG,QAAQ,EAAE,IAAI,OAAO,KAAK;AACpE;AAEA,SAAS,aAAa,SAAS,WAAW;AACxC,MAAI,CAAC,QAAQ,IAAI;AACf,YAAQ,KAAK,GAAG,yBAAyB,IAAI,SAAS,IAAI,QAAQ;AAAA,EACpE;AACF;AACA,IAAM,yCAAyC;AAK/C,IAAM,YAAN,MAAgB;AAAA,EACd,YAAY,cAAc,QAAQ;AAChC,SAAK,mBAAmB,IAAI,qBAAQ;AACpC,SAAK,SAAS,CAAC;AACf,SAAK,qBAAqB;AAE1B,SAAK,kBAAkB,CAAC;AACxB,SAAK,gBAAgB,IAAI,qBAAQ;AACjC,SAAK,eAAe,KAAK;AACzB,UAAM,oBAAoB,OAAO,QAAQ,qBAAqB,WAAW,OAAO,mBAAmB;AACnG,QAAI,QAAQ,eAAe;AACzB,WAAK,mBAAmB,OAAO;AAAA,IACjC;AACA,SAAK,OAAO,cAAc,eAAe,cAAc,aAAa,UAAU,aAAa,KAAK,UAAQ,OAAO,KAAK,aAAa,UAAU,GAAG;AAC5I,YAAM,IAAI,MAAM,0EAA0E;AAAA,IAC5F;AACA,SAAK,SAAS,YAAY;AAC1B,SAAK,iBAAiB,iBAAiB;AAAA,EACzC;AAAA,EACA,UAAU;AACR,SAAK,kBAAkB,CAAC;AACxB,SAAK,iBAAiB,SAAS;AAC/B,SAAK,cAAc,SAAS;AAAA,EAC9B;AAAA,EACA,4BAA4B,OAAO;AACjC,SAAK,qBAAqB;AAAA,EAC5B;AAAA,EACA,SAAS,OAAO;AACd,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,UAAU,OAAO;AACf,UAAM,UAAU,MAAM;AAGtB,QAAI,MAAM,OAAO,MAAM,IAAI,WAAW,GAAG;AACvC,WAAK,iBAAiB,KAAK,MAAM,IAAI,kBAAkB,CAAC;AAAA,IAC1D,WAAW,WAAW,KAAK,WAAW,KAAK,WAAW,QAAQ,WAAW,MAAM;AAC7E,WAAK,iBAAiB,KAAK,OAAO,aAAa,OAAO,CAAC;AAAA,IACzD;AAAA,EACF;AAAA;AAAA,EAEA,WAAW;AACT,WAAO,KAAK,gBAAgB,SAAS;AAAA,EACvC;AAAA;AAAA,EAEA,QAAQ;AACN,SAAK,kBAAkB,CAAC;AAAA,EAC1B;AAAA,EACA,iBAAiB,mBAAmB;AAIlC,SAAK,iBAAiB,SAAK,uBAAI,YAAU,KAAK,gBAAgB,KAAK,MAAM,CAAC,OAAG,gCAAa,iBAAiB,OAAG,0BAAO,MAAM,KAAK,gBAAgB,SAAS,CAAC,OAAG,uBAAI,MAAM,KAAK,gBAAgB,KAAK,EAAE,EAAE,kBAAkB,CAAC,CAAC,EAAE,UAAU,iBAAe;AAGlP,eAAS,IAAI,GAAG,IAAI,KAAK,OAAO,SAAS,GAAG,KAAK;AAC/C,cAAM,SAAS,KAAK,qBAAqB,KAAK,KAAK,OAAO;AAC1D,cAAM,OAAO,KAAK,OAAO,KAAK;AAC9B,YAAI,CAAC,KAAK,mBAAmB,IAAI,KAAK,KAAK,WAAW,EAAE,kBAAkB,EAAE,KAAK,EAAE,QAAQ,WAAW,MAAM,GAAG;AAC7G,eAAK,cAAc,KAAK,IAAI;AAC5B;AAAA,QACF;AAAA,MACF;AACA,WAAK,kBAAkB,CAAC;AAAA,IAC1B,CAAC;AAAA,EACH;AACF;AA8XA,IAAM,iBAAN,MAAqB;AAAA,EACnB,mBAAmB;AACjB,QAAI,KAAK,sBAAsB,KAAK,OAAO,WAAW,GAAG;AACvD;AAAA,IACF;AACA,QAAI,cAAc;AAClB,aAAS,IAAI,GAAG,IAAI,KAAK,OAAO,QAAQ,KAAK;AAC3C,UAAI,CAAC,KAAK,iBAAiB,KAAK,OAAO,CAAC,CAAC,KAAK,CAAC,KAAK,gBAAgB,KAAK,OAAO,CAAC,CAAC,GAAG;AACnF,sBAAc;AACd;AAAA,MACF;AAAA,IACF;AACA,UAAM,aAAa,KAAK,OAAO,WAAW;AAG1C,QAAI,WAAW,eAAe;AAC5B,WAAK,aAAa,QAAQ;AAC1B,WAAK,mBAAmB;AACxB,WAAK,cAAc;AACnB,WAAK,YAAY,4BAA4B,WAAW;AACxD,iBAAW,cAAc;AAAA,IAC3B,OAAO;AAEL,WAAK,UAAU,WAAW;AAAA,IAC5B;AACA,SAAK,qBAAqB;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,YAAY,OAAO,QAAQ;AAEzB,SAAK,mBAAmB;AAExB,SAAK,cAAc;AAEnB,SAAK,+BAA+B;AAKpC,SAAK,yBAAyB;AAS9B,SAAK,mBAAmB,WAAS;AAEjC,SAAK,aAAa,UAAQ;AAE1B,SAAK,SAAS,CAAC;AACf,SAAK,yBAAyB,0BAAa;AAC3C,SAAK,qBAAqB;AAE1B,SAAK,SAAS,IAAI,qBAAQ;AAI1B,QAAI,iBAAiB,WAAW;AAC9B,WAAK,SAAS,MAAM,QAAQ;AAC5B,YAAM,QAAQ,UAAU,cAAY;AAClC,aAAK,SAAS,SAAS,QAAQ;AAC/B,aAAK,YAAY,SAAS,KAAK,MAAM;AACrC,aAAK,uBAAuB,KAAK,MAAM;AACvC,aAAK,iBAAiB;AAAA,MACxB,CAAC;AAAA,IACH,eAAW,2BAAa,KAAK,GAAG;AAC9B,YAAM,UAAU,cAAY;AAC1B,aAAK,SAAS;AACd,aAAK,YAAY,SAAS,QAAQ;AAClC,aAAK,uBAAuB,QAAQ;AACpC,aAAK,iBAAiB;AAAA,MACxB,CAAC;AAAA,IACH,OAAO;AACL,WAAK,SAAS;AACd,WAAK,iBAAiB;AAAA,IACxB;AACA,QAAI,OAAO,OAAO,gCAAgC,WAAW;AAC3D,WAAK,+BAA+B,OAAO;AAAA,IAC7C;AACA,QAAI,OAAO,uBAAuB;AAChC,WAAK,yBAAyB,OAAO;AAAA,IACvC;AACA,QAAI,OAAO,eAAe;AACxB,WAAK,mBAAmB,OAAO;AAAA,IACjC;AACA,QAAI,OAAO,SAAS;AAClB,WAAK,aAAa,OAAO;AAAA,IAC3B;AACA,QAAI,OAAO,OAAO,8BAA8B,aAAa;AAC3D,WAAK,cAAc,OAAO,yBAAyB;AAAA,IACrD;AAAA,EACF;AAAA;AAAA,EAEA,UAAU;AACR,SAAK,uBAAuB,YAAY;AACxC,SAAK,YAAY,QAAQ;AACzB,SAAK,OAAO,SAAS;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,OAAO;AACf,UAAM,MAAM,MAAM;AAClB,YAAQ,KAAK;AAAA,MACX,KAAK;AAEH;AAAA,MACF,KAAK;AACH,aAAK,eAAe;AACpB;AAAA,MACF,KAAK;AACH,aAAK,mBAAmB;AACxB;AAAA,MACF,KAAK;AACH,aAAK,2BAA2B,QAAQ,KAAK,qBAAqB,IAAI,KAAK,mBAAmB;AAC9F;AAAA,MACF,KAAK;AACH,aAAK,2BAA2B,QAAQ,KAAK,mBAAmB,IAAI,KAAK,qBAAqB;AAC9F;AAAA,MACF,KAAK;AACH,aAAK,gBAAgB;AACrB;AAAA,MACF,KAAK;AACH,aAAK,eAAe;AACpB;AAAA,MACF,KAAK;AAAA,MACL,KAAK;AACH,aAAK,qBAAqB;AAC1B;AAAA,MACF;AACE,YAAI,MAAM,QAAQ,KAAK;AACrB,eAAK,kCAAkC;AACvC;AAAA,QACF;AACA,aAAK,YAAY,UAAU,KAAK;AAGhC;AAAA,IACJ;AAEA,SAAK,YAAY,MAAM;AACvB,UAAM,eAAe;AAAA,EACvB;AAAA;AAAA,EAEA,qBAAqB;AACnB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,gBAAgB;AACd,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,kBAAkB;AAChB,SAAK,UAAU,KAAK,4BAA4B,EAAE,CAAC;AAAA,EACrD;AAAA;AAAA,EAEA,iBAAiB;AACf,SAAK,UAAU,KAAK,gCAAgC,KAAK,OAAO,MAAM,CAAC;AAAA,EACzE;AAAA;AAAA,EAEA,iBAAiB;AACf,SAAK,UAAU,KAAK,4BAA4B,KAAK,gBAAgB,CAAC;AAAA,EACxE;AAAA;AAAA,EAEA,qBAAqB;AACnB,SAAK,UAAU,KAAK,gCAAgC,KAAK,gBAAgB,CAAC;AAAA,EAC5E;AAAA,EACA,UAAU,aAAa,UAAU,CAAC,GAAG;AAEnC,YAAQ,oBAAoB;AAC5B,QAAI,QAAQ,OAAO,gBAAgB,WAAW,cAAc,KAAK,OAAO,UAAU,UAAQ,KAAK,WAAW,IAAI,MAAM,KAAK,WAAW,WAAW,CAAC;AAChJ,QAAI,QAAQ,KAAK,SAAS,KAAK,OAAO,QAAQ;AAC5C;AAAA,IACF;AACA,UAAM,aAAa,KAAK,OAAO,KAAK;AAEpC,QAAI,KAAK,gBAAgB,QAAQ,KAAK,WAAW,UAAU,MAAM,KAAK,WAAW,KAAK,WAAW,GAAG;AAClG;AAAA,IACF;AACA,UAAM,qBAAqB,KAAK;AAChC,SAAK,cAAc,cAAc;AACjC,SAAK,mBAAmB;AACxB,SAAK,YAAY,4BAA4B,KAAK;AAClD,SAAK,aAAa,MAAM;AACxB,wBAAoB,QAAQ;AAC5B,QAAI,QAAQ,iBAAiB;AAC3B,WAAK,OAAO,KAAK,KAAK,WAAW;AAAA,IACnC;AACA,QAAI,KAAK,8BAA8B;AACrC,WAAK,qBAAqB;AAAA,IAC5B;AAAA,EACF;AAAA,EACA,uBAAuB,UAAU;AAC/B,UAAM,aAAa,KAAK;AACxB,QAAI,CAAC,YAAY;AACf;AAAA,IACF;AACA,UAAM,WAAW,SAAS,UAAU,UAAQ,KAAK,WAAW,IAAI,MAAM,KAAK,WAAW,UAAU,CAAC;AACjG,QAAI,WAAW,MAAM,aAAa,KAAK,kBAAkB;AACvD,WAAK,mBAAmB;AACxB,WAAK,YAAY,4BAA4B,QAAQ;AAAA,IACvD;AAAA,EACF;AAAA,EACA,cAAc,kBAAkB;AAC9B,SAAK,aAAa,IAAI,UAAU,KAAK,QAAQ;AAAA,MAC3C,kBAAkB,OAAO,qBAAqB,WAAW,mBAAmB;AAAA,MAC5E,eAAe,UAAQ,KAAK,iBAAiB,IAAI;AAAA,IACnD,CAAC;AACD,SAAK,yBAAyB,KAAK,WAAW,aAAa,UAAU,UAAQ;AAC3E,WAAK,UAAU,IAAI;AAAA,IACrB,CAAC;AAAA,EACH;AAAA,EACA,4BAA4B,eAAe;AACzC,aAAS,IAAI,gBAAgB,GAAG,IAAI,KAAK,OAAO,QAAQ,KAAK;AAC3D,UAAI,CAAC,KAAK,iBAAiB,KAAK,OAAO,CAAC,CAAC,GAAG;AAC1C,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,gCAAgC,eAAe;AAC7C,aAAS,IAAI,gBAAgB,GAAG,KAAK,GAAG,KAAK;AAC3C,UAAI,CAAC,KAAK,iBAAiB,KAAK,OAAO,CAAC,CAAC,GAAG;AAC1C,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,uBAAuB;AACrB,QAAI,CAAC,KAAK,aAAa;AACrB;AAAA,IACF;AACA,QAAI,KAAK,uBAAuB,GAAG;AACjC,WAAK,YAAY,SAAS;AAAA,IAC5B,OAAO;AACL,YAAM,SAAS,KAAK,YAAY,UAAU;AAC1C,UAAI,CAAC,UAAU,KAAK,iBAAiB,MAAM,GAAG;AAC5C;AAAA,MACF;AACA,WAAK,UAAU,MAAM;AAAA,IACvB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,qBAAqB;AACnB,QAAI,CAAC,KAAK,aAAa;AACrB;AAAA,IACF;AACA,QAAI,CAAC,KAAK,uBAAuB,GAAG;AAClC,WAAK,YAAY,OAAO;AAAA,IAC1B,OAAO;AACL,uBAAiB,KAAK,YAAY,YAAY,CAAC,EAAE,SAAK,wBAAK,CAAC,CAAC,EAAE,UAAU,cAAY;AACnF,cAAM,aAAa,SAAS,KAAK,WAAS,CAAC,KAAK,iBAAiB,KAAK,CAAC;AACvE,YAAI,CAAC,YAAY;AACf;AAAA,QACF;AACA,aAAK,UAAU,UAAU;AAAA,MAC3B,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,yBAAyB;AACvB,QAAI,CAAC,KAAK,aAAa;AACrB,aAAO;AAAA,IACT;AACA,WAAO,OAAO,KAAK,YAAY,eAAe,YAAY,KAAK,YAAY,aAAa,KAAK,YAAY,WAAW;AAAA,EACtH;AAAA,EACA,gBAAgB,MAAM;AACpB,WAAO,OAAO,KAAK,eAAe,YAAY,KAAK,aAAa,KAAK,aAAa;AAAA,EACpF;AAAA;AAAA,EAEA,oCAAoC;AAClC,QAAI,CAAC,KAAK,aAAa;AACrB;AAAA,IACF;AACA,UAAM,SAAS,KAAK,YAAY,UAAU;AAC1C,QAAI;AACJ,QAAI,CAAC,QAAQ;AACX,0BAAgB,iBAAG,KAAK,OAAO,OAAO,UAAQ,KAAK,UAAU,MAAM,IAAI,CAAC;AAAA,IAC1E,OAAO;AACL,sBAAgB,iBAAiB,OAAO,YAAY,CAAC;AAAA,IACvD;AACA,kBAAc,SAAK,wBAAK,CAAC,CAAC,EAAE,UAAU,WAAS;AAC7C,iBAAW,QAAQ,OAAO;AACxB,aAAK,OAAO;AAAA,MACd;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,uBAAuB;AACrB,SAAK,aAAa,SAAS;AAAA,EAC7B;AACF;AAEA,SAAS,2BAA2B;AAClC,SAAO,CAAC,OAAO,YAAY,IAAI,eAAe,OAAO,OAAO;AAC9D;AAEA,IAAM,mBAAmB,IAAI,eAAe,oBAAoB;AAAA,EAC9D,YAAY;AAAA,EACZ,SAAS;AACX,CAAC;AA8GD,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,YAAY,WAAW;AACrB,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW,SAAS;AAGlB,WAAO,QAAQ,aAAa,UAAU;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,UAAU,SAAS;AACjB,WAAO,YAAY,OAAO,KAAK,iBAAiB,OAAO,EAAE,eAAe;AAAA,EAC1E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW,SAAS;AAElB,QAAI,CAAC,KAAK,UAAU,WAAW;AAC7B,aAAO;AAAA,IACT;AACA,UAAM,eAAe,gBAAgB,UAAU,OAAO,CAAC;AACvD,QAAI,cAAc;AAEhB,UAAI,iBAAiB,YAAY,MAAM,IAAI;AACzC,eAAO;AAAA,MACT;AAEA,UAAI,CAAC,KAAK,UAAU,YAAY,GAAG;AACjC,eAAO;AAAA,MACT;AAAA,IACF;AACA,QAAI,WAAW,QAAQ,SAAS,YAAY;AAC5C,QAAI,gBAAgB,iBAAiB,OAAO;AAC5C,QAAI,QAAQ,aAAa,iBAAiB,GAAG;AAC3C,aAAO,kBAAkB;AAAA,IAC3B;AACA,QAAI,aAAa,YAAY,aAAa,UAAU;AAIlD,aAAO;AAAA,IACT;AAEA,QAAI,KAAK,UAAU,UAAU,KAAK,UAAU,OAAO,CAAC,yBAAyB,OAAO,GAAG;AACrF,aAAO;AAAA,IACT;AACA,QAAI,aAAa,SAAS;AAGxB,UAAI,CAAC,QAAQ,aAAa,UAAU,GAAG;AACrC,eAAO;AAAA,MACT;AAGA,aAAO,kBAAkB;AAAA,IAC3B;AACA,QAAI,aAAa,SAAS;AAKxB,UAAI,kBAAkB,IAAI;AACxB,eAAO;AAAA,MACT;AAGA,UAAI,kBAAkB,MAAM;AAC1B,eAAO;AAAA,MACT;AAIA,aAAO,KAAK,UAAU,WAAW,QAAQ,aAAa,UAAU;AAAA,IAClE;AACA,WAAO,QAAQ,YAAY;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY,SAAS,QAAQ;AAG3B,WAAO,uBAAuB,OAAO,KAAK,CAAC,KAAK,WAAW,OAAO,MAAM,QAAQ,oBAAoB,KAAK,UAAU,OAAO;AAAA,EAC5H;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,6BAA6B,mBAAmB;AACnE,aAAO,KAAK,qBAAqB,uBAAyB,SAAY,QAAQ,CAAC;AAAA,IACjF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,sBAAqB;AAAA,MAC9B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AAMH,SAAS,gBAAgBC,SAAQ;AAC/B,MAAI;AACF,WAAOA,QAAO;AAAA,EAChB,QAAQ;AACN,WAAO;AAAA,EACT;AACF;AAEA,SAAS,YAAY,SAAS;AAG5B,SAAO,CAAC,EAAE,QAAQ,eAAe,QAAQ,gBAAgB,OAAO,QAAQ,mBAAmB,cAAc,QAAQ,eAAe,EAAE;AACpI;AAEA,SAAS,oBAAoB,SAAS;AACpC,MAAI,WAAW,QAAQ,SAAS,YAAY;AAC5C,SAAO,aAAa,WAAW,aAAa,YAAY,aAAa,YAAY,aAAa;AAChG;AAEA,SAAS,cAAc,SAAS;AAC9B,SAAO,eAAe,OAAO,KAAK,QAAQ,QAAQ;AACpD;AAEA,SAAS,iBAAiB,SAAS;AACjC,SAAO,gBAAgB,OAAO,KAAK,QAAQ,aAAa,MAAM;AAChE;AAEA,SAAS,eAAe,SAAS;AAC/B,SAAO,QAAQ,SAAS,YAAY,KAAK;AAC3C;AAEA,SAAS,gBAAgB,SAAS;AAChC,SAAO,QAAQ,SAAS,YAAY,KAAK;AAC3C;AAEA,SAAS,iBAAiB,SAAS;AACjC,MAAI,CAAC,QAAQ,aAAa,UAAU,KAAK,QAAQ,aAAa,QAAW;AACvE,WAAO;AAAA,EACT;AACA,MAAI,WAAW,QAAQ,aAAa,UAAU;AAC9C,SAAO,CAAC,EAAE,YAAY,CAAC,MAAM,SAAS,UAAU,EAAE,CAAC;AACrD;AAKA,SAAS,iBAAiB,SAAS;AACjC,MAAI,CAAC,iBAAiB,OAAO,GAAG;AAC9B,WAAO;AAAA,EACT;AAEA,QAAM,WAAW,SAAS,QAAQ,aAAa,UAAU,KAAK,IAAI,EAAE;AACpE,SAAO,MAAM,QAAQ,IAAI,KAAK;AAChC;AAEA,SAAS,yBAAyB,SAAS;AACzC,MAAI,WAAW,QAAQ,SAAS,YAAY;AAC5C,MAAI,YAAY,aAAa,WAAW,QAAQ;AAChD,SAAO,cAAc,UAAU,cAAc,cAAc,aAAa,YAAY,aAAa;AACnG;AAKA,SAAS,uBAAuB,SAAS;AAEvC,MAAI,cAAc,OAAO,GAAG;AAC1B,WAAO;AAAA,EACT;AACA,SAAO,oBAAoB,OAAO,KAAK,iBAAiB,OAAO,KAAK,QAAQ,aAAa,iBAAiB,KAAK,iBAAiB,OAAO;AACzI;AAEA,SAAS,UAAU,MAAM;AAEvB,SAAO,KAAK,iBAAiB,KAAK,cAAc,eAAe;AACjE;AASA,IAAM,YAAN,MAAgB;AAAA;AAAA,EAEd,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,OAAO;AACjB,SAAK,WAAW;AAChB,QAAI,KAAK,gBAAgB,KAAK,YAAY;AACxC,WAAK,sBAAsB,OAAO,KAAK,YAAY;AACnD,WAAK,sBAAsB,OAAO,KAAK,UAAU;AAAA,IACnD;AAAA,EACF;AAAA,EACA,YAAY,UAAU,UAAU,SAAS,WAAW,eAAe,OACnE,WAAW;AACT,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,UAAU;AACf,SAAK,YAAY;AACjB,SAAK,YAAY;AACjB,SAAK,eAAe;AAEpB,SAAK,sBAAsB,MAAM,KAAK,yBAAyB;AAC/D,SAAK,oBAAoB,MAAM,KAAK,0BAA0B;AAC9D,SAAK,WAAW;AAChB,QAAI,CAAC,cAAc;AACjB,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA;AAAA,EAEA,UAAU;AACR,UAAM,cAAc,KAAK;AACzB,UAAM,YAAY,KAAK;AACvB,QAAI,aAAa;AACf,kBAAY,oBAAoB,SAAS,KAAK,mBAAmB;AACjE,kBAAY,OAAO;AAAA,IACrB;AACA,QAAI,WAAW;AACb,gBAAU,oBAAoB,SAAS,KAAK,iBAAiB;AAC7D,gBAAU,OAAO;AAAA,IACnB;AACA,SAAK,eAAe,KAAK,aAAa;AACtC,SAAK,eAAe;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB;AAEd,QAAI,KAAK,cAAc;AACrB,aAAO;AAAA,IACT;AACA,SAAK,QAAQ,kBAAkB,MAAM;AACnC,UAAI,CAAC,KAAK,cAAc;AACtB,aAAK,eAAe,KAAK,cAAc;AACvC,aAAK,aAAa,iBAAiB,SAAS,KAAK,mBAAmB;AAAA,MACtE;AACA,UAAI,CAAC,KAAK,YAAY;AACpB,aAAK,aAAa,KAAK,cAAc;AACrC,aAAK,WAAW,iBAAiB,SAAS,KAAK,iBAAiB;AAAA,MAClE;AAAA,IACF,CAAC;AACD,QAAI,KAAK,SAAS,YAAY;AAC5B,WAAK,SAAS,WAAW,aAAa,KAAK,cAAc,KAAK,QAAQ;AACtE,WAAK,SAAS,WAAW,aAAa,KAAK,YAAY,KAAK,SAAS,WAAW;AAChF,WAAK,eAAe;AAAA,IACtB;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,6BAA6B,SAAS;AACpC,WAAO,IAAI,QAAQ,aAAW;AAC5B,WAAK,iBAAiB,MAAM,QAAQ,KAAK,oBAAoB,OAAO,CAAC,CAAC;AAAA,IACxE,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,mCAAmC,SAAS;AAC1C,WAAO,IAAI,QAAQ,aAAW;AAC5B,WAAK,iBAAiB,MAAM,QAAQ,KAAK,0BAA0B,OAAO,CAAC,CAAC;AAAA,IAC9E,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,kCAAkC,SAAS;AACzC,WAAO,IAAI,QAAQ,aAAW;AAC5B,WAAK,iBAAiB,MAAM,QAAQ,KAAK,yBAAyB,OAAO,CAAC,CAAC;AAAA,IAC7E,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,mBAAmB,OAAO;AAExB,UAAM,UAAU,KAAK,SAAS,iBAAiB,qBAAqB,KAAK,qBAA0B,KAAK,iBAAsB,KAAK,GAAG;AACtI,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AAEvC,YAAI,QAAQ,CAAC,EAAE,aAAa,aAAa,KAAK,EAAE,GAAG;AACjD,kBAAQ,KAAK,gDAAgD,KAAK,yBAA8B,KAAK,iEAAsE,QAAQ,CAAC,CAAC;AAAA,QACvL,WAAW,QAAQ,CAAC,EAAE,aAAa,oBAAoB,KAAK,EAAE,GAAG;AAC/D,kBAAQ,KAAK,uDAAuD,KAAK,yBAA8B,KAAK,iEAAsE,QAAQ,CAAC,CAAC;AAAA,QAC9L;AAAA,MACF;AAAA,IACF;AACA,QAAI,SAAS,SAAS;AACpB,aAAO,QAAQ,SAAS,QAAQ,CAAC,IAAI,KAAK,yBAAyB,KAAK,QAAQ;AAAA,IAClF;AACA,WAAO,QAAQ,SAAS,QAAQ,QAAQ,SAAS,CAAC,IAAI,KAAK,wBAAwB,KAAK,QAAQ;AAAA,EAClG;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB,SAAS;AAE3B,UAAM,oBAAoB,KAAK,SAAS,cAAc,wCAA6C;AACnG,QAAI,mBAAmB;AAErB,WAAK,OAAO,cAAc,eAAe,cAAc,kBAAkB,aAAa,mBAAmB,GAAG;AAC1G,gBAAQ,KAAK,2IAAqJ,iBAAiB;AAAA,MACrL;AAGA,WAAK,OAAO,cAAc,eAAe,cAAc,CAAC,KAAK,SAAS,YAAY,iBAAiB,GAAG;AACpG,gBAAQ,KAAK,0DAA0D,iBAAiB;AAAA,MAC1F;AACA,UAAI,CAAC,KAAK,SAAS,YAAY,iBAAiB,GAAG;AACjD,cAAM,iBAAiB,KAAK,yBAAyB,iBAAiB;AACtE,wBAAgB,MAAM,OAAO;AAC7B,eAAO,CAAC,CAAC;AAAA,MACX;AACA,wBAAkB,MAAM,OAAO;AAC/B,aAAO;AAAA,IACT;AACA,WAAO,KAAK,0BAA0B,OAAO;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,0BAA0B,SAAS;AACjC,UAAM,oBAAoB,KAAK,mBAAmB,OAAO;AACzD,QAAI,mBAAmB;AACrB,wBAAkB,MAAM,OAAO;AAAA,IACjC;AACA,WAAO,CAAC,CAAC;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,yBAAyB,SAAS;AAChC,UAAM,oBAAoB,KAAK,mBAAmB,KAAK;AACvD,QAAI,mBAAmB;AACrB,wBAAkB,MAAM,OAAO;AAAA,IACjC;AACA,WAAO,CAAC,CAAC;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AACZ,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,yBAAyB,MAAM;AAC7B,QAAI,KAAK,SAAS,YAAY,IAAI,KAAK,KAAK,SAAS,WAAW,IAAI,GAAG;AACrE,aAAO;AAAA,IACT;AACA,UAAM,WAAW,KAAK;AACtB,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,YAAM,gBAAgB,SAAS,CAAC,EAAE,aAAa,KAAK,UAAU,eAAe,KAAK,yBAAyB,SAAS,CAAC,CAAC,IAAI;AAC1H,UAAI,eAAe;AACjB,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,wBAAwB,MAAM;AAC5B,QAAI,KAAK,SAAS,YAAY,IAAI,KAAK,KAAK,SAAS,WAAW,IAAI,GAAG;AACrE,aAAO;AAAA,IACT;AAEA,UAAM,WAAW,KAAK;AACtB,aAAS,IAAI,SAAS,SAAS,GAAG,KAAK,GAAG,KAAK;AAC7C,YAAM,gBAAgB,SAAS,CAAC,EAAE,aAAa,KAAK,UAAU,eAAe,KAAK,wBAAwB,SAAS,CAAC,CAAC,IAAI;AACzH,UAAI,eAAe;AACjB,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,gBAAgB;AACd,UAAM,SAAS,KAAK,UAAU,cAAc,KAAK;AACjD,SAAK,sBAAsB,KAAK,UAAU,MAAM;AAChD,WAAO,UAAU,IAAI,qBAAqB;AAC1C,WAAO,UAAU,IAAI,uBAAuB;AAC5C,WAAO,aAAa,eAAe,MAAM;AACzC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,sBAAsB,WAAW,QAAQ;AAGvC,gBAAY,OAAO,aAAa,YAAY,GAAG,IAAI,OAAO,gBAAgB,UAAU;AAAA,EACtF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc,SAAS;AACrB,QAAI,KAAK,gBAAgB,KAAK,YAAY;AACxC,WAAK,sBAAsB,SAAS,KAAK,YAAY;AACrD,WAAK,sBAAsB,SAAS,KAAK,UAAU;AAAA,IACrD;AAAA,EACF;AAAA;AAAA,EAEA,iBAAiB,IAAI;AAEnB,QAAI,KAAK,WAAW;AAClB,sBAAgB,IAAI;AAAA,QAClB,UAAU,KAAK;AAAA,MACjB,CAAC;AAAA,IACH,OAAO;AACL,iBAAW,EAAE;AAAA,IACf;AAAA,EACF;AACF;AAIA,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,YAAY,UAAU,SAAS,WAAW;AACxC,SAAK,WAAW;AAChB,SAAK,UAAU;AACf,SAAK,YAAY,OAAO,QAAQ;AAChC,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,SAAS,uBAAuB,OAAO;AAC5C,WAAO,IAAI,UAAU,SAAS,KAAK,UAAU,KAAK,SAAS,KAAK,WAAW,sBAAsB,KAAK,SAAS;AAAA,EACjH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,mBAAmB;AAC/D,aAAO,KAAK,qBAAqB,mBAAqB,SAAS,oBAAoB,GAAM,SAAY,MAAM,GAAM,SAAS,QAAQ,CAAC;AAAA,IACrI;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,kBAAiB;AAAA,MAC1B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AAEH,IAAM,eAAN,MAAM,cAAa;AAAA;AAAA,EAEjB,IAAI,UAAU;AACZ,WAAO,KAAK,WAAW,WAAW;AAAA,EACpC;AAAA,EACA,IAAI,QAAQ,OAAO;AACjB,QAAI,KAAK,WAAW;AAClB,WAAK,UAAU,UAAU;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,YAAY,aAAa,mBAKzB,WAAW;AACT,SAAK,cAAc;AACnB,SAAK,oBAAoB;AAEzB,SAAK,4BAA4B;AACjC,UAAM,WAAW,OAAO,QAAQ;AAChC,QAAI,SAAS,WAAW;AACtB,WAAK,YAAY,KAAK,kBAAkB,OAAO,KAAK,YAAY,eAAe,IAAI;AAAA,IACrF;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,WAAW,QAAQ;AAGxB,QAAI,KAAK,2BAA2B;AAClC,WAAK,0BAA0B,MAAM;AACrC,WAAK,4BAA4B;AAAA,IACnC;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,SAAK,WAAW,cAAc;AAC9B,QAAI,KAAK,aAAa;AACpB,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA,EACA,YAAY;AACV,QAAI,KAAK,aAAa,CAAC,KAAK,UAAU,YAAY,GAAG;AACnD,WAAK,UAAU,cAAc;AAAA,IAC/B;AAAA,EACF;AAAA,EACA,YAAY,SAAS;AACnB,UAAM,oBAAoB,QAAQ,aAAa;AAC/C,QAAI,qBAAqB,CAAC,kBAAkB,eAAe,KAAK,eAAe,KAAK,WAAW,YAAY,GAAG;AAC5G,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA,EACA,gBAAgB;AACd,SAAK,4BAA4B,kCAAkC;AACnE,SAAK,WAAW,6BAA6B;AAAA,EAC/C;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,qBAAqB,mBAAmB;AAC3D,aAAO,KAAK,qBAAqB,eAAiB,kBAAqB,UAAU,GAAM,kBAAkB,gBAAgB,GAAM,kBAAkB,QAAQ,CAAC;AAAA,IAC5J;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,gBAAgB,EAAE,CAAC;AAAA,MACpC,QAAQ;AAAA,QACN,SAAS,CAAC,GAAG,gBAAgB,WAAW,gBAAgB;AAAA,QACxD,aAAa,CAAC,GAAG,2BAA2B,eAAe,gBAAgB;AAAA,MAC7E;AAAA,MACA,UAAU,CAAC,cAAc;AAAA,MACzB,YAAY;AAAA,MACZ,UAAU,CAAI,0BAA6B,oBAAoB;AAAA,IACjE,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAQH,IAAM,wBAAN,cAAoC,UAAU;AAAA;AAAA,EAE5C,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,OAAO;AACjB,SAAK,WAAW;AAChB,QAAI,KAAK,UAAU;AACjB,WAAK,kBAAkB,SAAS,IAAI;AAAA,IACtC,OAAO;AACL,WAAK,kBAAkB,WAAW,IAAI;AAAA,IACxC;AAAA,EACF;AAAA,EACA,YAAY,UAAU,UAAU,SAAS,WAAW,mBAAmB,gBAAgB,QAAQ,UAAU;AACvG,UAAM,UAAU,UAAU,SAAS,WAAW,OAAO,OAAO,QAAQ;AACpE,SAAK,oBAAoB;AACzB,SAAK,iBAAiB;AACtB,SAAK,kBAAkB,SAAS,IAAI;AAAA,EACtC;AAAA;AAAA,EAEA,UAAU;AACR,SAAK,kBAAkB,WAAW,IAAI;AACtC,UAAM,QAAQ;AAAA,EAChB;AAAA;AAAA,EAEA,UAAU;AACR,SAAK,eAAe,aAAa,IAAI;AACrC,SAAK,cAAc,IAAI;AAAA,EACzB;AAAA;AAAA,EAEA,WAAW;AACT,SAAK,eAAe,WAAW,IAAI;AACnC,SAAK,cAAc,KAAK;AAAA,EAC1B;AACF;AAMA,IAAM,sCAAN,MAA0C;AAAA,EACxC,cAAc;AAEZ,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA,EAEA,aAAa,WAAW;AAEtB,QAAI,KAAK,WAAW;AAClB,gBAAU,UAAU,oBAAoB,SAAS,KAAK,WAAW,IAAI;AAAA,IACvE;AACA,SAAK,YAAY,OAAK,KAAK,WAAW,WAAW,CAAC;AAClD,cAAU,QAAQ,kBAAkB,MAAM;AACxC,gBAAU,UAAU,iBAAiB,SAAS,KAAK,WAAW,IAAI;AAAA,IACpE,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,WAAW,WAAW;AACpB,QAAI,CAAC,KAAK,WAAW;AACnB;AAAA,IACF;AACA,cAAU,UAAU,oBAAoB,SAAS,KAAK,WAAW,IAAI;AACrE,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW,WAAW,OAAO;AAC3B,UAAM,SAAS,MAAM;AACrB,UAAM,gBAAgB,UAAU;AAGhC,QAAI,UAAU,CAAC,cAAc,SAAS,MAAM,KAAK,CAAC,OAAO,UAAU,sBAAsB,GAAG;AAI1F,iBAAW,MAAM;AAEf,YAAI,UAAU,WAAW,CAAC,cAAc,SAAS,UAAU,UAAU,aAAa,GAAG;AACnF,oBAAU,0BAA0B;AAAA,QACtC;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF;AAGA,IAAM,4BAA4B,IAAI,eAAe,2BAA2B;AAGhF,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,cAAc;AAGZ,SAAK,kBAAkB,CAAC;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS,WAAW;AAElB,SAAK,kBAAkB,KAAK,gBAAgB,OAAO,QAAM,OAAO,SAAS;AACzE,QAAI,QAAQ,KAAK;AACjB,QAAI,MAAM,QAAQ;AAChB,YAAM,MAAM,SAAS,CAAC,EAAE,SAAS;AAAA,IACnC;AACA,UAAM,KAAK,SAAS;AACpB,cAAU,QAAQ;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,WAAW;AACpB,cAAU,SAAS;AACnB,UAAM,QAAQ,KAAK;AACnB,UAAM,IAAI,MAAM,QAAQ,SAAS;AACjC,QAAI,MAAM,IAAI;AACZ,YAAM,OAAO,GAAG,CAAC;AACjB,UAAI,MAAM,QAAQ;AAChB,cAAM,MAAM,SAAS,CAAC,EAAE,QAAQ;AAAA,MAClC;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,mBAAmB;AAC/D,aAAO,KAAK,qBAAqB,mBAAkB;AAAA,IACrD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,kBAAiB;AAAA,MAC1B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAGH,IAAM,+BAAN,MAAM,8BAA6B;AAAA,EACjC,YAAY,UAAU,SAAS,mBAAmB,WAAW,gBAAgB;AAC3E,SAAK,WAAW;AAChB,SAAK,UAAU;AACf,SAAK,oBAAoB;AACzB,SAAK,YAAY,OAAO,QAAQ;AAChC,SAAK,YAAY;AAEjB,SAAK,iBAAiB,kBAAkB,IAAI,oCAAoC;AAAA,EAClF;AAAA,EACA,OAAO,SAAS,SAAS;AAAA,IACvB,OAAO;AAAA,EACT,GAAG;AACD,QAAI;AACJ,QAAI,OAAO,WAAW,WAAW;AAC/B,qBAAe;AAAA,QACb,OAAO;AAAA,MACT;AAAA,IACF,OAAO;AACL,qBAAe;AAAA,IACjB;AACA,WAAO,IAAI,sBAAsB,SAAS,KAAK,UAAU,KAAK,SAAS,KAAK,WAAW,KAAK,mBAAmB,KAAK,gBAAgB,cAAc,KAAK,SAAS;AAAA,EAClK;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,qCAAqC,mBAAmB;AAC3E,aAAO,KAAK,qBAAqB,+BAAiC,SAAS,oBAAoB,GAAM,SAAY,MAAM,GAAM,SAAS,gBAAgB,GAAM,SAAS,QAAQ,GAAM,SAAS,2BAA2B,CAAC,CAAC;AAAA,IAC3N;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,8BAA6B;AAAA,MACtC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,8BAA8B,CAAC;AAAA,IACrG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,yBAAyB;AAAA,IAClC,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AAGH,SAAS,gCAAgC,OAAO;AAM9C,SAAO,MAAM,YAAY,KAAK,MAAM,WAAW;AACjD;AAEA,SAAS,iCAAiC,OAAO;AAC/C,QAAM,QAAQ,MAAM,WAAW,MAAM,QAAQ,CAAC,KAAK,MAAM,kBAAkB,MAAM,eAAe,CAAC;AAKjG,SAAO,CAAC,CAAC,SAAS,MAAM,eAAe,OAAO,MAAM,WAAW,QAAQ,MAAM,YAAY,OAAO,MAAM,WAAW,QAAQ,MAAM,YAAY;AAC7I;AAMA,IAAM,kCAAkC,IAAI,eAAe,qCAAqC;AAiBhG,IAAM,0CAA0C;AAAA,EAC9C,YAAY,CAAC,KAAK,SAAS,UAAU,MAAM,KAAK;AAClD;AAQA,IAAM,kBAAkB;AAKxB,IAAM,+BAA+B,gCAAgC;AAAA,EACnE,SAAS;AAAA,EACT,SAAS;AACX,CAAC;AAeD,IAAM,wBAAN,MAAM,uBAAsB;AAAA;AAAA,EAE1B,IAAI,qBAAqB;AACvB,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA,EACA,YAAY,WAAW,QAAQC,WAAU,SAAS;AAChD,SAAK,YAAY;AAKjB,SAAK,oBAAoB;AAEzB,SAAK,YAAY,IAAI,6BAAgB,IAAI;AAKzC,SAAK,eAAe;AAKpB,SAAK,aAAa,WAAS;AAGzB,UAAI,KAAK,UAAU,YAAY,KAAK,aAAW,YAAY,MAAM,OAAO,GAAG;AACzE;AAAA,MACF;AACA,WAAK,UAAU,KAAK,UAAU;AAC9B,WAAK,oBAAoB,gBAAgB,KAAK;AAAA,IAChD;AAKA,SAAK,eAAe,WAAS;AAI3B,UAAI,KAAK,IAAI,IAAI,KAAK,eAAe,iBAAiB;AACpD;AAAA,MACF;AAGA,WAAK,UAAU,KAAK,gCAAgC,KAAK,IAAI,aAAa,OAAO;AACjF,WAAK,oBAAoB,gBAAgB,KAAK;AAAA,IAChD;AAKA,SAAK,gBAAgB,WAAS;AAG5B,UAAI,iCAAiC,KAAK,GAAG;AAC3C,aAAK,UAAU,KAAK,UAAU;AAC9B;AAAA,MACF;AAGA,WAAK,eAAe,KAAK,IAAI;AAC7B,WAAK,UAAU,KAAK,OAAO;AAC3B,WAAK,oBAAoB,gBAAgB,KAAK;AAAA,IAChD;AACA,SAAK,WAAW,kCACX,0CACA;AAGL,SAAK,mBAAmB,KAAK,UAAU,SAAK,wBAAK,CAAC,CAAC;AACnD,SAAK,kBAAkB,KAAK,iBAAiB,SAAK,wCAAqB,CAAC;AAGxE,QAAI,UAAU,WAAW;AACvB,aAAO,kBAAkB,MAAM;AAC7B,QAAAA,UAAS,iBAAiB,WAAW,KAAK,YAAY,4BAA4B;AAClF,QAAAA,UAAS,iBAAiB,aAAa,KAAK,cAAc,4BAA4B;AACtF,QAAAA,UAAS,iBAAiB,cAAc,KAAK,eAAe,4BAA4B;AAAA,MAC1F,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,UAAU,SAAS;AACxB,QAAI,KAAK,UAAU,WAAW;AAC5B,eAAS,oBAAoB,WAAW,KAAK,YAAY,4BAA4B;AACrF,eAAS,oBAAoB,aAAa,KAAK,cAAc,4BAA4B;AACzF,eAAS,oBAAoB,cAAc,KAAK,eAAe,4BAA4B;AAAA,IAC7F;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,8BAA8B,mBAAmB;AACpE,aAAO,KAAK,qBAAqB,wBAA0B,SAAY,QAAQ,GAAM,SAAY,MAAM,GAAM,SAAS,QAAQ,GAAM,SAAS,iCAAiC,CAAC,CAAC;AAAA,IAClL;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,uBAAsB;AAAA,MAC/B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,+BAA+B;AAAA,IACxC,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,+BAA+B,IAAI,eAAe,wBAAwB;AAAA,EAC9E,YAAY;AAAA,EACZ,SAAS;AACX,CAAC;AAED,SAAS,uCAAuC;AAC9C,SAAO;AACT;AAEA,IAAM,iCAAiC,IAAI,eAAe,gCAAgC;AAC1F,IAAI,YAAY;AAChB,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,YAAY,cAAc,SAAS,WAAW,iBAAiB;AAC7D,SAAK,UAAU;AACf,SAAK,kBAAkB;AAIvB,SAAK,YAAY;AACjB,SAAK,eAAe,gBAAgB,KAAK,mBAAmB;AAAA,EAC9D;AAAA,EACA,SAAS,YAAY,MAAM;AACzB,UAAM,iBAAiB,KAAK;AAC5B,QAAI;AACJ,QAAI;AACJ,QAAI,KAAK,WAAW,KAAK,OAAO,KAAK,CAAC,MAAM,UAAU;AACpD,iBAAW,KAAK,CAAC;AAAA,IACnB,OAAO;AACL,OAAC,YAAY,QAAQ,IAAI;AAAA,IAC3B;AACA,SAAK,MAAM;AACX,iBAAa,KAAK,gBAAgB;AAClC,QAAI,CAAC,YAAY;AACf,mBAAa,kBAAkB,eAAe,aAAa,eAAe,aAAa;AAAA,IACzF;AACA,QAAI,YAAY,QAAQ,gBAAgB;AACtC,iBAAW,eAAe;AAAA,IAC5B;AAEA,SAAK,aAAa,aAAa,aAAa,UAAU;AACtD,QAAI,KAAK,aAAa,IAAI;AACxB,WAAK,yBAAyB,KAAK,aAAa,EAAE;AAAA,IACpD;AAMA,WAAO,KAAK,QAAQ,kBAAkB,MAAM;AAC1C,UAAI,CAAC,KAAK,iBAAiB;AACzB,aAAK,kBAAkB,IAAI,QAAQ,aAAW,KAAK,kBAAkB,OAAO;AAAA,MAC9E;AACA,mBAAa,KAAK,gBAAgB;AAClC,WAAK,mBAAmB,WAAW,MAAM;AACvC,aAAK,aAAa,cAAc;AAChC,YAAI,OAAO,aAAa,UAAU;AAChC,eAAK,mBAAmB,WAAW,MAAM,KAAK,MAAM,GAAG,QAAQ;AAAA,QACjE;AAGA,aAAK,kBAAkB;AACvB,aAAK,kBAAkB,KAAK,kBAAkB;AAAA,MAChD,GAAG,GAAG;AACN,aAAO,KAAK;AAAA,IACd,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ;AACN,QAAI,KAAK,cAAc;AACrB,WAAK,aAAa,cAAc;AAAA,IAClC;AAAA,EACF;AAAA,EACA,cAAc;AACZ,iBAAa,KAAK,gBAAgB;AAClC,SAAK,cAAc,OAAO;AAC1B,SAAK,eAAe;AACpB,SAAK,kBAAkB;AACvB,SAAK,kBAAkB,KAAK,kBAAkB;AAAA,EAChD;AAAA,EACA,qBAAqB;AACnB,UAAM,eAAe;AACrB,UAAM,mBAAmB,KAAK,UAAU,uBAAuB,YAAY;AAC3E,UAAM,SAAS,KAAK,UAAU,cAAc,KAAK;AAEjD,aAAS,IAAI,GAAG,IAAI,iBAAiB,QAAQ,KAAK;AAChD,uBAAiB,CAAC,EAAE,OAAO;AAAA,IAC7B;AACA,WAAO,UAAU,IAAI,YAAY;AACjC,WAAO,UAAU,IAAI,qBAAqB;AAC1C,WAAO,aAAa,eAAe,MAAM;AACzC,WAAO,aAAa,aAAa,QAAQ;AACzC,WAAO,KAAK,sBAAsB,WAAW;AAC7C,SAAK,UAAU,KAAK,YAAY,MAAM;AACtC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,yBAAyB,IAAI;AAO3B,UAAM,SAAS,KAAK,UAAU,iBAAiB,mDAAmD;AAClG,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,YAAM,QAAQ,OAAO,CAAC;AACtB,YAAM,WAAW,MAAM,aAAa,WAAW;AAC/C,UAAI,CAAC,UAAU;AACb,cAAM,aAAa,aAAa,EAAE;AAAA,MACpC,WAAW,SAAS,QAAQ,EAAE,MAAM,IAAI;AACtC,cAAM,aAAa,aAAa,WAAW,MAAM,EAAE;AAAA,MACrD;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,sBAAsB,mBAAmB;AAC5D,aAAO,KAAK,qBAAqB,gBAAkB,SAAS,8BAA8B,CAAC,GAAM,SAAY,MAAM,GAAM,SAAS,QAAQ,GAAM,SAAS,gCAAgC,CAAC,CAAC;AAAA,IAC7L;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,eAAc;AAAA,MACvB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,4BAA4B;AAAA,IACrC,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,8BAA8B;AAAA,IACvC,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AAKH,IAAM,cAAN,MAAM,aAAY;AAAA;AAAA,EAEhB,IAAI,aAAa;AACf,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,WAAW,OAAO;AACpB,SAAK,cAAc,UAAU,SAAS,UAAU,cAAc,QAAQ;AACtE,QAAI,KAAK,gBAAgB,OAAO;AAC9B,UAAI,KAAK,eAAe;AACtB,aAAK,cAAc,YAAY;AAC/B,aAAK,gBAAgB;AAAA,MACvB;AAAA,IACF,WAAW,CAAC,KAAK,eAAe;AAC9B,WAAK,gBAAgB,KAAK,QAAQ,kBAAkB,MAAM;AACxD,eAAO,KAAK,iBAAiB,QAAQ,KAAK,WAAW,EAAE,UAAU,MAAM;AAErE,gBAAM,cAAc,KAAK,YAAY,cAAc;AAGnD,cAAI,gBAAgB,KAAK,wBAAwB;AAC/C,iBAAK,eAAe,SAAS,aAAa,KAAK,aAAa,KAAK,QAAQ;AACzE,iBAAK,yBAAyB;AAAA,UAChC;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,YAAY,aAAa,gBAAgB,kBAAkB,SAAS;AAClE,SAAK,cAAc;AACnB,SAAK,iBAAiB;AACtB,SAAK,mBAAmB;AACxB,SAAK,UAAU;AACf,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,eAAe;AACtB,WAAK,cAAc,YAAY;AAAA,IACjC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,oBAAoB,mBAAmB;AAC1D,aAAO,KAAK,qBAAqB,cAAgB,kBAAqB,UAAU,GAAM,kBAAkB,aAAa,GAAM,kBAAuB,eAAe,GAAM,kBAAqB,MAAM,CAAC;AAAA,IACrM;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,eAAe,EAAE,CAAC;AAAA,MACnC,QAAQ;AAAA,QACN,YAAY,CAAC,GAAG,eAAe,YAAY;AAAA,QAC3C,UAAU,CAAC,GAAG,uBAAuB,UAAU;AAAA,MACjD;AAAA,MACA,UAAU,CAAC,aAAa;AAAA,MACxB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAGH,IAAI;AAAA,CACH,SAAUC,4BAA2B;AAMpC,EAAAA,2BAA0BA,2BAA0B,WAAW,IAAI,CAAC,IAAI;AAKxE,EAAAA,2BAA0BA,2BAA0B,UAAU,IAAI,CAAC,IAAI;AACzE,GAAG,8BAA8B,4BAA4B,CAAC,EAAE;AAEhE,IAAM,gCAAgC,IAAI,eAAe,mCAAmC;AAK5F,IAAM,8BAA8B,gCAAgC;AAAA,EAClE,SAAS;AAAA,EACT,SAAS;AACX,CAAC;AAED,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,YAAY,SAAS,WAAW,wBAChCD,WAAU,SAAS;AACjB,SAAK,UAAU;AACf,SAAK,YAAY;AACjB,SAAK,yBAAyB;AAE9B,SAAK,UAAU;AAEf,SAAK,iBAAiB;AAKtB,SAAK,8BAA8B;AAEnC,SAAK,eAAe,oBAAI,IAAI;AAE5B,SAAK,yBAAyB;AAO9B,SAAK,8BAA8B,oBAAI,IAAI;AAK3C,SAAK,uBAAuB,MAAM;AAGhC,WAAK,iBAAiB;AACtB,WAAK,wBAAwB,OAAO,WAAW,MAAM,KAAK,iBAAiB,KAAK;AAAA,IAClF;AAEA,SAAK,6BAA6B,IAAI,qBAAQ;AAK9C,SAAK,gCAAgC,WAAS;AAC5C,YAAM,SAAS,gBAAgB,KAAK;AAEpC,eAAS,UAAU,QAAQ,SAAS,UAAU,QAAQ,eAAe;AACnE,YAAI,MAAM,SAAS,SAAS;AAC1B,eAAK,SAAS,OAAO,OAAO;AAAA,QAC9B,OAAO;AACL,eAAK,QAAQ,OAAO,OAAO;AAAA,QAC7B;AAAA,MACF;AAAA,IACF;AACA,SAAK,YAAYA;AACjB,SAAK,iBAAiB,SAAS,iBAAiB,0BAA0B;AAAA,EAC5E;AAAA,EACA,QAAQ,SAAS,gBAAgB,OAAO;AACtC,UAAM,gBAAgB,cAAc,OAAO;AAE3C,QAAI,CAAC,KAAK,UAAU,aAAa,cAAc,aAAa,GAAG;AAE7D,iBAAO,iBAAG;AAAA,IACZ;AAIA,UAAM,WAAW,eAAe,aAAa,KAAK,KAAK,aAAa;AACpE,UAAM,aAAa,KAAK,aAAa,IAAI,aAAa;AAEtD,QAAI,YAAY;AACd,UAAI,eAAe;AAIjB,mBAAW,gBAAgB;AAAA,MAC7B;AACA,aAAO,WAAW;AAAA,IACpB;AAEA,UAAM,OAAO;AAAA,MACX;AAAA,MACA,SAAS,IAAI,qBAAQ;AAAA,MACrB;AAAA,IACF;AACA,SAAK,aAAa,IAAI,eAAe,IAAI;AACzC,SAAK,yBAAyB,IAAI;AAClC,WAAO,KAAK;AAAA,EACd;AAAA,EACA,eAAe,SAAS;AACtB,UAAM,gBAAgB,cAAc,OAAO;AAC3C,UAAM,cAAc,KAAK,aAAa,IAAI,aAAa;AACvD,QAAI,aAAa;AACf,kBAAY,QAAQ,SAAS;AAC7B,WAAK,YAAY,aAAa;AAC9B,WAAK,aAAa,OAAO,aAAa;AACtC,WAAK,uBAAuB,WAAW;AAAA,IACzC;AAAA,EACF;AAAA,EACA,SAAS,SAAS,QAAQ,SAAS;AACjC,UAAM,gBAAgB,cAAc,OAAO;AAC3C,UAAM,iBAAiB,KAAK,aAAa,EAAE;AAI3C,QAAI,kBAAkB,gBAAgB;AACpC,WAAK,wBAAwB,aAAa,EAAE,QAAQ,CAAC,CAAC,gBAAgB,IAAI,MAAM,KAAK,eAAe,gBAAgB,QAAQ,IAAI,CAAC;AAAA,IACnI,OAAO;AACL,WAAK,WAAW,MAAM;AAEtB,UAAI,OAAO,cAAc,UAAU,YAAY;AAC7C,sBAAc,MAAM,OAAO;AAAA,MAC7B;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,aAAa,QAAQ,CAAC,OAAO,YAAY,KAAK,eAAe,OAAO,CAAC;AAAA,EAC5E;AAAA;AAAA,EAEA,eAAe;AACb,WAAO,KAAK,aAAa;AAAA,EAC3B;AAAA;AAAA,EAEA,aAAa;AACX,UAAM,MAAM,KAAK,aAAa;AAC9B,WAAO,IAAI,eAAe;AAAA,EAC5B;AAAA,EACA,gBAAgB,kBAAkB;AAChC,QAAI,KAAK,SAAS;AAGhB,UAAI,KAAK,6BAA6B;AACpC,eAAO,KAAK,2BAA2B,gBAAgB,IAAI,UAAU;AAAA,MACvE,OAAO;AACL,eAAO,KAAK;AAAA,MACd;AAAA,IACF;AAUA,QAAI,KAAK,kBAAkB,KAAK,kBAAkB;AAChD,aAAO,KAAK;AAAA,IACd;AAKA,QAAI,oBAAoB,KAAK,iCAAiC,gBAAgB,GAAG;AAC/E,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,2BAA2B,kBAAkB;AAW3C,WAAO,KAAK,mBAAmB,0BAA0B,YAAY,CAAC,CAAC,kBAAkB,SAAS,KAAK,uBAAuB,iBAAiB;AAAA,EACjJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,SAAS,QAAQ;AAC3B,YAAQ,UAAU,OAAO,eAAe,CAAC,CAAC,MAAM;AAChD,YAAQ,UAAU,OAAO,qBAAqB,WAAW,OAAO;AAChE,YAAQ,UAAU,OAAO,wBAAwB,WAAW,UAAU;AACtE,YAAQ,UAAU,OAAO,qBAAqB,WAAW,OAAO;AAChE,YAAQ,UAAU,OAAO,uBAAuB,WAAW,SAAS;AAAA,EACtE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW,QAAQ,oBAAoB,OAAO;AAC5C,SAAK,QAAQ,kBAAkB,MAAM;AACnC,WAAK,UAAU;AACf,WAAK,8BAA8B,WAAW,WAAW;AAMzD,UAAI,KAAK,mBAAmB,0BAA0B,WAAW;AAC/D,qBAAa,KAAK,gBAAgB;AAClC,cAAM,KAAK,KAAK,8BAA8B,kBAAkB;AAChE,aAAK,mBAAmB,WAAW,MAAM,KAAK,UAAU,MAAM,EAAE;AAAA,MAClE;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS,OAAO,SAAS;AAOvB,UAAM,cAAc,KAAK,aAAa,IAAI,OAAO;AACjD,UAAM,mBAAmB,gBAAgB,KAAK;AAC9C,QAAI,CAAC,eAAe,CAAC,YAAY,iBAAiB,YAAY,kBAAkB;AAC9E;AAAA,IACF;AACA,SAAK,eAAe,SAAS,KAAK,gBAAgB,gBAAgB,GAAG,WAAW;AAAA,EAClF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ,OAAO,SAAS;AAGtB,UAAM,cAAc,KAAK,aAAa,IAAI,OAAO;AACjD,QAAI,CAAC,eAAe,YAAY,iBAAiB,MAAM,yBAAyB,QAAQ,QAAQ,SAAS,MAAM,aAAa,GAAG;AAC7H;AAAA,IACF;AACA,SAAK,YAAY,OAAO;AACxB,SAAK,YAAY,aAAa,IAAI;AAAA,EACpC;AAAA,EACA,YAAY,MAAM,QAAQ;AACxB,QAAI,KAAK,QAAQ,UAAU,QAAQ;AACjC,WAAK,QAAQ,IAAI,MAAM,KAAK,QAAQ,KAAK,MAAM,CAAC;AAAA,IAClD;AAAA,EACF;AAAA,EACA,yBAAyB,aAAa;AACpC,QAAI,CAAC,KAAK,UAAU,WAAW;AAC7B;AAAA,IACF;AACA,UAAM,WAAW,YAAY;AAC7B,UAAM,yBAAyB,KAAK,4BAA4B,IAAI,QAAQ,KAAK;AACjF,QAAI,CAAC,wBAAwB;AAC3B,WAAK,QAAQ,kBAAkB,MAAM;AACnC,iBAAS,iBAAiB,SAAS,KAAK,+BAA+B,2BAA2B;AAClG,iBAAS,iBAAiB,QAAQ,KAAK,+BAA+B,2BAA2B;AAAA,MACnG,CAAC;AAAA,IACH;AACA,SAAK,4BAA4B,IAAI,UAAU,yBAAyB,CAAC;AAEzE,QAAI,EAAE,KAAK,2BAA2B,GAAG;AAGvC,WAAK,QAAQ,kBAAkB,MAAM;AACnC,cAAMD,UAAS,KAAK,WAAW;AAC/B,QAAAA,QAAO,iBAAiB,SAAS,KAAK,oBAAoB;AAAA,MAC5D,CAAC;AAED,WAAK,uBAAuB,iBAAiB,SAAK,6BAAU,KAAK,0BAA0B,CAAC,EAAE,UAAU,cAAY;AAClH,aAAK;AAAA,UAAW;AAAA,UAAU;AAAA;AAAA,QAA4B;AAAA,MACxD,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,uBAAuB,aAAa;AAClC,UAAM,WAAW,YAAY;AAC7B,QAAI,KAAK,4BAA4B,IAAI,QAAQ,GAAG;AAClD,YAAM,yBAAyB,KAAK,4BAA4B,IAAI,QAAQ;AAC5E,UAAI,yBAAyB,GAAG;AAC9B,aAAK,4BAA4B,IAAI,UAAU,yBAAyB,CAAC;AAAA,MAC3E,OAAO;AACL,iBAAS,oBAAoB,SAAS,KAAK,+BAA+B,2BAA2B;AACrG,iBAAS,oBAAoB,QAAQ,KAAK,+BAA+B,2BAA2B;AACpG,aAAK,4BAA4B,OAAO,QAAQ;AAAA,MAClD;AAAA,IACF;AAEA,QAAI,CAAE,EAAE,KAAK,wBAAwB;AACnC,YAAMA,UAAS,KAAK,WAAW;AAC/B,MAAAA,QAAO,oBAAoB,SAAS,KAAK,oBAAoB;AAE7D,WAAK,2BAA2B,KAAK;AAErC,mBAAa,KAAK,qBAAqB;AACvC,mBAAa,KAAK,gBAAgB;AAAA,IACpC;AAAA,EACF;AAAA;AAAA,EAEA,eAAe,SAAS,QAAQ,aAAa;AAC3C,SAAK,YAAY,SAAS,MAAM;AAChC,SAAK,YAAY,aAAa,MAAM;AACpC,SAAK,mBAAmB;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,wBAAwB,SAAS;AAC/B,UAAM,UAAU,CAAC;AACjB,SAAK,aAAa,QAAQ,CAAC,MAAM,mBAAmB;AAClD,UAAI,mBAAmB,WAAW,KAAK,iBAAiB,eAAe,SAAS,OAAO,GAAG;AACxF,gBAAQ,KAAK,CAAC,gBAAgB,IAAI,CAAC;AAAA,MACrC;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,iCAAiC,kBAAkB;AACjD,UAAM;AAAA,MACJ,mBAAmB;AAAA,MACnB;AAAA,IACF,IAAI,KAAK;AAIT,QAAI,uBAAuB,WAAW,CAAC,oBAAoB,qBAAqB,oBAAoB,iBAAiB,aAAa,WAAW,iBAAiB,aAAa,cAAc,iBAAiB,UAAU;AAClN,aAAO;AAAA,IACT;AACA,UAAM,SAAS,iBAAiB;AAChC,QAAI,QAAQ;AACV,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,YAAI,OAAO,CAAC,EAAE,SAAS,gBAAgB,GAAG;AACxC,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,qBAAqB,mBAAmB;AAC3D,aAAO,KAAK,qBAAqB,eAAiB,SAAY,MAAM,GAAM,SAAY,QAAQ,GAAM,SAAS,qBAAqB,GAAM,SAAS,UAAU,CAAC,GAAM,SAAS,+BAA+B,CAAC,CAAC;AAAA,IAC9M;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,cAAa;AAAA,MACtB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,6BAA6B;AAAA,IACtC,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AAUH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,YAAY,aAAa,eAAe;AACtC,SAAK,cAAc;AACnB,SAAK,gBAAgB;AACrB,SAAK,eAAe;AACpB,SAAK,iBAAiB,IAAI,aAAa;AAAA,EACzC;AAAA,EACA,IAAI,cAAc;AAChB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,kBAAkB;AAChB,UAAM,UAAU,KAAK,YAAY;AACjC,SAAK,uBAAuB,KAAK,cAAc,QAAQ,SAAS,QAAQ,aAAa,KAAK,QAAQ,aAAa,wBAAwB,CAAC,EAAE,UAAU,YAAU;AAC5J,WAAK,eAAe;AACpB,WAAK,eAAe,KAAK,MAAM;AAAA,IACjC,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,cAAc,eAAe,KAAK,WAAW;AAClD,QAAI,KAAK,sBAAsB;AAC7B,WAAK,qBAAqB,YAAY;AAAA,IACxC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,wBAAwB,mBAAmB;AAC9D,aAAO,KAAK,qBAAqB,kBAAoB,kBAAqB,UAAU,GAAM,kBAAkB,YAAY,CAAC;AAAA,IAC3H;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,0BAA0B,EAAE,GAAG,CAAC,IAAI,0BAA0B,EAAE,CAAC;AAAA,MAClF,SAAS;AAAA,QACP,gBAAgB;AAAA,MAClB;AAAA,MACA,UAAU,CAAC,iBAAiB;AAAA,MAC5B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,CAAC,GAAG;AAAA,IACF,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAGH,IAAI;AAAA,CACH,SAAUG,mBAAkB;AAC3B,EAAAA,kBAAiBA,kBAAiB,MAAM,IAAI,CAAC,IAAI;AACjD,EAAAA,kBAAiBA,kBAAiB,gBAAgB,IAAI,CAAC,IAAI;AAC3D,EAAAA,kBAAiBA,kBAAiB,gBAAgB,IAAI,CAAC,IAAI;AAC7D,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;AAE9C,IAAM,2BAA2B;AAEjC,IAAM,2BAA2B;AAEjC,IAAM,sCAAsC;AAY5C,IAAM,2BAAN,MAAM,0BAAyB;AAAA,EAC7B,YAAY,WAAWF,WAAU;AAC/B,SAAK,YAAY;AACjB,SAAK,YAAYA;AACjB,SAAK,0BAA0B,OAAO,kBAAkB,EAAE,QAAQ,yBAAyB,EAAE,UAAU,MAAM;AAC3G,UAAI,KAAK,6BAA6B;AACpC,aAAK,8BAA8B;AACnC,aAAK,qCAAqC;AAAA,MAC5C;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,sBAAsB;AACpB,QAAI,CAAC,KAAK,UAAU,WAAW;AAC7B,aAAO,iBAAiB;AAAA,IAC1B;AAIA,UAAM,cAAc,KAAK,UAAU,cAAc,KAAK;AACtD,gBAAY,MAAM,kBAAkB;AACpC,gBAAY,MAAM,WAAW;AAC7B,SAAK,UAAU,KAAK,YAAY,WAAW;AAK3C,UAAM,iBAAiB,KAAK,UAAU,eAAe;AACrD,UAAM,gBAAgB,kBAAkB,eAAe,mBAAmB,eAAe,iBAAiB,WAAW,IAAI;AACzH,UAAM,iBAAiB,iBAAiB,cAAc,mBAAmB,IAAI,QAAQ,MAAM,EAAE;AAC7F,gBAAY,OAAO;AACnB,YAAQ,eAAe;AAAA,MAErB,KAAK;AAAA,MAEL,KAAK;AAAA,MACL,KAAK;AACH,eAAO,iBAAiB;AAAA,MAE1B,KAAK;AAAA,MAEL,KAAK;AACH,eAAO,iBAAiB;AAAA,IAC5B;AACA,WAAO,iBAAiB;AAAA,EAC1B;AAAA,EACA,cAAc;AACZ,SAAK,wBAAwB,YAAY;AAAA,EAC3C;AAAA;AAAA,EAEA,uCAAuC;AACrC,QAAI,CAAC,KAAK,+BAA+B,KAAK,UAAU,aAAa,KAAK,UAAU,MAAM;AACxF,YAAM,cAAc,KAAK,UAAU,KAAK;AACxC,kBAAY,OAAO,qCAAqC,0BAA0B,wBAAwB;AAC1G,WAAK,8BAA8B;AACnC,YAAM,OAAO,KAAK,oBAAoB;AACtC,UAAI,SAAS,iBAAiB,gBAAgB;AAC5C,oBAAY,IAAI,qCAAqC,wBAAwB;AAAA,MAC/E,WAAW,SAAS,iBAAiB,gBAAgB;AACnD,oBAAY,IAAI,qCAAqC,wBAAwB;AAAA,MAC/E;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,iCAAiC,mBAAmB;AACvE,aAAO,KAAK,qBAAqB,2BAA6B,SAAY,QAAQ,GAAM,SAAS,QAAQ,CAAC;AAAA,IAC5G;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,0BAAyB;AAAA,MAClC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,aAAN,MAAM,YAAW;AAAA,EACf,YAAY,0BAA0B;AACpC,6BAAyB,qCAAqC;AAAA,EAChE;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,mBAAmB,mBAAmB;AACzD,aAAO,KAAK,qBAAqB,aAAe,SAAS,wBAAwB,CAAC;AAAA,IACpF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,iBAAiB,aAAa,cAAc,eAAe;AAAA,MACrE,SAAS,CAAC,aAAa,cAAc,eAAe;AAAA,IACtD,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,eAAe;AAAA,IAC3B,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,iBAAiB,aAAa,cAAc,eAAe;AAAA,MACrE,SAAS,CAAC,aAAa,cAAc,eAAe;AAAA,IACtD,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,CAAC,GAAG,IAAI;AACV,GAAG;;;AGriGH,IAAAG,eAA6G;AAC7G,IAAAC,oBAAgE;AAIhE,SAAS,cAAc,MAAM;AAC3B,QAAM,QAAQ,KAAK,UAAU,IAAI;AACjC,QAAM,oBAAoB,MAAM,iBAAiB,MAAM;AACvD,QAAM,WAAW,KAAK,SAAS,YAAY;AAE3C,QAAM,gBAAgB,IAAI;AAC1B,WAAS,IAAI,GAAG,IAAI,kBAAkB,QAAQ,KAAK;AACjD,sBAAkB,CAAC,EAAE,gBAAgB,IAAI;AAAA,EAC3C;AACA,MAAI,aAAa,UAAU;AACzB,uBAAmB,MAAM,KAAK;AAAA,EAChC,WAAW,aAAa,WAAW,aAAa,YAAY,aAAa,YAAY;AACnF,sBAAkB,MAAM,KAAK;AAAA,EAC/B;AACA,eAAa,UAAU,MAAM,OAAO,kBAAkB;AACtD,eAAa,2BAA2B,MAAM,OAAO,iBAAiB;AACtE,SAAO;AACT;AAEA,SAAS,aAAa,UAAU,MAAM,OAAO,UAAU;AACrD,QAAM,qBAAqB,KAAK,iBAAiB,QAAQ;AACzD,MAAI,mBAAmB,QAAQ;AAC7B,UAAM,gBAAgB,MAAM,iBAAiB,QAAQ;AACrD,aAAS,IAAI,GAAG,IAAI,mBAAmB,QAAQ,KAAK;AAClD,eAAS,mBAAmB,CAAC,GAAG,cAAc,CAAC,CAAC;AAAA,IAClD;AAAA,EACF;AACF;AAEA,IAAI,gBAAgB;AAEpB,SAAS,kBAAkB,QAAQ,OAAO;AAExC,MAAI,MAAM,SAAS,QAAQ;AACzB,UAAM,QAAQ,OAAO;AAAA,EACvB;AAIA,MAAI,MAAM,SAAS,WAAW,MAAM,MAAM;AACxC,UAAM,OAAO,aAAa,MAAM,IAAI,IAAI,eAAe;AAAA,EACzD;AACF;AAEA,SAAS,mBAAmB,QAAQ,OAAO;AACzC,QAAM,UAAU,MAAM,WAAW,IAAI;AACrC,MAAI,SAAS;AAGX,QAAI;AACF,cAAQ,UAAU,QAAQ,GAAG,CAAC;AAAA,IAChC,QAAQ;AAAA,IAAC;AAAA,EACX;AACF;AAGA,SAAS,qBAAqB,SAAS;AACrC,QAAM,OAAO,QAAQ,sBAAsB;AAK3C,SAAO;AAAA,IACL,KAAK,KAAK;AAAA,IACV,OAAO,KAAK;AAAA,IACZ,QAAQ,KAAK;AAAA,IACb,MAAM,KAAK;AAAA,IACX,OAAO,KAAK;AAAA,IACZ,QAAQ,KAAK;AAAA,IACb,GAAG,KAAK;AAAA,IACR,GAAG,KAAK;AAAA,EACV;AACF;AAOA,SAAS,mBAAmB,YAAY,GAAG,GAAG;AAC5C,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,SAAO,KAAK,OAAO,KAAK,UAAU,KAAK,QAAQ,KAAK;AACtD;AAOA,SAAS,cAAc,SAAS,KAAK,MAAM;AACzC,UAAQ,OAAO;AACf,UAAQ,SAAS,QAAQ,MAAM,QAAQ;AACvC,UAAQ,QAAQ;AAChB,UAAQ,QAAQ,QAAQ,OAAO,QAAQ;AACzC;AAQA,SAAS,qBAAqB,MAAM,WAAW,UAAU,UAAU;AACjE,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,aAAa,QAAQ;AAC3B,QAAM,aAAa,SAAS;AAC5B,SAAO,WAAW,MAAM,cAAc,WAAW,SAAS,cAAc,WAAW,OAAO,cAAc,WAAW,QAAQ;AAC7H;AAGA,IAAM,wBAAN,MAA4B;AAAA,EAC1B,YAAY,WAAW;AACrB,SAAK,YAAY;AAEjB,SAAK,YAAY,oBAAI,IAAI;AAAA,EAC3B;AAAA;AAAA,EAEA,QAAQ;AACN,SAAK,UAAU,MAAM;AAAA,EACvB;AAAA;AAAA,EAEA,MAAM,UAAU;AACd,SAAK,MAAM;AACX,SAAK,UAAU,IAAI,KAAK,WAAW;AAAA,MACjC,gBAAgB,KAAK,0BAA0B;AAAA,IACjD,CAAC;AACD,aAAS,QAAQ,aAAW;AAC1B,WAAK,UAAU,IAAI,SAAS;AAAA,QAC1B,gBAAgB;AAAA,UACd,KAAK,QAAQ;AAAA,UACb,MAAM,QAAQ;AAAA,QAChB;AAAA,QACA,YAAY,qBAAqB,OAAO;AAAA,MAC1C,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,aAAa,OAAO;AAClB,UAAM,SAAS,gBAAgB,KAAK;AACpC,UAAM,iBAAiB,KAAK,UAAU,IAAI,MAAM;AAChD,QAAI,CAAC,gBAAgB;AACnB,aAAO;AAAA,IACT;AACA,UAAM,iBAAiB,eAAe;AACtC,QAAI;AACJ,QAAI;AACJ,QAAI,WAAW,KAAK,WAAW;AAC7B,YAAM,yBAAyB,KAAK,0BAA0B;AAC9D,eAAS,uBAAuB;AAChC,gBAAU,uBAAuB;AAAA,IACnC,OAAO;AACL,eAAS,OAAO;AAChB,gBAAU,OAAO;AAAA,IACnB;AACA,UAAM,gBAAgB,eAAe,MAAM;AAC3C,UAAM,iBAAiB,eAAe,OAAO;AAG7C,SAAK,UAAU,QAAQ,CAAC,UAAU,SAAS;AACzC,UAAI,SAAS,cAAc,WAAW,QAAQ,OAAO,SAAS,IAAI,GAAG;AACnE,sBAAc,SAAS,YAAY,eAAe,cAAc;AAAA,MAClE;AAAA,IACF,CAAC;AACD,mBAAe,MAAM;AACrB,mBAAe,OAAO;AACtB,WAAO;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,4BAA4B;AAC1B,WAAO;AAAA,MACL,KAAK,OAAO;AAAA,MACZ,MAAM,OAAO;AAAA,IACf;AAAA,EACF;AACF;AAMA,SAAS,YAAY,SAAS,WAAW;AACvC,QAAM,YAAY,QAAQ;AAC1B,MAAI,UAAU,WAAW,KAAK,UAAU,CAAC,EAAE,aAAa,UAAU,cAAc;AAC9E,WAAO,UAAU,CAAC;AAAA,EACpB;AACA,QAAM,UAAU,UAAU,cAAc,KAAK;AAC7C,YAAU,QAAQ,UAAQ,QAAQ,YAAY,IAAI,CAAC;AACnD,SAAO;AACT;AAOA,SAAS,aAAa,MAAM,QAAQC,sBAAqB;AACvD,WAAS,OAAO,QAAQ;AACtB,QAAI,OAAO,eAAe,GAAG,GAAG;AAC9B,YAAM,QAAQ,OAAO,GAAG;AACxB,UAAI,OAAO;AACT,aAAK,YAAY,KAAK,OAAOA,sBAAqB,IAAI,GAAG,IAAI,cAAc,EAAE;AAAA,MAC/E,OAAO;AACL,aAAK,eAAe,GAAG;AAAA,MACzB;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAOA,SAAS,6BAA6B,SAAS,QAAQ;AACrD,QAAM,aAAa,SAAS,KAAK;AACjC,eAAa,QAAQ,OAAO;AAAA,IAC1B,gBAAgB,SAAS,KAAK;AAAA,IAC9B,qBAAqB,SAAS,KAAK;AAAA,IACnC,+BAA+B,SAAS,KAAK;AAAA,IAC7C,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,uBAAuB;AAAA,IACvB,oBAAoB;AAAA,EACtB,CAAC;AACH;AAQA,SAAS,iBAAiB,SAAS,QAAQA,sBAAqB;AAC9D,eAAa,QAAQ,OAAO;AAAA,IAC1B,UAAU,SAAS,KAAK;AAAA,IACxB,KAAK,SAAS,KAAK;AAAA,IACnB,SAAS,SAAS,KAAK;AAAA,IACvB,MAAM,SAAS,KAAK;AAAA,EACtB,GAAGA,oBAAmB;AACxB;AAKA,SAAS,kBAAkB,WAAW,kBAAkB;AACtD,SAAO,oBAAoB,oBAAoB,SAAS,YAAY,MAAM,mBAAmB;AAC/F;AAMA,SAAS,iBAAiB,QAAQ,YAAY;AAC5C,SAAO,MAAM,QAAQ,GAAG,WAAW,KAAK;AACxC,SAAO,MAAM,SAAS,GAAG,WAAW,MAAM;AAC1C,SAAO,MAAM,YAAY,aAAa,WAAW,MAAM,WAAW,GAAG;AACvE;AAMA,SAAS,aAAa,GAAG,GAAG;AAG1B,SAAO,eAAe,KAAK,MAAM,CAAC,CAAC,OAAO,KAAK,MAAM,CAAC,CAAC;AACzD;AAGA,SAAS,sBAAsB,OAAO;AAEpC,QAAM,aAAa,MAAM,YAAY,EAAE,QAAQ,IAAI,IAAI,KAAK,IAAI;AAChE,SAAO,WAAW,KAAK,IAAI;AAC7B;AAEA,SAAS,mCAAmC,SAAS;AACnD,QAAM,gBAAgB,iBAAiB,OAAO;AAC9C,QAAM,yBAAyB,sBAAsB,eAAe,qBAAqB;AACzF,QAAM,WAAW,uBAAuB,KAAK,UAAQ,SAAS,eAAe,SAAS,KAAK;AAE3F,MAAI,CAAC,UAAU;AACb,WAAO;AAAA,EACT;AAGA,QAAM,gBAAgB,uBAAuB,QAAQ,QAAQ;AAC7D,QAAM,eAAe,sBAAsB,eAAe,qBAAqB;AAC/E,QAAM,YAAY,sBAAsB,eAAe,kBAAkB;AACzE,SAAO,sBAAsB,aAAa,aAAa,CAAC,IAAI,sBAAsB,UAAU,aAAa,CAAC;AAC5G;AAEA,SAAS,sBAAsB,eAAe,MAAM;AAClD,QAAM,QAAQ,cAAc,iBAAiB,IAAI;AACjD,SAAO,MAAM,MAAM,GAAG,EAAE,IAAI,UAAQ,KAAK,KAAK,CAAC;AACjD;AAGA,IAAM,sBAAsB,oBAAI,IAAI;AAAA;AAAA,EAEpC;AAAU,CAAC;AACX,IAAM,aAAN,MAAiB;AAAA,EACf,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,YAAY,WAAW,cAAc,YAAY,iBAAiB,kBAAkB,eAAe,uBAAuB,mBAAmB,SAAS;AACpJ,SAAK,YAAY;AACjB,SAAK,eAAe;AACpB,SAAK,aAAa;AAClB,SAAK,kBAAkB;AACvB,SAAK,mBAAmB;AACxB,SAAK,gBAAgB;AACrB,SAAK,wBAAwB;AAC7B,SAAK,oBAAoB;AACzB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,OAAO,QAAQ;AACb,SAAK,WAAW,KAAK,eAAe;AACpC,WAAO,YAAY,KAAK,QAAQ;AAGhC,QAAI,gBAAgB,KAAK,QAAQ,GAAG;AAClC,WAAK,SAAS,aAAa,EAAE;AAAA,IAC/B;AAAA,EACF;AAAA,EACA,UAAU;AACR,SAAK,SAAS,OAAO;AACrB,SAAK,sBAAsB,QAAQ;AACnC,SAAK,WAAW,KAAK,uBAAuB;AAAA,EAC9C;AAAA,EACA,aAAa,OAAO;AAClB,SAAK,SAAS,MAAM,YAAY;AAAA,EAClC;AAAA,EACA,wBAAwB;AACtB,WAAO,KAAK,SAAS,sBAAsB;AAAA,EAC7C;AAAA,EACA,SAAS,WAAW;AAClB,SAAK,SAAS,UAAU,IAAI,SAAS;AAAA,EACvC;AAAA,EACA,wBAAwB;AACtB,WAAO,mCAAmC,KAAK,QAAQ;AAAA,EACzD;AAAA,EACA,iBAAiB,MAAM,SAAS;AAC9B,SAAK,SAAS,iBAAiB,MAAM,OAAO;AAAA,EAC9C;AAAA,EACA,oBAAoB,MAAM,SAAS;AACjC,SAAK,SAAS,oBAAoB,MAAM,OAAO;AAAA,EACjD;AAAA,EACA,iBAAiB;AACf,UAAM,gBAAgB,KAAK;AAC3B,UAAM,eAAe,KAAK;AAC1B,UAAM,kBAAkB,gBAAgB,cAAc,WAAW;AACjE,QAAI;AACJ,QAAI,mBAAmB,eAAe;AAGpC,YAAM,WAAW,cAAc,YAAY,KAAK,kBAAkB;AAClE,YAAM,UAAU,cAAc,cAAc,mBAAmB,iBAAiB,cAAc,OAAO;AACrG,cAAQ,cAAc;AACtB,gBAAU,YAAY,SAAS,KAAK,SAAS;AAC7C,WAAK,uBAAuB;AAC5B,UAAI,cAAc,WAAW;AAC3B,yBAAiB,SAAS,QAAQ;AAAA,MACpC,OAAO;AACL,gBAAQ,MAAM,YAAY,aAAa,KAAK,sBAAsB,GAAG,KAAK,sBAAsB,CAAC;AAAA,MACnG;AAAA,IACF,OAAO;AACL,gBAAU,cAAc,KAAK,YAAY;AACzC,uBAAiB,SAAS,KAAK,eAAe;AAC9C,UAAI,KAAK,mBAAmB;AAC1B,gBAAQ,MAAM,YAAY,KAAK;AAAA,MACjC;AAAA,IACF;AACA,iBAAa,QAAQ,OAAO;AAAA;AAAA;AAAA,MAG1B,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMlB,UAAU,gBAAgB,OAAO,IAAI,eAAe;AAAA,MACpD,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,WAAW,KAAK,UAAU;AAAA,IAC5B,GAAG,mBAAmB;AACtB,iCAA6B,SAAS,KAAK;AAC3C,YAAQ,UAAU,IAAI,kBAAkB;AACxC,YAAQ,aAAa,WAAW,QAAQ;AACxC,YAAQ,aAAa,OAAO,KAAK,UAAU;AAC3C,QAAI,cAAc;AAChB,UAAI,MAAM,QAAQ,YAAY,GAAG;AAC/B,qBAAa,QAAQ,eAAa,QAAQ,UAAU,IAAI,SAAS,CAAC;AAAA,MACpE,OAAO;AACL,gBAAQ,UAAU,IAAI,YAAY;AAAA,MACpC;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AAEA,SAAS,gBAAgB,SAAS;AAChC,SAAO,iBAAiB;AAC1B;AAGA,IAAM,8BAA8B,gCAAgC;AAAA,EAClE,SAAS;AACX,CAAC;AAED,IAAM,6BAA6B,gCAAgC;AAAA,EACjE,SAAS;AACX,CAAC;AAED,IAAM,gCAAgC,gCAAgC;AAAA,EACpE,SAAS;AAAA,EACT,SAAS;AACX,CAAC;AAOD,IAAM,0BAA0B;AAEhC,IAAM,0BAA0B,oBAAI,IAAI;AAAA;AAAA,EAExC;AAAU,CAAC;AAIX,IAAM,UAAN,MAAc;AAAA;AAAA,EAEZ,IAAI,WAAW;AACb,WAAO,KAAK,aAAa,CAAC,EAAE,KAAK,kBAAkB,KAAK,eAAe;AAAA,EACzE;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,QAAI,UAAU,KAAK,WAAW;AAC5B,WAAK,YAAY;AACjB,WAAK,8BAA8B;AACnC,WAAK,SAAS,QAAQ,YAAU,6BAA6B,QAAQ,KAAK,CAAC;AAAA,IAC7E;AAAA,EACF;AAAA,EACA,YAAY,SAAS,SAAS,WAAW,SAAS,gBAAgB,mBAAmB;AACnF,SAAK,UAAU;AACf,SAAK,YAAY;AACjB,SAAK,UAAU;AACf,SAAK,iBAAiB;AACtB,SAAK,oBAAoB;AAOzB,SAAK,oBAAoB;AAAA,MACvB,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAEA,SAAK,mBAAmB;AAAA,MACtB,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAKA,SAAK,sBAAsB,OAAO,KAAK;AAEvC,SAAK,cAAc,IAAI,qBAAQ;AAE/B,SAAK,2BAA2B,0BAAa;AAE7C,SAAK,yBAAyB,0BAAa;AAE3C,SAAK,sBAAsB,0BAAa;AAExC,SAAK,sBAAsB,0BAAa;AAExC,SAAK,mBAAmB;AAExB,SAAK,6BAA6B;AAElC,SAAK,WAAW,CAAC;AAEjB,SAAK,mBAAmB,oBAAI,IAAI;AAEhC,SAAK,aAAa;AAKlB,SAAK,iBAAiB;AAKtB,SAAK,QAAQ;AACb,SAAK,YAAY;AAEjB,SAAK,gBAAgB,IAAI,qBAAQ;AAEjC,SAAK,UAAU,IAAI,qBAAQ;AAE3B,SAAK,WAAW,IAAI,qBAAQ;AAE5B,SAAK,QAAQ,IAAI,qBAAQ;AAEzB,SAAK,UAAU,IAAI,qBAAQ;AAE3B,SAAK,SAAS,IAAI,qBAAQ;AAE1B,SAAK,UAAU,IAAI,qBAAQ;AAK3B,SAAK,QAAQ,KAAK;AAElB,SAAK,eAAe,WAAS;AAC3B,WAAK,cAAc,KAAK;AAExB,UAAI,KAAK,SAAS,QAAQ;AACxB,cAAM,eAAe,KAAK,iBAAiB,KAAK;AAChD,YAAI,gBAAgB,CAAC,KAAK,iBAAiB,IAAI,YAAY,KAAK,CAAC,KAAK,UAAU;AAC9E,eAAK,wBAAwB,cAAc,KAAK;AAAA,QAClD;AAAA,MACF,WAAW,CAAC,KAAK,UAAU;AACzB,aAAK,wBAAwB,KAAK,cAAc,KAAK;AAAA,MACvD;AAAA,IACF;AAEA,SAAK,eAAe,WAAS;AAC3B,YAAM,kBAAkB,KAAK,0BAA0B,KAAK;AAC5D,UAAI,CAAC,KAAK,oBAAoB,GAAG;AAC/B,cAAM,YAAY,KAAK,IAAI,gBAAgB,IAAI,KAAK,sBAAsB,CAAC;AAC3E,cAAM,YAAY,KAAK,IAAI,gBAAgB,IAAI,KAAK,sBAAsB,CAAC;AAC3E,cAAM,kBAAkB,YAAY,aAAa,KAAK,QAAQ;AAK9D,YAAI,iBAAiB;AACnB,gBAAM,iBAAiB,KAAK,IAAI,KAAK,KAAK,iBAAiB,KAAK,mBAAmB,KAAK;AACxF,gBAAM,YAAY,KAAK;AACvB,cAAI,CAAC,gBAAgB;AACnB,iBAAK,iBAAiB,KAAK;AAC3B;AAAA,UACF;AAIA,cAAI,CAAC,aAAa,CAAC,UAAU,WAAW,KAAK,CAAC,UAAU,YAAY,GAAG;AAGrE,gBAAI,MAAM,YAAY;AACpB,oBAAM,eAAe;AAAA,YACvB;AACA,iBAAK,oBAAoB,IAAI,IAAI;AACjC,iBAAK,QAAQ,IAAI,MAAM,KAAK,mBAAmB,KAAK,CAAC;AAAA,UACvD;AAAA,QACF;AACA;AAAA,MACF;AAIA,UAAI,MAAM,YAAY;AACpB,cAAM,eAAe;AAAA,MACvB;AACA,YAAM,6BAA6B,KAAK,+BAA+B,eAAe;AACtF,WAAK,YAAY;AACjB,WAAK,4BAA4B;AACjC,WAAK,6BAA6B,0BAA0B;AAC5D,UAAI,KAAK,gBAAgB;AACvB,aAAK,2BAA2B,4BAA4B,eAAe;AAAA,MAC7E,OAAO;AAGL,cAAM,SAAS,KAAK,oBAAoB,KAAK,kBAAkB,KAAK;AACpE,cAAM,kBAAkB,KAAK;AAC7B,wBAAgB,IAAI,2BAA2B,IAAI,OAAO,IAAI,KAAK,kBAAkB;AACrF,wBAAgB,IAAI,2BAA2B,IAAI,OAAO,IAAI,KAAK,kBAAkB;AACrF,aAAK,2BAA2B,gBAAgB,GAAG,gBAAgB,CAAC;AAAA,MACtE;AAIA,UAAI,KAAK,YAAY,UAAU,QAAQ;AACrC,aAAK,QAAQ,IAAI,MAAM;AACrB,eAAK,YAAY,KAAK;AAAA,YACpB,QAAQ;AAAA,YACR,iBAAiB;AAAA,YACjB;AAAA,YACA,UAAU,KAAK,iBAAiB,0BAA0B;AAAA,YAC1D,OAAO,KAAK;AAAA,UACd,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,IACF;AAEA,SAAK,aAAa,WAAS;AACzB,WAAK,iBAAiB,KAAK;AAAA,IAC7B;AAEA,SAAK,mBAAmB,WAAS;AAC/B,UAAI,KAAK,SAAS,QAAQ;AACxB,cAAM,eAAe,KAAK,iBAAiB,KAAK;AAChD,YAAI,gBAAgB,CAAC,KAAK,iBAAiB,IAAI,YAAY,KAAK,CAAC,KAAK,UAAU;AAC9E,gBAAM,eAAe;AAAA,QACvB;AAAA,MACF,WAAW,CAAC,KAAK,UAAU;AAGzB,cAAM,eAAe;AAAA,MACvB;AAAA,IACF;AACA,SAAK,gBAAgB,OAAO,EAAE,WAAW,QAAQ,iBAAiB,IAAI;AACtE,SAAK,mBAAmB,IAAI,sBAAsB,SAAS;AAC3D,sBAAkB,iBAAiB,IAAI;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,wBAAwB;AACtB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,iBAAiB;AACf,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB;AAClB,WAAO,KAAK,WAAW,IAAI,KAAK,sBAAsB,IAAI,KAAK,eAAe;AAAA,EAChF;AAAA;AAAA,EAEA,YAAY,SAAS;AACnB,SAAK,WAAW,QAAQ,IAAI,YAAU,cAAc,MAAM,CAAC;AAC3D,SAAK,SAAS,QAAQ,YAAU,6BAA6B,QAAQ,KAAK,QAAQ,CAAC;AACnF,SAAK,8BAA8B;AAKnC,UAAM,kBAAkB,oBAAI,IAAI;AAChC,SAAK,iBAAiB,QAAQ,YAAU;AACtC,UAAI,KAAK,SAAS,QAAQ,MAAM,IAAI,IAAI;AACtC,wBAAgB,IAAI,MAAM;AAAA,MAC5B;AAAA,IACF,CAAC;AACD,SAAK,mBAAmB;AACxB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB,UAAU;AAC5B,SAAK,mBAAmB;AACxB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,wBAAwB,UAAU;AAChC,SAAK,uBAAuB;AAC5B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB,aAAa;AAC3B,UAAM,UAAU,cAAc,WAAW;AACzC,QAAI,YAAY,KAAK,cAAc;AACjC,UAAI,KAAK,cAAc;AACrB,aAAK,4BAA4B,KAAK,YAAY;AAAA,MACpD;AACA,WAAK,QAAQ,kBAAkB,MAAM;AACnC,gBAAQ,iBAAiB,aAAa,KAAK,cAAc,0BAA0B;AACnF,gBAAQ,iBAAiB,cAAc,KAAK,cAAc,2BAA2B;AACrF,gBAAQ,iBAAiB,aAAa,KAAK,kBAAkB,0BAA0B;AAAA,MACzF,CAAC;AACD,WAAK,oBAAoB;AACzB,WAAK,eAAe;AAAA,IACtB;AACA,QAAI,OAAO,eAAe,eAAe,KAAK,wBAAwB,YAAY;AAChF,WAAK,mBAAmB,KAAK,aAAa;AAAA,IAC5C;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,oBAAoB,iBAAiB;AACnC,SAAK,mBAAmB,kBAAkB,cAAc,eAAe,IAAI;AAC3E,SAAK,oBAAoB,YAAY;AACrC,QAAI,iBAAiB;AACnB,WAAK,sBAAsB,KAAK,eAAe,OAAO,EAAE,EAAE,UAAU,MAAM,KAAK,+BAA+B,CAAC;AAAA,IACjH;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,WAAW,QAAQ;AACjB,SAAK,iBAAiB;AACtB,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,UAAU;AACR,SAAK,4BAA4B,KAAK,YAAY;AAGlD,QAAI,KAAK,WAAW,GAAG;AAGrB,WAAK,cAAc,OAAO;AAAA,IAC5B;AACA,SAAK,SAAS,OAAO;AACrB,SAAK,gBAAgB;AACrB,SAAK,oBAAoB;AACzB,SAAK,kBAAkB,eAAe,IAAI;AAC1C,SAAK,iBAAiB;AACtB,SAAK,cAAc,SAAS;AAC5B,SAAK,QAAQ,SAAS;AACtB,SAAK,SAAS,SAAS;AACvB,SAAK,MAAM,SAAS;AACpB,SAAK,QAAQ,SAAS;AACtB,SAAK,OAAO,SAAS;AACrB,SAAK,QAAQ,SAAS;AACtB,SAAK,YAAY,SAAS;AAC1B,SAAK,WAAW,CAAC;AACjB,SAAK,iBAAiB,MAAM;AAC5B,SAAK,iBAAiB;AACtB,SAAK,oBAAoB,YAAY;AACrC,SAAK,iBAAiB,MAAM;AAC5B,SAAK,mBAAmB,KAAK,eAAe,KAAK,mBAAmB,KAAK,uBAAuB,KAAK,mBAAmB,KAAK,UAAU,KAAK,iBAAiB;AAAA,EAC/J;AAAA;AAAA,EAEA,aAAa;AACX,WAAO,KAAK,oBAAoB,KAAK,KAAK,kBAAkB,WAAW,IAAI;AAAA,EAC7E;AAAA;AAAA,EAEA,QAAQ;AACN,SAAK,aAAa,MAAM,YAAY,KAAK,qBAAqB;AAC9D,SAAK,mBAAmB;AAAA,MACtB,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AACA,SAAK,oBAAoB;AAAA,MACvB,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc,QAAQ;AACpB,QAAI,CAAC,KAAK,iBAAiB,IAAI,MAAM,KAAK,KAAK,SAAS,QAAQ,MAAM,IAAI,IAAI;AAC5E,WAAK,iBAAiB,IAAI,MAAM;AAChC,mCAA6B,QAAQ,IAAI;AAAA,IAC3C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa,QAAQ;AACnB,QAAI,KAAK,iBAAiB,IAAI,MAAM,GAAG;AACrC,WAAK,iBAAiB,OAAO,MAAM;AACnC,mCAA6B,QAAQ,KAAK,QAAQ;AAAA,IACpD;AAAA,EACF;AAAA;AAAA,EAEA,cAAc,WAAW;AACvB,SAAK,aAAa;AAClB,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,mBAAmB,WAAW;AAC5B,SAAK,iBAAiB;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA,EAIA,sBAAsB;AACpB,UAAM,WAAW,KAAK,WAAW,IAAI,KAAK,mBAAmB,KAAK;AAClE,WAAO;AAAA,MACL,GAAG,SAAS;AAAA,MACZ,GAAG,SAAS;AAAA,IACd;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB,OAAO;AACzB,SAAK,mBAAmB;AAAA,MACtB,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AACA,SAAK,kBAAkB,IAAI,MAAM;AACjC,SAAK,kBAAkB,IAAI,MAAM;AACjC,QAAI,CAAC,KAAK,gBAAgB;AACxB,WAAK,2BAA2B,MAAM,GAAG,MAAM,CAAC;AAAA,IAClD;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB,OAAO;AAC1B,SAAK,oBAAoB;AACzB,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,+BAA+B;AAC7B,UAAM,WAAW,KAAK;AACtB,QAAI,YAAY,KAAK,gBAAgB;AACnC,WAAK,2BAA2B,KAAK,+BAA+B,QAAQ,GAAG,QAAQ;AAAA,IACzF;AAAA,EACF;AAAA;AAAA,EAEA,mBAAmB;AACjB,SAAK,yBAAyB,YAAY;AAC1C,SAAK,uBAAuB,YAAY;AACxC,SAAK,oBAAoB,YAAY;AACrC,SAAK,eAAe,GAAG,oBAAoB,eAAe,sBAAsB,6BAA6B;AAAA,EAC/G;AAAA;AAAA,EAEA,kBAAkB;AAChB,SAAK,UAAU,QAAQ;AACvB,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA,EAEA,sBAAsB;AACpB,SAAK,cAAc,OAAO;AAC1B,SAAK,iBAAiB,QAAQ;AAC9B,SAAK,eAAe,KAAK,kBAAkB;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB,OAAO;AAKtB,QAAI,CAAC,KAAK,kBAAkB,WAAW,IAAI,GAAG;AAC5C;AAAA,IACF;AACA,SAAK,iBAAiB;AACtB,SAAK,kBAAkB,aAAa,IAAI;AACxC,SAAK,8BAA8B;AACnC,QAAI,KAAK,UAAU;AACjB,WAAK,aAAa,MAAM,0BAA0B,KAAK;AAAA,IACzD;AACA,QAAI,CAAC,KAAK,oBAAoB,GAAG;AAC/B;AAAA,IACF;AACA,SAAK,SAAS,KAAK;AAAA,MACjB,QAAQ;AAAA,MACR;AAAA,IACF,CAAC;AACD,QAAI,KAAK,gBAAgB;AAEvB,WAAK,eAAe,eAAe;AACnC,WAAK,6BAA6B,EAAE,KAAK,MAAM;AAC7C,aAAK,sBAAsB,KAAK;AAChC,aAAK,yBAAyB;AAC9B,aAAK,kBAAkB,aAAa,IAAI;AAAA,MAC1C,CAAC;AAAA,IACH,OAAO;AAIL,WAAK,kBAAkB,IAAI,KAAK,iBAAiB;AACjD,YAAM,kBAAkB,KAAK,0BAA0B,KAAK;AAC5D,WAAK,kBAAkB,IAAI,KAAK,iBAAiB;AACjD,WAAK,QAAQ,IAAI,MAAM;AACrB,aAAK,MAAM,KAAK;AAAA,UACd,QAAQ;AAAA,UACR,UAAU,KAAK,iBAAiB,eAAe;AAAA,UAC/C,WAAW;AAAA,UACX;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AACD,WAAK,yBAAyB;AAC9B,WAAK,kBAAkB,aAAa,IAAI;AAAA,IAC1C;AAAA,EACF;AAAA;AAAA,EAEA,mBAAmB,OAAO;AACxB,QAAI,aAAa,KAAK,GAAG;AACvB,WAAK,sBAAsB,KAAK,IAAI;AAAA,IACtC;AACA,SAAK,8BAA8B;AAEnC,UAAM,aAAa,KAAK,eAAe;AACvC,UAAM,gBAAgB,KAAK;AAC3B,QAAI,YAAY;AAGd,WAAK,QAAQ,kBAAkB,MAAM;AACnC,mBAAW,iBAAiB,eAAe,sBAAsB,6BAA6B;AAAA,MAChG,CAAC;AAAA,IACH;AACA,QAAI,eAAe;AACjB,YAAM,UAAU,KAAK;AACrB,YAAM,SAAS,QAAQ;AACvB,YAAM,cAAc,KAAK,eAAe,KAAK,0BAA0B;AACvE,YAAM,SAAS,KAAK,UAAU,KAAK,WAAW,KAAK,UAAU,cAAc,OAAO,cAAc,eAAe,YAAY,oBAAoB,EAAE;AAEjJ,aAAO,aAAa,QAAQ,OAAO;AAGnC,WAAK,oBAAoB,QAAQ,MAAM,aAAa;AAGpD,WAAK,WAAW,IAAI,WAAW,KAAK,WAAW,KAAK,cAAc,KAAK,YAAY,KAAK,iBAAiB,KAAK,oBAAoB,MAAM,KAAK,gBAAgB,MAAM,KAAK,uBAAuB,KAAK,mBAAmB,KAAK,QAAQ,UAAU,GAAI;AAClP,WAAK,SAAS,OAAO,KAAK,0BAA0B,QAAQ,UAAU,CAAC;AAIvE,uBAAiB,SAAS,OAAO,uBAAuB;AACxD,WAAK,UAAU,KAAK,YAAY,OAAO,aAAa,aAAa,OAAO,CAAC;AACzE,WAAK,QAAQ,KAAK;AAAA,QAChB,QAAQ;AAAA,QACR;AAAA,MACF,CAAC;AACD,oBAAc,MAAM;AACpB,WAAK,oBAAoB;AACzB,WAAK,gBAAgB,cAAc,aAAa,IAAI;AAAA,IACtD,OAAO;AACL,WAAK,QAAQ,KAAK;AAAA,QAChB,QAAQ;AAAA,QACR;AAAA,MACF,CAAC;AACD,WAAK,oBAAoB,KAAK,gBAAgB;AAAA,IAChD;AAGA,SAAK,iBAAiB,MAAM,gBAAgB,cAAc,qBAAqB,IAAI,CAAC,CAAC;AAAA,EACvF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,wBAAwB,kBAAkB,OAAO;AAG/C,QAAI,KAAK,gBAAgB;AACvB,YAAM,gBAAgB;AAAA,IACxB;AACA,UAAM,aAAa,KAAK,WAAW;AACnC,UAAM,kBAAkB,aAAa,KAAK;AAC1C,UAAM,yBAAyB,CAAC,mBAAmB,MAAM,WAAW;AACpE,UAAM,cAAc,KAAK;AACzB,UAAM,SAAS,gBAAgB,KAAK;AACpC,UAAM,mBAAmB,CAAC,mBAAmB,KAAK,uBAAuB,KAAK,sBAAsB,0BAA0B,KAAK,IAAI;AACvI,UAAM,cAAc,kBAAkB,iCAAiC,KAAK,IAAI,gCAAgC,KAAK;AAOrH,QAAI,UAAU,OAAO,aAAa,MAAM,SAAS,aAAa;AAC5D,YAAM,eAAe;AAAA,IACvB;AAEA,QAAI,cAAc,0BAA0B,oBAAoB,aAAa;AAC3E;AAAA,IACF;AAIA,QAAI,KAAK,SAAS,QAAQ;AACxB,YAAM,aAAa,YAAY;AAC/B,WAAK,2BAA2B,WAAW,2BAA2B;AACtE,iBAAW,0BAA0B;AAAA,IACvC;AACA,SAAK,YAAY;AACjB,SAAK,oBAAoB,IAAI,KAAK,SAAS;AAG3C,SAAK,iBAAiB;AACtB,SAAK,kBAAkB,KAAK,aAAa,sBAAsB;AAC/D,SAAK,2BAA2B,KAAK,kBAAkB,YAAY,UAAU,KAAK,YAAY;AAC9F,SAAK,yBAAyB,KAAK,kBAAkB,UAAU,UAAU,KAAK,UAAU;AACxF,SAAK,sBAAsB,KAAK,kBAAkB,SAAS,KAAK,eAAe,CAAC,EAAE,UAAU,iBAAe,KAAK,gBAAgB,WAAW,CAAC;AAC5I,QAAI,KAAK,kBAAkB;AACzB,WAAK,gBAAgB,qBAAqB,KAAK,gBAAgB;AAAA,IACjE;AAIA,UAAM,kBAAkB,KAAK;AAC7B,SAAK,2BAA2B,mBAAmB,gBAAgB,YAAY,CAAC,gBAAgB,YAAY;AAAA,MAC1G,GAAG;AAAA,MACH,GAAG;AAAA,IACL,IAAI,KAAK,6BAA6B,KAAK,iBAAiB,kBAAkB,KAAK;AACnF,UAAM,kBAAkB,KAAK,wBAAwB,KAAK,4BAA4B,KAAK,0BAA0B,KAAK;AAC1H,SAAK,yBAAyB;AAAA,MAC5B,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AACA,SAAK,wCAAwC;AAAA,MAC3C,GAAG,gBAAgB;AAAA,MACnB,GAAG,gBAAgB;AAAA,IACrB;AACA,SAAK,iBAAiB,KAAK,IAAI;AAC/B,SAAK,kBAAkB,cAAc,MAAM,KAAK;AAAA,EAClD;AAAA;AAAA,EAEA,sBAAsB,OAAO;AAK3B,qBAAiB,KAAK,cAAc,MAAM,uBAAuB;AACjE,SAAK,QAAQ,WAAW,aAAa,KAAK,cAAc,KAAK,OAAO;AACpE,SAAK,gBAAgB;AACrB,SAAK,oBAAoB;AACzB,SAAK,kBAAkB,KAAK,gBAAgB,KAAK,eAAe,KAAK,oBAAoB;AAEzF,SAAK,QAAQ,IAAI,MAAM;AACrB,YAAM,YAAY,KAAK;AACvB,YAAM,eAAe,UAAU,aAAa,IAAI;AAChD,YAAM,kBAAkB,KAAK,0BAA0B,KAAK;AAC5D,YAAM,WAAW,KAAK,iBAAiB,eAAe;AACtD,YAAM,yBAAyB,UAAU,iBAAiB,gBAAgB,GAAG,gBAAgB,CAAC;AAC9F,WAAK,MAAM,KAAK;AAAA,QACd,QAAQ;AAAA,QACR;AAAA,QACA,WAAW;AAAA,QACX;AAAA,MACF,CAAC;AACD,WAAK,QAAQ,KAAK;AAAA,QAChB,MAAM;AAAA,QACN;AAAA,QACA,eAAe,KAAK;AAAA,QACpB;AAAA,QACA,mBAAmB,KAAK;AAAA,QACxB;AAAA,QACA;AAAA,QACA,WAAW;AAAA,QACX;AAAA,MACF,CAAC;AACD,gBAAU,KAAK,MAAM,cAAc,KAAK,eAAe,KAAK,mBAAmB,wBAAwB,UAAU,iBAAiB,KAAK;AACvI,WAAK,iBAAiB,KAAK;AAAA,IAC7B,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,2BAA2B;AAAA,IACzB;AAAA,IACA;AAAA,EACF,GAAG;AAAA,IACD,GAAG;AAAA,IACH,GAAG;AAAA,EACL,GAAG;AAED,QAAI,eAAe,KAAK,kBAAkB,iCAAiC,MAAM,GAAG,CAAC;AAKrF,QAAI,CAAC,gBAAgB,KAAK,mBAAmB,KAAK,qBAAqB,KAAK,kBAAkB,iBAAiB,GAAG,CAAC,GAAG;AACpH,qBAAe,KAAK;AAAA,IACtB;AACA,QAAI,gBAAgB,iBAAiB,KAAK,gBAAgB;AACxD,WAAK,QAAQ,IAAI,MAAM;AAErB,aAAK,OAAO,KAAK;AAAA,UACf,MAAM;AAAA,UACN,WAAW,KAAK;AAAA,QAClB,CAAC;AACD,aAAK,eAAe,KAAK,IAAI;AAE7B,aAAK,iBAAiB;AACtB,aAAK,eAAe,MAAM,MAAM,GAAG,GAAG,iBAAiB,KAAK;AAAA;AAAA,QAG5D,aAAa,kBAAkB,KAAK,gBAAgB,MAAS;AAC7D,aAAK,QAAQ,KAAK;AAAA,UAChB,MAAM;AAAA,UACN,WAAW;AAAA,UACX,cAAc,aAAa,aAAa,IAAI;AAAA,QAC9C,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAEA,QAAI,KAAK,WAAW,GAAG;AACrB,WAAK,eAAe,2BAA2B,MAAM,IAAI;AACzD,WAAK,eAAe,UAAU,MAAM,GAAG,GAAG,KAAK,sBAAsB;AACrE,UAAI,KAAK,mBAAmB;AAC1B,aAAK,uBAAuB,GAAG,CAAC;AAAA,MAClC,OAAO;AACL,aAAK,uBAAuB,IAAI,KAAK,yBAAyB,GAAG,IAAI,KAAK,yBAAyB,CAAC;AAAA,MACtG;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,+BAA+B;AAE7B,QAAI,CAAC,KAAK,WAAW;AACnB,aAAO,QAAQ,QAAQ;AAAA,IACzB;AACA,UAAM,kBAAkB,KAAK,aAAa,sBAAsB;AAEhE,SAAK,SAAS,SAAS,oBAAoB;AAE3C,SAAK,uBAAuB,gBAAgB,MAAM,gBAAgB,GAAG;AAKrE,UAAM,WAAW,KAAK,SAAS,sBAAsB;AACrD,QAAI,aAAa,GAAG;AAClB,aAAO,QAAQ,QAAQ;AAAA,IACzB;AACA,WAAO,KAAK,QAAQ,kBAAkB,MAAM;AAC1C,aAAO,IAAI,QAAQ,aAAW;AAC5B,cAAM,UAAU,WAAS;AACvB,cAAI,CAAC,SAAS,KAAK,YAAY,gBAAgB,KAAK,MAAM,KAAK,SAAS,WAAW,MAAM,iBAAiB,aAAa;AACrH,iBAAK,UAAU,oBAAoB,iBAAiB,OAAO;AAC3D,oBAAQ;AACR,yBAAa,OAAO;AAAA,UACtB;AAAA,QACF;AAIA,cAAM,UAAU,WAAW,SAAS,WAAW,GAAG;AAClD,aAAK,SAAS,iBAAiB,iBAAiB,OAAO;AAAA,MACzD,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,4BAA4B;AAC1B,UAAM,oBAAoB,KAAK;AAC/B,UAAM,sBAAsB,oBAAoB,kBAAkB,WAAW;AAC7E,QAAI;AACJ,QAAI,qBAAqB;AACvB,WAAK,kBAAkB,kBAAkB,cAAc,mBAAmB,qBAAqB,kBAAkB,OAAO;AACxH,WAAK,gBAAgB,cAAc;AACnC,oBAAc,YAAY,KAAK,iBAAiB,KAAK,SAAS;AAAA,IAChE,OAAO;AACL,oBAAc,cAAc,KAAK,YAAY;AAAA,IAC/C;AAGA,gBAAY,MAAM,gBAAgB;AAClC,gBAAY,UAAU,IAAI,sBAAsB;AAChD,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,6BAA6B,aAAa,kBAAkB,OAAO;AACjE,UAAM,gBAAgB,qBAAqB,KAAK,eAAe,OAAO;AACtE,UAAM,gBAAgB,gBAAgB,cAAc,sBAAsB,IAAI;AAC9E,UAAM,QAAQ,aAAa,KAAK,IAAI,MAAM,cAAc,CAAC,IAAI;AAC7D,UAAM,iBAAiB,KAAK,2BAA2B;AACvD,UAAM,IAAI,MAAM,QAAQ,cAAc,OAAO,eAAe;AAC5D,UAAM,IAAI,MAAM,QAAQ,cAAc,MAAM,eAAe;AAC3D,WAAO;AAAA,MACL,GAAG,cAAc,OAAO,YAAY,OAAO;AAAA,MAC3C,GAAG,cAAc,MAAM,YAAY,MAAM;AAAA,IAC3C;AAAA,EACF;AAAA;AAAA,EAEA,0BAA0B,OAAO;AAC/B,UAAM,iBAAiB,KAAK,2BAA2B;AACvD,UAAM,QAAQ,aAAa,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQhC,MAAM,QAAQ,CAAC,KAAK,MAAM,eAAe,CAAC,KAAK;AAAA,QAC7C,OAAO;AAAA,QACP,OAAO;AAAA,MACT;AAAA,QAAI;AACJ,UAAM,IAAI,MAAM,QAAQ,eAAe;AACvC,UAAM,IAAI,MAAM,QAAQ,eAAe;AAGvC,QAAI,KAAK,kBAAkB;AACzB,YAAM,YAAY,KAAK,iBAAiB,aAAa;AACrD,UAAI,WAAW;AACb,cAAM,WAAW,KAAK,iBAAiB,eAAe;AACtD,iBAAS,IAAI;AACb,iBAAS,IAAI;AACb,eAAO,SAAS,gBAAgB,UAAU,QAAQ,CAAC;AAAA,MACrD;AAAA,IACF;AACA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,+BAA+B,OAAO;AACpC,UAAM,oBAAoB,KAAK,iBAAiB,KAAK,eAAe,WAAW;AAC/E,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF,IAAI,KAAK,oBAAoB,KAAK,kBAAkB,OAAO,MAAM,KAAK,iBAAiB,KAAK,wBAAwB,IAAI;AACxH,QAAI,KAAK,aAAa,OAAO,sBAAsB,KAAK;AACtD,UAAI,KAAK,sBAAsB,KAAK,KAAK,oBAAoB,KAAK,yBAAyB,IAAI;AAAA,IACjG,WAAW,KAAK,aAAa,OAAO,sBAAsB,KAAK;AAC7D,UAAI,KAAK,sBAAsB,KAAK,KAAK,oBAAoB,KAAK,yBAAyB,IAAI;AAAA,IACjG;AACA,QAAI,KAAK,eAAe;AAGtB,YAAM;AAAA,QACJ,GAAG;AAAA,QACH,GAAG;AAAA,MACL,IAAI,CAAC,KAAK,oBAAoB,KAAK,2BAA2B;AAAA,QAC5D,GAAG;AAAA,QACH,GAAG;AAAA,MACL;AACA,YAAM,eAAe,KAAK;AAC1B,YAAM;AAAA,QACJ,OAAO;AAAA,QACP,QAAQ;AAAA,MACV,IAAI,KAAK,gBAAgB;AACzB,YAAM,OAAO,aAAa,MAAM;AAChC,YAAM,OAAO,aAAa,UAAU,gBAAgB;AACpD,YAAM,OAAO,aAAa,OAAO;AACjC,YAAM,OAAO,aAAa,SAAS,eAAe;AAClD,UAAI,QAAQ,GAAG,MAAM,IAAI;AACzB,UAAI,QAAQ,GAAG,MAAM,IAAI;AAAA,IAC3B;AACA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,6BAA6B,uBAAuB;AAClD,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,QAAQ,KAAK;AACnB,UAAM,0BAA0B,KAAK;AAErC,UAAM,UAAU,KAAK,IAAI,IAAI,wBAAwB,CAAC;AACtD,UAAM,UAAU,KAAK,IAAI,IAAI,wBAAwB,CAAC;AAKtD,QAAI,UAAU,KAAK,QAAQ,iCAAiC;AAC1D,YAAM,IAAI,IAAI,wBAAwB,IAAI,IAAI;AAC9C,8BAAwB,IAAI;AAAA,IAC9B;AACA,QAAI,UAAU,KAAK,QAAQ,iCAAiC;AAC1D,YAAM,IAAI,IAAI,wBAAwB,IAAI,IAAI;AAC9C,8BAAwB,IAAI;AAAA,IAC9B;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,gCAAgC;AAC9B,QAAI,CAAC,KAAK,gBAAgB,CAAC,KAAK,UAAU;AACxC;AAAA,IACF;AACA,UAAM,eAAe,KAAK,SAAS,SAAS,KAAK,CAAC,KAAK,WAAW;AAClE,QAAI,iBAAiB,KAAK,4BAA4B;AACpD,WAAK,6BAA6B;AAClC,mCAA6B,KAAK,cAAc,YAAY;AAAA,IAC9D;AAAA,EACF;AAAA;AAAA,EAEA,4BAA4B,SAAS;AACnC,YAAQ,oBAAoB,aAAa,KAAK,cAAc,0BAA0B;AACtF,YAAQ,oBAAoB,cAAc,KAAK,cAAc,2BAA2B;AACxF,YAAQ,oBAAoB,aAAa,KAAK,kBAAkB,0BAA0B;AAAA,EAC5F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,2BAA2B,GAAG,GAAG;AAC/B,UAAM,QAAQ,IAAI,KAAK;AACvB,UAAM,YAAY,aAAa,IAAI,OAAO,IAAI,KAAK;AACnD,UAAM,SAAS,KAAK,aAAa;AAIjC,QAAI,KAAK,qBAAqB,MAAM;AAClC,WAAK,oBAAoB,OAAO,aAAa,OAAO,aAAa,SAAS,OAAO,YAAY;AAAA,IAC/F;AAIA,WAAO,YAAY,kBAAkB,WAAW,KAAK,iBAAiB;AAAA,EACxE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,uBAAuB,GAAG,GAAG;AAG3B,UAAM,mBAAmB,KAAK,kBAAkB,WAAW,SAAY,KAAK;AAC5E,UAAM,YAAY,aAAa,GAAG,CAAC;AACnC,SAAK,SAAS,aAAa,kBAAkB,WAAW,gBAAgB,CAAC;AAAA,EAC3E;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB,iBAAiB;AAChC,UAAM,iBAAiB,KAAK;AAC5B,QAAI,gBAAgB;AAClB,aAAO;AAAA,QACL,GAAG,gBAAgB,IAAI,eAAe;AAAA,QACtC,GAAG,gBAAgB,IAAI,eAAe;AAAA,MACxC;AAAA,IACF;AACA,WAAO;AAAA,MACL,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAAA,EACF;AAAA;AAAA,EAEA,2BAA2B;AACzB,SAAK,gBAAgB,KAAK,eAAe;AACzC,SAAK,iBAAiB,MAAM;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iCAAiC;AAC/B,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF,IAAI,KAAK;AACT,QAAI,MAAM,KAAK,MAAM,KAAK,KAAK,WAAW,KAAK,CAAC,KAAK,kBAAkB;AACrE;AAAA,IACF;AAEA,UAAM,cAAc,KAAK,aAAa,sBAAsB;AAC5D,UAAM,eAAe,KAAK,iBAAiB,sBAAsB;AAGjE,QAAI,aAAa,UAAU,KAAK,aAAa,WAAW,KAAK,YAAY,UAAU,KAAK,YAAY,WAAW,GAAG;AAChH;AAAA,IACF;AACA,UAAM,eAAe,aAAa,OAAO,YAAY;AACrD,UAAM,gBAAgB,YAAY,QAAQ,aAAa;AACvD,UAAM,cAAc,aAAa,MAAM,YAAY;AACnD,UAAM,iBAAiB,YAAY,SAAS,aAAa;AAGzD,QAAI,aAAa,QAAQ,YAAY,OAAO;AAC1C,UAAI,eAAe,GAAG;AACpB,aAAK;AAAA,MACP;AACA,UAAI,gBAAgB,GAAG;AACrB,aAAK;AAAA,MACP;AAAA,IACF,OAAO;AACL,UAAI;AAAA,IACN;AAGA,QAAI,aAAa,SAAS,YAAY,QAAQ;AAC5C,UAAI,cAAc,GAAG;AACnB,aAAK;AAAA,MACP;AACA,UAAI,iBAAiB,GAAG;AACtB,aAAK;AAAA,MACP;AAAA,IACF,OAAO;AACL,UAAI;AAAA,IACN;AACA,QAAI,MAAM,KAAK,kBAAkB,KAAK,MAAM,KAAK,kBAAkB,GAAG;AACpE,WAAK,oBAAoB;AAAA,QACvB;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA,EAEA,mBAAmB,OAAO;AACxB,UAAM,QAAQ,KAAK;AACnB,QAAI,OAAO,UAAU,UAAU;AAC7B,aAAO;AAAA,IACT,WAAW,aAAa,KAAK,GAAG;AAC9B,aAAO,MAAM;AAAA,IACf;AACA,WAAO,QAAQ,MAAM,QAAQ;AAAA,EAC/B;AAAA;AAAA,EAEA,gBAAgB,OAAO;AACrB,UAAM,mBAAmB,KAAK,iBAAiB,aAAa,KAAK;AACjE,QAAI,kBAAkB;AACpB,YAAM,SAAS,gBAAgB,KAAK;AAGpC,UAAI,KAAK,iBAAiB,WAAW,KAAK,oBAAoB,OAAO,SAAS,KAAK,gBAAgB,GAAG;AACpG,sBAAc,KAAK,eAAe,iBAAiB,KAAK,iBAAiB,IAAI;AAAA,MAC/E;AACA,WAAK,sBAAsB,KAAK,iBAAiB;AACjD,WAAK,sBAAsB,KAAK,iBAAiB;AAGjD,UAAI,CAAC,KAAK,gBAAgB;AACxB,aAAK,iBAAiB,KAAK,iBAAiB;AAC5C,aAAK,iBAAiB,KAAK,iBAAiB;AAC5C,aAAK,2BAA2B,KAAK,iBAAiB,GAAG,KAAK,iBAAiB,CAAC;AAAA,MAClF;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,6BAA6B;AAC3B,WAAO,KAAK,iBAAiB,UAAU,IAAI,KAAK,SAAS,GAAG,kBAAkB,KAAK,iBAAiB,0BAA0B;AAAA,EAChI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,iBAAiB;AACf,QAAI,KAAK,sBAAsB,QAAW;AACxC,WAAK,oBAAoB,eAAe,KAAK,YAAY;AAAA,IAC3D;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,0BAA0B,eAAe,YAAY;AACnD,UAAM,mBAAmB,KAAK,qBAAqB;AACnD,QAAI,qBAAqB,UAAU;AACjC,aAAO;AAAA,IACT;AACA,QAAI,qBAAqB,UAAU;AACjC,YAAM,cAAc,KAAK;AAIzB,aAAO,cAAc,YAAY,qBAAqB,YAAY,2BAA2B,YAAY,wBAAwB,YAAY,uBAAuB,YAAY;AAAA,IAClL;AACA,WAAO,cAAc,gBAAgB;AAAA,EACvC;AAAA;AAAA,EAEA,kBAAkB;AAGhB,QAAI,CAAC,KAAK,gBAAgB,CAAC,KAAK,aAAa,SAAS,CAAC,KAAK,aAAa,QAAQ;AAC/E,WAAK,eAAe,KAAK,WAAW,KAAK,SAAS,sBAAsB,IAAI,KAAK;AAAA,IACnF;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,iBAAiB,OAAO;AACtB,WAAO,KAAK,SAAS,KAAK,YAAU;AAClC,aAAO,MAAM,WAAW,MAAM,WAAW,UAAU,OAAO,SAAS,MAAM,MAAM;AAAA,IACjF,CAAC;AAAA,EACH;AACF;AAEA,SAAS,QAAQ,OAAO,KAAK,KAAK;AAChC,SAAO,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC;AAC3C;AAEA,SAAS,aAAa,OAAO;AAI3B,SAAO,MAAM,KAAK,CAAC,MAAM;AAC3B;AAEA,SAAS,qBAAqB,OAAO;AACnC,QAAM,eAAe;AACvB;AAQA,SAAS,gBAAgB,OAAO,WAAW,SAAS;AAClD,QAAM,OAAO,MAAM,WAAW,MAAM,SAAS,CAAC;AAC9C,QAAM,KAAK,MAAM,SAAS,MAAM,SAAS,CAAC;AAC1C,MAAI,SAAS,IAAI;AACf;AAAA,EACF;AACA,QAAM,SAAS,MAAM,IAAI;AACzB,QAAM,QAAQ,KAAK,OAAO,KAAK;AAC/B,WAAS,IAAI,MAAM,MAAM,IAAI,KAAK,OAAO;AACvC,UAAM,CAAC,IAAI,MAAM,IAAI,KAAK;AAAA,EAC5B;AACA,QAAM,EAAE,IAAI;AACd;AAQA,SAAS,kBAAkB,cAAc,aAAa,cAAc,aAAa;AAC/E,QAAM,OAAO,MAAM,cAAc,aAAa,SAAS,CAAC;AACxD,QAAM,KAAK,MAAM,aAAa,YAAY,MAAM;AAChD,MAAI,aAAa,QAAQ;AACvB,gBAAY,OAAO,IAAI,GAAG,aAAa,OAAO,MAAM,CAAC,EAAE,CAAC,CAAC;AAAA,EAC3D;AACF;AAUA,SAAS,cAAc,cAAc,aAAa,cAAc,aAAa;AAC3E,QAAM,KAAK,MAAM,aAAa,YAAY,MAAM;AAChD,MAAI,aAAa,QAAQ;AACvB,gBAAY,OAAO,IAAI,GAAG,aAAa,YAAY,CAAC;AAAA,EACtD;AACF;AAEA,SAAS,MAAM,OAAO,KAAK;AACzB,SAAO,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,CAAC;AACzC;AAOA,IAAM,yBAAN,MAA6B;AAAA,EAC3B,YAAY,mBAAmB;AAC7B,SAAK,oBAAoB;AAEzB,SAAK,iBAAiB,CAAC;AAEvB,SAAK,cAAc;AAMnB,SAAK,gBAAgB;AAAA,MACnB,MAAM;AAAA,MACN,OAAO;AAAA,MACP,UAAU;AAAA,IACZ;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,OAAO;AACX,SAAK,UAAU,KAAK;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,KAAK,MAAM,UAAU,UAAU,cAAc;AAC3C,UAAM,WAAW,KAAK;AACtB,UAAM,WAAW,KAAK,iCAAiC,MAAM,UAAU,UAAU,YAAY;AAC7F,QAAI,aAAa,MAAM,SAAS,SAAS,GAAG;AAC1C,aAAO;AAAA,IACT;AACA,UAAM,eAAe,KAAK,gBAAgB;AAC1C,UAAM,eAAe,SAAS,UAAU,iBAAe,YAAY,SAAS,IAAI;AAChF,UAAM,uBAAuB,SAAS,QAAQ;AAC9C,UAAM,kBAAkB,SAAS,YAAY,EAAE;AAC/C,UAAM,cAAc,qBAAqB;AACzC,UAAM,QAAQ,eAAe,WAAW,IAAI;AAE5C,UAAM,aAAa,KAAK,iBAAiB,iBAAiB,aAAa,KAAK;AAE5E,UAAM,gBAAgB,KAAK,oBAAoB,cAAc,UAAU,KAAK;AAG5E,UAAM,WAAW,SAAS,MAAM;AAEhC,oBAAgB,UAAU,cAAc,QAAQ;AAChD,aAAS,QAAQ,CAAC,SAAS,UAAU;AAEnC,UAAI,SAAS,KAAK,MAAM,SAAS;AAC/B;AAAA,MACF;AACA,YAAM,gBAAgB,QAAQ,SAAS;AACvC,YAAM,SAAS,gBAAgB,aAAa;AAC5C,YAAM,kBAAkB,gBAAgB,KAAK,sBAAsB,IAAI,QAAQ,KAAK,eAAe;AAEnG,cAAQ,UAAU;AAClB,YAAM,kBAAkB,KAAK,MAAM,QAAQ,UAAU,IAAI,QAAQ,KAAK,MAAM;AAK5E,UAAI,cAAc;AAGhB,wBAAgB,MAAM,YAAY,kBAAkB,eAAe,eAAe,aAAa,QAAQ,gBAAgB;AACvH,sBAAc,QAAQ,YAAY,GAAG,MAAM;AAAA,MAC7C,OAAO;AACL,wBAAgB,MAAM,YAAY,kBAAkB,kBAAkB,eAAe,UAAU,QAAQ,gBAAgB;AACvH,sBAAc,QAAQ,YAAY,QAAQ,CAAC;AAAA,MAC7C;AAAA,IACF,CAAC;AAED,SAAK,cAAc,WAAW,mBAAmB,aAAa,UAAU,QAAQ;AAChF,SAAK,cAAc,OAAO,qBAAqB;AAC/C,SAAK,cAAc,QAAQ,eAAe,aAAa,IAAI,aAAa;AACxE,WAAO;AAAA,MACL,eAAe;AAAA,MACf,cAAc;AAAA,IAChB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,MAAM,UAAU,UAAU,OAAO;AACrC,UAAM,WAAW,SAAS,QAAQ,QAAQ;AAAA;AAAA;AAAA,MAG1C,KAAK,iCAAiC,MAAM,UAAU,QAAQ;AAAA,QAAI;AAClE,UAAM,mBAAmB,KAAK;AAC9B,UAAM,eAAe,iBAAiB,QAAQ,IAAI;AAClD,UAAM,cAAc,KAAK,sBAAsB;AAC/C,QAAI,uBAAuB,iBAAiB,QAAQ;AAIpD,QAAI,yBAAyB,MAAM;AACjC,6BAAuB,iBAAiB,WAAW,CAAC;AAAA,IACtD;AAGA,QAAI,CAAC,yBAAyB,YAAY,QAAQ,aAAa,MAAM,WAAW,iBAAiB,SAAS,MAAM,KAAK,yBAAyB,UAAU,QAAQ,GAAG;AACjK,6BAAuB,iBAAiB,CAAC;AAAA,IAC3C;AAGA,QAAI,eAAe,IAAI;AACrB,uBAAiB,OAAO,cAAc,CAAC;AAAA,IACzC;AAGA,QAAI,wBAAwB,CAAC,KAAK,kBAAkB,WAAW,oBAAoB,GAAG;AACpF,YAAM,UAAU,qBAAqB,eAAe;AACpD,cAAQ,cAAc,aAAa,aAAa,OAAO;AACvD,uBAAiB,OAAO,UAAU,GAAG,IAAI;AAAA,IAC3C,OAAO;AACL,WAAK,SAAS,YAAY,WAAW;AACrC,uBAAiB,KAAK,IAAI;AAAA,IAC5B;AAEA,gBAAY,MAAM,YAAY;AAI9B,SAAK,oBAAoB;AAAA,EAC3B;AAAA;AAAA,EAEA,UAAU,OAAO;AACf,SAAK,oBAAoB,MAAM,MAAM;AACrC,SAAK,oBAAoB;AAAA,EAC3B;AAAA;AAAA,EAEA,kBAAkB,WAAW;AAC3B,SAAK,iBAAiB;AAAA,EACxB;AAAA;AAAA,EAEA,QAAQ;AAEN,SAAK,mBAAmB,QAAQ,UAAQ;AACtC,YAAM,cAAc,KAAK,eAAe;AACxC,UAAI,aAAa;AACf,cAAM,mBAAmB,KAAK,eAAe,KAAK,OAAK,EAAE,SAAS,IAAI,GAAG;AACzE,oBAAY,MAAM,YAAY,oBAAoB;AAAA,MACpD;AAAA,IACF,CAAC;AACD,SAAK,iBAAiB,CAAC;AACvB,SAAK,oBAAoB,CAAC;AAC1B,SAAK,cAAc,OAAO;AAC1B,SAAK,cAAc,QAAQ;AAC3B,SAAK,cAAc,WAAW;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,yBAAyB;AACvB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,aAAa,MAAM;AAIjB,UAAM,QAAQ,KAAK,gBAAgB,gBAAgB,KAAK,cAAc,QAAQ,KAAK,eAAe,MAAM,EAAE,QAAQ,IAAI,KAAK;AAC3H,WAAO,MAAM,UAAU,iBAAe,YAAY,SAAS,IAAI;AAAA,EACjE;AAAA;AAAA,EAEA,eAAe,eAAe,gBAAgB;AAK5C,SAAK,eAAe,QAAQ,CAAC;AAAA,MAC3B;AAAA,IACF,MAAM;AACJ,oBAAc,YAAY,eAAe,cAAc;AAAA,IACzD,CAAC;AAGD,SAAK,eAAe,QAAQ,CAAC;AAAA,MAC3B;AAAA,IACF,MAAM;AACJ,UAAI,KAAK,kBAAkB,WAAW,IAAI,GAAG;AAG3C,aAAK,6BAA6B;AAAA,MACpC;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,qBAAqB,WAAW;AAC9B,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA,EAEA,sBAAsB;AACpB,UAAM,eAAe,KAAK,gBAAgB;AAC1C,SAAK,iBAAiB,KAAK,kBAAkB,IAAI,UAAQ;AACvD,YAAM,mBAAmB,KAAK,kBAAkB;AAChD,aAAO;AAAA,QACL;AAAA,QACA,QAAQ;AAAA,QACR,kBAAkB,iBAAiB,MAAM,aAAa;AAAA,QACtD,YAAY,qBAAqB,gBAAgB;AAAA,MACnD;AAAA,IACF,CAAC,EAAE,KAAK,CAAC,GAAG,MAAM;AAChB,aAAO,eAAe,EAAE,WAAW,OAAO,EAAE,WAAW,OAAO,EAAE,WAAW,MAAM,EAAE,WAAW;AAAA,IAChG,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,iBAAiB,iBAAiB,aAAa,OAAO;AACpD,UAAM,eAAe,KAAK,gBAAgB;AAC1C,QAAI,aAAa,eAAe,YAAY,OAAO,gBAAgB,OAAO,YAAY,MAAM,gBAAgB;AAE5G,QAAI,UAAU,IAAI;AAChB,oBAAc,eAAe,YAAY,QAAQ,gBAAgB,QAAQ,YAAY,SAAS,gBAAgB;AAAA,IAChH;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,oBAAoB,cAAc,UAAU,OAAO;AACjD,UAAM,eAAe,KAAK,gBAAgB;AAC1C,UAAM,kBAAkB,SAAS,YAAY,EAAE;AAC/C,UAAM,mBAAmB,SAAS,eAAe,QAAQ,EAAE;AAC3D,QAAI,gBAAgB,gBAAgB,eAAe,UAAU,QAAQ,IAAI;AACzE,QAAI,kBAAkB;AACpB,YAAM,QAAQ,eAAe,SAAS;AACtC,YAAM,MAAM,eAAe,UAAU;AAKrC,UAAI,UAAU,IAAI;AAChB,yBAAiB,iBAAiB,WAAW,KAAK,IAAI,gBAAgB,GAAG;AAAA,MAC3E,OAAO;AACL,yBAAiB,gBAAgB,KAAK,IAAI,iBAAiB,WAAW,GAAG;AAAA,MAC3E;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,yBAAyB,UAAU,UAAU;AAC3C,QAAI,CAAC,KAAK,kBAAkB,QAAQ;AAClC,aAAO;AAAA,IACT;AACA,UAAM,gBAAgB,KAAK;AAC3B,UAAM,eAAe,KAAK,gBAAgB;AAG1C,UAAM,WAAW,cAAc,CAAC,EAAE,SAAS,KAAK,kBAAkB,CAAC;AACnE,QAAI,UAAU;AACZ,YAAM,eAAe,cAAc,cAAc,SAAS,CAAC,EAAE;AAC7D,aAAO,eAAe,YAAY,aAAa,QAAQ,YAAY,aAAa;AAAA,IAClF,OAAO;AACL,YAAM,gBAAgB,cAAc,CAAC,EAAE;AACvC,aAAO,eAAe,YAAY,cAAc,OAAO,YAAY,cAAc;AAAA,IACnF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,iCAAiC,MAAM,UAAU,UAAU,OAAO;AAChE,UAAM,eAAe,KAAK,gBAAgB;AAC1C,UAAM,QAAQ,KAAK,eAAe,UAAU,CAAC;AAAA,MAC3C;AAAA,MACA;AAAA,IACF,MAAM;AAEJ,UAAI,SAAS,MAAM;AACjB,eAAO;AAAA,MACT;AACA,UAAI,OAAO;AACT,cAAM,YAAY,eAAe,MAAM,IAAI,MAAM;AAIjD,YAAI,SAAS,KAAK,cAAc,QAAQ,KAAK,cAAc,YAAY,cAAc,KAAK,cAAc,OAAO;AAC7G,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA;AAAA;AAAA,QAGP,YAAY,KAAK,MAAM,WAAW,IAAI,KAAK,WAAW,KAAK,MAAM,WAAW,KAAK;AAAA,UAAI,YAAY,KAAK,MAAM,WAAW,GAAG,KAAK,WAAW,KAAK,MAAM,WAAW,MAAM;AAAA,IACxK,CAAC;AACD,WAAO,UAAU,MAAM,CAAC,KAAK,eAAe,OAAO,IAAI,IAAI,KAAK;AAAA,EAClE;AACF;AAOA,IAAM,oBAAN,MAAwB;AAAA,EACtB,YAAY,WAAW,mBAAmB;AACxC,SAAK,YAAY;AACjB,SAAK,oBAAoB;AAMzB,SAAK,gBAAgB;AAAA,MACnB,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,UAAU;AAAA,IACZ;AAKA,SAAK,gBAAgB,CAAC;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,OAAO;AACX,UAAM,aAAa,KAAK,SAAS;AACjC,SAAK,gBAAgB,CAAC;AACtB,aAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,YAAM,OAAO,WAAW,CAAC;AACzB,WAAK,cAAc,KAAK,CAAC,MAAM,KAAK,WAAW,CAAC;AAAA,IAClD;AACA,SAAK,UAAU,KAAK;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,KAAK,MAAM,UAAU,UAAU,cAAc;AAC3C,UAAM,WAAW,KAAK,iCAAiC,MAAM,UAAU,QAAQ;AAC/E,UAAM,eAAe,KAAK;AAC1B,QAAI,aAAa,MAAM,KAAK,aAAa,QAAQ,MAAM,MAAM;AAC3D,aAAO;AAAA,IACT;AACA,UAAM,aAAa,KAAK,aAAa,QAAQ;AAE7C,QAAI,aAAa,SAAS,cAAc,aAAa,YAAY,aAAa,WAAW,aAAa,KAAK,aAAa,WAAW,aAAa,GAAG;AACjJ,aAAO;AAAA,IACT;AACA,UAAM,gBAAgB,KAAK,aAAa,IAAI;AAC5C,UAAM,UAAU,KAAK,sBAAsB;AAC3C,UAAM,iBAAiB,WAAW,eAAe;AACjD,QAAI,WAAW,eAAe;AAC5B,qBAAe,MAAM,OAAO;AAAA,IAC9B,OAAO;AACL,qBAAe,OAAO,OAAO;AAAA,IAC/B;AACA,oBAAgB,KAAK,cAAc,eAAe,QAAQ;AAC1D,UAAM,oBAAoB,KAAK,aAAa,EAAE,iBAAiB,UAAU,QAAQ;AAGjF,iBAAa,SAAS,aAAa;AACnC,iBAAa,SAAS,aAAa;AACnC,iBAAa,OAAO;AACpB,iBAAa,WAAW,mBAAmB,qBAAqB,eAAe,SAAS,iBAAiB;AACzG,WAAO;AAAA,MACL;AAAA,MACA,cAAc;AAAA,IAChB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,MAAM,UAAU,UAAU,OAAO;AACrC,QAAI,aAAa,SAAS,QAAQ,QAAQ,IAAI,KAAK,iCAAiC,MAAM,UAAU,QAAQ,IAAI;AAIhH,QAAI,eAAe,IAAI;AACrB,mBAAa,KAAK,8BAA8B,MAAM,UAAU,QAAQ;AAAA,IAC1E;AACA,UAAM,aAAa,KAAK,aAAa,UAAU;AAC/C,UAAM,eAAe,KAAK,aAAa,QAAQ,IAAI;AACnD,QAAI,eAAe,IAAI;AACrB,WAAK,aAAa,OAAO,cAAc,CAAC;AAAA,IAC1C;AACA,QAAI,cAAc,CAAC,KAAK,kBAAkB,WAAW,UAAU,GAAG;AAChE,WAAK,aAAa,OAAO,YAAY,GAAG,IAAI;AAC5C,iBAAW,eAAe,EAAE,OAAO,KAAK,sBAAsB,CAAC;AAAA,IACjE,OAAO;AACL,WAAK,aAAa,KAAK,IAAI;AAC3B,WAAK,SAAS,YAAY,KAAK,sBAAsB,CAAC;AAAA,IACxD;AAAA,EACF;AAAA;AAAA,EAEA,UAAU,OAAO;AACf,SAAK,eAAe,MAAM,MAAM;AAAA,EAClC;AAAA;AAAA,EAEA,kBAAkB,WAAW;AAC3B,SAAK,iBAAiB;AAAA,EACxB;AAAA;AAAA,EAEA,QAAQ;AACN,UAAM,OAAO,KAAK;AAClB,UAAM,eAAe,KAAK;AAQ1B,aAAS,IAAI,KAAK,cAAc,SAAS,GAAG,IAAI,IAAI,KAAK;AACvD,YAAM,CAAC,MAAM,WAAW,IAAI,KAAK,cAAc,CAAC;AAChD,UAAI,KAAK,eAAe,QAAQ,KAAK,gBAAgB,aAAa;AAChE,YAAI,gBAAgB,MAAM;AACxB,eAAK,YAAY,IAAI;AAAA,QACvB,WAAW,YAAY,eAAe,MAAM;AAC1C,eAAK,aAAa,MAAM,WAAW;AAAA,QACrC;AAAA,MACF;AAAA,IACF;AACA,SAAK,gBAAgB,CAAC;AACtB,SAAK,eAAe,CAAC;AACrB,iBAAa,OAAO;AACpB,iBAAa,SAAS,aAAa,SAAS;AAC5C,iBAAa,WAAW;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,yBAAyB;AACvB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,aAAa,MAAM;AACjB,WAAO,KAAK,aAAa,QAAQ,IAAI;AAAA,EACvC;AAAA;AAAA,EAEA,iBAAiB;AACf,SAAK,aAAa,QAAQ,UAAQ;AAChC,UAAI,KAAK,kBAAkB,WAAW,IAAI,GAAG;AAG3C,aAAK,6BAA6B;AAAA,MACpC;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,qBAAqB,WAAW;AAC9B,QAAI,cAAc,KAAK,UAAU;AAC/B,WAAK,WAAW;AAChB,WAAK,YAAY;AAAA,IACnB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,iCAAiC,MAAM,UAAU,UAAU;AACzD,UAAM,iBAAiB,KAAK,aAAa,EAAE,iBAAiB,KAAK,MAAM,QAAQ,GAAG,KAAK,MAAM,QAAQ,CAAC;AACtG,UAAM,QAAQ,iBAAiB,KAAK,aAAa,UAAU,CAAAC,UAAQ;AACjE,YAAM,OAAOA,MAAK,eAAe;AACjC,aAAO,mBAAmB,QAAQ,KAAK,SAAS,cAAc;AAAA,IAChE,CAAC,IAAI;AACL,WAAO,UAAU,MAAM,CAAC,KAAK,eAAe,OAAO,IAAI,IAAI,KAAK;AAAA,EAClE;AAAA;AAAA,EAEA,eAAe;AAEb,QAAI,CAAC,KAAK,WAAW;AACnB,WAAK,YAAY,eAAe,KAAK,QAAQ,KAAK,KAAK;AAAA,IACzD;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,8BAA8B,MAAM,UAAU,UAAU;AACtD,QAAI,KAAK,aAAa,WAAW,GAAG;AAClC,aAAO;AAAA,IACT;AACA,QAAI,KAAK,aAAa,WAAW,GAAG;AAClC,aAAO;AAAA,IACT;AACA,QAAI,cAAc;AAClB,QAAI,WAAW;AAKf,aAAS,IAAI,GAAG,IAAI,KAAK,aAAa,QAAQ,KAAK;AACjD,YAAM,UAAU,KAAK,aAAa,CAAC;AACnC,UAAI,YAAY,MAAM;AACpB,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI,QAAQ,eAAe,EAAE,sBAAsB;AACnD,cAAM,WAAW,KAAK,MAAM,WAAW,GAAG,WAAW,CAAC;AACtD,YAAI,WAAW,aAAa;AAC1B,wBAAc;AACd,qBAAW;AAAA,QACb;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AAMA,IAAM,2BAA2B;AAKjC,IAAM,6BAA6B;AAEnC,IAAI;AAAA,CACH,SAAUC,8BAA6B;AACtC,EAAAA,6BAA4BA,6BAA4B,MAAM,IAAI,CAAC,IAAI;AACvE,EAAAA,6BAA4BA,6BAA4B,IAAI,IAAI,CAAC,IAAI;AACrE,EAAAA,6BAA4BA,6BAA4B,MAAM,IAAI,CAAC,IAAI;AACzE,GAAG,gCAAgC,8BAA8B,CAAC,EAAE;AAEpE,IAAI;AAAA,CACH,SAAUC,gCAA+B;AACxC,EAAAA,+BAA8BA,+BAA8B,MAAM,IAAI,CAAC,IAAI;AAC3E,EAAAA,+BAA8BA,+BAA8B,MAAM,IAAI,CAAC,IAAI;AAC3E,EAAAA,+BAA8BA,+BAA8B,OAAO,IAAI,CAAC,IAAI;AAC9E,GAAG,kCAAkC,gCAAgC,CAAC,EAAE;AAIxE,IAAM,cAAN,MAAkB;AAAA,EAChB,YAAY,SAAS,mBAAmB,WAAW,SAAS,gBAAgB;AAC1E,SAAK,oBAAoB;AACzB,SAAK,UAAU;AACf,SAAK,iBAAiB;AAEtB,SAAK,WAAW;AAEhB,SAAK,kBAAkB;AAKvB,SAAK,qBAAqB;AAE1B,SAAK,iBAAiB;AAKtB,SAAK,iBAAiB,MAAM;AAE5B,SAAK,gBAAgB,MAAM;AAE3B,SAAK,gBAAgB,IAAI,qBAAQ;AAIjC,SAAK,UAAU,IAAI,qBAAQ;AAK3B,SAAK,SAAS,IAAI,qBAAQ;AAE1B,SAAK,UAAU,IAAI,qBAAQ;AAE3B,SAAK,SAAS,IAAI,qBAAQ;AAE1B,SAAK,mBAAmB,IAAI,qBAAQ;AAEpC,SAAK,mBAAmB,IAAI,qBAAQ;AAEpC,SAAK,cAAc;AAEnB,SAAK,cAAc,CAAC;AAEpB,SAAK,YAAY,CAAC;AAElB,SAAK,kBAAkB,oBAAI,IAAI;AAE/B,SAAK,8BAA8B,0BAAa;AAEhD,SAAK,2BAA2B,4BAA4B;AAE5D,SAAK,6BAA6B,8BAA8B;AAEhE,SAAK,oBAAoB,IAAI,qBAAQ;AAErC,SAAK,oBAAoB;AAEzB,SAAK,sBAAsB,CAAC;AAE5B,SAAK,aAAa;AAElB,SAAK,uBAAuB,MAAM;AAChC,WAAK,eAAe;AACpB,iCAAS,GAAG,oCAAuB,EAAE,SAAK,6BAAU,KAAK,iBAAiB,CAAC,EAAE,UAAU,MAAM;AAC3F,cAAM,OAAO,KAAK;AAClB,cAAM,aAAa,KAAK;AACxB,YAAI,KAAK,6BAA6B,4BAA4B,IAAI;AACpE,eAAK,SAAS,GAAG,CAAC,UAAU;AAAA,QAC9B,WAAW,KAAK,6BAA6B,4BAA4B,MAAM;AAC7E,eAAK,SAAS,GAAG,UAAU;AAAA,QAC7B;AACA,YAAI,KAAK,+BAA+B,8BAA8B,MAAM;AAC1E,eAAK,SAAS,CAAC,YAAY,CAAC;AAAA,QAC9B,WAAW,KAAK,+BAA+B,8BAA8B,OAAO;AAClF,eAAK,SAAS,YAAY,CAAC;AAAA,QAC7B;AAAA,MACF,CAAC;AAAA,IACH;AACA,UAAM,iBAAiB,KAAK,UAAU,cAAc,OAAO;AAC3D,SAAK,YAAY;AACjB,SAAK,gBAAgB,UAAU,EAAE,qBAAqB,cAAc;AACpE,sBAAkB,sBAAsB,IAAI;AAC5C,SAAK,mBAAmB,IAAI,sBAAsB,SAAS;AAAA,EAC7D;AAAA;AAAA,EAEA,UAAU;AACR,SAAK,eAAe;AACpB,SAAK,kBAAkB,SAAS;AAChC,SAAK,4BAA4B,YAAY;AAC7C,SAAK,cAAc,SAAS;AAC5B,SAAK,QAAQ,SAAS;AACtB,SAAK,OAAO,SAAS;AACrB,SAAK,QAAQ,SAAS;AACtB,SAAK,OAAO,SAAS;AACrB,SAAK,iBAAiB,SAAS;AAC/B,SAAK,iBAAiB,SAAS;AAC/B,SAAK,gBAAgB,MAAM;AAC3B,SAAK,cAAc;AACnB,SAAK,iBAAiB,MAAM;AAC5B,SAAK,kBAAkB,oBAAoB,IAAI;AAAA,EACjD;AAAA;AAAA,EAEA,aAAa;AACX,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,QAAQ;AACN,SAAK,iBAAiB;AACtB,SAAK,yBAAyB;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,MAAM,UAAU,UAAU,OAAO;AACrC,SAAK,iBAAiB;AAGtB,QAAI,SAAS,QAAQ,KAAK,iBAAiB;AACzC,cAAQ,KAAK,YAAY,QAAQ,IAAI;AAAA,IACvC;AACA,SAAK,cAAc,MAAM,MAAM,UAAU,UAAU,KAAK;AAGxD,SAAK,sBAAsB;AAE3B,SAAK,yBAAyB;AAC9B,SAAK,QAAQ,KAAK;AAAA,MAChB;AAAA,MACA,WAAW;AAAA,MACX,cAAc,KAAK,aAAa,IAAI;AAAA,IACtC,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,KAAK,MAAM;AACT,SAAK,OAAO;AACZ,SAAK,OAAO,KAAK;AAAA,MACf;AAAA,MACA,WAAW;AAAA,IACb,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,KAAK,MAAM,cAAc,eAAe,mBAAmB,wBAAwB,UAAU,WAAW,QAAQ,CAAC,GAAG;AAClH,SAAK,OAAO;AACZ,SAAK,QAAQ,KAAK;AAAA,MAChB;AAAA,MACA;AAAA,MACA;AAAA,MACA,WAAW;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,OAAO;AACf,UAAM,gBAAgB,KAAK;AAC3B,SAAK,cAAc;AACnB,UAAM,QAAQ,UAAQ,KAAK,mBAAmB,IAAI,CAAC;AACnD,QAAI,KAAK,WAAW,GAAG;AACrB,YAAM,eAAe,cAAc,OAAO,UAAQ,KAAK,WAAW,CAAC;AAGnE,UAAI,aAAa,MAAM,UAAQ,MAAM,QAAQ,IAAI,MAAM,EAAE,GAAG;AAC1D,aAAK,OAAO;AAAA,MACd,OAAO;AACL,aAAK,cAAc,UAAU,KAAK,WAAW;AAAA,MAC/C;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,cAAc,WAAW;AACvB,SAAK,aAAa;AAClB,QAAI,KAAK,yBAAyB,wBAAwB;AACxD,WAAK,cAAc,YAAY;AAAA,IACjC;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,aAAa;AACvB,SAAK,YAAY,YAAY,MAAM;AACnC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB,aAAa;AAC3B,QAAI,gBAAgB,SAAS;AAC3B,WAAK,gBAAgB,IAAI,kBAAkB,KAAK,WAAW,KAAK,iBAAiB;AAAA,IACnF,OAAO;AACL,YAAM,WAAW,IAAI,uBAAuB,KAAK,iBAAiB;AAClE,eAAS,YAAY,KAAK;AAC1B,eAAS,cAAc;AACvB,WAAK,gBAAgB;AAAA,IACvB;AACA,SAAK,cAAc,qBAAqB,KAAK,UAAU;AACvD,SAAK,cAAc,kBAAkB,CAAC,OAAO,SAAS,KAAK,cAAc,OAAO,MAAM,IAAI,CAAC;AAC3F,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,sBAAsB,UAAU;AAC9B,UAAM,UAAU,KAAK;AAGrB,SAAK,sBAAsB,SAAS,QAAQ,OAAO,MAAM,KAAK,CAAC,SAAS,GAAG,QAAQ,IAAI,SAAS,MAAM;AACtG,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,qBAAqB,WAAW;AAC9B,QAAI,cAAc,KAAK,YAAY;AACjC,aAAO;AAAA,IACT;AACA,UAAM,UAAU,cAAc,KAAK,OAAO;AAC1C,SAAK,OAAO,cAAc,eAAe,cAAc,cAAc,WAAW,CAAC,QAAQ,SAAS,SAAS,GAAG;AAC5G,YAAM,IAAI,MAAM,yGAAyG;AAAA,IAC3H;AACA,UAAM,oBAAoB,KAAK,oBAAoB,QAAQ,KAAK,UAAU;AAC1E,UAAM,oBAAoB,KAAK,oBAAoB,QAAQ,SAAS;AACpE,QAAI,oBAAoB,IAAI;AAC1B,WAAK,oBAAoB,OAAO,mBAAmB,CAAC;AAAA,IACtD;AACA,QAAI,oBAAoB,IAAI;AAC1B,WAAK,oBAAoB,OAAO,mBAAmB,CAAC;AAAA,IACtD;AACA,QAAI,KAAK,eAAe;AACtB,WAAK,cAAc,qBAAqB,SAAS;AAAA,IACnD;AACA,SAAK,oBAAoB;AACzB,SAAK,oBAAoB,QAAQ,SAAS;AAC1C,SAAK,aAAa;AAClB,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,uBAAuB;AACrB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa,MAAM;AACjB,WAAO,KAAK,cAAc,KAAK,cAAc,aAAa,IAAI,IAAI,KAAK,YAAY,QAAQ,IAAI;AAAA,EACjG;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc;AACZ,WAAO,KAAK,gBAAgB,OAAO;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,UAAU,MAAM,UAAU,UAAU,cAAc;AAEhD,QAAI,KAAK,mBAAmB,CAAC,KAAK,YAAY,CAAC,qBAAqB,KAAK,UAAU,0BAA0B,UAAU,QAAQ,GAAG;AAChI;AAAA,IACF;AACA,UAAM,SAAS,KAAK,cAAc,KAAK,MAAM,UAAU,UAAU,YAAY;AAC7E,QAAI,QAAQ;AACV,WAAK,OAAO,KAAK;AAAA,QACf,eAAe,OAAO;AAAA,QACtB,cAAc,OAAO;AAAA,QACrB,WAAW;AAAA,QACX;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,2BAA2B,UAAU,UAAU;AAC7C,QAAI,KAAK,oBAAoB;AAC3B;AAAA,IACF;AACA,QAAI;AACJ,QAAI,0BAA0B,4BAA4B;AAC1D,QAAI,4BAA4B,8BAA8B;AAE9D,SAAK,iBAAiB,UAAU,QAAQ,CAAC,UAAU,YAAY;AAG7D,UAAI,YAAY,KAAK,aAAa,CAAC,SAAS,cAAc,YAAY;AACpE;AAAA,MACF;AACA,UAAI,qBAAqB,SAAS,YAAY,0BAA0B,UAAU,QAAQ,GAAG;AAC3F,SAAC,yBAAyB,yBAAyB,IAAI,2BAA2B,SAAS,SAAS,YAAY,KAAK,YAAY,UAAU,QAAQ;AACnJ,YAAI,2BAA2B,2BAA2B;AACxD,uBAAa;AAAA,QACf;AAAA,MACF;AAAA,IACF,CAAC;AAED,QAAI,CAAC,2BAA2B,CAAC,2BAA2B;AAC1D,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,KAAK,eAAe,gBAAgB;AACxC,YAAM,UAAU;AAAA,QACd;AAAA,QACA;AAAA,QACA,KAAK;AAAA,QACL,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,MAAM;AAAA,MACR;AACA,gCAA0B,2BAA2B,SAAS,QAAQ;AACtE,kCAA4B,6BAA6B,SAAS,QAAQ;AAC1E,mBAAa;AAAA,IACf;AACA,QAAI,eAAe,4BAA4B,KAAK,4BAA4B,8BAA8B,KAAK,8BAA8B,eAAe,KAAK,cAAc;AACjL,WAAK,2BAA2B;AAChC,WAAK,6BAA6B;AAClC,WAAK,cAAc;AACnB,WAAK,2BAA2B,8BAA8B,YAAY;AACxE,aAAK,QAAQ,kBAAkB,KAAK,oBAAoB;AAAA,MAC1D,OAAO;AACL,aAAK,eAAe;AAAA,MACtB;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,iBAAiB;AACf,SAAK,kBAAkB,KAAK;AAAA,EAC9B;AAAA;AAAA,EAEA,mBAAmB;AACjB,UAAM,SAAS,KAAK,WAAW;AAC/B,SAAK,cAAc,KAAK;AACxB,SAAK,cAAc;AACnB,SAAK,OAAO,cAAc,eAAe;AAAA;AAAA,IAGzC,KAAK,eAAe,cAAc,KAAK,OAAO,GAAG;AAC/C,iBAAW,QAAQ,KAAK,aAAa;AACnC,YAAI,CAAC,KAAK,WAAW,KAAK,KAAK,kBAAkB,EAAE,eAAe,KAAK,YAAY;AACjF,gBAAM,IAAI,MAAM,yGAAyG;AAAA,QAC3H;AAAA,MACF;AAAA,IACF;AAIA,SAAK,qBAAqB,OAAO,oBAAoB,OAAO,kBAAkB;AAC9E,WAAO,iBAAiB,OAAO,mBAAmB;AAClD,SAAK,cAAc,MAAM,KAAK,WAAW;AACzC,SAAK,sBAAsB;AAC3B,SAAK,4BAA4B,YAAY;AAC7C,SAAK,sBAAsB;AAAA,EAC7B;AAAA;AAAA,EAEA,wBAAwB;AACtB,SAAK,iBAAiB,MAAM,KAAK,mBAAmB;AAGpD,SAAK,WAAW,KAAK,iBAAiB,UAAU,IAAI,KAAK,UAAU,EAAE;AAAA,EACvE;AAAA;AAAA,EAEA,SAAS;AACP,SAAK,cAAc;AACnB,UAAM,SAAS,KAAK,WAAW;AAC/B,WAAO,iBAAiB,OAAO,mBAAmB,KAAK;AACvD,SAAK,UAAU,QAAQ,aAAW,QAAQ,eAAe,IAAI,CAAC;AAC9D,SAAK,cAAc,MAAM;AACzB,SAAK,eAAe;AACpB,SAAK,4BAA4B,YAAY;AAC7C,SAAK,iBAAiB,MAAM;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,iBAAiB,GAAG,GAAG;AACrB,WAAO,KAAK,YAAY,QAAQ,mBAAmB,KAAK,UAAU,GAAG,CAAC;AAAA,EACxE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,iCAAiC,MAAM,GAAG,GAAG;AAC3C,WAAO,KAAK,UAAU,KAAK,aAAW,QAAQ,YAAY,MAAM,GAAG,CAAC,CAAC;AAAA,EACvE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY,MAAM,GAAG,GAAG;AACtB,QAAI,CAAC,KAAK,YAAY,CAAC,mBAAmB,KAAK,UAAU,GAAG,CAAC,KAAK,CAAC,KAAK,eAAe,MAAM,IAAI,GAAG;AAClG,aAAO;AAAA,IACT;AACA,UAAM,mBAAmB,KAAK,eAAe,EAAE,iBAAiB,GAAG,CAAC;AAGpE,QAAI,CAAC,kBAAkB;AACrB,aAAO;AAAA,IACT;AAOA,WAAO,qBAAqB,KAAK,cAAc,KAAK,WAAW,SAAS,gBAAgB;AAAA,EAC1F;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB,SAAS,OAAO;AAC9B,UAAM,iBAAiB,KAAK;AAC5B,QAAI,CAAC,eAAe,IAAI,OAAO,KAAK,MAAM,MAAM,UAAQ;AAKtD,aAAO,KAAK,eAAe,MAAM,IAAI,KAAK,KAAK,YAAY,QAAQ,IAAI,IAAI;AAAA,IAC7E,CAAC,GAAG;AACF,qBAAe,IAAI,OAAO;AAC1B,WAAK,sBAAsB;AAC3B,WAAK,sBAAsB;AAC3B,WAAK,iBAAiB,KAAK;AAAA,QACzB,WAAW;AAAA,QACX,UAAU;AAAA,QACV;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe,SAAS;AACtB,SAAK,gBAAgB,OAAO,OAAO;AACnC,SAAK,4BAA4B,YAAY;AAC7C,SAAK,iBAAiB,KAAK;AAAA,MACzB,WAAW;AAAA,MACX,UAAU;AAAA,IACZ,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,wBAAwB;AACtB,SAAK,8BAA8B,KAAK,kBAAkB,SAAS,KAAK,eAAe,CAAC,EAAE,UAAU,WAAS;AAC3G,UAAI,KAAK,WAAW,GAAG;AACrB,cAAM,mBAAmB,KAAK,iBAAiB,aAAa,KAAK;AACjE,YAAI,kBAAkB;AACpB,eAAK,cAAc,eAAe,iBAAiB,KAAK,iBAAiB,IAAI;AAAA,QAC/E;AAAA,MACF,WAAW,KAAK,YAAY,GAAG;AAC7B,aAAK,sBAAsB;AAAA,MAC7B;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,iBAAiB;AACf,QAAI,CAAC,KAAK,mBAAmB;AAC3B,YAAM,aAAa,eAAe,KAAK,UAAU;AACjD,WAAK,oBAAoB,cAAc,KAAK;AAAA,IAC9C;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,2BAA2B;AACzB,UAAM,eAAe,KAAK,cAAc,uBAAuB,EAAE,OAAO,UAAQ,KAAK,WAAW,CAAC;AACjG,SAAK,UAAU,QAAQ,aAAW,QAAQ,gBAAgB,MAAM,YAAY,CAAC;AAAA,EAC/E;AACF;AAMA,SAAS,2BAA2B,YAAY,UAAU;AACxD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,aAAa,SAAS;AAC5B,MAAI,YAAY,MAAM,cAAc,YAAY,MAAM,YAAY;AAChE,WAAO,4BAA4B;AAAA,EACrC,WAAW,YAAY,SAAS,cAAc,YAAY,SAAS,YAAY;AAC7E,WAAO,4BAA4B;AAAA,EACrC;AACA,SAAO,4BAA4B;AACrC;AAMA,SAAS,6BAA6B,YAAY,UAAU;AAC1D,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,aAAa,QAAQ;AAC3B,MAAI,YAAY,OAAO,cAAc,YAAY,OAAO,YAAY;AAClE,WAAO,8BAA8B;AAAA,EACvC,WAAW,YAAY,QAAQ,cAAc,YAAY,QAAQ,YAAY;AAC3E,WAAO,8BAA8B;AAAA,EACvC;AACA,SAAO,8BAA8B;AACvC;AAUA,SAAS,2BAA2B,SAAS,YAAY,WAAW,UAAU,UAAU;AACtF,QAAM,mBAAmB,2BAA2B,YAAY,QAAQ;AACxE,QAAM,qBAAqB,6BAA6B,YAAY,QAAQ;AAC5E,MAAI,0BAA0B,4BAA4B;AAC1D,MAAI,4BAA4B,8BAA8B;AAK9D,MAAI,kBAAkB;AACpB,UAAM,YAAY,QAAQ;AAC1B,QAAI,qBAAqB,4BAA4B,IAAI;AACvD,UAAI,YAAY,GAAG;AACjB,kCAA0B,4BAA4B;AAAA,MACxD;AAAA,IACF,WAAW,QAAQ,eAAe,YAAY,QAAQ,cAAc;AAClE,gCAA0B,4BAA4B;AAAA,IACxD;AAAA,EACF;AACA,MAAI,oBAAoB;AACtB,UAAM,aAAa,QAAQ;AAC3B,QAAI,cAAc,OAAO;AACvB,UAAI,uBAAuB,8BAA8B,OAAO;AAE9D,YAAI,aAAa,GAAG;AAClB,sCAA4B,8BAA8B;AAAA,QAC5D;AAAA,MACF,WAAW,QAAQ,cAAc,aAAa,QAAQ,aAAa;AACjE,oCAA4B,8BAA8B;AAAA,MAC5D;AAAA,IACF,OAAO;AACL,UAAI,uBAAuB,8BAA8B,MAAM;AAC7D,YAAI,aAAa,GAAG;AAClB,sCAA4B,8BAA8B;AAAA,QAC5D;AAAA,MACF,WAAW,QAAQ,cAAc,aAAa,QAAQ,aAAa;AACjE,oCAA4B,8BAA8B;AAAA,MAC5D;AAAA,IACF;AAAA,EACF;AACA,SAAO,CAAC,yBAAyB,yBAAyB;AAC5D;AAGA,IAAM,8BAA8B,gCAAgC;AAAA,EAClE,SAAS;AAAA,EACT,SAAS;AACX,CAAC;AAED,IAAM,aAAa,oBAAI,IAAI;AAK3B,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,OAAO;AACL,SAAK,OAAO,SAAS,sBAAsB,mBAAmB;AAC5D,aAAO,KAAK,qBAAqB,gBAAe;AAAA,IAClD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,cAAc,CAAC;AAAA,MAC5B,WAAW,CAAC,6BAA6B,EAAE;AAAA,MAC3C,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,MACjC,OAAO;AAAA,MACP,MAAM;AAAA,MACN,UAAU,SAAS,uBAAuB,IAAI,KAAK;AAAA,MAAC;AAAA,MACpD,QAAQ,CAAC,iLAAiL;AAAA,MAC1L,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,MACZ,eAAe,oBAAkB;AAAA,MACjC,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,MAAM;AAAA,QACJ,6BAA6B;AAAA,MAC/B;AAAA,MACA,QAAQ,CAAC,iLAAiL;AAAA,IAC5L,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAOH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,YAAY,SAAS,WAAW;AAC9B,SAAK,UAAU;AACf,SAAK,UAAU,OAAO,cAAc;AACpC,SAAK,uBAAuB,OAAO,mBAAmB;AAEtD,SAAK,iBAAiB,oBAAI,IAAI;AAE9B,SAAK,iBAAiB,oBAAI,IAAI;AAE9B,SAAK,uBAAuB,OAAO,CAAC,CAAC;AAErC,SAAK,mBAAmB,oBAAI,IAAI;AAKhC,SAAK,qBAAqB,UAAQ,KAAK,WAAW;AAKlD,SAAK,cAAc,IAAI,qBAAQ;AAK/B,SAAK,YAAY,IAAI,qBAAQ;AAM7B,SAAK,SAAS,IAAI,qBAAQ;AAK1B,SAAK,+BAA+B,WAAS;AAC3C,UAAI,KAAK,qBAAqB,EAAE,SAAS,GAAG;AAC1C,cAAM,eAAe;AAAA,MACvB;AAAA,IACF;AAEA,SAAK,+BAA+B,WAAS;AAC3C,UAAI,KAAK,qBAAqB,EAAE,SAAS,GAAG;AAI1C,YAAI,KAAK,qBAAqB,EAAE,KAAK,KAAK,kBAAkB,GAAG;AAC7D,gBAAM,eAAe;AAAA,QACvB;AACA,aAAK,YAAY,KAAK,KAAK;AAAA,MAC7B;AAAA,IACF;AACA,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA,EAEA,sBAAsB,MAAM;AAC1B,QAAI,CAAC,KAAK,eAAe,IAAI,IAAI,GAAG;AAClC,WAAK,eAAe,IAAI,IAAI;AAAA,IAC9B;AAAA,EACF;AAAA;AAAA,EAEA,iBAAiB,MAAM;AACrB,SAAK,eAAe,IAAI,IAAI;AAI5B,QAAI,KAAK,eAAe,SAAS,GAAG;AAClC,WAAK,QAAQ,kBAAkB,MAAM;AAGnC,aAAK,UAAU,iBAAiB,aAAa,KAAK,8BAA8B,2BAA2B;AAAA,MAC7G,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA,EAEA,oBAAoB,MAAM;AACxB,SAAK,eAAe,OAAO,IAAI;AAAA,EACjC;AAAA;AAAA,EAEA,eAAe,MAAM;AACnB,SAAK,eAAe,OAAO,IAAI;AAC/B,SAAK,aAAa,IAAI;AACtB,QAAI,KAAK,eAAe,SAAS,GAAG;AAClC,WAAK,UAAU,oBAAoB,aAAa,KAAK,8BAA8B,2BAA2B;AAAA,IAChH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,cAAc,MAAM,OAAO;AAEzB,QAAI,KAAK,qBAAqB,EAAE,QAAQ,IAAI,IAAI,IAAI;AAClD;AAAA,IACF;AACA,SAAK,YAAY;AACjB,SAAK,qBAAqB,OAAO,eAAa,CAAC,GAAG,WAAW,IAAI,CAAC;AAClE,QAAI,KAAK,qBAAqB,EAAE,WAAW,GAAG;AAC5C,YAAMC,gBAAe,MAAM,KAAK,WAAW,OAAO;AAIlD,WAAK,iBAAiB,IAAIA,gBAAe,aAAa,WAAW;AAAA,QAC/D,SAAS,OAAK,KAAK,UAAU,KAAK,CAAC;AAAA,QACnC,SAAS;AAAA,MACX,CAAC,EAAE,IAAI,UAAU;AAAA,QACf,SAAS,OAAK,KAAK,OAAO,KAAK,CAAC;AAAA;AAAA;AAAA,QAGhC,SAAS;AAAA,MACX,CAAC,EAKA,IAAI,eAAe;AAAA,QAClB,SAAS,KAAK;AAAA,QACd,SAAS;AAAA,MACX,CAAC;AAGD,UAAI,CAACA,eAAc;AACjB,aAAK,iBAAiB,IAAI,aAAa;AAAA,UACrC,SAAS,OAAK,KAAK,YAAY,KAAK,CAAC;AAAA,UACrC,SAAS;AAAA,QACX,CAAC;AAAA,MACH;AACA,WAAK,QAAQ,kBAAkB,MAAM;AACnC,aAAK,iBAAiB,QAAQ,CAAC,QAAQ,SAAS;AAC9C,eAAK,UAAU,iBAAiB,MAAM,OAAO,SAAS,OAAO,OAAO;AAAA,QACtE,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA,EAEA,aAAa,MAAM;AACjB,SAAK,qBAAqB,OAAO,eAAa;AAC5C,YAAM,QAAQ,UAAU,QAAQ,IAAI;AACpC,UAAI,QAAQ,IAAI;AACd,kBAAU,OAAO,OAAO,CAAC;AACzB,eAAO,CAAC,GAAG,SAAS;AAAA,MACtB;AACA,aAAO;AAAA,IACT,CAAC;AACD,QAAI,KAAK,qBAAqB,EAAE,WAAW,GAAG;AAC5C,WAAK,sBAAsB;AAAA,IAC7B;AAAA,EACF;AAAA;AAAA,EAEA,WAAW,MAAM;AACf,WAAO,KAAK,qBAAqB,EAAE,QAAQ,IAAI,IAAI;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,SAAS,YAAY;AACnB,UAAM,UAAU,CAAC,KAAK,MAAM;AAC5B,QAAI,cAAc,eAAe,KAAK,WAAW;AAI/C,cAAQ,KAAK,IAAI,wBAAW,cAAY;AACtC,eAAO,KAAK,QAAQ,kBAAkB,MAAM;AAC1C,gBAAM,eAAe;AACrB,gBAAM,WAAW,WAAS;AACxB,gBAAI,KAAK,qBAAqB,EAAE,QAAQ;AACtC,uBAAS,KAAK,KAAK;AAAA,YACrB;AAAA,UACF;AACA,qBAAW,iBAAiB,UAAU,UAAU,YAAY;AAC5D,iBAAO,MAAM;AACX,uBAAW,oBAAoB,UAAU,UAAU,YAAY;AAAA,UACjE;AAAA,QACF,CAAC;AAAA,MACH,CAAC,CAAC;AAAA,IACJ;AACA,eAAO,oBAAM,GAAG,OAAO;AAAA,EACzB;AAAA,EACA,cAAc;AACZ,SAAK,eAAe,QAAQ,cAAY,KAAK,eAAe,QAAQ,CAAC;AACrE,SAAK,eAAe,QAAQ,cAAY,KAAK,oBAAoB,QAAQ,CAAC;AAC1E,SAAK,sBAAsB;AAC3B,SAAK,YAAY,SAAS;AAC1B,SAAK,UAAU,SAAS;AAAA,EAC1B;AAAA;AAAA,EAEA,wBAAwB;AACtB,SAAK,iBAAiB,QAAQ,CAAC,QAAQ,SAAS;AAC9C,WAAK,UAAU,oBAAoB,MAAM,OAAO,SAAS,OAAO,OAAO;AAAA,IACzE,CAAC;AACD,SAAK,iBAAiB,MAAM;AAAA,EAC9B;AAAA;AAAA;AAAA,EAGA,cAAc;AACZ,QAAI,CAAC,WAAW,IAAI,KAAK,OAAO,GAAG;AACjC,iBAAW,IAAI,KAAK,OAAO;AAC3B,YAAM,eAAe,gBAAgB,eAAe;AAAA,QAClD,qBAAqB,KAAK;AAAA,MAC5B,CAAC;AACD,WAAK,QAAQ,UAAU,MAAM;AAC3B,mBAAW,OAAO,KAAK,OAAO;AAC9B,YAAI,WAAW,SAAS,GAAG;AACzB,uBAAa,QAAQ;AAAA,QACvB;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,mBAAmB;AAC/D,aAAO,KAAK,qBAAqB,mBAAqB,SAAY,MAAM,GAAM,SAAS,QAAQ,CAAC;AAAA,IAClG;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,kBAAiB;AAAA,MAC1B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AAGH,IAAM,iBAAiB;AAAA,EACrB,oBAAoB;AAAA,EACpB,iCAAiC;AACnC;AAIA,IAAM,WAAN,MAAM,UAAS;AAAA,EACb,YAAY,WAAW,SAAS,gBAAgB,mBAAmB;AACjE,SAAK,YAAY;AACjB,SAAK,UAAU;AACf,SAAK,iBAAiB;AACtB,SAAK,oBAAoB;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,SAAS,SAAS,gBAAgB;AAC3C,WAAO,IAAI,QAAQ,SAAS,QAAQ,KAAK,WAAW,KAAK,SAAS,KAAK,gBAAgB,KAAK,iBAAiB;AAAA,EAC/G;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe,SAAS;AACtB,WAAO,IAAI,YAAY,SAAS,KAAK,mBAAmB,KAAK,WAAW,KAAK,SAAS,KAAK,cAAc;AAAA,EAC3G;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,iBAAiB,mBAAmB;AACvD,aAAO,KAAK,qBAAqB,WAAa,SAAS,QAAQ,GAAM,SAAY,MAAM,GAAM,SAAY,aAAa,GAAM,SAAS,gBAAgB,CAAC;AAAA,IACxJ;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,UAAS;AAAA,MAClB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,UAAU,CAAC;AAAA,IACjF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,CAAC,GAAG,IAAI;AACV,GAAG;AAQH,IAAM,kBAAkB,IAAI,eAAe,iBAAiB;AAO5D,SAAS,kBAAkB,MAAM,MAAM;AACrC,MAAI,KAAK,aAAa,GAAG;AACvB,UAAM,MAAM,GAAG,IAAI,gEAAqE,KAAK,QAAQ,IAAI;AAAA,EAC3G;AACF;AAOA,IAAM,kBAAkB,IAAI,eAAe,eAAe;AAE1D,IAAM,gBAAN,MAAM,eAAc;AAAA;AAAA,EAElB,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY;AACjB,SAAK,cAAc,KAAK,IAAI;AAAA,EAC9B;AAAA,EACA,YAAY,SAAS,aAAa;AAChC,SAAK,UAAU;AACf,SAAK,cAAc;AAEnB,SAAK,gBAAgB,IAAI,qBAAQ;AACjC,SAAK,YAAY;AACjB,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,wBAAkB,QAAQ,eAAe,eAAe;AAAA,IAC1D;AACA,iBAAa,WAAW,IAAI;AAAA,EAC9B;AAAA,EACA,cAAc;AACZ,SAAK,aAAa,cAAc,IAAI;AACpC,SAAK,cAAc,SAAS;AAAA,EAC9B;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,sBAAsB,mBAAmB;AAC5D,aAAO,KAAK,qBAAqB,gBAAkB,kBAAqB,UAAU,GAAM,kBAAkB,iBAAiB,EAAE,CAAC;AAAA,IAChI;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,iBAAiB,EAAE,CAAC;AAAA,MACrC,WAAW,CAAC,GAAG,iBAAiB;AAAA,MAChC,QAAQ;AAAA,QACN,UAAU,CAAC,GAAG,yBAAyB,YAAY,gBAAgB;AAAA,MACrE;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC;AAAA,QAChC,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC,CAAC,GAAM,wBAAwB;AAAA,IAClC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,MAAM;AAAA,QACJ,SAAS;AAAA,MACX;AAAA,MACA,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,kBAAkB,IAAI,eAAe,iBAAiB;AAC5D,IAAM,kBAAkB;AAMxB,IAAM,gBAAgB,IAAI,eAAe,aAAa;AAEtD,IAAM,UAAN,MAAM,SAAQ;AAAA,EACZ,OAAO;AACL,SAAK,iBAAiB,CAAC;AAAA,EACzB;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,WAAO,KAAK,aAAa,KAAK,iBAAiB,KAAK,cAAc;AAAA,EACpE;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY;AACjB,SAAK,SAAS,WAAW,KAAK;AAAA,EAChC;AAAA,EACA,YACA,SACA,eAKA,WAAW,SAAS,mBAAmB,QAAQ,MAAM,UAAU,oBAAoB,aAAa,aAAa;AAC3G,SAAK,UAAU;AACf,SAAK,gBAAgB;AACrB,SAAK,UAAU;AACf,SAAK,oBAAoB;AACzB,SAAK,OAAO;AACZ,SAAK,qBAAqB;AAC1B,SAAK,cAAc;AACnB,SAAK,cAAc;AACnB,SAAK,aAAa,IAAI,qBAAQ;AAC9B,SAAK,WAAW,IAAI,6BAAgB,CAAC,CAAC;AAKtC,SAAK,QAAQ;AAEb,SAAK,UAAU,IAAI,aAAa;AAEhC,SAAK,WAAW,IAAI,aAAa;AAEjC,SAAK,QAAQ,IAAI,aAAa;AAE9B,SAAK,UAAU,IAAI,aAAa;AAEhC,SAAK,SAAS,IAAI,aAAa;AAE/B,SAAK,UAAU,IAAI,aAAa;AAKhC,SAAK,QAAQ,IAAI,wBAAW,cAAY;AACtC,YAAM,eAAe,KAAK,SAAS,MAAM,SAAK,uBAAI,iBAAe;AAAA,QAC/D,QAAQ;AAAA,QACR,iBAAiB,WAAW;AAAA,QAC5B,OAAO,WAAW;AAAA,QAClB,OAAO,WAAW;AAAA,QAClB,UAAU,WAAW;AAAA,MACvB,EAAE,CAAC,EAAE,UAAU,QAAQ;AACvB,aAAO,MAAM;AACX,qBAAa,YAAY;AAAA,MAC3B;AAAA,IACF,CAAC;AACD,SAAK,YAAY,OAAO,QAAQ;AAChC,SAAK,WAAW,SAAS,WAAW,SAAS;AAAA,MAC3C,oBAAoB,UAAU,OAAO,sBAAsB,OAAO,OAAO,qBAAqB;AAAA,MAC9F,iCAAiC,UAAU,OAAO,mCAAmC,OAAO,OAAO,kCAAkC;AAAA,MACrI,QAAQ,QAAQ;AAAA,IAClB,CAAC;AACD,SAAK,SAAS,OAAO;AAIrB,aAAQ,eAAe,KAAK,IAAI;AAChC,QAAI,QAAQ;AACV,WAAK,gBAAgB,MAAM;AAAA,IAC7B;AAQA,QAAI,eAAe;AACjB,WAAK,SAAS,mBAAmB,cAAc,YAAY;AAC3D,oBAAc,QAAQ,IAAI;AAE1B,oBAAc,aAAa,cAAc,SAAK,6BAAU,KAAK,UAAU,CAAC,EAAE,UAAU,MAAM;AACxF,aAAK,SAAS,QAAQ,KAAK;AAAA,MAC7B,CAAC;AAAA,IACH;AACA,SAAK,YAAY,KAAK,QAAQ;AAC9B,SAAK,cAAc,KAAK,QAAQ;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,wBAAwB;AACtB,WAAO,KAAK,SAAS,sBAAsB;AAAA,EAC7C;AAAA;AAAA,EAEA,iBAAiB;AACf,WAAO,KAAK,SAAS,eAAe;AAAA,EACtC;AAAA;AAAA,EAEA,QAAQ;AACN,SAAK,SAAS,MAAM;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA,EAIA,sBAAsB;AACpB,WAAO,KAAK,SAAS,oBAAoB;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB,OAAO;AACzB,SAAK,SAAS,oBAAoB,KAAK;AAAA,EACzC;AAAA,EACA,kBAAkB;AAKhB,oBAAgB,MAAM;AACpB,WAAK,mBAAmB;AACxB,WAAK,sBAAsB;AAC3B,WAAK,SAAS,QAAQ,KAAK;AAC3B,UAAI,KAAK,kBAAkB;AACzB,aAAK,SAAS,oBAAoB,KAAK,gBAAgB;AAAA,MACzD;AAAA,IACF,GAAG;AAAA,MACD,UAAU,KAAK;AAAA,IACjB,CAAC;AAAA,EACH;AAAA,EACA,YAAY,SAAS;AACnB,UAAM,qBAAqB,QAAQ,qBAAqB;AACxD,UAAM,iBAAiB,QAAQ,kBAAkB;AAGjD,QAAI,sBAAsB,CAAC,mBAAmB,aAAa;AACzD,WAAK,mBAAmB;AAAA,IAC1B;AAEA,SAAK,SAAS,QAAQ,KAAK;AAG3B,QAAI,kBAAkB,CAAC,eAAe,eAAe,KAAK,kBAAkB;AAC1E,WAAK,SAAS,oBAAoB,KAAK,gBAAgB;AAAA,IACzD;AAAA,EACF;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,eAAe;AACtB,WAAK,cAAc,WAAW,IAAI;AAAA,IACpC;AACA,UAAM,QAAQ,SAAQ,eAAe,QAAQ,IAAI;AACjD,QAAI,QAAQ,IAAI;AACd,eAAQ,eAAe,OAAO,OAAO,CAAC;AAAA,IACxC;AAEA,SAAK,QAAQ,kBAAkB,MAAM;AACnC,WAAK,SAAS,SAAS;AACvB,WAAK,WAAW,KAAK;AACrB,WAAK,WAAW,SAAS;AACzB,WAAK,SAAS,QAAQ;AAAA,IACxB,CAAC;AAAA,EACH;AAAA,EACA,WAAW,QAAQ;AACjB,UAAM,UAAU,KAAK,SAAS,SAAS;AACvC,YAAQ,KAAK,MAAM;AACnB,SAAK,SAAS,KAAK,OAAO;AAAA,EAC5B;AAAA,EACA,cAAc,QAAQ;AACpB,UAAM,UAAU,KAAK,SAAS,SAAS;AACvC,UAAM,QAAQ,QAAQ,QAAQ,MAAM;AACpC,QAAI,QAAQ,IAAI;AACd,cAAQ,OAAO,OAAO,CAAC;AACvB,WAAK,SAAS,KAAK,OAAO;AAAA,IAC5B;AAAA,EACF;AAAA,EACA,oBAAoB,SAAS;AAC3B,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EACA,sBAAsB,SAAS;AAC7B,QAAI,YAAY,KAAK,kBAAkB;AACrC,WAAK,mBAAmB;AAAA,IAC1B;AAAA,EACF;AAAA,EACA,wBAAwB,aAAa;AACnC,SAAK,uBAAuB;AAAA,EAC9B;AAAA,EACA,0BAA0B,aAAa;AACrC,QAAI,gBAAgB,KAAK,sBAAsB;AAC7C,WAAK,uBAAuB;AAAA,IAC9B;AAAA,EACF;AAAA;AAAA,EAEA,qBAAqB;AACnB,UAAM,UAAU,KAAK,QAAQ;AAC7B,QAAI,cAAc;AAClB,QAAI,KAAK,qBAAqB;AAC5B,oBAAc,QAAQ,YAAY,SAAY,QAAQ,QAAQ,KAAK,mBAAmB;AAAA;AAAA,QAEtF,QAAQ,eAAe,QAAQ,KAAK,mBAAmB;AAAA;AAAA,IACzD;AACA,QAAI,gBAAgB,OAAO,cAAc,eAAe,YAAY;AAClE,wBAAkB,aAAa,SAAS;AAAA,IAC1C;AACA,SAAK,SAAS,gBAAgB,eAAe,OAAO;AAAA,EACtD;AAAA;AAAA,EAEA,sBAAsB;AACpB,UAAM,WAAW,KAAK;AACtB,QAAI,CAAC,UAAU;AACb,aAAO;AAAA,IACT;AACA,QAAI,OAAO,aAAa,UAAU;AAChC,aAAO,KAAK,QAAQ,cAAc,QAAQ,QAAQ;AAAA,IACpD;AACA,WAAO,cAAc,QAAQ;AAAA,EAC/B;AAAA;AAAA,EAEA,YAAY,KAAK;AACf,QAAI,cAAc,UAAU,MAAM;AAChC,UAAI,CAAC,IAAI,WAAW,GAAG;AACrB,cAAM,MAAM,KAAK;AACjB,cAAM,iBAAiB,KAAK;AAC5B,cAAM,cAAc,KAAK,uBAAuB;AAAA,UAC9C,UAAU,KAAK,qBAAqB;AAAA,UACpC,SAAS,KAAK,qBAAqB;AAAA,UACnC,eAAe,KAAK;AAAA,QACtB,IAAI;AACJ,cAAM,UAAU,KAAK,mBAAmB;AAAA,UACtC,UAAU,KAAK,iBAAiB;AAAA,UAChC,SAAS,KAAK,iBAAiB;AAAA,UAC/B,WAAW,KAAK,iBAAiB;AAAA,UACjC,eAAe,KAAK;AAAA,QACtB,IAAI;AACJ,YAAI,WAAW,KAAK;AACpB,YAAI,WAAW,KAAK;AACpB,YAAI,QAAQ,KAAK;AACjB,YAAI,iBAAiB,OAAO,mBAAmB,YAAY,iBAAiB,iBAAiB,qBAAqB,cAAc;AAChI,YAAI,oBAAoB,KAAK;AAC7B,YAAI,eAAe,KAAK;AACxB,YAAI,oBAAoB,KAAK,oBAAoB,CAAC,EAAE,wBAAwB,WAAW,EAAE,oBAAoB,OAAO,EAAE,qBAAqB,KAAK,oBAAoB,QAAQ;AAC5K,YAAI,KAAK;AACP,cAAI,cAAc,IAAI,KAAK;AAAA,QAC7B;AAAA,MACF;AAAA,IACF,CAAC;AAED,QAAI,cAAc,SAAK,wBAAK,CAAC,CAAC,EAAE,UAAU,MAAM;AAE9C,UAAI,KAAK,aAAa;AACpB,YAAI,WAAW,KAAK,YAAY,QAAQ;AACxC;AAAA,MACF;AAGA,UAAI,SAAS,KAAK,QAAQ,cAAc;AACxC,aAAO,QAAQ;AACb,YAAI,OAAO,UAAU,SAAS,eAAe,GAAG;AAC9C,cAAI,WAAW,SAAQ,eAAe,KAAK,UAAQ;AACjD,mBAAO,KAAK,QAAQ,kBAAkB;AAAA,UACxC,CAAC,GAAG,YAAY,IAAI;AACpB;AAAA,QACF;AACA,iBAAS,OAAO;AAAA,MAClB;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,cAAc,KAAK;AACjB,QAAI,QAAQ,UAAU,gBAAc;AAClC,WAAK,QAAQ,KAAK;AAAA,QAChB,QAAQ;AAAA,QACR,OAAO,WAAW;AAAA,MACpB,CAAC;AAGD,WAAK,mBAAmB,aAAa;AAAA,IACvC,CAAC;AACD,QAAI,SAAS,UAAU,kBAAgB;AACrC,WAAK,SAAS,KAAK;AAAA,QACjB,QAAQ;AAAA,QACR,OAAO,aAAa;AAAA,MACtB,CAAC;AAAA,IACH,CAAC;AACD,QAAI,MAAM,UAAU,cAAY;AAC9B,WAAK,MAAM,KAAK;AAAA,QACd,QAAQ;AAAA,QACR,UAAU,SAAS;AAAA,QACnB,WAAW,SAAS;AAAA,QACpB,OAAO,SAAS;AAAA,MAClB,CAAC;AAGD,WAAK,mBAAmB,aAAa;AAAA,IACvC,CAAC;AACD,QAAI,QAAQ,UAAU,gBAAc;AAClC,WAAK,QAAQ,KAAK;AAAA,QAChB,WAAW,WAAW,UAAU;AAAA,QAChC,MAAM;AAAA,QACN,cAAc,WAAW;AAAA,MAC3B,CAAC;AAAA,IACH,CAAC;AACD,QAAI,OAAO,UAAU,eAAa;AAChC,WAAK,OAAO,KAAK;AAAA,QACf,WAAW,UAAU,UAAU;AAAA,QAC/B,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AACD,QAAI,QAAQ,UAAU,eAAa;AACjC,WAAK,QAAQ,KAAK;AAAA,QAChB,eAAe,UAAU;AAAA,QACzB,cAAc,UAAU;AAAA,QACxB,mBAAmB,UAAU,kBAAkB;AAAA,QAC/C,WAAW,UAAU,UAAU;AAAA,QAC/B,wBAAwB,UAAU;AAAA,QAClC,MAAM;AAAA,QACN,UAAU,UAAU;AAAA,QACpB,WAAW,UAAU;AAAA,QACrB,OAAO,UAAU;AAAA,MACnB,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,gBAAgB,QAAQ;AACtB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,SAAK,WAAW,oBAAoB,OAAO,QAAQ;AACnD,SAAK,iBAAiB,kBAAkB;AACxC,QAAI,UAAU;AACZ,WAAK,WAAW;AAAA,IAClB;AACA,QAAI,mBAAmB;AACrB,WAAK,oBAAoB;AAAA,IAC3B;AACA,QAAI,cAAc;AAChB,WAAK,eAAe;AAAA,IACtB;AACA,QAAI,iBAAiB;AACnB,WAAK,kBAAkB;AAAA,IACzB;AACA,QAAI,qBAAqB;AACvB,WAAK,sBAAsB;AAAA,IAC7B;AACA,QAAI,kBAAkB;AACpB,WAAK,mBAAmB;AAAA,IAC1B;AAAA,EACF;AAAA;AAAA,EAEA,wBAAwB;AAEtB,SAAK,SAAS;AAAA;AAAA,UAEd,uBAAI,aAAW;AACb,cAAM,iBAAiB,QAAQ,IAAI,YAAU,OAAO,OAAO;AAI3D,YAAI,KAAK,eAAe,KAAK,qBAAqB;AAChD,yBAAe,KAAK,KAAK,OAAO;AAAA,QAClC;AACA,aAAK,SAAS,YAAY,cAAc;AAAA,MAC1C,CAAC;AAAA;AAAA,UAED,6BAAU,aAAW;AACnB,mBAAO,oBAAM,GAAG,QAAQ,IAAI,UAAQ,KAAK,cAAc,SAAK,6BAAU,IAAI,CAAC,CAAC,CAAC;AAAA,MAC/E,CAAC;AAAA,UAAG,6BAAU,KAAK,UAAU;AAAA,IAAC,EAAE,UAAU,oBAAkB;AAE1D,YAAM,UAAU,KAAK;AACrB,YAAM,SAAS,eAAe,QAAQ;AACtC,qBAAe,WAAW,QAAQ,cAAc,MAAM,IAAI,QAAQ,aAAa,MAAM;AAAA,IACvF,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,gBAAgB,mBAAmB;AACtD,aAAO,KAAK,qBAAqB,UAAY,kBAAqB,UAAU,GAAM,kBAAkB,eAAe,EAAE,GAAM,kBAAkB,QAAQ,GAAM,kBAAqB,MAAM,GAAM,kBAAqB,gBAAgB,GAAM,kBAAkB,iBAAiB,CAAC,GAAM,kBAAuB,gBAAgB,CAAC,GAAM,kBAAkB,QAAQ,GAAM,kBAAqB,iBAAiB,GAAM,kBAAkB,iBAAiB,EAAE,GAAM,kBAAkB,iBAAiB,EAAE,CAAC;AAAA,IAC9d;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,WAAW,EAAE,CAAC;AAAA,MAC/B,WAAW,CAAC,GAAG,UAAU;AAAA,MACzB,UAAU;AAAA,MACV,cAAc,SAAS,qBAAqB,IAAI,KAAK;AACnD,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,qBAAqB,IAAI,QAAQ,EAAE,qBAAqB,IAAI,SAAS,WAAW,CAAC;AAAA,QAClG;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,MAAM,CAAC,GAAG,eAAe,MAAM;AAAA,QAC/B,UAAU,CAAC,GAAG,mBAAmB,UAAU;AAAA,QAC3C,qBAAqB,CAAC,GAAG,sBAAsB,qBAAqB;AAAA,QACpE,iBAAiB,CAAC,GAAG,mBAAmB,iBAAiB;AAAA,QACzD,gBAAgB,CAAC,GAAG,qBAAqB,gBAAgB;AAAA,QACzD,kBAAkB,CAAC,GAAG,2BAA2B,kBAAkB;AAAA,QACnE,UAAU,CAAC,GAAG,mBAAmB,YAAY,gBAAgB;AAAA,QAC7D,mBAAmB,CAAC,GAAG,4BAA4B,mBAAmB;AAAA,QACtE,cAAc,CAAC,GAAG,uBAAuB,cAAc;AAAA,QACvD,kBAAkB,CAAC,GAAG,2BAA2B,kBAAkB;AAAA,QACnE,OAAO,CAAC,GAAG,gBAAgB,SAAS,eAAe;AAAA,MACrD;AAAA,MACA,SAAS;AAAA,QACP,SAAS;AAAA,QACT,UAAU;AAAA,QACV,OAAO;AAAA,QACP,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU,CAAC,SAAS;AAAA,MACpB,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC;AAAA,QAChC,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC,CAAC,GAAM,0BAA6B,oBAAoB;AAAA,IAC3D,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,SAAS,CAAC;AAAA,IAChF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,6BAA6B;AAAA,QAC7B,6BAA6B;AAAA,MAC/B;AAAA,MACA,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAW;AAAA,IACX,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,yBAAyB;AAAA,IAClC,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,MACN,MAAM,CAAC,0BAA0B;AAAA,IACnC,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,yBAAyB;AAAA,IAClC,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,IACvB,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,IACvB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAOH,IAAM,sBAAsB,IAAI,eAAe,kBAAkB;AAOjE,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,cAAc;AAEZ,SAAK,SAAS,oBAAI,IAAI;AAEtB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,cAAc;AACZ,SAAK,OAAO,MAAM;AAAA,EACpB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,mBAAmB;AAC/D,aAAO,KAAK,qBAAqB,mBAAkB;AAAA,IACrD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,oBAAoB,EAAE,CAAC;AAAA,MACxC,QAAQ;AAAA,QACN,UAAU,CAAC,GAAG,4BAA4B,YAAY,gBAAgB;AAAA,MACxE;AAAA,MACA,UAAU,CAAC,kBAAkB;AAAA,MAC7B,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC;AAAA,QAChC,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC,CAAC,GAAM,wBAAwB;AAAA,IAClC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAGH,IAAI,mBAAmB;AAEvB,IAAM,cAAN,MAAM,aAAY;AAAA,EAEhB,OAAO;AACL,SAAK,aAAa,CAAC;AAAA,EACrB;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,WAAO,KAAK,aAAa,CAAC,CAAC,KAAK,UAAU,KAAK,OAAO;AAAA,EACxD;AAAA,EACA,IAAI,SAAS,OAAO;AAKlB,SAAK,aAAa,WAAW,KAAK,YAAY;AAAA,EAChD;AAAA,EACA,YACA,SAAS,UAAU,oBAAoB,mBAAmB,MAAM,QAAQ,QAAQ;AAC9E,SAAK,UAAU;AACf,SAAK,qBAAqB;AAC1B,SAAK,oBAAoB;AACzB,SAAK,OAAO;AACZ,SAAK,SAAS;AAEd,SAAK,aAAa,IAAI,qBAAQ;AAM9B,SAAK,cAAc,CAAC;AAKpB,SAAK,KAAK,iBAAiB,kBAAkB;AAK7C,SAAK,iBAAiB,MAAM;AAE5B,SAAK,gBAAgB,MAAM;AAE3B,SAAK,UAAU,IAAI,aAAa;AAIhC,SAAK,UAAU,IAAI,aAAa;AAKhC,SAAK,SAAS,IAAI,aAAa;AAE/B,SAAK,SAAS,IAAI,aAAa;AAQ/B,SAAK,iBAAiB,oBAAI,IAAI;AAC9B,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,wBAAkB,QAAQ,eAAe,aAAa;AAAA,IACxD;AACA,SAAK,eAAe,SAAS,eAAe,OAAO;AACnD,SAAK,aAAa,OAAO;AACzB,QAAI,QAAQ;AACV,WAAK,gBAAgB,MAAM;AAAA,IAC7B;AACA,SAAK,aAAa,iBAAiB,CAAC,MAAM,SAAS;AACjD,aAAO,KAAK,eAAe,KAAK,MAAM,KAAK,IAAI;AAAA,IACjD;AACA,SAAK,aAAa,gBAAgB,CAAC,OAAO,MAAM,SAAS;AACvD,aAAO,KAAK,cAAc,OAAO,KAAK,MAAM,KAAK,IAAI;AAAA,IACvD;AACA,SAAK,4BAA4B,KAAK,YAAY;AAClD,SAAK,cAAc,KAAK,YAAY;AACpC,iBAAY,WAAW,KAAK,IAAI;AAChC,QAAI,QAAQ;AACV,aAAO,OAAO,IAAI,IAAI;AAAA,IACxB;AAAA,EACF;AAAA;AAAA,EAEA,QAAQ,MAAM;AACZ,SAAK,eAAe,IAAI,IAAI;AAC5B,QAAI,KAAK,aAAa,WAAW,GAAG;AAClC,WAAK,kBAAkB;AAAA,IACzB;AAAA,EACF;AAAA;AAAA,EAEA,WAAW,MAAM;AACf,SAAK,eAAe,OAAO,IAAI;AAC/B,QAAI,KAAK,aAAa,WAAW,GAAG;AAClC,WAAK,kBAAkB;AAAA,IACzB;AAAA,EACF;AAAA;AAAA,EAEA,iBAAiB;AACf,WAAO,MAAM,KAAK,KAAK,cAAc,EAAE,KAAK,CAAC,GAAG,MAAM;AACpD,YAAM,mBAAmB,EAAE,SAAS,kBAAkB,EAAE,wBAAwB,EAAE,SAAS,kBAAkB,CAAC;AAI9G,aAAO,mBAAmB,KAAK,8BAA8B,KAAK;AAAA,IACpE,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,UAAM,QAAQ,aAAY,WAAW,QAAQ,IAAI;AACjD,QAAI,QAAQ,IAAI;AACd,mBAAY,WAAW,OAAO,OAAO,CAAC;AAAA,IACxC;AACA,QAAI,KAAK,QAAQ;AACf,WAAK,OAAO,OAAO,OAAO,IAAI;AAAA,IAChC;AACA,SAAK,eAAe,MAAM;AAC1B,SAAK,aAAa,QAAQ;AAC1B,SAAK,WAAW,KAAK;AACrB,SAAK,WAAW,SAAS;AAAA,EAC3B;AAAA;AAAA,EAEA,4BAA4B,KAAK;AAC/B,QAAI,KAAK,MAAM;AACb,WAAK,KAAK,OAAO,SAAK,6BAAU,KAAK,KAAK,KAAK,OAAG,6BAAU,KAAK,UAAU,CAAC,EAAE,UAAU,WAAS,IAAI,cAAc,KAAK,CAAC;AAAA,IAC3H;AACA,QAAI,cAAc,UAAU,MAAM;AAChC,YAAM,WAAW,YAAY,KAAK,WAAW,EAAE,IAAI,UAAQ;AACzD,YAAI,OAAO,SAAS,UAAU;AAC5B,gBAAM,wBAAwB,aAAY,WAAW,KAAK,UAAQ,KAAK,OAAO,IAAI;AAClF,cAAI,CAAC,0BAA0B,OAAO,cAAc,eAAe,YAAY;AAC7E,oBAAQ,KAAK,2DAA2D,IAAI,GAAG;AAAA,UACjF;AACA,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT,CAAC;AACD,UAAI,KAAK,QAAQ;AACf,aAAK,OAAO,OAAO,QAAQ,UAAQ;AACjC,cAAI,SAAS,QAAQ,IAAI,MAAM,IAAI;AACjC,qBAAS,KAAK,IAAI;AAAA,UACpB;AAAA,QACF,CAAC;AAAA,MACH;AAGA,UAAI,CAAC,KAAK,4BAA4B;AACpC,cAAM,oBAAoB,KAAK,kBAAkB,4BAA4B,KAAK,OAAO,EAAE,IAAI,gBAAc,WAAW,cAAc,EAAE,aAAa;AACrJ,aAAK,aAAa,sBAAsB,iBAAiB;AAGzD,aAAK,6BAA6B;AAAA,MACpC;AACA,UAAI,KAAK,0BAA0B;AACjC,cAAM,YAAY,KAAK,QAAQ,cAAc,cAAc,KAAK,wBAAwB;AACxF,YAAI,CAAC,cAAc,OAAO,cAAc,eAAe,YAAY;AACjE,gBAAM,IAAI,MAAM,0EAA0E,KAAK,wBAAwB,GAAG;AAAA,QAC5H;AACA,YAAI,qBAAqB,SAAS;AAAA,MACpC;AACA,UAAI,WAAW,KAAK;AACpB,UAAI,WAAW,KAAK;AACpB,UAAI,kBAAkB,KAAK;AAC3B,UAAI,qBAAqB,KAAK;AAC9B,UAAI,iBAAiB,qBAAqB,KAAK,gBAAgB,CAAC;AAChE,UAAI,YAAY,SAAS,OAAO,UAAQ,QAAQ,SAAS,IAAI,EAAE,IAAI,UAAQ,KAAK,YAAY,CAAC,EAAE,gBAAgB,KAAK,WAAW;AAAA,IACjI,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,cAAc,KAAK;AACjB,QAAI,cAAc,UAAU,MAAM;AAChC,WAAK,kBAAkB;AACvB,WAAK,mBAAmB,aAAa;AAAA,IACvC,CAAC;AACD,QAAI,QAAQ,UAAU,WAAS;AAC7B,WAAK,QAAQ,KAAK;AAAA,QAChB,WAAW;AAAA,QACX,MAAM,MAAM,KAAK;AAAA,QACjB,cAAc,MAAM;AAAA,MACtB,CAAC;AAAA,IACH,CAAC;AACD,QAAI,OAAO,UAAU,WAAS;AAC5B,WAAK,OAAO,KAAK;AAAA,QACf,WAAW;AAAA,QACX,MAAM,MAAM,KAAK;AAAA,MACnB,CAAC;AACD,WAAK,mBAAmB,aAAa;AAAA,IACvC,CAAC;AACD,QAAI,OAAO,UAAU,WAAS;AAC5B,WAAK,OAAO,KAAK;AAAA,QACf,eAAe,MAAM;AAAA,QACrB,cAAc,MAAM;AAAA,QACpB,WAAW;AAAA,QACX,MAAM,MAAM,KAAK;AAAA,MACnB,CAAC;AAAA,IACH,CAAC;AACD,QAAI,QAAQ,UAAU,eAAa;AACjC,WAAK,QAAQ,KAAK;AAAA,QAChB,eAAe,UAAU;AAAA,QACzB,cAAc,UAAU;AAAA,QACxB,mBAAmB,UAAU,kBAAkB;AAAA,QAC/C,WAAW,UAAU,UAAU;AAAA,QAC/B,MAAM,UAAU,KAAK;AAAA,QACrB,wBAAwB,UAAU;AAAA,QAClC,UAAU,UAAU;AAAA,QACpB,WAAW,UAAU;AAAA,QACrB,OAAO,UAAU;AAAA,MACnB,CAAC;AAGD,WAAK,mBAAmB,aAAa;AAAA,IACvC,CAAC;AACD,4BAAM,IAAI,kBAAkB,IAAI,gBAAgB,EAAE,UAAU,MAAM,KAAK,mBAAmB,aAAa,CAAC;AAAA,EAC1G;AAAA;AAAA,EAEA,gBAAgB,QAAQ;AACtB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,SAAK,WAAW,oBAAoB,OAAO,QAAQ;AACnD,SAAK,kBAAkB,mBAAmB,OAAO,QAAQ;AACzD,SAAK,qBAAqB,0BAA0B,OAAO,QAAQ;AACnE,SAAK,cAAc,mBAAmB;AACtC,QAAI,UAAU;AACZ,WAAK,WAAW;AAAA,IAClB;AAAA,EACF;AAAA;AAAA,EAEA,oBAAoB;AAClB,SAAK,aAAa,UAAU,KAAK,eAAe,EAAE,IAAI,UAAQ,KAAK,QAAQ,CAAC;AAAA,EAC9E;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,oBAAoB,mBAAmB;AAC1D,aAAO,KAAK,qBAAqB,cAAgB,kBAAqB,UAAU,GAAM,kBAAkB,QAAQ,GAAM,kBAAqB,iBAAiB,GAAM,kBAAqB,gBAAgB,GAAM,kBAAuB,gBAAgB,CAAC,GAAM,kBAAkB,qBAAqB,EAAE,GAAM,kBAAkB,iBAAiB,CAAC,CAAC;AAAA,IACjV;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,eAAe,EAAE,GAAG,CAAC,eAAe,CAAC;AAAA,MACtD,WAAW,CAAC,GAAG,eAAe;AAAA,MAC9B,UAAU;AAAA,MACV,cAAc,SAAS,yBAAyB,IAAI,KAAK;AACvD,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,MAAM,IAAI,EAAE;AAC3B,UAAG,YAAY,0BAA0B,IAAI,QAAQ,EAAE,0BAA0B,IAAI,aAAa,WAAW,CAAC,EAAE,2BAA2B,IAAI,aAAa,YAAY,CAAC;AAAA,QAC3K;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,aAAa,CAAC,GAAG,0BAA0B,aAAa;AAAA,QACxD,MAAM,CAAC,GAAG,mBAAmB,MAAM;AAAA,QACnC,aAAa,CAAC,GAAG,0BAA0B,aAAa;AAAA,QACxD,IAAI;AAAA,QACJ,UAAU,CAAC,GAAG,uBAAuB,UAAU;AAAA,QAC/C,UAAU,CAAC,GAAG,uBAAuB,YAAY,gBAAgB;AAAA,QACjE,iBAAiB,CAAC,GAAG,8BAA8B,mBAAmB,gBAAgB;AAAA,QACtF,gBAAgB,CAAC,GAAG,6BAA6B,gBAAgB;AAAA,QACjE,eAAe,CAAC,GAAG,4BAA4B,eAAe;AAAA,QAC9D,oBAAoB,CAAC,GAAG,iCAAiC,sBAAsB,gBAAgB;AAAA,QAC/F,gBAAgB,CAAC,GAAG,6BAA6B,gBAAgB;AAAA,QACjE,0BAA0B,CAAC,GAAG,+BAA+B,0BAA0B;AAAA,MACzF;AAAA,MACA,SAAS;AAAA,QACP,SAAS;AAAA,QACT,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,QAAQ;AAAA,MACV;AAAA,MACA,UAAU,CAAC,aAAa;AAAA,MACxB,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA;AAAA,QAEjC;AAAA,UACE,SAAS;AAAA,UACT,UAAU;AAAA,QACZ;AAAA,QAAG;AAAA,UACD,SAAS;AAAA,UACT,aAAa;AAAA,QACf;AAAA,MAAC,CAAC,GAAM,wBAAwB;AAAA,IAClC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,WAAW;AAAA;AAAA,QAEX;AAAA,UACE,SAAS;AAAA,UACT,UAAU;AAAA,QACZ;AAAA,QAAG;AAAA,UACD,SAAS;AAAA,UACT,aAAa;AAAA,QACf;AAAA,MAAC;AAAA,MACD,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,aAAa;AAAA,QACb,kCAAkC;AAAA,QAClC,kCAAkC;AAAA,QAClC,mCAAmC;AAAA,MACrC;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAW;AAAA,IACX,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,wBAAwB;AAAA,IACjC,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,wBAAwB;AAAA,IACjC,CAAC;AAAA,IACD,IAAI,CAAC;AAAA,MACH,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,2BAA2B;AAAA,IACpC,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,0BAA0B;AAAA,IACnC,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,2BAA2B;AAAA,IACpC,CAAC;AAAA,IACD,0BAA0B,CAAC;AAAA,MACzB,MAAM;AAAA,MACN,MAAM,CAAC,6BAA6B;AAAA,IACtC,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAOH,IAAM,mBAAmB,IAAI,eAAe,gBAAgB;AAK5D,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,YAAY,aAAa;AACvB,SAAK,cAAc;AACnB,SAAK,QAAQ,OAAO,iBAAiB;AAAA,MACnC,UAAU;AAAA,IACZ,CAAC;AAED,SAAK,YAAY;AACjB,SAAK,OAAO,oBAAoB,IAAI;AAAA,EACtC;AAAA,EACA,cAAc;AACZ,SAAK,OAAO,sBAAsB,IAAI;AAAA,EACxC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,uBAAuB,mBAAmB;AAC7D,aAAO,KAAK,qBAAqB,iBAAmB,kBAAqB,WAAW,CAAC;AAAA,IACvF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,eAAe,kBAAkB,EAAE,CAAC;AAAA,MACjD,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,WAAW,CAAC,GAAG,aAAa,aAAa,gBAAgB;AAAA,MAC3D;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC;AAAA,QAChC,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC,CAAC,GAAM,wBAAwB;AAAA,IAClC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAOH,IAAM,uBAAuB,IAAI,eAAe,oBAAoB;AAKpE,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,YAAY,aAAa;AACvB,SAAK,cAAc;AACnB,SAAK,QAAQ,OAAO,iBAAiB;AAAA,MACnC,UAAU;AAAA,IACZ,CAAC;AACD,SAAK,OAAO,wBAAwB,IAAI;AAAA,EAC1C;AAAA,EACA,cAAc;AACZ,SAAK,OAAO,0BAA0B,IAAI;AAAA,EAC5C;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,2BAA2B,mBAAmB;AACjE,aAAO,KAAK,qBAAqB,qBAAuB,kBAAqB,WAAW,CAAC;AAAA,IAC3F;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,eAAe,sBAAsB,EAAE,CAAC;AAAA,MACrD,QAAQ;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC;AAAA,QAChC,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC,CAAC,CAAC;AAAA,IACL,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,uBAAuB,CAAC,aAAa,kBAAkB,SAAS,eAAe,gBAAgB,kBAAkB;AACvH,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,OAAO;AACL,SAAK,OAAO,SAAS,uBAAuB,mBAAmB;AAC7D,aAAO,KAAK,qBAAqB,iBAAgB;AAAA,IACnD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,aAAa,kBAAkB,SAAS,eAAe,gBAAgB,kBAAkB;AAAA,MACnG,SAAS,CAAC,qBAAqB,aAAa,kBAAkB,SAAS,eAAe,gBAAgB,kBAAkB;AAAA,IAC1H,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,WAAW,CAAC,QAAQ;AAAA,MACpB,SAAS,CAAC,mBAAmB;AAAA,IAC/B,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS;AAAA,MACT,SAAS,CAAC,qBAAqB,GAAG,oBAAoB;AAAA,MACtD,WAAW,CAAC,QAAQ;AAAA,IACtB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["import_rxjs", "import_operators", "import_rxjs", "window", "document", "FocusMonitorDetectionMode", "HighContrastMode", "import_rxjs", "import_operators", "importantProperties", "item", "AutoScrollVerticalDirection", "AutoScrollHorizontalDirection", "isTouchEvent"]}