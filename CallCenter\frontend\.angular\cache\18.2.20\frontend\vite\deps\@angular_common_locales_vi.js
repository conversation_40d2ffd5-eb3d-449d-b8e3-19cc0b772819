import "./chunk-WDMUDEB6.js";

// node_modules/@angular/common/locales/vi.mjs
var u = void 0;
function plural(val) {
  const n = val;
  return 5;
}
var vi_default = ["vi", [["s", "c"], ["SA", "CH"], u], [["SA", "CH"], u, u], [["CN", "T2", "T3", "T4", "T5", "T6", "T7"], ["CN", "Th 2", "Th 3", "Th 4", "Th 5", "Th 6", "Th 7"], ["<PERSON><PERSON> Nhậ<PERSON>", "<PERSON><PERSON><PERSON>", "T<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Th<PERSON>"], ["CN", "T2", "T3", "T4", "T5", "T6", "T7"]], u, [["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12"], ["thg 1", "thg 2", "thg 3", "thg 4", "thg 5", "thg 6", "thg 7", "thg 8", "thg 9", "thg 10", "thg 11", "thg 12"], ["tháng 1", "tháng 2", "tháng 3", "tháng 4", "tháng 5", "tháng 6", "tháng 7", "tháng 8", "tháng 9", "tháng 10", "tháng 11", "tháng 12"]], [["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12"], ["Thg 1", "Thg 2", "Thg 3", "Thg 4", "Thg 5", "Thg 6", "Thg 7", "Thg 8", "Thg 9", "Thg 10", "Thg 11", "Thg 12"], ["Tháng 1", "Tháng 2", "Tháng 3", "Tháng 4", "Tháng 5", "Tháng 6", "Tháng 7", "Tháng 8", "Tháng 9", "Tháng 10", "Tháng 11", "Tháng 12"]], [["tr. CN", "sau CN"], ["Trước CN", "Sau CN"], ["Trước Thiên Chúa", "Sau Công Nguyên"]], 1, [6, 0], ["dd/MM/y", "d MMM, y", "d MMMM, y", "EEEE, d MMMM, y"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{0}, {1}", u, "{0} {1}", u], [",", ".", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "#,##0.00 ¤", "#E0"], "VND", "₫", "Đồng Việt Nam", {
  "AUD": ["AU$", "$"],
  "BYN": [u, "р."],
  "PHP": [u, "₱"],
  "THB": ["฿"],
  "TWD": ["NT$"],
  "USD": ["US$", "$"],
  "XXX": []
}, "ltr", plural];
export {
  vi_default as default
};
/*! Bundled license information:

@angular/common/locales/vi.mjs:
  (**
   * @license
   * Copyright Google LLC All Rights Reserved.
   *
   * Use of this source code is governed by an MIT-style license that can be
   * found in the LICENSE file at https://angular.dev/license
   *)
*/
//# sourceMappingURL=@angular_common_locales_vi.js.map
