import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { Call, CallFilter, CallListResponse } from '../../shared/models/call.interface';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class CallService {
  private readonly apiUrl = `${environment.apiUrl}/calls`;
  private callsSubject = new BehaviorSubject<Call[]>([]);
  public calls$ = this.callsSubject.asObservable();

  constructor(private http: HttpClient) {}

  /**
   * Get paginated list of calls with optional filtering
   */
  getCalls(page: number = 1, pageSize: number = 20, filter?: CallFilter): Observable<CallListResponse> {
    let params = new HttpParams()
      .set('page', page.toString())
      .set('pageSize', pageSize.toString());

    if (filter) {
      if (filter.dateFrom) {
        params = params.set('dateFrom', filter.dateFrom.toISOString());
      }
      if (filter.dateTo) {
        params = params.set('dateTo', filter.dateTo.toISOString());
      }
      if (filter.agentIds?.length) {
        params = params.set('agentIds', filter.agentIds.join(','));
      }
      if (filter.status?.length) {
        params = params.set('status', filter.status.join(','));
      }
      if (filter.direction?.length) {
        params = params.set('direction', filter.direction.join(','));
      }
      if (filter.qaStatus?.length) {
        params = params.set('qaStatus', filter.qaStatus.join(','));
      }
      if (filter.minDuration !== undefined) {
        params = params.set('minDuration', filter.minDuration.toString());
      }
      if (filter.maxDuration !== undefined) {
        params = params.set('maxDuration', filter.maxDuration.toString());
      }
      if (filter.searchTerm) {
        params = params.set('searchTerm', filter.searchTerm);
      }
    }

    return this.http.get<CallListResponse>(this.apiUrl, { params }).pipe(
      map(response => ({
        ...response,
        calls: response.calls.map(call => ({
          ...call,
          startTime: new Date(call.startTime),
          endTime: new Date(call.endTime),
          createdAt: new Date(call.createdAt),
          updatedAt: new Date(call.updatedAt)
        }))
      })),
      catchError(this.handleError)
    );
  }

  /**
   * Get a single call by ID
   */
  getCallById(id: string): Observable<Call> {
    return this.http.get<Call>(`${this.apiUrl}/${id}`).pipe(
      map(call => ({
        ...call,
        startTime: new Date(call.startTime),
        endTime: new Date(call.endTime),
        createdAt: new Date(call.createdAt),
        updatedAt: new Date(call.updatedAt)
      })),
      catchError(this.handleError)
    );
  }

  /**
   * Update call notes
   */
  updateCallNotes(id: string, notes: string): Observable<Call> {
    return this.http.patch<Call>(`${this.apiUrl}/${id}/notes`, { notes }).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Add tags to a call
   */
  addCallTags(id: string, tags: string[]): Observable<Call> {
    return this.http.patch<Call>(`${this.apiUrl}/${id}/tags`, { tags }).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Get call recording URL
   */
  getCallRecording(id: string): Observable<string> {
    return this.http.get<{ url: string }>(`${this.apiUrl}/${id}/recording`).pipe(
      map(response => response.url),
      catchError(this.handleError)
    );
  }

  /**
   * Get call transcript
   */
  getCallTranscript(id: string): Observable<any> {
    return this.http.get(`${this.apiUrl}/${id}/transcript`).pipe(
      catchError(this.handleError)
    );
  }

  private handleError = (error: any): Observable<never> => {
    console.error('CallService Error:', error);
    throw error;
  };
}
