import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { QaAssessmentComponent } from './qa-assessment/qa-assessment.component';
import { QaProcedureManagementComponent } from './qa-procedure-management/qa-procedure-management.component';

const routes: Routes = [
  {
    path: 'assessment',
    component: QaAssessmentComponent
  },
  {
    path: 'procedure',
    component: QaProcedureManagementComponent
  },
  {
    path: '',
    redirectTo: 'assessment',
    pathMatch: 'full'
  }
];

@NgModule({
  declarations: [],
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    QaAssessmentComponent,
    QaProcedureManagementComponent
  ],
  exports: [RouterModule]
})
export class QaModule { }
