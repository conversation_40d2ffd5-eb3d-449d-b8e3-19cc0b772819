{"version": 3, "sources": ["../../../../../../node_modules/tds-ui/fesm2022/tds-ui-shared-utility.mjs"], "sourcesContent": ["import { coerceBooleanProperty, _isNumberValue, coerceCssPixelValue } from '@angular/cdk/coercion';\nimport { Subject } from 'rxjs';\nimport { take } from 'rxjs/operators';\n\n// @dynamic\nclass TDSHelperArray {\n  static joinNumber(arr) {\n    if (this.hasListValue(arr)) {\n      return arr.join(\",\");\n    }\n    return \"\";\n  }\n  static joinString(arr) {\n    if (this.hasListValue(arr)) {\n      return arr.join(\"],[\");\n    }\n    return \"\";\n  }\n  static hasListValue(value) {\n    return !(value === undefined || value === null || value.length == 0);\n  }\n  /**\n   * Kiểm tra a là array\n   * @param a\n   * @returns\n   */\n  static isArray(a) {\n    a = Object.prototype.toString.call(a);\n    return \"[object Array]\" === a || \"[object Array Iterator]\" === a;\n  }\n  /**\n  * tìm min của mảng a\n  * @param a\n  * @returns\n  */\n  static arrayMin(a) {\n    for (var c = a.length, k = a[0]; c--;) a[c] < k && (k = a[c]);\n    return k;\n  }\n  /**\n   * tìm max của mảng a\n   * @param a\n   * @returns\n   */\n  static arrayMax(a) {\n    for (var c = a.length, k = a[0]; c--;) a[c] > k && (k = a[c]);\n    return k;\n  }\n  /**\n   * gộp mảng 2 vào mảng 1\n   * @param array1\n   * @param array2\n   */\n  static concat(array1, array2) {\n    array1.push.apply(array1, array2);\n  }\n  //   var exampleData = [\n  //     { position: 1, name: 'Hydrogen', weight: 1.0079, symbol: 'H' },\n  //     { position: 1, name: 'Helium', weight: 4.0026, symbol: 'H' },\n  //     { position: 2, name: 'Lithium', weight: 6.941, symbol: 'L' },    //    \n  //   ]\n  //   // expect \n  //   // key = ['symbol']\n  //   var TREE1 = [\n  //     {\n  //       symbol: 'H',\n  //       children: [\n  //         { position: 1, name: 'Hydrogen', weight: 1.0079, symbol: 'H' },\n  //         { position: 1, name: 'Helium', weight: 4.0026, symbol: 'H' },\n  //       ]\n  //     },\n  //     {\n  //       symbol: 'L',\n  //       children: [\n  //         { position: 2, name: 'Lithium', weight: 6.941, symbol: 'L' },\n  //       ]\n  //     },\n  //   ]\n  //   // OR -------------------------------------------------------------------------\n  //   // key = ['symbol', 'position']\n  //   var TREE2 = [ \n  //     {\n  //       symbol: 'H',\n  //       children: [\n  //         {\n  //           position: 1,\n  //           children: [\n  //             { position: 1, name: 'Hydrogen', weight: 1.0079, symbol: 'H' },\n  //             { position: 1, name: 'Helium', weight: 4.0026, symbol: 'H' },\n  //           ]\n  //         }\n  //       ]\n  //     },\n  //     {\n  //       symbol: 'N',\n  //       children: [\n  //         {\n  //           position: 5,\n  //           children: [{ position: 5, name: 'Nitrogen', weight: 14.0067, symbol: 'N' }]\n  //         },\n  //         {\n  //           position: 8,\n  //           children: [{ position: 8, name: 'Neon', weight: 20.1797, symbol: 'N' }]\n  //         }\n  //       ]\n  //     },\n  //   ]\n  /**\n   *\n   * @param items mảng đối tượng\n   * @param keys các  fields cần groupby\n   * @returns [[field groupby]:'',children:[]]\n   */\n  static groupByKey(items, ...keys) {\n    if (!this.hasListValue(keys)) {\n      return items;\n    }\n    const result = [];\n    const keySet = new Set();\n    const groupByKey = keys.shift();\n    items.forEach(item => {\n      if (!keySet.has(item[groupByKey])) {\n        keySet.add(item[groupByKey]);\n        result.push({\n          [groupByKey]: item[groupByKey],\n          children: [item]\n        });\n      } else {\n        result.find(res => res[groupByKey] === item[groupByKey]).children.push(item);\n      }\n    });\n    if (keys.length) {\n      result.forEach(res => {\n        res.children = this.groupByKey(res.children, ...keys);\n      });\n    }\n    return result;\n  }\n}\n\n// @dynamic\nclass TDSHelperObject {\n  /**\n    * Kiểm tra đối tượng có giá trị hay ko\n    * @param value\n    *\n    */\n  static hasValue(value) {\n    return !(value === undefined || value === null);\n  }\n  /**\n  * sao chép đối tượng\n  * @param obj\n  *\n  */\n  static cloneObject(obj) {\n    if (this.hasValue(obj)) {\n      return JSON.parse(JSON.stringify(obj));\n    } else {\n      return obj;\n    }\n  }\n  static copyObject(taget, source) {\n    taget = Object.assign(taget, source);\n  }\n  static isObject(k, c) {\n    return !!k && \"object\" === typeof k && (!c || !TDSHelperArray.isArray(k));\n  }\n  /** Coerces a data-bound value (typically a string) to a boolean. */\n  static coerceBooleanProperty(value) {\n    return value != null && `${value}` !== 'false';\n  }\n  /**\n   * kiểm tra đối tượng là function\n   * @param fun\n   * return true/false\n   */\n  static isFunction(fun) {\n    return typeof fun === 'function';\n  }\n  /**\n   * lấy giá trị theo Property của Obj\n   * @param obj : object\n   * @param field : tên property của obj\n   * @param separator : \".\", dựa vào separator để tách chuỗi thành mảng các phần tử con, Ví dụ: propa.propb hoặc propA#propB\n   * return undefined hoặc giá trị của property\n   */\n  static getValueByField(obj, field, separator = \".\") {\n    if (!this.hasValue(obj)) {\n      return undefined;\n    }\n    const keys = field.split(separator); // Chia chuỗi thành mảng các phần tử con\n    let result = obj;\n    for (const key of keys) {\n      if (result.hasOwnProperty(key)) {\n        result = result[key];\n      } else {\n        // Trả về undefined nếu không tìm thấy thuộc tính\n        return undefined;\n      }\n    }\n    return result;\n  }\n  static isEmpty(obj) {\n    return !TDSHelperObject.hasValue(obj) || Object.keys(obj).length === 0;\n  }\n}\n\n// @dynamic\nclass TDSHelperString {\n  static hasValueString(value) {\n    return !(value === undefined || value === null || value === \"\");\n  }\n  static guid() {\n    let str = \"\";\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n      return ((1 + Math.random()) * 0x10000 | 0).toString(16).substring(1);\n    });\n  }\n  static gen(count) {\n    let out = \"\";\n    for (let i = 0; i < count; i++) {\n      out += ((1 + Math.random()) * 0x10000 | 0).toString(16).substring(1);\n    }\n    return out;\n  }\n  static genid() {\n    return this.guid().replace(/-/g, \"\");\n  }\n  static isString(str) {\n    return \"string\" === typeof str;\n  }\n  /**\n   * replace chuỗi\n   * @param str\n   * @param searchValue chuỗi cần replace\n   * @param replaceValue  chuỗi replace\n   * @returns\n   */\n  static replaceAll(str, searchValue, replaceValue) {\n    if (this.hasValueString(str)) {\n      if (str.indexOf(searchValue) > -1) {\n        return str.split(searchValue).join(replaceValue);\n      }\n    }\n    return str;\n  }\n  /**\n   * Kiểm tra chuỗi chứa tất cả các kí tự là khoảng trắng (space)\n   * @param value\n   * @returns trả về true nếu tất cả kí tự KHÔNG là khoảng trắng (space)\n   */\n  static checkStringAllSpace(value) {\n    return this.hasValueString(value.trim());\n  }\n  static getDiacritics() {\n    return {\n      '\\u24B6': 'A',\n      '\\uFF21': 'A',\n      '\\u00C0': 'A',\n      '\\u00C1': 'A',\n      '\\u00C2': 'A',\n      '\\u1EA6': 'A',\n      '\\u1EA4': 'A',\n      '\\u1EAA': 'A',\n      '\\u1EA8': 'A',\n      '\\u00C3': 'A',\n      '\\u0100': 'A',\n      '\\u0102': 'A',\n      '\\u1EB0': 'A',\n      '\\u1EAE': 'A',\n      '\\u1EB4': 'A',\n      '\\u1EB2': 'A',\n      '\\u0226': 'A',\n      '\\u01E0': 'A',\n      '\\u00C4': 'A',\n      '\\u01DE': 'A',\n      '\\u1EA2': 'A',\n      '\\u00C5': 'A',\n      '\\u01FA': 'A',\n      '\\u01CD': 'A',\n      '\\u0200': 'A',\n      '\\u0202': 'A',\n      '\\u1EA0': 'A',\n      '\\u1EAC': 'A',\n      '\\u1EB6': 'A',\n      '\\u1E00': 'A',\n      '\\u0104': 'A',\n      '\\u023A': 'A',\n      '\\u2C6F': 'A',\n      '\\uA732': 'AA',\n      '\\u00C6': 'AE',\n      '\\u01FC': 'AE',\n      '\\u01E2': 'AE',\n      '\\uA734': 'AO',\n      '\\uA736': 'AU',\n      '\\uA738': 'AV',\n      '\\uA73A': 'AV',\n      '\\uA73C': 'AY',\n      '\\u24B7': 'B',\n      '\\uFF22': 'B',\n      '\\u1E02': 'B',\n      '\\u1E04': 'B',\n      '\\u1E06': 'B',\n      '\\u0243': 'B',\n      '\\u0182': 'B',\n      '\\u0181': 'B',\n      '\\u24B8': 'C',\n      '\\uFF23': 'C',\n      '\\u0106': 'C',\n      '\\u0108': 'C',\n      '\\u010A': 'C',\n      '\\u010C': 'C',\n      '\\u00C7': 'C',\n      '\\u1E08': 'C',\n      '\\u0187': 'C',\n      '\\u023B': 'C',\n      '\\uA73E': 'C',\n      '\\u24B9': 'D',\n      '\\uFF24': 'D',\n      '\\u1E0A': 'D',\n      '\\u010E': 'D',\n      '\\u1E0C': 'D',\n      '\\u1E10': 'D',\n      '\\u1E12': 'D',\n      '\\u1E0E': 'D',\n      '\\u0110': 'D',\n      '\\u018B': 'D',\n      '\\u018A': 'D',\n      '\\u0189': 'D',\n      '\\uA779': 'D',\n      '\\u01F1': 'DZ',\n      '\\u01C4': 'DZ',\n      '\\u01F2': 'Dz',\n      '\\u01C5': 'Dz',\n      '\\u24BA': 'E',\n      '\\uFF25': 'E',\n      '\\u00C8': 'E',\n      '\\u00C9': 'E',\n      '\\u00CA': 'E',\n      '\\u1EC0': 'E',\n      '\\u1EBE': 'E',\n      '\\u1EC4': 'E',\n      '\\u1EC2': 'E',\n      '\\u1EBC': 'E',\n      '\\u0112': 'E',\n      '\\u1E14': 'E',\n      '\\u1E16': 'E',\n      '\\u0114': 'E',\n      '\\u0116': 'E',\n      '\\u00CB': 'E',\n      '\\u1EBA': 'E',\n      '\\u011A': 'E',\n      '\\u0204': 'E',\n      '\\u0206': 'E',\n      '\\u1EB8': 'E',\n      '\\u1EC6': 'E',\n      '\\u0228': 'E',\n      '\\u1E1C': 'E',\n      '\\u0118': 'E',\n      '\\u1E18': 'E',\n      '\\u1E1A': 'E',\n      '\\u0190': 'E',\n      '\\u018E': 'E',\n      '\\u24BB': 'F',\n      '\\uFF26': 'F',\n      '\\u1E1E': 'F',\n      '\\u0191': 'F',\n      '\\uA77B': 'F',\n      '\\u24BC': 'G',\n      '\\uFF27': 'G',\n      '\\u01F4': 'G',\n      '\\u011C': 'G',\n      '\\u1E20': 'G',\n      '\\u011E': 'G',\n      '\\u0120': 'G',\n      '\\u01E6': 'G',\n      '\\u0122': 'G',\n      '\\u01E4': 'G',\n      '\\u0193': 'G',\n      '\\uA7A0': 'G',\n      '\\uA77D': 'G',\n      '\\uA77E': 'G',\n      '\\u24BD': 'H',\n      '\\uFF28': 'H',\n      '\\u0124': 'H',\n      '\\u1E22': 'H',\n      '\\u1E26': 'H',\n      '\\u021E': 'H',\n      '\\u1E24': 'H',\n      '\\u1E28': 'H',\n      '\\u1E2A': 'H',\n      '\\u0126': 'H',\n      '\\u2C67': 'H',\n      '\\u2C75': 'H',\n      '\\uA78D': 'H',\n      '\\u24BE': 'I',\n      '\\uFF29': 'I',\n      '\\u00CC': 'I',\n      '\\u00CD': 'I',\n      '\\u00CE': 'I',\n      '\\u0128': 'I',\n      '\\u012A': 'I',\n      '\\u012C': 'I',\n      '\\u0130': 'I',\n      '\\u00CF': 'I',\n      '\\u1E2E': 'I',\n      '\\u1EC8': 'I',\n      '\\u01CF': 'I',\n      '\\u0208': 'I',\n      '\\u020A': 'I',\n      '\\u1ECA': 'I',\n      '\\u012E': 'I',\n      '\\u1E2C': 'I',\n      '\\u0197': 'I',\n      '\\u24BF': 'J',\n      '\\uFF2A': 'J',\n      '\\u0134': 'J',\n      '\\u0248': 'J',\n      '\\u24C0': 'K',\n      '\\uFF2B': 'K',\n      '\\u1E30': 'K',\n      '\\u01E8': 'K',\n      '\\u1E32': 'K',\n      '\\u0136': 'K',\n      '\\u1E34': 'K',\n      '\\u0198': 'K',\n      '\\u2C69': 'K',\n      '\\uA740': 'K',\n      '\\uA742': 'K',\n      '\\uA744': 'K',\n      '\\uA7A2': 'K',\n      '\\u24C1': 'L',\n      '\\uFF2C': 'L',\n      '\\u013F': 'L',\n      '\\u0139': 'L',\n      '\\u013D': 'L',\n      '\\u1E36': 'L',\n      '\\u1E38': 'L',\n      '\\u013B': 'L',\n      '\\u1E3C': 'L',\n      '\\u1E3A': 'L',\n      '\\u0141': 'L',\n      '\\u023D': 'L',\n      '\\u2C62': 'L',\n      '\\u2C60': 'L',\n      '\\uA748': 'L',\n      '\\uA746': 'L',\n      '\\uA780': 'L',\n      '\\u01C7': 'LJ',\n      '\\u01C8': 'Lj',\n      '\\u24C2': 'M',\n      '\\uFF2D': 'M',\n      '\\u1E3E': 'M',\n      '\\u1E40': 'M',\n      '\\u1E42': 'M',\n      '\\u2C6E': 'M',\n      '\\u019C': 'M',\n      '\\u24C3': 'N',\n      '\\uFF2E': 'N',\n      '\\u01F8': 'N',\n      '\\u0143': 'N',\n      '\\u00D1': 'N',\n      '\\u1E44': 'N',\n      '\\u0147': 'N',\n      '\\u1E46': 'N',\n      '\\u0145': 'N',\n      '\\u1E4A': 'N',\n      '\\u1E48': 'N',\n      '\\u0220': 'N',\n      '\\u019D': 'N',\n      '\\uA790': 'N',\n      '\\uA7A4': 'N',\n      '\\u01CA': 'NJ',\n      '\\u01CB': 'Nj',\n      '\\u24C4': 'O',\n      '\\uFF2F': 'O',\n      '\\u00D2': 'O',\n      '\\u00D3': 'O',\n      '\\u00D4': 'O',\n      '\\u1ED2': 'O',\n      '\\u1ED0': 'O',\n      '\\u1ED6': 'O',\n      '\\u1ED4': 'O',\n      '\\u00D5': 'O',\n      '\\u1E4C': 'O',\n      '\\u022C': 'O',\n      '\\u1E4E': 'O',\n      '\\u014C': 'O',\n      '\\u1E50': 'O',\n      '\\u1E52': 'O',\n      '\\u014E': 'O',\n      '\\u022E': 'O',\n      '\\u0230': 'O',\n      '\\u00D6': 'O',\n      '\\u022A': 'O',\n      '\\u1ECE': 'O',\n      '\\u0150': 'O',\n      '\\u01D1': 'O',\n      '\\u020C': 'O',\n      '\\u020E': 'O',\n      '\\u01A0': 'O',\n      '\\u1EDC': 'O',\n      '\\u1EDA': 'O',\n      '\\u1EE0': 'O',\n      '\\u1EDE': 'O',\n      '\\u1EE2': 'O',\n      '\\u1ECC': 'O',\n      '\\u1ED8': 'O',\n      '\\u01EA': 'O',\n      '\\u01EC': 'O',\n      '\\u00D8': 'O',\n      '\\u01FE': 'O',\n      '\\u0186': 'O',\n      '\\u019F': 'O',\n      '\\uA74A': 'O',\n      '\\uA74C': 'O',\n      '\\u01A2': 'OI',\n      '\\uA74E': 'OO',\n      '\\u0222': 'OU',\n      '\\u24C5': 'P',\n      '\\uFF30': 'P',\n      '\\u1E54': 'P',\n      '\\u1E56': 'P',\n      '\\u01A4': 'P',\n      '\\u2C63': 'P',\n      '\\uA750': 'P',\n      '\\uA752': 'P',\n      '\\uA754': 'P',\n      '\\u24C6': 'Q',\n      '\\uFF31': 'Q',\n      '\\uA756': 'Q',\n      '\\uA758': 'Q',\n      '\\u024A': 'Q',\n      '\\u24C7': 'R',\n      '\\uFF32': 'R',\n      '\\u0154': 'R',\n      '\\u1E58': 'R',\n      '\\u0158': 'R',\n      '\\u0210': 'R',\n      '\\u0212': 'R',\n      '\\u1E5A': 'R',\n      '\\u1E5C': 'R',\n      '\\u0156': 'R',\n      '\\u1E5E': 'R',\n      '\\u024C': 'R',\n      '\\u2C64': 'R',\n      '\\uA75A': 'R',\n      '\\uA7A6': 'R',\n      '\\uA782': 'R',\n      '\\u24C8': 'S',\n      '\\uFF33': 'S',\n      '\\u1E9E': 'S',\n      '\\u015A': 'S',\n      '\\u1E64': 'S',\n      '\\u015C': 'S',\n      '\\u1E60': 'S',\n      '\\u0160': 'S',\n      '\\u1E66': 'S',\n      '\\u1E62': 'S',\n      '\\u1E68': 'S',\n      '\\u0218': 'S',\n      '\\u015E': 'S',\n      '\\u2C7E': 'S',\n      '\\uA7A8': 'S',\n      '\\uA784': 'S',\n      '\\u24C9': 'T',\n      '\\uFF34': 'T',\n      '\\u1E6A': 'T',\n      '\\u0164': 'T',\n      '\\u1E6C': 'T',\n      '\\u021A': 'T',\n      '\\u0162': 'T',\n      '\\u1E70': 'T',\n      '\\u1E6E': 'T',\n      '\\u0166': 'T',\n      '\\u01AC': 'T',\n      '\\u01AE': 'T',\n      '\\u023E': 'T',\n      '\\uA786': 'T',\n      '\\uA728': 'TZ',\n      '\\u24CA': 'U',\n      '\\uFF35': 'U',\n      '\\u00D9': 'U',\n      '\\u00DA': 'U',\n      '\\u00DB': 'U',\n      '\\u0168': 'U',\n      '\\u1E78': 'U',\n      '\\u016A': 'U',\n      '\\u1E7A': 'U',\n      '\\u016C': 'U',\n      '\\u00DC': 'U',\n      '\\u01DB': 'U',\n      '\\u01D7': 'U',\n      '\\u01D5': 'U',\n      '\\u01D9': 'U',\n      '\\u1EE6': 'U',\n      '\\u016E': 'U',\n      '\\u0170': 'U',\n      '\\u01D3': 'U',\n      '\\u0214': 'U',\n      '\\u0216': 'U',\n      '\\u01AF': 'U',\n      '\\u1EEA': 'U',\n      '\\u1EE8': 'U',\n      '\\u1EEE': 'U',\n      '\\u1EEC': 'U',\n      '\\u1EF0': 'U',\n      '\\u1EE4': 'U',\n      '\\u1E72': 'U',\n      '\\u0172': 'U',\n      '\\u1E76': 'U',\n      '\\u1E74': 'U',\n      '\\u0244': 'U',\n      '\\u24CB': 'V',\n      '\\uFF36': 'V',\n      '\\u1E7C': 'V',\n      '\\u1E7E': 'V',\n      '\\u01B2': 'V',\n      '\\uA75E': 'V',\n      '\\u0245': 'V',\n      '\\uA760': 'VY',\n      '\\u24CC': 'W',\n      '\\uFF37': 'W',\n      '\\u1E80': 'W',\n      '\\u1E82': 'W',\n      '\\u0174': 'W',\n      '\\u1E86': 'W',\n      '\\u1E84': 'W',\n      '\\u1E88': 'W',\n      '\\u2C72': 'W',\n      '\\u24CD': 'X',\n      '\\uFF38': 'X',\n      '\\u1E8A': 'X',\n      '\\u1E8C': 'X',\n      '\\u24CE': 'Y',\n      '\\uFF39': 'Y',\n      '\\u1EF2': 'Y',\n      '\\u00DD': 'Y',\n      '\\u0176': 'Y',\n      '\\u1EF8': 'Y',\n      '\\u0232': 'Y',\n      '\\u1E8E': 'Y',\n      '\\u0178': 'Y',\n      '\\u1EF6': 'Y',\n      '\\u1EF4': 'Y',\n      '\\u01B3': 'Y',\n      '\\u024E': 'Y',\n      '\\u1EFE': 'Y',\n      '\\u24CF': 'Z',\n      '\\uFF3A': 'Z',\n      '\\u0179': 'Z',\n      '\\u1E90': 'Z',\n      '\\u017B': 'Z',\n      '\\u017D': 'Z',\n      '\\u1E92': 'Z',\n      '\\u1E94': 'Z',\n      '\\u01B5': 'Z',\n      '\\u0224': 'Z',\n      '\\u2C7F': 'Z',\n      '\\u2C6B': 'Z',\n      '\\uA762': 'Z',\n      '\\u24D0': 'a',\n      '\\uFF41': 'a',\n      '\\u1E9A': 'a',\n      '\\u00E0': 'a',\n      '\\u00E1': 'a',\n      '\\u00E2': 'a',\n      '\\u1EA7': 'a',\n      '\\u1EA5': 'a',\n      '\\u1EAB': 'a',\n      '\\u1EA9': 'a',\n      '\\u00E3': 'a',\n      '\\u0101': 'a',\n      '\\u0103': 'a',\n      '\\u1EB1': 'a',\n      '\\u1EAF': 'a',\n      '\\u1EB5': 'a',\n      '\\u1EB3': 'a',\n      '\\u0227': 'a',\n      '\\u01E1': 'a',\n      '\\u00E4': 'a',\n      '\\u01DF': 'a',\n      '\\u1EA3': 'a',\n      '\\u00E5': 'a',\n      '\\u01FB': 'a',\n      '\\u01CE': 'a',\n      '\\u0201': 'a',\n      '\\u0203': 'a',\n      '\\u1EA1': 'a',\n      '\\u1EAD': 'a',\n      '\\u1EB7': 'a',\n      '\\u1E01': 'a',\n      '\\u0105': 'a',\n      '\\u2C65': 'a',\n      '\\u0250': 'a',\n      '\\uA733': 'aa',\n      '\\u00E6': 'ae',\n      '\\u01FD': 'ae',\n      '\\u01E3': 'ae',\n      '\\uA735': 'ao',\n      '\\uA737': 'au',\n      '\\uA739': 'av',\n      '\\uA73B': 'av',\n      '\\uA73D': 'ay',\n      '\\u24D1': 'b',\n      '\\uFF42': 'b',\n      '\\u1E03': 'b',\n      '\\u1E05': 'b',\n      '\\u1E07': 'b',\n      '\\u0180': 'b',\n      '\\u0183': 'b',\n      '\\u0253': 'b',\n      '\\u24D2': 'c',\n      '\\uFF43': 'c',\n      '\\u0107': 'c',\n      '\\u0109': 'c',\n      '\\u010B': 'c',\n      '\\u010D': 'c',\n      '\\u00E7': 'c',\n      '\\u1E09': 'c',\n      '\\u0188': 'c',\n      '\\u023C': 'c',\n      '\\uA73F': 'c',\n      '\\u2184': 'c',\n      '\\u24D3': 'd',\n      '\\uFF44': 'd',\n      '\\u1E0B': 'd',\n      '\\u010F': 'd',\n      '\\u1E0D': 'd',\n      '\\u1E11': 'd',\n      '\\u1E13': 'd',\n      '\\u1E0F': 'd',\n      '\\u0111': 'd',\n      '\\u018C': 'd',\n      '\\u0256': 'd',\n      '\\u0257': 'd',\n      '\\uA77A': 'd',\n      '\\u01F3': 'dz',\n      '\\u01C6': 'dz',\n      '\\u24D4': 'e',\n      '\\uFF45': 'e',\n      '\\u00E8': 'e',\n      '\\u00E9': 'e',\n      '\\u00EA': 'e',\n      '\\u1EC1': 'e',\n      '\\u1EBF': 'e',\n      '\\u1EC5': 'e',\n      '\\u1EC3': 'e',\n      '\\u1EBD': 'e',\n      '\\u0113': 'e',\n      '\\u1E15': 'e',\n      '\\u1E17': 'e',\n      '\\u0115': 'e',\n      '\\u0117': 'e',\n      '\\u00EB': 'e',\n      '\\u1EBB': 'e',\n      '\\u011B': 'e',\n      '\\u0205': 'e',\n      '\\u0207': 'e',\n      '\\u1EB9': 'e',\n      '\\u1EC7': 'e',\n      '\\u0229': 'e',\n      '\\u1E1D': 'e',\n      '\\u0119': 'e',\n      '\\u1E19': 'e',\n      '\\u1E1B': 'e',\n      '\\u0247': 'e',\n      '\\u025B': 'e',\n      '\\u01DD': 'e',\n      '\\u24D5': 'f',\n      '\\uFF46': 'f',\n      '\\u1E1F': 'f',\n      '\\u0192': 'f',\n      '\\uA77C': 'f',\n      '\\u24D6': 'g',\n      '\\uFF47': 'g',\n      '\\u01F5': 'g',\n      '\\u011D': 'g',\n      '\\u1E21': 'g',\n      '\\u011F': 'g',\n      '\\u0121': 'g',\n      '\\u01E7': 'g',\n      '\\u0123': 'g',\n      '\\u01E5': 'g',\n      '\\u0260': 'g',\n      '\\uA7A1': 'g',\n      '\\u1D79': 'g',\n      '\\uA77F': 'g',\n      '\\u24D7': 'h',\n      '\\uFF48': 'h',\n      '\\u0125': 'h',\n      '\\u1E23': 'h',\n      '\\u1E27': 'h',\n      '\\u021F': 'h',\n      '\\u1E25': 'h',\n      '\\u1E29': 'h',\n      '\\u1E2B': 'h',\n      '\\u1E96': 'h',\n      '\\u0127': 'h',\n      '\\u2C68': 'h',\n      '\\u2C76': 'h',\n      '\\u0265': 'h',\n      '\\u0195': 'hv',\n      '\\u24D8': 'i',\n      '\\uFF49': 'i',\n      '\\u00EC': 'i',\n      '\\u00ED': 'i',\n      '\\u00EE': 'i',\n      '\\u0129': 'i',\n      '\\u012B': 'i',\n      '\\u012D': 'i',\n      '\\u00EF': 'i',\n      '\\u1E2F': 'i',\n      '\\u1EC9': 'i',\n      '\\u01D0': 'i',\n      '\\u0209': 'i',\n      '\\u020B': 'i',\n      '\\u1ECB': 'i',\n      '\\u012F': 'i',\n      '\\u1E2D': 'i',\n      '\\u0268': 'i',\n      '\\u0131': 'i',\n      '\\u24D9': 'j',\n      '\\uFF4A': 'j',\n      '\\u0135': 'j',\n      '\\u01F0': 'j',\n      '\\u0249': 'j',\n      '\\u24DA': 'k',\n      '\\uFF4B': 'k',\n      '\\u1E31': 'k',\n      '\\u01E9': 'k',\n      '\\u1E33': 'k',\n      '\\u0137': 'k',\n      '\\u1E35': 'k',\n      '\\u0199': 'k',\n      '\\u2C6A': 'k',\n      '\\uA741': 'k',\n      '\\uA743': 'k',\n      '\\uA745': 'k',\n      '\\uA7A3': 'k',\n      '\\u24DB': 'l',\n      '\\uFF4C': 'l',\n      '\\u0140': 'l',\n      '\\u013A': 'l',\n      '\\u013E': 'l',\n      '\\u1E37': 'l',\n      '\\u1E39': 'l',\n      '\\u013C': 'l',\n      '\\u1E3D': 'l',\n      '\\u1E3B': 'l',\n      '\\u017F': 'l',\n      '\\u0142': 'l',\n      '\\u019A': 'l',\n      '\\u026B': 'l',\n      '\\u2C61': 'l',\n      '\\uA749': 'l',\n      '\\uA781': 'l',\n      '\\uA747': 'l',\n      '\\u01C9': 'lj',\n      '\\u24DC': 'm',\n      '\\uFF4D': 'm',\n      '\\u1E3F': 'm',\n      '\\u1E41': 'm',\n      '\\u1E43': 'm',\n      '\\u0271': 'm',\n      '\\u026F': 'm',\n      '\\u24DD': 'n',\n      '\\uFF4E': 'n',\n      '\\u01F9': 'n',\n      '\\u0144': 'n',\n      '\\u00F1': 'n',\n      '\\u1E45': 'n',\n      '\\u0148': 'n',\n      '\\u1E47': 'n',\n      '\\u0146': 'n',\n      '\\u1E4B': 'n',\n      '\\u1E49': 'n',\n      '\\u019E': 'n',\n      '\\u0272': 'n',\n      '\\u0149': 'n',\n      '\\uA791': 'n',\n      '\\uA7A5': 'n',\n      '\\u01CC': 'nj',\n      '\\u24DE': 'o',\n      '\\uFF4F': 'o',\n      '\\u00F2': 'o',\n      '\\u00F3': 'o',\n      '\\u00F4': 'o',\n      '\\u1ED3': 'o',\n      '\\u1ED1': 'o',\n      '\\u1ED7': 'o',\n      '\\u1ED5': 'o',\n      '\\u00F5': 'o',\n      '\\u1E4D': 'o',\n      '\\u022D': 'o',\n      '\\u1E4F': 'o',\n      '\\u014D': 'o',\n      '\\u1E51': 'o',\n      '\\u1E53': 'o',\n      '\\u014F': 'o',\n      '\\u022F': 'o',\n      '\\u0231': 'o',\n      '\\u00F6': 'o',\n      '\\u022B': 'o',\n      '\\u1ECF': 'o',\n      '\\u0151': 'o',\n      '\\u01D2': 'o',\n      '\\u020D': 'o',\n      '\\u020F': 'o',\n      '\\u01A1': 'o',\n      '\\u1EDD': 'o',\n      '\\u1EDB': 'o',\n      '\\u1EE1': 'o',\n      '\\u1EDF': 'o',\n      '\\u1EE3': 'o',\n      '\\u1ECD': 'o',\n      '\\u1ED9': 'o',\n      '\\u01EB': 'o',\n      '\\u01ED': 'o',\n      '\\u00F8': 'o',\n      '\\u01FF': 'o',\n      '\\u0254': 'o',\n      '\\uA74B': 'o',\n      '\\uA74D': 'o',\n      '\\u0275': 'o',\n      '\\u01A3': 'oi',\n      '\\u0223': 'ou',\n      '\\uA74F': 'oo',\n      '\\u24DF': 'p',\n      '\\uFF50': 'p',\n      '\\u1E55': 'p',\n      '\\u1E57': 'p',\n      '\\u01A5': 'p',\n      '\\u1D7D': 'p',\n      '\\uA751': 'p',\n      '\\uA753': 'p',\n      '\\uA755': 'p',\n      '\\u24E0': 'q',\n      '\\uFF51': 'q',\n      '\\u024B': 'q',\n      '\\uA757': 'q',\n      '\\uA759': 'q',\n      '\\u24E1': 'r',\n      '\\uFF52': 'r',\n      '\\u0155': 'r',\n      '\\u1E59': 'r',\n      '\\u0159': 'r',\n      '\\u0211': 'r',\n      '\\u0213': 'r',\n      '\\u1E5B': 'r',\n      '\\u1E5D': 'r',\n      '\\u0157': 'r',\n      '\\u1E5F': 'r',\n      '\\u024D': 'r',\n      '\\u027D': 'r',\n      '\\uA75B': 'r',\n      '\\uA7A7': 'r',\n      '\\uA783': 'r',\n      '\\u24E2': 's',\n      '\\uFF53': 's',\n      '\\u00DF': 's',\n      '\\u015B': 's',\n      '\\u1E65': 's',\n      '\\u015D': 's',\n      '\\u1E61': 's',\n      '\\u0161': 's',\n      '\\u1E67': 's',\n      '\\u1E63': 's',\n      '\\u1E69': 's',\n      '\\u0219': 's',\n      '\\u015F': 's',\n      '\\u023F': 's',\n      '\\uA7A9': 's',\n      '\\uA785': 's',\n      '\\u1E9B': 's',\n      '\\u24E3': 't',\n      '\\uFF54': 't',\n      '\\u1E6B': 't',\n      '\\u1E97': 't',\n      '\\u0165': 't',\n      '\\u1E6D': 't',\n      '\\u021B': 't',\n      '\\u0163': 't',\n      '\\u1E71': 't',\n      '\\u1E6F': 't',\n      '\\u0167': 't',\n      '\\u01AD': 't',\n      '\\u0288': 't',\n      '\\u2C66': 't',\n      '\\uA787': 't',\n      '\\uA729': 'tz',\n      '\\u24E4': 'u',\n      '\\uFF55': 'u',\n      '\\u00F9': 'u',\n      '\\u00FA': 'u',\n      '\\u00FB': 'u',\n      '\\u0169': 'u',\n      '\\u1E79': 'u',\n      '\\u016B': 'u',\n      '\\u1E7B': 'u',\n      '\\u016D': 'u',\n      '\\u00FC': 'u',\n      '\\u01DC': 'u',\n      '\\u01D8': 'u',\n      '\\u01D6': 'u',\n      '\\u01DA': 'u',\n      '\\u1EE7': 'u',\n      '\\u016F': 'u',\n      '\\u0171': 'u',\n      '\\u01D4': 'u',\n      '\\u0215': 'u',\n      '\\u0217': 'u',\n      '\\u01B0': 'u',\n      '\\u1EEB': 'u',\n      '\\u1EE9': 'u',\n      '\\u1EEF': 'u',\n      '\\u1EED': 'u',\n      '\\u1EF1': 'u',\n      '\\u1EE5': 'u',\n      '\\u1E73': 'u',\n      '\\u0173': 'u',\n      '\\u1E77': 'u',\n      '\\u1E75': 'u',\n      '\\u0289': 'u',\n      '\\u24E5': 'v',\n      '\\uFF56': 'v',\n      '\\u1E7D': 'v',\n      '\\u1E7F': 'v',\n      '\\u028B': 'v',\n      '\\uA75F': 'v',\n      '\\u028C': 'v',\n      '\\uA761': 'vy',\n      '\\u24E6': 'w',\n      '\\uFF57': 'w',\n      '\\u1E81': 'w',\n      '\\u1E83': 'w',\n      '\\u0175': 'w',\n      '\\u1E87': 'w',\n      '\\u1E85': 'w',\n      '\\u1E98': 'w',\n      '\\u1E89': 'w',\n      '\\u2C73': 'w',\n      '\\u24E7': 'x',\n      '\\uFF58': 'x',\n      '\\u1E8B': 'x',\n      '\\u1E8D': 'x',\n      '\\u24E8': 'y',\n      '\\uFF59': 'y',\n      '\\u1EF3': 'y',\n      '\\u00FD': 'y',\n      '\\u0177': 'y',\n      '\\u1EF9': 'y',\n      '\\u0233': 'y',\n      '\\u1E8F': 'y',\n      '\\u00FF': 'y',\n      '\\u1EF7': 'y',\n      '\\u1E99': 'y',\n      '\\u1EF5': 'y',\n      '\\u01B4': 'y',\n      '\\u024F': 'y',\n      '\\u1EFF': 'y',\n      '\\u24E9': 'z',\n      '\\uFF5A': 'z',\n      '\\u017A': 'z',\n      '\\u1E91': 'z',\n      '\\u017C': 'z',\n      '\\u017E': 'z',\n      '\\u1E93': 'z',\n      '\\u1E95': 'z',\n      '\\u01B6': 'z',\n      '\\u0225': 'z',\n      '\\u0240': 'z',\n      '\\u2C6C': 'z',\n      '\\uA763': 'z',\n      '\\u0386': '\\u0391',\n      '\\u0388': '\\u0395',\n      '\\u0389': '\\u0397',\n      '\\u038A': '\\u0399',\n      '\\u03AA': '\\u0399',\n      '\\u038C': '\\u039F',\n      '\\u038E': '\\u03A5',\n      '\\u03AB': '\\u03A5',\n      '\\u038F': '\\u03A9',\n      '\\u03AC': '\\u03B1',\n      '\\u03AD': '\\u03B5',\n      '\\u03AE': '\\u03B7',\n      '\\u03AF': '\\u03B9',\n      '\\u03CA': '\\u03B9',\n      '\\u0390': '\\u03B9',\n      '\\u03CC': '\\u03BF',\n      '\\u03CD': '\\u03C5',\n      '\\u03CB': '\\u03C5',\n      '\\u03B0': '\\u03C5',\n      '\\u03C9': '\\u03C9',\n      '\\u03C2': '\\u03C3'\n    };\n  }\n  /**\n   * Chuyển chuỗi có dấu sang không dấu ví dụ: á,ă,â... => a\n   * @param text : chuỗi có dấu\n   * @returns chuỗi không dấu\n   */\n  static stripSpecialChars(text) {\n    const match = a => {\n      return this.getDiacritics()[a] || a;\n    };\n    return this.compoundUnicode(text).replace(/[^\\u0000-\\u007E]/g, match);\n  }\n  /**\n   * Chuyển chuỗi Unicode Tổ Hợp sang Unicode Dựng Sẵn\n   * @param text : Unicode Tổ Hợp\n   * @returns Unicode Dựng Sẵn\n   */\n  static compoundUnicode(unicode_str) {\n    unicode_str = unicode_str.replace(/\\u0065\\u0309/g, \"\\u1EBB\"); // ẻ\n    unicode_str = unicode_str.replace(/\\u0065\\u0301/g, \"\\u00E9\"); // é\n    unicode_str = unicode_str.replace(/\\u0065\\u0300/g, \"\\u00E8\"); // è\n    unicode_str = unicode_str.replace(/\\u0065\\u0323/g, \"\\u1EB9\"); // ẹ\n    unicode_str = unicode_str.replace(/\\u0065\\u0303/g, \"\\u1EBD\"); // ẽ\n    unicode_str = unicode_str.replace(/\\u00EA\\u0309/g, \"\\u1EC3\"); // ể\n    unicode_str = unicode_str.replace(/\\u00EA\\u0301/g, \"\\u1EBF\"); // ế\n    unicode_str = unicode_str.replace(/\\u00EA\\u0300/g, \"\\u1EC1\"); // ề\n    unicode_str = unicode_str.replace(/\\u00EA\\u0323/g, \"\\u1EC7\"); // ệ\n    unicode_str = unicode_str.replace(/\\u00EA\\u0303/g, \"\\u1EC5\"); // ễ\n    unicode_str = unicode_str.replace(/\\u0079\\u0309/g, \"\\u1EF7\"); // ỷ\n    unicode_str = unicode_str.replace(/\\u0079\\u0301/g, \"\\u00FD\"); // ý\n    unicode_str = unicode_str.replace(/\\u0079\\u0300/g, \"\\u1EF3\"); // ỳ\n    unicode_str = unicode_str.replace(/\\u0079\\u0323/g, \"\\u1EF5\"); // ỵ\n    unicode_str = unicode_str.replace(/\\u0079\\u0303/g, \"\\u1EF9\"); // ỹ\n    unicode_str = unicode_str.replace(/\\u0075\\u0309/g, \"\\u1EE7\"); // ủ\n    unicode_str = unicode_str.replace(/\\u0075\\u0301/g, \"\\u00FA\"); // ú\n    unicode_str = unicode_str.replace(/\\u0075\\u0300/g, \"\\u00F9\"); // ù\n    unicode_str = unicode_str.replace(/\\u0075\\u0323/g, \"\\u1EE5\"); // ụ\n    unicode_str = unicode_str.replace(/\\u0075\\u0303/g, \"\\u0169\"); // ũ\n    unicode_str = unicode_str.replace(/\\u01B0\\u0309/g, \"\\u1EED\"); // ử\n    unicode_str = unicode_str.replace(/\\u01B0\\u0301/g, \"\\u1EE9\"); // ứ\n    unicode_str = unicode_str.replace(/\\u01B0\\u0300/g, \"\\u1EEB\"); // ừ\n    unicode_str = unicode_str.replace(/\\u01B0\\u0323/g, \"\\u1EF1\"); // ự\n    unicode_str = unicode_str.replace(/\\u01B0\\u0303/g, \"\\u1EEF\"); // ữ\n    unicode_str = unicode_str.replace(/\\u0069\\u0309/g, \"\\u1EC9\"); // ỉ\n    unicode_str = unicode_str.replace(/\\u0069\\u0301/g, \"\\u00ED\"); // í\n    unicode_str = unicode_str.replace(/\\u0069\\u0300/g, \"\\u00EC\"); // ì\n    unicode_str = unicode_str.replace(/\\u0069\\u0323/g, \"\\u1ECB\"); // ị\n    unicode_str = unicode_str.replace(/\\u0069\\u0303/g, \"\\u0129\"); // ĩ\n    unicode_str = unicode_str.replace(/\\u006F\\u0309/g, \"\\u1ECF\"); // ỏ\n    unicode_str = unicode_str.replace(/\\u006F\\u0301/g, \"\\u00F3\"); // ó\n    unicode_str = unicode_str.replace(/\\u006F\\u0300/g, \"\\u00F2\"); // ò\n    unicode_str = unicode_str.replace(/\\u006F\\u0323/g, \"\\u1ECD\"); // ọ\n    unicode_str = unicode_str.replace(/\\u006F\\u0303/g, \"\\u00F5\"); // õ\n    unicode_str = unicode_str.replace(/\\u01A1\\u0309/g, \"\\u1EDF\"); // ở\n    unicode_str = unicode_str.replace(/\\u01A1\\u0301/g, \"\\u1EDB\"); // ớ\n    unicode_str = unicode_str.replace(/\\u01A1\\u0300/g, \"\\u1EDD\"); // ờ\n    unicode_str = unicode_str.replace(/\\u01A1\\u0323/g, \"\\u1EE3\"); // ợ\n    unicode_str = unicode_str.replace(/\\u01A1\\u0303/g, \"\\u1EE1\"); // ỡ\n    unicode_str = unicode_str.replace(/\\u00F4\\u0309/g, \"\\u1ED5\"); // ổ\n    unicode_str = unicode_str.replace(/\\u00F4\\u0301/g, \"\\u1ED1\"); // ố\n    unicode_str = unicode_str.replace(/\\u00F4\\u0300/g, \"\\u1ED3\"); // ồ\n    unicode_str = unicode_str.replace(/\\u00F4\\u0323/g, \"\\u1ED9\"); // ộ\n    unicode_str = unicode_str.replace(/\\u00F4\\u0303/g, \"\\u1ED7\"); // ỗ\n    unicode_str = unicode_str.replace(/\\u0061\\u0309/g, \"\\u1EA3\"); // ả\n    unicode_str = unicode_str.replace(/\\u0061\\u0301/g, \"\\u00E1\"); // á\n    unicode_str = unicode_str.replace(/\\u0061\\u0300/g, \"\\u00E0\"); // à\n    unicode_str = unicode_str.replace(/\\u0061\\u0323/g, \"\\u1EA1\"); // ạ\n    unicode_str = unicode_str.replace(/\\u0061\\u0303/g, \"\\u00E3\"); // ã\n    unicode_str = unicode_str.replace(/\\u0103\\u0309/g, \"\\u1EB3\"); // ẳ\n    unicode_str = unicode_str.replace(/\\u0103\\u0301/g, \"\\u1EAF\"); // ắ\n    unicode_str = unicode_str.replace(/\\u0103\\u0300/g, \"\\u1EB1\"); // ằ\n    unicode_str = unicode_str.replace(/\\u0103\\u0323/g, \"\\u1EB7\"); // ặ\n    unicode_str = unicode_str.replace(/\\u0103\\u0303/g, \"\\u1EB5\"); // ẵ\n    unicode_str = unicode_str.replace(/\\u00E2\\u0309/g, \"\\u1EA9\"); // ẩ\n    unicode_str = unicode_str.replace(/\\u00E2\\u0301/g, \"\\u1EA5\"); // ấ\n    unicode_str = unicode_str.replace(/\\u00E2\\u0300/g, \"\\u1EA7\"); // ầ\n    unicode_str = unicode_str.replace(/\\u00E2\\u0323/g, \"\\u1EAD\"); // ậ\n    unicode_str = unicode_str.replace(/\\u00E2\\u0303/g, \"\\u1EAB\"); // ẫ\n    unicode_str = unicode_str.replace(/\\u0045\\u0309/g, \"\\u1EBA\"); // Ẻ\n    unicode_str = unicode_str.replace(/\\u0045\\u0301/g, \"\\u00C9\"); // É\n    unicode_str = unicode_str.replace(/\\u0045\\u0300/g, \"\\u00C8\"); // È\n    unicode_str = unicode_str.replace(/\\u0045\\u0323/g, \"\\u1EB8\"); // Ẹ\n    unicode_str = unicode_str.replace(/\\u0045\\u0303/g, \"\\u1EBC\"); // Ẽ\n    unicode_str = unicode_str.replace(/\\u00CA\\u0309/g, \"\\u1EC2\"); // Ể\n    unicode_str = unicode_str.replace(/\\u00CA\\u0301/g, \"\\u1EBE\"); // Ế\n    unicode_str = unicode_str.replace(/\\u00CA\\u0300/g, \"\\u1EC0\"); // Ề\n    unicode_str = unicode_str.replace(/\\u00CA\\u0323/g, \"\\u1EC6\"); // Ệ\n    unicode_str = unicode_str.replace(/\\u00CA\\u0303/g, \"\\u1EC4\"); // Ễ\n    unicode_str = unicode_str.replace(/\\u0059\\u0309/g, \"\\u1EF6\"); // Ỷ\n    unicode_str = unicode_str.replace(/\\u0059\\u0301/g, \"\\u00DD\"); // Ý\n    unicode_str = unicode_str.replace(/\\u0059\\u0300/g, \"\\u1EF2\"); // Ỳ\n    unicode_str = unicode_str.replace(/\\u0059\\u0323/g, \"\\u1EF4\"); // Ỵ\n    unicode_str = unicode_str.replace(/\\u0059\\u0303/g, \"\\u1EF8\"); // Ỹ\n    unicode_str = unicode_str.replace(/\\u0055\\u0309/g, \"\\u1EE6\"); // Ủ\n    unicode_str = unicode_str.replace(/\\u0055\\u0301/g, \"\\u00DA\"); // Ú\n    unicode_str = unicode_str.replace(/\\u0055\\u0300/g, \"\\u00D9\"); // Ù\n    unicode_str = unicode_str.replace(/\\u0055\\u0323/g, \"\\u1EE4\"); // Ụ\n    unicode_str = unicode_str.replace(/\\u0055\\u0303/g, \"\\u0168\"); // Ũ\n    unicode_str = unicode_str.replace(/\\u01AF\\u0309/g, \"\\u1EEC\"); // Ử\n    unicode_str = unicode_str.replace(/\\u01AF\\u0301/g, \"\\u1EE8\"); // Ứ\n    unicode_str = unicode_str.replace(/\\u01AF\\u0300/g, \"\\u1EEA\"); // Ừ\n    unicode_str = unicode_str.replace(/\\u01AF\\u0323/g, \"\\u1EF0\"); // Ự\n    unicode_str = unicode_str.replace(/\\u01AF\\u0303/g, \"\\u1EEE\"); // Ữ\n    unicode_str = unicode_str.replace(/\\u0049\\u0309/g, \"\\u1EC8\"); // Ỉ\n    unicode_str = unicode_str.replace(/\\u0049\\u0301/g, \"\\u00CD\"); // Í\n    unicode_str = unicode_str.replace(/\\u0049\\u0300/g, \"\\u00CC\"); // Ì\n    unicode_str = unicode_str.replace(/\\u0049\\u0323/g, \"\\u1ECA\"); // Ị\n    unicode_str = unicode_str.replace(/\\u0049\\u0303/g, \"\\u0128\"); // Ĩ\n    unicode_str = unicode_str.replace(/\\u004F\\u0309/g, \"\\u1ECE\"); // Ỏ\n    unicode_str = unicode_str.replace(/\\u004F\\u0301/g, \"\\u00D3\"); // Ó\n    unicode_str = unicode_str.replace(/\\u004F\\u0300/g, \"\\u00D2\"); // Ò\n    unicode_str = unicode_str.replace(/\\u004F\\u0323/g, \"\\u1ECC\"); // Ọ\n    unicode_str = unicode_str.replace(/\\u004F\\u0303/g, \"\\u00D5\"); // Õ\n    unicode_str = unicode_str.replace(/\\u01A0\\u0309/g, \"\\u1EDE\"); // Ở\n    unicode_str = unicode_str.replace(/\\u01A0\\u0301/g, \"\\u1EDA\"); // Ớ\n    unicode_str = unicode_str.replace(/\\u01A0\\u0300/g, \"\\u1EDC\"); // Ờ\n    unicode_str = unicode_str.replace(/\\u01A0\\u0323/g, \"\\u1EE2\"); // Ợ\n    unicode_str = unicode_str.replace(/\\u01A0\\u0303/g, \"\\u1EE0\"); // Ỡ\n    unicode_str = unicode_str.replace(/\\u00D4\\u0309/g, \"\\u1ED4\"); // Ổ\n    unicode_str = unicode_str.replace(/\\u00D4\\u0301/g, \"\\u1ED0\"); // Ố\n    unicode_str = unicode_str.replace(/\\u00D4\\u0300/g, \"\\u1ED2\"); // Ồ\n    unicode_str = unicode_str.replace(/\\u00D4\\u0323/g, \"\\u1ED8\"); // Ộ\n    unicode_str = unicode_str.replace(/\\u00D4\\u0303/g, \"\\u1ED6\"); // Ỗ\n    unicode_str = unicode_str.replace(/\\u0041\\u0309/g, \"\\u1EA2\"); // Ả\n    unicode_str = unicode_str.replace(/\\u0041\\u0301/g, \"\\u00C1\"); // Á\n    unicode_str = unicode_str.replace(/\\u0041\\u0300/g, \"\\u00C0\"); // À\n    unicode_str = unicode_str.replace(/\\u0041\\u0323/g, \"\\u1EA0\"); // Ạ\n    unicode_str = unicode_str.replace(/\\u0041\\u0303/g, \"\\u00C3\"); // Ã\n    unicode_str = unicode_str.replace(/\\u0102\\u0309/g, \"\\u1EB2\"); // Ẳ\n    unicode_str = unicode_str.replace(/\\u0102\\u0301/g, \"\\u1EAE\"); // Ắ\n    unicode_str = unicode_str.replace(/\\u0102\\u0300/g, \"\\u1EB0\"); // Ằ\n    unicode_str = unicode_str.replace(/\\u0102\\u0323/g, \"\\u1EB6\"); // Ặ\n    unicode_str = unicode_str.replace(/\\u0102\\u0303/g, \"\\u1EB4\"); // Ẵ\n    unicode_str = unicode_str.replace(/\\u00C2\\u0309/g, \"\\u1EA8\"); // Ẩ\n    unicode_str = unicode_str.replace(/\\u00C2\\u0301/g, \"\\u1EA4\"); // Ấ\n    unicode_str = unicode_str.replace(/\\u00C2\\u0300/g, \"\\u1EA6\"); // Ầ\n    unicode_str = unicode_str.replace(/\\u00C2\\u0323/g, \"\\u1EAC\"); // Ậ\n    unicode_str = unicode_str.replace(/\\u00C2\\u0303/g, \"\\u1EAA\"); // Ẫ\n    return unicode_str;\n  }\n}\nfunction toBoolean(value) {\n  return coerceBooleanProperty(value);\n}\nfunction toNumber(value, fallbackValue = 0) {\n  return _isNumberValue(value) ? Number(value) : fallbackValue;\n}\nfunction toCssPixel(value) {\n  return coerceCssPixelValue(value);\n}\n// tslint:disable no-invalid-this\n/**\n * Get the function-property type's value\n */\nfunction valueFunctionProp(prop, ...args) {\n  return typeof prop === 'function' ? prop(...args) : prop;\n}\nfunction propDecoratorFactory(name, fallback) {\n  function propDecorator(target, propName, originalDescriptor) {\n    const privatePropName = `$$__${propName}`;\n    if (Object.prototype.hasOwnProperty.call(target, privatePropName)) {\n      console.log(`The prop \"${privatePropName}\" is already exist, it will be overrided by ${name} decorator.`);\n    }\n    Object.defineProperty(target, privatePropName, {\n      configurable: true,\n      writable: true\n    });\n    return {\n      get() {\n        return originalDescriptor && originalDescriptor.get ? originalDescriptor.get.bind(this)() : this[privatePropName];\n      },\n      set(value) {\n        if (originalDescriptor && originalDescriptor.set) {\n          originalDescriptor.set.bind(this)(fallback(value));\n        }\n        this[privatePropName] = fallback(value);\n      }\n    };\n  }\n  return propDecorator;\n}\n/**\n * Input decorator that handle a prop to do get/set automatically with toBoolean\n *\n * Why not using @InputBoolean alone without @Input? AOT needs @Input to be visible\n *\n * @howToUse\n * ```\n * @Input() @InputBoolean() visible: boolean = false;\n *\n * // Act as below:\n * // @Input()\n * // get visible() { return this.__visible; }\n * // set visible(value) { this.__visible = value; }\n * // __visible = false;\n * ```\n */\nfunction InputBoolean() {\n  return propDecoratorFactory('InputBoolean', toBoolean);\n}\nfunction InputCssPixel() {\n  return propDecoratorFactory('InputCssPixel', toCssPixel);\n}\nfunction InputNumber(fallbackValue) {\n  return propDecoratorFactory('InputNumber', value => toNumber(value, fallbackValue));\n}\nfunction ensureInBounds(value, boundValue) {\n  return value ? value < boundValue ? value : boundValue : boundValue;\n}\nfunction inNextTick() {\n  const timer = new Subject();\n  Promise.resolve().then(() => timer.next());\n  return timer.pipe(take(1));\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { InputBoolean, InputCssPixel, InputNumber, TDSHelperArray, TDSHelperObject, TDSHelperString, ensureInBounds, inNextTick, toBoolean, toCssPixel, toNumber, valueFunctionProp };\n"], "mappings": ";;;;;;;;;;;;;;;AACA,kBAAwB;AACxB,uBAAqB;AAGrB,IAAM,iBAAN,MAAqB;AAAA,EACnB,OAAO,WAAW,KAAK;AACrB,QAAI,KAAK,aAAa,GAAG,GAAG;AAC1B,aAAO,IAAI,KAAK,GAAG;AAAA,IACrB;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO,WAAW,KAAK;AACrB,QAAI,KAAK,aAAa,GAAG,GAAG;AAC1B,aAAO,IAAI,KAAK,KAAK;AAAA,IACvB;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO,aAAa,OAAO;AACzB,WAAO,EAAE,UAAU,UAAa,UAAU,QAAQ,MAAM,UAAU;AAAA,EACpE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,QAAQ,GAAG;AAChB,QAAI,OAAO,UAAU,SAAS,KAAK,CAAC;AACpC,WAAO,qBAAqB,KAAK,8BAA8B;AAAA,EACjE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,SAAS,GAAG;AACjB,aAAS,IAAI,EAAE,QAAQ,IAAI,EAAE,CAAC,GAAG,MAAM,GAAE,CAAC,IAAI,MAAM,IAAI,EAAE,CAAC;AAC3D,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,SAAS,GAAG;AACjB,aAAS,IAAI,EAAE,QAAQ,IAAI,EAAE,CAAC,GAAG,MAAM,GAAE,CAAC,IAAI,MAAM,IAAI,EAAE,CAAC;AAC3D,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,OAAO,QAAQ,QAAQ;AAC5B,WAAO,KAAK,MAAM,QAAQ,MAAM;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA0DA,OAAO,WAAW,UAAU,MAAM;AAChC,QAAI,CAAC,KAAK,aAAa,IAAI,GAAG;AAC5B,aAAO;AAAA,IACT;AACA,UAAM,SAAS,CAAC;AAChB,UAAM,SAAS,oBAAI,IAAI;AACvB,UAAM,aAAa,KAAK,MAAM;AAC9B,UAAM,QAAQ,UAAQ;AACpB,UAAI,CAAC,OAAO,IAAI,KAAK,UAAU,CAAC,GAAG;AACjC,eAAO,IAAI,KAAK,UAAU,CAAC;AAC3B,eAAO,KAAK;AAAA,UACV,CAAC,UAAU,GAAG,KAAK,UAAU;AAAA,UAC7B,UAAU,CAAC,IAAI;AAAA,QACjB,CAAC;AAAA,MACH,OAAO;AACL,eAAO,KAAK,SAAO,IAAI,UAAU,MAAM,KAAK,UAAU,CAAC,EAAE,SAAS,KAAK,IAAI;AAAA,MAC7E;AAAA,IACF,CAAC;AACD,QAAI,KAAK,QAAQ;AACf,aAAO,QAAQ,SAAO;AACpB,YAAI,WAAW,KAAK,WAAW,IAAI,UAAU,GAAG,IAAI;AAAA,MACtD,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AACF;AAGA,IAAM,kBAAN,MAAM,iBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMpB,OAAO,SAAS,OAAO;AACrB,WAAO,EAAE,UAAU,UAAa,UAAU;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,YAAY,KAAK;AACtB,QAAI,KAAK,SAAS,GAAG,GAAG;AACtB,aAAO,KAAK,MAAM,KAAK,UAAU,GAAG,CAAC;AAAA,IACvC,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,OAAO,WAAW,OAAO,QAAQ;AAC/B,YAAQ,OAAO,OAAO,OAAO,MAAM;AAAA,EACrC;AAAA,EACA,OAAO,SAAS,GAAG,GAAG;AACpB,WAAO,CAAC,CAAC,KAAK,aAAa,OAAO,MAAM,CAAC,KAAK,CAAC,eAAe,QAAQ,CAAC;AAAA,EACzE;AAAA;AAAA,EAEA,OAAO,sBAAsB,OAAO;AAClC,WAAO,SAAS,QAAQ,GAAG,KAAK,OAAO;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,WAAW,KAAK;AACrB,WAAO,OAAO,QAAQ;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,gBAAgB,KAAK,OAAO,YAAY,KAAK;AAClD,QAAI,CAAC,KAAK,SAAS,GAAG,GAAG;AACvB,aAAO;AAAA,IACT;AACA,UAAM,OAAO,MAAM,MAAM,SAAS;AAClC,QAAI,SAAS;AACb,eAAW,OAAO,MAAM;AACtB,UAAI,OAAO,eAAe,GAAG,GAAG;AAC9B,iBAAS,OAAO,GAAG;AAAA,MACrB,OAAO;AAEL,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO,QAAQ,KAAK;AAClB,WAAO,CAAC,iBAAgB,SAAS,GAAG,KAAK,OAAO,KAAK,GAAG,EAAE,WAAW;AAAA,EACvE;AACF;AAGA,IAAM,kBAAN,MAAsB;AAAA,EACpB,OAAO,eAAe,OAAO;AAC3B,WAAO,EAAE,UAAU,UAAa,UAAU,QAAQ,UAAU;AAAA,EAC9D;AAAA,EACA,OAAO,OAAO;AACZ,QAAI,MAAM;AACV,WAAO,uCAAuC,QAAQ,SAAS,SAAU,GAAG;AAC1E,eAAS,IAAI,KAAK,OAAO,KAAK,QAAU,GAAG,SAAS,EAAE,EAAE,UAAU,CAAC;AAAA,IACrE,CAAC;AAAA,EACH;AAAA,EACA,OAAO,IAAI,OAAO;AAChB,QAAI,MAAM;AACV,aAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC9B,eAAS,IAAI,KAAK,OAAO,KAAK,QAAU,GAAG,SAAS,EAAE,EAAE,UAAU,CAAC;AAAA,IACrE;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO,QAAQ;AACb,WAAO,KAAK,KAAK,EAAE,QAAQ,MAAM,EAAE;AAAA,EACrC;AAAA,EACA,OAAO,SAAS,KAAK;AACnB,WAAO,aAAa,OAAO;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,WAAW,KAAK,aAAa,cAAc;AAChD,QAAI,KAAK,eAAe,GAAG,GAAG;AAC5B,UAAI,IAAI,QAAQ,WAAW,IAAI,IAAI;AACjC,eAAO,IAAI,MAAM,WAAW,EAAE,KAAK,YAAY;AAAA,MACjD;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,oBAAoB,OAAO;AAChC,WAAO,KAAK,eAAe,MAAM,KAAK,CAAC;AAAA,EACzC;AAAA,EACA,OAAO,gBAAgB;AACrB,WAAO;AAAA,MACL,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,MACV,KAAU;AAAA,IACZ;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,kBAAkB,MAAM;AAC7B,UAAM,QAAQ,OAAK;AACjB,aAAO,KAAK,cAAc,EAAE,CAAC,KAAK;AAAA,IACpC;AACA,WAAO,KAAK,gBAAgB,IAAI,EAAE,QAAQ,qBAAqB,KAAK;AAAA,EACtE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,gBAAgB,aAAa;AAClC,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,kBAAc,YAAY,QAAQ,iBAAiB,GAAQ;AAC3D,WAAO;AAAA,EACT;AACF;AACA,SAAS,UAAU,OAAO;AACxB,SAAO,sBAAsB,KAAK;AACpC;AACA,SAAS,SAAS,OAAO,gBAAgB,GAAG;AAC1C,SAAO,eAAe,KAAK,IAAI,OAAO,KAAK,IAAI;AACjD;AACA,SAAS,WAAW,OAAO;AACzB,SAAO,oBAAoB,KAAK;AAClC;AAQA,SAAS,qBAAqB,MAAM,UAAU;AAC5C,WAAS,cAAc,QAAQ,UAAU,oBAAoB;AAC3D,UAAM,kBAAkB,OAAO,QAAQ;AACvC,QAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,eAAe,GAAG;AACjE,cAAQ,IAAI,aAAa,eAAe,+CAA+C,IAAI,aAAa;AAAA,IAC1G;AACA,WAAO,eAAe,QAAQ,iBAAiB;AAAA,MAC7C,cAAc;AAAA,MACd,UAAU;AAAA,IACZ,CAAC;AACD,WAAO;AAAA,MACL,MAAM;AACJ,eAAO,sBAAsB,mBAAmB,MAAM,mBAAmB,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK,eAAe;AAAA,MAClH;AAAA,MACA,IAAI,OAAO;AACT,YAAI,sBAAsB,mBAAmB,KAAK;AAChD,6BAAmB,IAAI,KAAK,IAAI,EAAE,SAAS,KAAK,CAAC;AAAA,QACnD;AACA,aAAK,eAAe,IAAI,SAAS,KAAK;AAAA,MACxC;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAiBA,SAAS,eAAe;AACtB,SAAO,qBAAqB,gBAAgB,SAAS;AACvD;AAIA,SAAS,YAAY,eAAe;AAClC,SAAO,qBAAqB,eAAe,WAAS,SAAS,OAAO,aAAa,CAAC;AACpF;AAIA,SAAS,aAAa;AACpB,QAAM,QAAQ,IAAI,oBAAQ;AAC1B,UAAQ,QAAQ,EAAE,KAAK,MAAM,MAAM,KAAK,CAAC;AACzC,SAAO,MAAM,SAAK,uBAAK,CAAC,CAAC;AAC3B;", "names": []}