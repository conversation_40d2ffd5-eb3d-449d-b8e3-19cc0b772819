import {
  TDSConfigService,
  TDSMapperPipe,
  TDSMapperPipeModule,
  WithConfig
} from "./chunk-ZNBBR6AY.js";
import {
  InputBoolean,
  InputNumber,
  TDSHelperString
} from "./chunk-I6D3KPPP.js";
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ContentChildren,
  ElementRef,
  EventEmitter,
  Input,
  NgModule,
  Output,
  ViewChild,
  ViewEncapsulation$1,
  __decorate,
  setClassMetadata,
  ɵɵNgOnChangesFeature,
  ɵɵStandaloneFeature,
  ɵɵadvance,
  ɵɵattribute,
  ɵɵclassMap,
  ɵɵclassProp,
  ɵɵconditional,
  ɵɵcontentQuery,
  ɵɵdefineComponent,
  ɵɵdefineInjector,
  ɵɵdefineNgModule,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵloadQuery,
  ɵɵnamespaceSVG,
  ɵɵnextContext,
  ɵɵpipe,
  ɵɵpipeBind2,
  ɵɵprojection,
  ɵɵprojectionDef,
  ɵɵproperty,
  ɵɵqueryRefresh,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵsanitizeUrl,
  ɵɵstyleProp,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵviewQuery
} from "./chunk-NGC5FY53.js";

// node_modules/tds-ui/fesm2022/tds-ui-avatar.mjs
var _c0 = ["textEl"];
function TDSAvatarComponent_Conditional_0_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelement(0, "i");
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext();
    ɵɵclassMap(ctx_r0.icon);
  }
}
function TDSAvatarComponent_Conditional_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r2 = ɵɵgetCurrentView();
    ɵɵelementStart(0, "img", 6);
    ɵɵlistener("error", function TDSAvatarComponent_Conditional_1_Template_img_error_0_listener($event) {
      ɵɵrestoreView(_r2);
      const ctx_r0 = ɵɵnextContext();
      return ɵɵresetView(ctx_r0.imgError($event));
    });
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext();
    ɵɵproperty("src", ctx_r0.tdsSrc, ɵɵsanitizeUrl);
    ɵɵattribute("srcset", ctx_r0.tdsSrcSet)("alt", ctx_r0.alt)("loading", ctx_r0.attrLoading);
  }
}
function TDSAvatarComponent_Conditional_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "span", 3, 0);
    ɵɵtext(2);
    ɵɵpipe(3, "tdsMapper");
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext();
    ɵɵadvance(2);
    ɵɵtextInterpolate(ɵɵpipeBind2(3, 1, ctx_r0.text, ctx_r0.mapperText));
  }
}
function TDSAvatarComponent_Conditional_3_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵnamespaceSVG();
    ɵɵelementStart(0, "svg", 4);
    ɵɵelement(1, "path", 7)(2, "path", 8)(3, "path", 9);
    ɵɵelementEnd();
  }
}
function TDSAvatarComponent_Conditional_4_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵnamespaceSVG();
    ɵɵelementStart(0, "svg", 5);
    ɵɵelement(1, "rect", 10)(2, "path", 11)(3, "path", 12);
    ɵɵelementEnd();
  }
}
function TDSAvatarComponent_Conditional_5_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelement(0, "img", 2);
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext();
    ɵɵproperty("src", ctx_r0.tdsSrcPlaceholder, ɵɵsanitizeUrl);
    ɵɵattribute("srcset", ctx_r0.tdsSrcPlaceholder)("alt", ctx_r0.alt)("loading", ctx_r0.attrLoading);
  }
}
var _c1 = ["*"];
var TDS_CONFIG_MODULE_NAME = "avatar";
var TDSAvatarComponent = class _TDSAvatarComponent {
  constructor(tdsConfigService, elementRef, cdr) {
    this.tdsConfigService = tdsConfigService;
    this.elementRef = elementRef;
    this.cdr = cdr;
    this._tdsModuleName = TDS_CONFIG_MODULE_NAME;
    this.shape = "circle";
    this.size = "md";
    this.gap = 4;
    this.isAvatar = true;
    this.attrLoading = "eager";
    this.tdsBordered = false;
    this.tdsTheme = "default";
    this.error = new EventEmitter();
    this.showPlaceholder = false;
    this.hasText = false;
    this.hasSrc = true;
    this.hasIcon = false;
    this.textStyles = {};
    this.customSize = null;
    this.el = this.elementRef.nativeElement;
    this.mapperText = (text) => {
      if (text.length > 2) {
        text.substring(0, 2);
      }
      return text;
    };
  }
  get hasTdsSrc() {
    return TDSHelperString.hasValueString(this.tdsSrc);
  }
  imgError($event) {
    this.error.emit($event);
    if (!$event.defaultPrevented) {
      this.hasSrc = false;
      this.hasIcon = false;
      this.hasText = false;
      this.showPlaceholder = false;
      if (TDSHelperString.hasValueString(this.icon)) {
        this.hasIcon = true;
      } else if (TDSHelperString.hasValueString(this.text)) {
        this.hasText = true;
      } else {
        this.showPlaceholder = true;
      }
      this.cdr.detectChanges();
      this.setSizeStyle();
      this.calcStringSize();
    }
  }
  ngOnChanges() {
    this.hasText = !TDSHelperString.hasValueString(this.tdsSrc) && TDSHelperString.hasValueString(this.text);
    this.hasIcon = !TDSHelperString.hasValueString(this.tdsSrc) && TDSHelperString.hasValueString(this.icon);
    this.hasSrc = TDSHelperString.hasValueString(this.tdsSrc);
    this.showPlaceholder = !this.hasIcon && !this.hasSrc && !this.hasText;
    this.setSizeStyle();
    this.calcStringSize();
  }
  calcStringSize() {
    if (!this.hasText || !this.textEl) {
      return;
    }
    const childrenWidth = this.textEl?.nativeElement.offsetWidth;
    const avatarWidth = this.el.getBoundingClientRect().width;
    const offset = this.gap * 2 < avatarWidth ? this.gap * 2 : 8;
    const scale = avatarWidth - offset < childrenWidth ? (avatarWidth - offset) / childrenWidth : 1;
    this.textStyles = {
      "transform-origin": `0 center`,
      transform: `scale(${scale}) translateX(-50%)`
    };
    if (this.customSize) {
      Object.assign(this.textStyles, {
        lineHeight: this.customSize
      });
    }
    this.cdr.detectChanges();
  }
  setSizeStyle() {
    if (typeof this.size === "number") {
      this.customSize = `${this.size}px`;
    } else {
      this.customSize = null;
    }
    this.cdr.markForCheck();
  }
  get hasTdsSrcPlaceholder() {
    return TDSHelperString.hasValueString(this.tdsSrcPlaceholder);
  }
  get element() {
    return this.el;
  }
  static {
    this.ɵfac = function TDSAvatarComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TDSAvatarComponent)(ɵɵdirectiveInject(TDSConfigService), ɵɵdirectiveInject(ElementRef), ɵɵdirectiveInject(ChangeDetectorRef));
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _TDSAvatarComponent,
      selectors: [["tds-avatar"]],
      viewQuery: function TDSAvatarComponent_Query(rf, ctx) {
        if (rf & 1) {
          ɵɵviewQuery(_c0, 5);
        }
        if (rf & 2) {
          let _t;
          ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.textEl = _t.first);
        }
      },
      hostAttrs: [1, "tds-avatar"],
      hostVars: 34,
      hostBindings: function TDSAvatarComponent_HostBindings(rf, ctx) {
        if (rf & 2) {
          ɵɵstyleProp("width", ctx.customSize)("height", ctx.customSize)("line-height", ctx.customSize)("font-size", ctx.hasIcon && ctx.customSize ? ctx.size / 2 : null, "px");
          ɵɵclassProp("tds-avatar-lg", ctx.size === "lg")("tds-avatar-sm", ctx.size === "sm")("tds-avatar-md", ctx.size === "md")("tds-avatar-xl", ctx.size === "xl")("tds-avatar-customsize", ctx.customSize)("tds-avatar-square", ctx.shape === "square")("tds-avatar-circle", ctx.shape === "circle")("tds-avatar-icon", ctx.icon)("tds-avatar-image", ctx.hasSrc)("tds-avatar-bordered", ctx.tdsBordered)("tds-avatar-theme-default", ctx.tdsTheme === "default")("tds-avatar-theme-light", ctx.tdsTheme === "light")("tds-avatar-theme-dark", ctx.tdsTheme === "dark");
        }
      },
      inputs: {
        shape: "shape",
        size: "size",
        gap: "gap",
        isAvatar: "isAvatar",
        text: "text",
        tdsSrc: "tdsSrc",
        tdsSrcSet: "tdsSrcSet",
        tdsSrcPlaceholder: "tdsSrcPlaceholder",
        alt: "alt",
        icon: "icon",
        attrLoading: "attrLoading",
        tdsBordered: "tdsBordered",
        tdsTheme: "tdsTheme"
      },
      outputs: {
        error: "error"
      },
      exportAs: ["tdsAvatar"],
      standalone: true,
      features: [ɵɵNgOnChangesFeature, ɵɵStandaloneFeature],
      decls: 6,
      vars: 6,
      consts: [["textEl", ""], [3, "class"], [3, "src"], [1, "tds-avatar-string"], ["viewBox", "0 0 36 36", "fill", "none", "xmlns", "http://www.w3.org/2000/svg", 1, "w-full", "h-full"], ["xmlns", "http://www.w3.org/2000/svg", "viewBox", "0 0 480 480", "fill", "none", 1, "w-full", "h-full"], [3, "error", "src"], ["d", "M36 0H0V36H36V0Z", 1, "fill-neutral-2-50", "dark:fill-d-neutral-1-200"], ["d", "M18 23.03C12.6318 23.0301 7.42685 24.876 3.258 28.258C2.07431 29.241 0.983696 30.3309 0 31.514L0 36H36V31.237C34.9771 30.1771 33.889 29.1822 32.742 28.258C28.5732 24.876 23.3682 23.0301 18 23.03Z", 1, "dark:fill-d-neutral-1-400", "fill-neutral-1-100"], ["d", "M18 19.8C19.335 19.8 20.6401 19.4041 21.7501 18.6624C22.8601 17.9207 23.7253 16.8665 24.2362 15.6331C24.7471 14.3997 24.8808 13.0425 24.6203 11.7331C24.3598 10.4238 23.717 9.22102 22.773 8.27702C21.829 7.33302 20.6262 6.69014 19.3169 6.42969C18.0075 6.16924 16.6503 6.30291 15.4169 6.8138C14.1835 7.3247 13.1293 8.18986 12.3876 9.29989C11.6459 10.4099 11.25 11.715 11.25 13.05C11.25 14.8402 11.9612 16.5571 13.227 17.823C14.4929 19.0888 16.2098 19.8 18 19.8Z", 1, "dark:fill-d-neutral-1-400", "fill-neutral-1-100"], ["fill", "#E9EDF2", 1, "w-full", "h-full"], ["d", "M195.249 231.124C205.01 231.124 212.948 223.163 212.948 213.373C212.948 203.584 205.01 195.623 195.249 195.623C185.487 195.623 177.548 203.584 177.548 213.373C177.548 223.163 185.487 231.124 195.249 231.124Z", "fill", "#A1ACB8"], ["d", "M305.874 169H173.124C160.93 169 151 178.958 151 191.188V288.812C151 301.042 160.93 311 173.124 311H305.874C318.07 311 328 301.042 328 288.812V191.188C328 178.958 318.07 169 305.874 169ZM173.124 186.75H305.874C308.318 186.75 310.3 188.738 310.3 191.188V254.192L282.343 221.478C279.378 217.99 275.085 216.126 270.475 216.021C265.89 216.047 261.59 218.088 258.652 221.621L225.782 261.184L215.074 250.472C209.021 244.402 199.17 244.402 193.126 250.472L168.7 274.958V191.188C168.7 188.738 170.682 186.75 173.124 186.75Z", "fill", "#A1ACB8"]],
      template: function TDSAvatarComponent_Template(rf, ctx) {
        if (rf & 1) {
          ɵɵtemplate(0, TDSAvatarComponent_Conditional_0_Template, 1, 2, "i", 1)(1, TDSAvatarComponent_Conditional_1_Template, 1, 4, "img", 2)(2, TDSAvatarComponent_Conditional_2_Template, 4, 4, "span", 3)(3, TDSAvatarComponent_Conditional_3_Template, 4, 0, ":svg:svg", 4)(4, TDSAvatarComponent_Conditional_4_Template, 4, 0, ":svg:svg", 5)(5, TDSAvatarComponent_Conditional_5_Template, 1, 4, "img", 2);
        }
        if (rf & 2) {
          ɵɵconditional(ctx.icon && ctx.hasIcon ? 0 : -1);
          ɵɵadvance();
          ɵɵconditional(ctx.tdsSrc && ctx.hasSrc ? 1 : -1);
          ɵɵadvance();
          ɵɵconditional(ctx.text && ctx.hasText ? 2 : -1);
          ɵɵadvance();
          ɵɵconditional(ctx.showPlaceholder && !ctx.hasTdsSrcPlaceholder && ctx.isAvatar ? 3 : -1);
          ɵɵadvance();
          ɵɵconditional(ctx.showPlaceholder && !ctx.hasTdsSrcPlaceholder && !ctx.isAvatar ? 4 : -1);
          ɵɵadvance();
          ɵɵconditional(ctx.showPlaceholder && ctx.hasTdsSrcPlaceholder ? 5 : -1);
        }
      },
      dependencies: [TDSMapperPipeModule, TDSMapperPipe],
      encapsulation: 2,
      changeDetection: 0
    });
  }
};
__decorate([WithConfig()], TDSAvatarComponent.prototype, "shape", void 0);
__decorate([WithConfig()], TDSAvatarComponent.prototype, "size", void 0);
__decorate([WithConfig(), InputNumber()], TDSAvatarComponent.prototype, "gap", void 0);
__decorate([InputBoolean()], TDSAvatarComponent.prototype, "tdsBordered", void 0);
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TDSAvatarComponent, [{
    type: Component,
    args: [{
      selector: "tds-avatar",
      exportAs: "tdsAvatar",
      standalone: true,
      imports: [TDSMapperPipeModule],
      template: `
    @if (icon && hasIcon) {
      <i [class]="icon"></i>
    }
    @if (tdsSrc && hasSrc) {
      <img [src]="tdsSrc" [attr.srcset]="tdsSrcSet" [attr.alt]="alt" (error)="imgError($event)"
        [attr.loading]='attrLoading' />
    }
    @if (text && hasText) {
      <span class="tds-avatar-string" #textEl>{{ text | tdsMapper:mapperText }}</span>
    }
    @if (showPlaceholder && !hasTdsSrcPlaceholder && isAvatar) {
      <svg viewBox="0 0 36 36" class="w-full h-full " fill="none"
        xmlns="http://www.w3.org/2000/svg">
        <path d="M36 0H0V36H36V0Z"  class="fill-neutral-2-50 dark:fill-d-neutral-1-200" />
        <path
          d="M18 23.03C12.6318 23.0301 7.42685 24.876 3.258 28.258C2.07431 29.241 0.983696 30.3309 0 31.514L0 36H36V31.237C34.9771 30.1771 33.889 29.1822 32.742 28.258C28.5732 24.876 23.3682 23.0301 18 23.03Z"
          class="dark:fill-d-neutral-1-400 fill-neutral-1-100"/>
          <path
            d="M18 19.8C19.335 19.8 20.6401 19.4041 21.7501 18.6624C22.8601 17.9207 23.7253 16.8665 24.2362 15.6331C24.7471 14.3997 24.8808 13.0425 24.6203 11.7331C24.3598 10.4238 23.717 9.22102 22.773 8.27702C21.829 7.33302 20.6262 6.69014 19.3169 6.42969C18.0075 6.16924 16.6503 6.30291 15.4169 6.8138C14.1835 7.3247 13.1293 8.18986 12.3876 9.29989C11.6459 10.4099 11.25 11.715 11.25 13.05C11.25 14.8402 11.9612 16.5571 13.227 17.823C14.4929 19.0888 16.2098 19.8 18 19.8Z"
            class="dark:fill-d-neutral-1-400 fill-neutral-1-100" />
          </svg>
        }
        @if (showPlaceholder && !hasTdsSrcPlaceholder && !isAvatar) {
          <svg class="w-full h-full"
            xmlns="http://www.w3.org/2000/svg" viewBox="0 0 480 480" fill="none">
            <rect class="w-full h-full" fill="#E9EDF2" />
            <path
              d="M195.249 231.124C205.01 231.124 212.948 223.163 212.948 213.373C212.948 203.584 205.01 195.623 195.249 195.623C185.487 195.623 177.548 203.584 177.548 213.373C177.548 223.163 185.487 231.124 195.249 231.124Z"
              fill="#A1ACB8" />
              <path
                d="M305.874 169H173.124C160.93 169 151 178.958 151 191.188V288.812C151 301.042 160.93 311 173.124 311H305.874C318.07 311 328 301.042 328 288.812V191.188C328 178.958 318.07 169 305.874 169ZM173.124 186.75H305.874C308.318 186.75 310.3 188.738 310.3 191.188V254.192L282.343 221.478C279.378 217.99 275.085 216.126 270.475 216.021C265.89 216.047 261.59 218.088 258.652 221.621L225.782 261.184L215.074 250.472C209.021 244.402 199.17 244.402 193.126 250.472L168.7 274.958V191.188C168.7 188.738 170.682 186.75 173.124 186.75Z"
                fill="#A1ACB8" />
              </svg>
            }
            @if (showPlaceholder && hasTdsSrcPlaceholder) {
              <img [src]="tdsSrcPlaceholder" [attr.srcset]="tdsSrcPlaceholder"
                [attr.alt]="alt" [attr.loading]='attrLoading' />
            }
    `,
      host: {
        "[class.tds-avatar-lg]": `size === 'lg'`,
        "[class.tds-avatar-sm]": `size === 'sm'`,
        "[class.tds-avatar-md]": `size === 'md'`,
        "[class.tds-avatar-xl]": `size === 'xl'`,
        "[class.tds-avatar-customsize]": `customSize`,
        "[class.tds-avatar-square]": `shape === 'square'`,
        "[class.tds-avatar-circle]": `shape === 'circle'`,
        "[class.tds-avatar-icon]": `icon`,
        "[class.tds-avatar-image]": `hasSrc`,
        "[class.tds-avatar-bordered]": `tdsBordered`,
        "[class.tds-avatar-theme-default]": 'tdsTheme === "default"',
        "[class.tds-avatar-theme-light]": 'tdsTheme === "light"',
        "[class.tds-avatar-theme-dark]": 'tdsTheme === "dark"',
        "[style.width]": "customSize",
        "[style.height]": "customSize",
        "[style.line-height]": "customSize",
        // size type is number when customSize is true
        "[style.font-size.px]": "(hasIcon && customSize) ? $any(size) / 2 : null",
        class: "tds-avatar"
      },
      preserveWhitespaces: false,
      changeDetection: ChangeDetectionStrategy.OnPush,
      encapsulation: ViewEncapsulation$1.None
    }]
  }], () => [{
    type: TDSConfigService
  }, {
    type: ElementRef
  }, {
    type: ChangeDetectorRef
  }], {
    shape: [{
      type: Input
    }],
    size: [{
      type: Input
    }],
    gap: [{
      type: Input
    }],
    isAvatar: [{
      type: Input
    }],
    text: [{
      type: Input
    }],
    tdsSrc: [{
      type: Input
    }],
    tdsSrcSet: [{
      type: Input
    }],
    tdsSrcPlaceholder: [{
      type: Input
    }],
    alt: [{
      type: Input
    }],
    icon: [{
      type: Input
    }],
    attrLoading: [{
      type: Input
    }],
    tdsBordered: [{
      type: Input
    }],
    tdsTheme: [{
      type: Input
    }],
    error: [{
      type: Output
    }],
    textEl: [{
      type: ViewChild,
      args: ["textEl", {
        static: false
      }]
    }]
  });
})();
var TDSAvatarGroupComponent = class _TDSAvatarGroupComponent {
  static {
    this.ɵfac = function TDSAvatarGroupComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TDSAvatarGroupComponent)();
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _TDSAvatarGroupComponent,
      selectors: [["tds-avatar-group"]],
      contentQueries: function TDSAvatarGroupComponent_ContentQueries(rf, ctx, dirIndex) {
        if (rf & 1) {
          ɵɵcontentQuery(dirIndex, TDSAvatarComponent, 5);
        }
        if (rf & 2) {
          let _t;
          ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.lstAvatar = _t);
        }
      },
      hostAttrs: [1, "tds-avatar-group"],
      exportAs: ["tdsAvatarGroup"],
      standalone: true,
      features: [ɵɵStandaloneFeature],
      ngContentSelectors: _c1,
      decls: 1,
      vars: 0,
      template: function TDSAvatarGroupComponent_Template(rf, ctx) {
        if (rf & 1) {
          ɵɵprojectionDef();
          ɵɵprojection(0);
        }
      },
      encapsulation: 2,
      changeDetection: 0
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TDSAvatarGroupComponent, [{
    type: Component,
    args: [{
      selector: "tds-avatar-group",
      exportAs: "tdsAvatarGroup",
      standalone: true,
      template: ` <ng-content></ng-content> `,
      changeDetection: ChangeDetectionStrategy.OnPush,
      host: {
        class: "tds-avatar-group"
      }
    }]
  }], null, {
    lstAvatar: [{
      type: ContentChildren,
      args: [TDSAvatarComponent, {
        descendants: true
      }]
    }]
  });
})();
var TDSAvatarModule = class _TDSAvatarModule {
  static {
    this.ɵfac = function TDSAvatarModule_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TDSAvatarModule)();
    };
  }
  static {
    this.ɵmod = ɵɵdefineNgModule({
      type: _TDSAvatarModule,
      imports: [TDSAvatarComponent, TDSAvatarGroupComponent],
      exports: [TDSAvatarComponent, TDSAvatarGroupComponent]
    });
  }
  static {
    this.ɵinj = ɵɵdefineInjector({
      imports: [TDSAvatarComponent]
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TDSAvatarModule, [{
    type: NgModule,
    args: [{
      exports: [TDSAvatarComponent, TDSAvatarGroupComponent],
      imports: [TDSAvatarComponent, TDSAvatarGroupComponent]
    }]
  }], null, null);
})();

export {
  TDSAvatarComponent,
  TDSAvatarGroupComponent,
  TDSAvatarModule
};
//# sourceMappingURL=chunk-LVNZ5KN7.js.map
