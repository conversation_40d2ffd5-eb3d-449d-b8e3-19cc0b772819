import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { LayoutComponent } from './layout.component';
import { TDSHeaderModule } from 'tds-ui/header';
import { TDSLayoutModule } from 'tds-ui/layout';
import { TDSDropDownModule } from 'tds-ui/dropdown';
import { TDSAvatarModule } from 'tds-ui/avatar';
import { RouterModule } from '@angular/router';
import { TDSMenuModule } from 'tds-ui/menu';

@NgModule({
  declarations: [LayoutComponent],
  imports: [
    CommonModule,
    RouterModule.forChild([]),
    TDSHeaderModule,
    TDSLayoutModule,
    TDSAvatarModule,
    TDSDropDownModule,
    TDSMenuModule
  ],
  exports: [LayoutComponent],
})
export class LayoutModule {}
