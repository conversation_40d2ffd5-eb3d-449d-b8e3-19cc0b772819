import { createRequire } from 'module';const require = createRequire(import.meta.url);
import {
  NavigationEnd,
  Router,
  RouterLink,
  RouterLinkActive,
  RouterModule
} from "./chunk-LICHUDVX.js";
import {
  CdkConnectedOverlay,
  CdkOverlayOrigin,
  DEFAULT_TOOLTIP_POSITIONS,
  OverlayModule,
  POSITION_MAP,
  TDSDestroyService,
  TDSOutletModule,
  TDSOverlayModule,
  TDSPipesModule,
  TDSSanitizerPipe,
  TDSStringTemplateOutletDirective,
  collapseMotion,
  getPlacementName,
  menuCollapseMotion,
  slideMotion,
  zoomBadgeMotion,
  zoomBigMotion
} from "./chunk-WKYNVKBH.js";
import {
  Directionality,
  Platform,
  _getEventTarget
} from "./chunk-6BGFCIZB.js";
import {
  NgClass,
  NgStyle,
  NgTemplateOutlet
} from "./chunk-D3JV2RY4.js";
import {
  TDSConfigService,
  TDSMapperPipe,
  TDSMapperPipeModule,
  WithConfig,
  __decorate
} from "./chunk-A2D67SU4.js";
import {
  InputBoolean,
  TDSHelperArray,
  TDSHelperObject,
  TDSHelperString,
  toBoolean,
  toNumber
} from "./chunk-VVZCKIK2.js";
import {
  coerceElement
} from "./chunk-PFNSG66E.js";
import {
  ANIMATION_MODULE_TYPE,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ContentChild,
  ContentChildren,
  Directive,
  ElementRef,
  EventEmitter,
  Host,
  Inject,
  Injectable,
  InjectionToken,
  Input,
  NgModule,
  NgZone,
  Optional,
  Output,
  Renderer2,
  SkipSelf,
  TemplateRef,
  ViewChild,
  ViewChildren,
  ViewContainerRef,
  ViewEncapsulation$1,
  forwardRef,
  inject,
  require_cjs,
  require_operators,
  setClassMetadata,
  ɵɵInheritDefinitionFeature,
  ɵɵNgOnChangesFeature,
  ɵɵProvidersFeature,
  ɵɵStandaloneFeature,
  ɵɵadvance,
  ɵɵattribute,
  ɵɵclassMap,
  ɵɵclassProp,
  ɵɵconditional,
  ɵɵcontentQuery,
  ɵɵdefineComponent,
  ɵɵdefineDirective,
  ɵɵdefineInjectable,
  ɵɵdefineInjector,
  ɵɵdefineNgModule,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementContainer,
  ɵɵelementContainerEnd,
  ɵɵelementContainerStart,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵinject,
  ɵɵlistener,
  ɵɵloadQuery,
  ɵɵnamespaceHTML,
  ɵɵnamespaceSVG,
  ɵɵnextContext,
  ɵɵpipe,
  ɵɵpipeBind2,
  ɵɵpipeBind3,
  ɵɵprojection,
  ɵɵprojectionDef,
  ɵɵproperty,
  ɵɵpureFunction0,
  ɵɵpureFunction1,
  ɵɵpureFunction2,
  ɵɵpureFunction3,
  ɵɵqueryRefresh,
  ɵɵreference,
  ɵɵrepeater,
  ɵɵrepeaterCreate,
  ɵɵrepeaterTrackByIdentity,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵsanitizeHtml,
  ɵɵstyleMap,
  ɵɵstyleProp,
  ɵɵsyntheticHostProperty,
  ɵɵtemplate,
  ɵɵtemplateRefExtractor,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1,
  ɵɵviewQuery
} from "./chunk-NCYSEW5N.js";
import {
  __spreadValues,
  __toESM
} from "./chunk-NQ4HTGF6.js";

// node_modules/tds-ui/fesm2022/tds-ui-menu.mjs
var import_rxjs5 = __toESM(require_cjs(), 1);
var import_operators5 = __toESM(require_operators(), 1);

// node_modules/tds-ui/fesm2022/tds-ui-tag.mjs
var import_rxjs = __toESM(require_cjs(), 1);
var import_operators = __toESM(require_operators(), 1);

// node_modules/tds-ui/fesm2022/tds-ui-tinycolor.mjs
function bound01(n, max) {
  if (isOnePointZero(n)) {
    n = "100%";
  }
  const isPercent = isPercentage(n);
  n = max === 360 ? n : Math.min(max, Math.max(0, parseFloat(n)));
  if (isPercent) {
    n = parseInt(String(n * max), 10) / 100;
  }
  if (Math.abs(n - max) < 1e-6) {
    return 1;
  }
  if (max === 360) {
    n = (n < 0 ? n % max + max : n % max) / parseFloat(String(max));
  } else {
    n = n % max / parseFloat(String(max));
  }
  return n;
}
function isOnePointZero(n) {
  return typeof n === "string" && n.indexOf(".") !== -1 && parseFloat(n) === 1;
}
function isPercentage(n) {
  return typeof n === "string" && n.indexOf("%") !== -1;
}
function boundAlpha(a) {
  a = parseFloat(a);
  if (isNaN(a) || a < 0 || a > 1) {
    a = 1;
  }
  return a;
}
function convertToPercentage(n) {
  if (typeof n == "number" && n <= 1) {
    return `${Number(n) * 100}%`;
  }
  return n;
}
function pad2(c) {
  return c.length === 1 ? "0" + c : String(c);
}
function rgbToRgb(r, g, b) {
  return {
    r: bound01(r, 255) * 255,
    g: bound01(g, 255) * 255,
    b: bound01(b, 255) * 255
  };
}
function hue2rgb(p, q, t) {
  if (t < 0) {
    t += 1;
  }
  if (t > 1) {
    t -= 1;
  }
  if (t < 1 / 6) {
    return p + (q - p) * (6 * t);
  }
  if (t < 1 / 2) {
    return q;
  }
  if (t < 2 / 3) {
    return p + (q - p) * (2 / 3 - t) * 6;
  }
  return p;
}
function hslToRgb(h, s, l) {
  let r;
  let g;
  let b;
  h = bound01(h, 360);
  s = bound01(s, 100);
  l = bound01(l, 100);
  if (s === 0) {
    g = l;
    b = l;
    r = l;
  } else {
    const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
    const p = 2 * l - q;
    r = hue2rgb(p, q, h + 1 / 3);
    g = hue2rgb(p, q, h);
    b = hue2rgb(p, q, h - 1 / 3);
  }
  return {
    r: r * 255,
    g: g * 255,
    b: b * 255
  };
}
function rgbToHsv(r, g, b) {
  r = bound01(r, 255);
  g = bound01(g, 255);
  b = bound01(b, 255);
  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  let h = 0;
  const v = max;
  const d = max - min;
  const s = max === 0 ? 0 : d / max;
  if (max === min) {
    h = 0;
  } else {
    switch (max) {
      case r:
        h = (g - b) / d + (g < b ? 6 : 0);
        break;
      case g:
        h = (b - r) / d + 2;
        break;
      case b:
        h = (r - g) / d + 4;
        break;
      default:
        break;
    }
    h /= 6;
  }
  return {
    h,
    s,
    v
  };
}
function hsvToRgb(h, s, v) {
  h = bound01(h, 360) * 6;
  s = bound01(s, 100);
  v = bound01(v, 100);
  const i = Math.floor(h);
  const f = h - i;
  const p = v * (1 - s);
  const q = v * (1 - f * s);
  const t = v * (1 - (1 - f) * s);
  const mod = i % 6;
  const r = [v, q, p, p, t, v][mod];
  const g = [t, v, v, q, p, p][mod];
  const b = [p, p, t, v, v, q][mod];
  return {
    r: r * 255,
    g: g * 255,
    b: b * 255
  };
}
function rgbToHex(r, g, b, allow3Char) {
  const hex = [pad2(Math.round(r).toString(16)), pad2(Math.round(g).toString(16)), pad2(Math.round(b).toString(16))];
  if (allow3Char && hex[0].startsWith(hex[0].charAt(1)) && hex[1].startsWith(hex[1].charAt(1)) && hex[2].startsWith(hex[2].charAt(1))) {
    return hex[0].charAt(0) + hex[1].charAt(0) + hex[2].charAt(0);
  }
  return hex.join("");
}
function convertHexToDecimal(h) {
  return parseIntFromHex(h) / 255;
}
function parseIntFromHex(val) {
  return parseInt(val, 16);
}
var names = {
  aliceblue: "#f0f8ff",
  antiquewhite: "#faebd7",
  aqua: "#00ffff",
  aquamarine: "#7fffd4",
  azure: "#f0ffff",
  beige: "#f5f5dc",
  bisque: "#ffe4c4",
  black: "#000000",
  blanchedalmond: "#ffebcd",
  blue: "#0000ff",
  blueviolet: "#8a2be2",
  brown: "#a52a2a",
  burlywood: "#deb887",
  cadetblue: "#5f9ea0",
  chartreuse: "#7fff00",
  chocolate: "#d2691e",
  coral: "#ff7f50",
  cornflowerblue: "#6495ed",
  cornsilk: "#fff8dc",
  crimson: "#dc143c",
  cyan: "#00ffff",
  darkblue: "#00008b",
  darkcyan: "#008b8b",
  darkgoldenrod: "#b8860b",
  darkgray: "#a9a9a9",
  darkgreen: "#006400",
  darkgrey: "#a9a9a9",
  darkkhaki: "#bdb76b",
  darkmagenta: "#8b008b",
  darkolivegreen: "#556b2f",
  darkorange: "#ff8c00",
  darkorchid: "#9932cc",
  darkred: "#8b0000",
  darksalmon: "#e9967a",
  darkseagreen: "#8fbc8f",
  darkslateblue: "#483d8b",
  darkslategray: "#2f4f4f",
  darkslategrey: "#2f4f4f",
  darkturquoise: "#00ced1",
  darkviolet: "#9400d3",
  deeppink: "#ff1493",
  deepskyblue: "#00bfff",
  dimgray: "#696969",
  dimgrey: "#696969",
  dodgerblue: "#1e90ff",
  firebrick: "#b22222",
  floralwhite: "#fffaf0",
  forestgreen: "#228b22",
  fuchsia: "#ff00ff",
  gainsboro: "#dcdcdc",
  ghostwhite: "#f8f8ff",
  goldenrod: "#daa520",
  gold: "#ffd700",
  gray: "#808080",
  green: "#008000",
  greenyellow: "#adff2f",
  grey: "#808080",
  honeydew: "#f0fff0",
  hotpink: "#ff69b4",
  indianred: "#cd5c5c",
  indigo: "#4b0082",
  ivory: "#fffff0",
  khaki: "#f0e68c",
  lavenderblush: "#fff0f5",
  lavender: "#e6e6fa",
  lawngreen: "#7cfc00",
  lemonchiffon: "#fffacd",
  lightblue: "#add8e6",
  lightcoral: "#f08080",
  lightcyan: "#e0ffff",
  lightgoldenrodyellow: "#fafad2",
  lightgray: "#d3d3d3",
  lightgreen: "#90ee90",
  lightgrey: "#d3d3d3",
  lightpink: "#ffb6c1",
  lightsalmon: "#ffa07a",
  lightseagreen: "#20b2aa",
  lightskyblue: "#87cefa",
  lightslategray: "#778899",
  lightslategrey: "#778899",
  lightsteelblue: "#b0c4de",
  lightyellow: "#ffffe0",
  lime: "#00ff00",
  limegreen: "#32cd32",
  linen: "#faf0e6",
  magenta: "#ff00ff",
  maroon: "#800000",
  mediumaquamarine: "#66cdaa",
  mediumblue: "#0000cd",
  mediumorchid: "#ba55d3",
  mediumpurple: "#9370db",
  mediumseagreen: "#3cb371",
  mediumslateblue: "#7b68ee",
  mediumspringgreen: "#00fa9a",
  mediumturquoise: "#48d1cc",
  mediumvioletred: "#c71585",
  midnightblue: "#191970",
  mintcream: "#f5fffa",
  mistyrose: "#ffe4e1",
  moccasin: "#ffe4b5",
  navajowhite: "#ffdead",
  navy: "#000080",
  oldlace: "#fdf5e6",
  olive: "#808000",
  olivedrab: "#6b8e23",
  orange: "#ffa500",
  orangered: "#ff4500",
  orchid: "#da70d6",
  palegoldenrod: "#eee8aa",
  palegreen: "#98fb98",
  paleturquoise: "#afeeee",
  palevioletred: "#db7093",
  papayawhip: "#ffefd5",
  peachpuff: "#ffdab9",
  peru: "#cd853f",
  pink: "#ffc0cb",
  plum: "#dda0dd",
  powderblue: "#b0e0e6",
  purple: "#800080",
  rebeccapurple: "#663399",
  red: "#ff0000",
  rosybrown: "#bc8f8f",
  royalblue: "#4169e1",
  saddlebrown: "#8b4513",
  salmon: "#fa8072",
  sandybrown: "#f4a460",
  seagreen: "#2e8b57",
  seashell: "#fff5ee",
  sienna: "#a0522d",
  silver: "#c0c0c0",
  skyblue: "#87ceeb",
  slateblue: "#6a5acd",
  slategray: "#708090",
  slategrey: "#708090",
  snow: "#fffafa",
  springgreen: "#00ff7f",
  steelblue: "#4682b4",
  tan: "#d2b48c",
  teal: "#008080",
  thistle: "#d8bfd8",
  tomato: "#ff6347",
  turquoise: "#40e0d0",
  violet: "#ee82ee",
  wheat: "#f5deb3",
  white: "#ffffff",
  whitesmoke: "#f5f5f5",
  yellow: "#ffff00",
  yellowgreen: "#9acd32"
};
function inputToRGB(color) {
  let rgb = {
    r: 0,
    g: 0,
    b: 0
  };
  let a = 1;
  let s = null;
  let v = null;
  let l = null;
  let ok = false;
  let format = false;
  if (typeof color === "string") {
    color = stringInputToObject(color);
  }
  if (typeof color === "object") {
    if (isValidCSSUnit(color.r) && isValidCSSUnit(color.g) && isValidCSSUnit(color.b)) {
      rgb = rgbToRgb(color.r, color.g, color.b);
      ok = true;
      format = String(color.r).substr(-1) === "%" ? "prgb" : "rgb";
    } else if (isValidCSSUnit(color.h) && isValidCSSUnit(color.s) && isValidCSSUnit(color.v)) {
      s = convertToPercentage(color.s);
      v = convertToPercentage(color.v);
      rgb = hsvToRgb(color.h, s, v);
      ok = true;
      format = "hsv";
    } else if (isValidCSSUnit(color.h) && isValidCSSUnit(color.s) && isValidCSSUnit(color.l)) {
      s = convertToPercentage(color.s);
      l = convertToPercentage(color.l);
      rgb = hslToRgb(color.h, s, l);
      ok = true;
      format = "hsl";
    }
    if (Object.prototype.hasOwnProperty.call(color, "a")) {
      a = color.a;
    }
  }
  a = boundAlpha(a);
  return {
    ok,
    format: color.format || format,
    r: Math.min(255, Math.max(rgb.r, 0)),
    g: Math.min(255, Math.max(rgb.g, 0)),
    b: Math.min(255, Math.max(rgb.b, 0)),
    a
  };
}
var CSS_INTEGER = "[-\\+]?\\d+%?";
var CSS_NUMBER = "[-\\+]?\\d*\\.\\d+%?";
var CSS_UNIT = `(?:${CSS_NUMBER})|(?:${CSS_INTEGER})`;
var PERMISSIVE_MATCH3 = `[\\s|\\(]+(${CSS_UNIT})[,|\\s]+(${CSS_UNIT})[,|\\s]+(${CSS_UNIT})\\s*\\)?`;
var PERMISSIVE_MATCH4 = `[\\s|\\(]+(${CSS_UNIT})[,|\\s]+(${CSS_UNIT})[,|\\s]+(${CSS_UNIT})[,|\\s]+(${CSS_UNIT})\\s*\\)?`;
var matchers = {
  CSS_UNIT: new RegExp(CSS_UNIT),
  rgb: new RegExp("rgb" + PERMISSIVE_MATCH3),
  rgba: new RegExp("rgba" + PERMISSIVE_MATCH4),
  hsl: new RegExp("hsl" + PERMISSIVE_MATCH3),
  hsla: new RegExp("hsla" + PERMISSIVE_MATCH4),
  hsv: new RegExp("hsv" + PERMISSIVE_MATCH3),
  hsva: new RegExp("hsva" + PERMISSIVE_MATCH4),
  hex3: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,
  hex6: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,
  hex4: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,
  hex8: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/
};
function stringInputToObject(color) {
  color = color.trim().toLowerCase();
  if (color.length === 0) {
    return false;
  }
  let named = false;
  if (names[color]) {
    color = names[color];
    named = true;
  } else if (color === "transparent") {
    return {
      r: 0,
      g: 0,
      b: 0,
      a: 0,
      format: "name"
    };
  }
  let match = matchers.rgb.exec(color);
  if (match) {
    return {
      r: match[1],
      g: match[2],
      b: match[3]
    };
  }
  match = matchers.rgba.exec(color);
  if (match) {
    return {
      r: match[1],
      g: match[2],
      b: match[3],
      a: match[4]
    };
  }
  match = matchers.hsl.exec(color);
  if (match) {
    return {
      h: match[1],
      s: match[2],
      l: match[3]
    };
  }
  match = matchers.hsla.exec(color);
  if (match) {
    return {
      h: match[1],
      s: match[2],
      l: match[3],
      a: match[4]
    };
  }
  match = matchers.hsv.exec(color);
  if (match) {
    return {
      h: match[1],
      s: match[2],
      v: match[3]
    };
  }
  match = matchers.hsva.exec(color);
  if (match) {
    return {
      h: match[1],
      s: match[2],
      v: match[3],
      a: match[4]
    };
  }
  match = matchers.hex8.exec(color);
  if (match) {
    return {
      r: parseIntFromHex(match[1]),
      g: parseIntFromHex(match[2]),
      b: parseIntFromHex(match[3]),
      a: convertHexToDecimal(match[4]),
      format: named ? "name" : "hex8"
    };
  }
  match = matchers.hex6.exec(color);
  if (match) {
    return {
      r: parseIntFromHex(match[1]),
      g: parseIntFromHex(match[2]),
      b: parseIntFromHex(match[3]),
      format: named ? "name" : "hex"
    };
  }
  match = matchers.hex4.exec(color);
  if (match) {
    return {
      r: parseIntFromHex(match[1] + match[1]),
      g: parseIntFromHex(match[2] + match[2]),
      b: parseIntFromHex(match[3] + match[3]),
      a: convertHexToDecimal(match[4] + match[4]),
      format: named ? "name" : "hex8"
    };
  }
  match = matchers.hex3.exec(color);
  if (match) {
    return {
      r: parseIntFromHex(match[1] + match[1]),
      g: parseIntFromHex(match[2] + match[2]),
      b: parseIntFromHex(match[3] + match[3]),
      format: named ? "name" : "hex"
    };
  }
  return false;
}
function isValidCSSUnit(color) {
  return Boolean(matchers.CSS_UNIT.exec(String(color)));
}

// node_modules/tds-ui/fesm2022/tds-ui-tag.mjs
var _c0 = ["*"];
function TDSTagComponent_Conditional_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = ɵɵgetCurrentView();
    ɵɵelementStart(0, "span", 1);
    ɵɵlistener("click", function TDSTagComponent_Conditional_1_Template_span_click_0_listener($event) {
      ɵɵrestoreView(_r1);
      const ctx_r1 = ɵɵnextContext();
      return ɵɵresetView(ctx_r1.onClickClose($event));
    });
    ɵɵelementEnd();
  }
}
function TDSTagColorComponent_Conditional_0_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "span", 1, 0);
    ɵɵelement(2, "span");
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext();
    ɵɵadvance(2);
    ɵɵclassMap(ctx_r0.tdsIcon);
    ɵɵstyleProp("color", ctx_r0.iconColor ? ctx_r0.iconColor : "unset");
  }
}
var TDSTagComponent = class _TDSTagComponent {
  constructor(_cdr, renderer, elementRef, ngZone) {
    this._cdr = _cdr;
    this.renderer = renderer;
    this.elementRef = elementRef;
    this.ngZone = ngZone;
    this.mode = "default";
    this.type = "default";
    this.status = "secondary";
    this.checked = false;
    this.size = "md";
    this.disabled = false;
    this.tdsTheme = "default";
    this.rounded = null;
    this.close = new EventEmitter();
    this.checkedChange = new EventEmitter();
    this.cssClass = "";
    this.destroy$ = new import_rxjs.Subject();
  }
  ngOnInit() {
    this.ngZone.runOutsideAngular(() => {
      (0, import_rxjs.fromEvent)(this.elementRef.nativeElement, "click").pipe((0, import_operators.takeUntil)(this.destroy$)).subscribe(() => {
        this.ngZone.run(() => this.updateCheckedStatus());
      });
    });
  }
  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
  ngOnChanges(changes) {
    this._cdr.markForCheck();
  }
  onClickClose(e) {
    if (this.mode === "closeable" && !this.disabled) {
      this.close.emit(e);
      if (!e.defaultPrevented) {
        this.renderer.removeChild(this.renderer.parentNode(this.elementRef.nativeElement), this.elementRef.nativeElement);
      }
    }
  }
  updateCheckedStatus() {
    if (this.mode === "checkable" && !this.disabled) {
      this.checked = !this.checked;
      this.checkedChange.emit(this.checked);
    }
  }
  static {
    this.ɵfac = function TDSTagComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TDSTagComponent)(ɵɵdirectiveInject(ChangeDetectorRef), ɵɵdirectiveInject(Renderer2), ɵɵdirectiveInject(ElementRef), ɵɵdirectiveInject(NgZone));
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _TDSTagComponent,
      selectors: [["tds-tag"]],
      hostVars: 50,
      hostBindings: function TDSTagComponent_HostBindings(rf, ctx) {
        if (rf & 2) {
          ɵɵstyleMap(ctx.tdsStyle);
          ɵɵclassMap(ctx.rounded);
          ɵɵclassProp("tds-tag-mode-default", ctx.mode === "default")("tds-tag-mode-closeable", ctx.mode === "closeable")("tds-tag-mode-checkable", ctx.mode === "checkable")("tds-tag-mode-checkable-checked", ctx.mode === "checkable" && ctx.checked)("tds-tag-secondary", ctx.status === "secondary")("tds-tag-primary", ctx.status === "primary")("tds-tag-success", ctx.status === "success")("tds-tag-info", ctx.status === "info")("tds-tag-error", ctx.status === "error")("tds-tag-warning", ctx.status === "warning")("tds-tag-neutral", ctx.status === "neutral")("tds-tag-custom", ctx.status === "custom")("tds-tag-default", ctx.status === "default")("tds-tag-type-default", ctx.type === "default")("tds-tag-type-outline", ctx.type === "outline")("tds-tag-size-sm", ctx.size === "sm")("tds-tag-size-md", ctx.size === "md")("tds-tag-size-lg", ctx.size === "lg")("tds-tag-size-xl", ctx.size === "xl")("tds-tag-disabled", ctx.disabled)("tds-tag-theme-default", ctx.tdsTheme === "default")("tds-tag-theme-light", ctx.tdsTheme === "light")("tds-tag-theme-dark", ctx.tdsTheme === "dark");
        }
      },
      inputs: {
        mode: "mode",
        type: "type",
        status: "status",
        tdsStyle: "tdsStyle",
        checked: "checked",
        size: "size",
        disabled: "disabled",
        tdsTheme: "tdsTheme",
        rounded: "rounded"
      },
      outputs: {
        close: "close",
        checkedChange: "checkedChange"
      },
      exportAs: ["tdsTag"],
      standalone: true,
      features: [ɵɵNgOnChangesFeature, ɵɵStandaloneFeature],
      ngContentSelectors: _c0,
      decls: 2,
      vars: 1,
      consts: [["tabindex", "-1", 1, "tds-tag-close-icon", "tdsi-close-fill"], ["tabindex", "-1", 1, "tds-tag-close-icon", "tdsi-close-fill", 3, "click"]],
      template: function TDSTagComponent_Template(rf, ctx) {
        if (rf & 1) {
          ɵɵprojectionDef();
          ɵɵprojection(0);
          ɵɵtemplate(1, TDSTagComponent_Conditional_1_Template, 1, 0, "span", 0);
        }
        if (rf & 2) {
          ɵɵadvance();
          ɵɵconditional(ctx.mode === "closeable" ? 1 : -1);
        }
      },
      encapsulation: 2,
      changeDetection: 0
    });
  }
};
__decorate([InputBoolean()], TDSTagComponent.prototype, "checked", void 0);
__decorate([InputBoolean()], TDSTagComponent.prototype, "disabled", void 0);
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TDSTagComponent, [{
    type: Component,
    args: [{
      selector: "tds-tag",
      exportAs: "tdsTag",
      template: `
    <ng-content></ng-content>
    @if (mode === 'closeable') {
      <span class='tds-tag-close-icon tdsi-close-fill'
        tabindex="-1"
      (click)="onClickClose($event)"></span>
    }
    `,
      preserveWhitespaces: false,
      changeDetection: ChangeDetectionStrategy.OnPush,
      encapsulation: ViewEncapsulation$1.None,
      host: {
        "[class.tds-tag-mode-default]": 'mode === "default"',
        "[class.tds-tag-mode-closeable]": 'mode === "closeable"',
        "[class.tds-tag-mode-checkable]": 'mode === "checkable"',
        "[class.tds-tag-mode-checkable-checked]": 'mode === "checkable" && checked',
        "[class.tds-tag-secondary]": 'status === "secondary"',
        "[class.tds-tag-primary]": 'status === "primary"',
        "[class.tds-tag-success]": 'status === "success"',
        "[class.tds-tag-info]": 'status === "info"',
        "[class.tds-tag-error]": 'status === "error"',
        "[class.tds-tag-warning]": 'status === "warning"',
        "[class.tds-tag-neutral]": 'status === "neutral"',
        "[class.tds-tag-custom]": 'status === "custom"',
        "[class.tds-tag-default]": 'status === "default"',
        "[class.tds-tag-type-default]": 'type === "default"',
        "[class.tds-tag-type-outline]": 'type === "outline"',
        "[class.tds-tag-size-sm]": 'size === "sm"',
        "[class.tds-tag-size-md]": 'size === "md"',
        "[class.tds-tag-size-lg]": 'size === "lg"',
        "[class.tds-tag-size-xl]": 'size === "xl"',
        "[class.tds-tag-disabled]": "disabled",
        "[class.tds-tag-theme-default]": 'tdsTheme === "default"',
        "[class.tds-tag-theme-light]": 'tdsTheme === "light"',
        "[class.tds-tag-theme-dark]": 'tdsTheme === "dark"',
        "[style]": "tdsStyle!",
        "[class]": "rounded"
      },
      standalone: true
    }]
  }], () => [{
    type: ChangeDetectorRef
  }, {
    type: Renderer2
  }, {
    type: ElementRef
  }, {
    type: NgZone
  }], {
    mode: [{
      type: Input
    }],
    type: [{
      type: Input
    }],
    status: [{
      type: Input
    }],
    tdsStyle: [{
      type: Input
    }],
    checked: [{
      type: Input
    }],
    size: [{
      type: Input
    }],
    disabled: [{
      type: Input
    }],
    tdsTheme: [{
      type: Input
    }],
    rounded: [{
      type: Input
    }],
    close: [{
      type: Output
    }],
    checkedChange: [{
      type: Output
    }]
  });
})();
var TDSTagGenarateColorService = class _TDSTagGenarateColorService {
  constructor() {
    this._hueStep = 2;
    this._saturationStep = 0.16;
    this._saturationStep2 = 0.05;
    this._brightnessStep1 = 0.05;
    this._brightnessStep2 = 0.15;
    this._lightColorCount = 5;
    this._darkColorCount = 4;
    this.darkColorMap = [{
      index: 7,
      opacity: 0.15
    }, {
      index: 6,
      opacity: 0.25
    }, {
      index: 5,
      opacity: 0.3
    }, {
      index: 5,
      opacity: 0.45
    }, {
      index: 5,
      opacity: 0.65
    }, {
      index: 5,
      opacity: 0.85
    }, {
      index: 4,
      opacity: 0.9
    }, {
      index: 3,
      opacity: 0.95
    }, {
      index: 2,
      opacity: 0.97
    }, {
      index: 1,
      opacity: 0.98
    }];
  }
  get hueStep() {
    return this._hueStep;
  }
  set hueStep(hueStep) {
    this._hueStep = toNumber(hueStep);
  }
  get saturationStep() {
    return this._saturationStep;
  }
  set saturationStep(saturationStep) {
    this._saturationStep = toNumber(saturationStep);
  }
  get saturationStep2() {
    return this._saturationStep2;
  }
  set saturationStep2(saturationStep2) {
    this._saturationStep2 = toNumber(saturationStep2);
  }
  get brightnessStep1() {
    return this._brightnessStep1;
  }
  set brightnessStep1(brightnessStep1) {
    this._brightnessStep1 = toNumber(brightnessStep1);
  }
  get brightnessStep2() {
    return this._brightnessStep2;
  }
  set brightnessStep2(brightnessStep2) {
    this._brightnessStep2 = toNumber(brightnessStep2);
  }
  get lightColorCount() {
    return this._lightColorCount;
  }
  set lightColorCount(lightColorCount) {
    this._lightColorCount = lightColorCount;
  }
  get darkColorCount() {
    return this._darkColorCount;
  }
  set darkColorCount(darkColorCount) {
    this._darkColorCount = darkColorCount;
  }
  // Wrapper function ported from TinyColor.prototype.toHsv
  // Keep it here because of `hsv.h * 360`
  toHsv({
    r,
    g,
    b
  }) {
    const hsv = rgbToHsv(r, g, b);
    return {
      h: hsv.h * 360,
      s: hsv.s,
      v: hsv.v
    };
  }
  // Wrapper function ported from TinyColor.prototype.toHexString
  // Keep it here because of the prefix `#`
  toHex({
    r,
    g,
    b
  }) {
    return `#${rgbToHex(r, g, b, false)}`;
  }
  mix(rgb1, rgb2, amount) {
    const p = amount / 100;
    const rgb = {
      r: (rgb2.r - rgb1.r) * p + rgb1.r,
      g: (rgb2.g - rgb1.g) * p + rgb1.g,
      b: (rgb2.b - rgb1.b) * p + rgb1.b
    };
    return rgb;
  }
  getHue(hsv, i, light) {
    let hue;
    if (Math.round(hsv.h) >= 60 && Math.round(hsv.h) <= 240) {
      hue = light ? Math.round(hsv.h) - this.hueStep * i : Math.round(hsv.h) + this.hueStep * i;
    } else {
      hue = light ? Math.round(hsv.h) + this.hueStep * i : Math.round(hsv.h) - this.hueStep * i;
    }
    if (hue < 0) {
      hue += 360;
    } else if (hue >= 360) {
      hue -= 360;
    }
    return hue;
  }
  getSaturation(hsv, i, light) {
    if (hsv.h === 0 && hsv.s === 0) {
      return hsv.s;
    }
    let saturation;
    if (light) {
      saturation = hsv.s - this.saturationStep * i;
    } else if (i === this.darkColorCount) {
      saturation = hsv.s + this.saturationStep;
    } else {
      saturation = hsv.s + this.saturationStep2 * i;
    }
    if (saturation > 1) {
      saturation = 1;
    }
    if (light && i === this.lightColorCount && saturation > 0.1) {
      saturation = 0.1;
    }
    if (saturation < 0.06) {
      saturation = 0.06;
    }
    return Number(saturation.toFixed(2));
  }
  getValue(hsv, i, light) {
    let value;
    if (light) {
      value = hsv.v + this.brightnessStep1 * i;
    } else {
      value = hsv.v - this.brightnessStep2 * i;
    }
    if (value > 1) {
      value = 1;
    }
    return Number(value.toFixed(2));
  }
  generate(color, opts = {}) {
    const patterns = [];
    const pColor = inputToRGB(color);
    for (let i = this.lightColorCount; i > 0; i -= 1) {
      const hsv = this.toHsv(pColor);
      const colorString = this.toHex(inputToRGB({
        h: this.getHue(hsv, i, true),
        s: this.getSaturation(hsv, i, true),
        v: this.getValue(hsv, i, true)
      }));
      patterns.push(colorString);
    }
    patterns.push(this.toHex(pColor));
    for (let i = 1; i <= this.darkColorCount; i += 1) {
      const hsv = this.toHsv(pColor);
      const colorString = this.toHex(inputToRGB({
        h: this.getHue(hsv, i),
        s: this.getSaturation(hsv, i),
        v: this.getValue(hsv, i)
      }));
      patterns.push(colorString);
    }
    if (opts.theme === "dark") {
      return this.darkColorMap.map(({
        index,
        opacity
      }) => {
        const darkColorString = this.toHex(this.mix(inputToRGB(opts.backgroundColor), inputToRGB(patterns[index]), opacity * 100));
        return darkColorString;
      });
    }
    return patterns;
  }
  static {
    this.ɵfac = function TDSTagGenarateColorService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TDSTagGenarateColorService)();
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _TDSTagGenarateColorService,
      factory: _TDSTagGenarateColorService.ɵfac
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TDSTagGenarateColorService, [{
    type: Injectable
  }], () => [], null);
})();
var TDS_CONFIG_MODULE_NAME = "tagMultiColor";
var TDSTagColorComponent = class _TDSTagColorComponent {
  constructor(element, genarateColorSevice, cdr, renderer, destroy$, tdsConfigService) {
    this.element = element;
    this.genarateColorSevice = genarateColorSevice;
    this.cdr = cdr;
    this.renderer = renderer;
    this.destroy$ = destroy$;
    this.tdsConfigService = tdsConfigService;
    this._tdsModuleName = TDS_CONFIG_MODULE_NAME;
    this.tdsTheme = "default";
    this.tdsColor = "";
    this.tdsBgColor = "100";
    this.tdsBorderColor = "100";
    this.tdsIconColor = "500";
    this.tdsSize = "md";
    this.listColor = [];
    this.rangeColor = ["50", "100", "200", "300", "400", "500", "600", "700", "800", "900"];
  }
  ngOnInit() {
    this.genarate();
    this.setUpColor();
    this.tdsConfigService.getConfigChangeEventForComponent(TDS_CONFIG_MODULE_NAME).pipe((0, import_operators.takeUntil)(this.destroy$)).subscribe(() => {
      this.genarate();
      this.setUpColor();
      this.cdr.markForCheck();
    });
  }
  ngOnChanges(changes) {
    const {
      tdsTheme,
      tdsColor
    } = changes;
    if (tdsTheme || tdsColor) {
      this.genarate();
    }
    this.setUpColor();
    this.cdr.markForCheck();
  }
  genarate() {
    this.listColor = this.genarateColorSevice.generate(this.tdsColor, {
      theme: this.tdsTheme,
      backgroundColor: "#141414"
    });
  }
  setUpColor() {
    if (TDSHelperArray.hasListValue(this.listColor)) {
      this.bgColor = this.listColor[this.rangeColor.findIndex((f) => this.tdsBgColor === f) || 0];
      this.borderColor = this.listColor[this.rangeColor.findIndex((f) => this.tdsBorderColor === f) || 0];
      this.iconColor = this.listColor[this.rangeColor.findIndex((f) => this.tdsIconColor === f) || 0];
      this.renderer.setStyle(this.element.nativeElement, "border-color", this.borderColor ? this.borderColor : "transparent");
      this.renderer.setStyle(this.element.nativeElement, "background", this.bgColor);
      const brightness = this.getBrightness(inputToRGB(this.bgColor));
      this.textColor = brightness < 125 ? "white" : "unset";
    }
  }
  /**
  * Returns the perceived brightness of the color, from 0-255.
  */
  getBrightness({
    r,
    g,
    b
  }) {
    return (r * 299 + g * 587 + b * 114) / 1e3;
  }
  static {
    this.ɵfac = function TDSTagColorComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TDSTagColorComponent)(ɵɵdirectiveInject(ElementRef), ɵɵdirectiveInject(TDSTagGenarateColorService), ɵɵdirectiveInject(ChangeDetectorRef), ɵɵdirectiveInject(Renderer2), ɵɵdirectiveInject(TDSDestroyService), ɵɵdirectiveInject(TDSConfigService));
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _TDSTagColorComponent,
      selectors: [["tds-tag-multi-color"]],
      hostVars: 8,
      hostBindings: function TDSTagColorComponent_HostBindings(rf, ctx) {
        if (rf & 2) {
          ɵɵclassProp("tds-tag-size-sm", ctx.tdsSize === "sm")("tds-tag-size-md", ctx.tdsSize === "md")("tds-tag-size-lg", ctx.tdsSize === "lg")("tds-tag-size-xl", ctx.tdsSize === "xl");
        }
      },
      inputs: {
        tdsTheme: "tdsTheme",
        tdsColor: "tdsColor",
        tdsIcon: "tdsIcon",
        tdsBgColor: "tdsBgColor",
        tdsBorderColor: "tdsBorderColor",
        tdsIconColor: "tdsIconColor",
        tdsSize: "tdsSize"
      },
      exportAs: ["tdsTagMultiColor"],
      standalone: true,
      features: [ɵɵProvidersFeature([TDSTagGenarateColorService, TDSDestroyService]), ɵɵNgOnChangesFeature, ɵɵStandaloneFeature],
      ngContentSelectors: _c0,
      decls: 4,
      vars: 3,
      consts: [["icon", ""], [1, "tds-tag-multi-color-icon"], [1, "tds-tag-multi-color-text"]],
      template: function TDSTagColorComponent_Template(rf, ctx) {
        if (rf & 1) {
          ɵɵprojectionDef();
          ɵɵtemplate(0, TDSTagColorComponent_Conditional_0_Template, 3, 4, "span", 1);
          ɵɵelementStart(1, "span", 2);
          ɵɵprojection(2);
          ɵɵelement(3, "span");
          ɵɵelementEnd();
        }
        if (rf & 2) {
          ɵɵconditional(ctx.tdsIcon ? 0 : -1);
          ɵɵadvance();
          ɵɵstyleProp("color", ctx.textColor ? ctx.textColor : "unset");
        }
      },
      encapsulation: 2,
      changeDetection: 0
    });
  }
};
__decorate([WithConfig()], TDSTagColorComponent.prototype, "tdsTheme", void 0);
__decorate([WithConfig()], TDSTagColorComponent.prototype, "tdsColor", void 0);
__decorate([WithConfig()], TDSTagColorComponent.prototype, "tdsIcon", void 0);
__decorate([WithConfig()], TDSTagColorComponent.prototype, "tdsBgColor", void 0);
__decorate([WithConfig()], TDSTagColorComponent.prototype, "tdsBorderColor", void 0);
__decorate([WithConfig()], TDSTagColorComponent.prototype, "tdsIconColor", void 0);
__decorate([WithConfig()], TDSTagColorComponent.prototype, "tdsSize", void 0);
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TDSTagColorComponent, [{
    type: Component,
    args: [{
      selector: "tds-tag-multi-color",
      exportAs: "tdsTagMultiColor",
      template: `
      @if (tdsIcon) {
        <span class='tds-tag-multi-color-icon' #icon>
          <span [class]='tdsIcon' [style.color]="iconColor ? iconColor : 'unset'"></span>
        </span>
      }
      <span class='tds-tag-multi-color-text'  [style.color]="textColor ? textColor : 'unset'">
        <ng-content></ng-content>
        <span>
      `,
      preserveWhitespaces: false,
      changeDetection: ChangeDetectionStrategy.OnPush,
      encapsulation: ViewEncapsulation$1.None,
      host: {
        "[class.tds-tag-size-sm]": 'tdsSize === "sm"',
        "[class.tds-tag-size-md]": 'tdsSize === "md"',
        "[class.tds-tag-size-lg]": 'tdsSize === "lg"',
        "[class.tds-tag-size-xl]": 'tdsSize === "xl"'
      },
      providers: [TDSTagGenarateColorService, TDSDestroyService],
      standalone: true
    }]
  }], () => [{
    type: ElementRef
  }, {
    type: TDSTagGenarateColorService
  }, {
    type: ChangeDetectorRef
  }, {
    type: Renderer2
  }, {
    type: TDSDestroyService
  }, {
    type: TDSConfigService
  }], {
    tdsTheme: [{
      type: Input
    }],
    tdsColor: [{
      type: Input
    }],
    tdsIcon: [{
      type: Input
    }],
    tdsBgColor: [{
      type: Input
    }],
    tdsBorderColor: [{
      type: Input
    }],
    tdsIconColor: [{
      type: Input
    }],
    tdsSize: [{
      type: Input
    }]
  });
})();
var TDSTagModule = class _TDSTagModule {
  static {
    this.ɵfac = function TDSTagModule_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TDSTagModule)();
    };
  }
  static {
    this.ɵmod = ɵɵdefineNgModule({
      type: _TDSTagModule,
      imports: [TDSTagComponent, TDSTagColorComponent],
      exports: [TDSTagComponent, TDSTagColorComponent]
    });
  }
  static {
    this.ɵinj = ɵɵdefineInjector({});
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TDSTagModule, [{
    type: NgModule,
    args: [{
      imports: [TDSTagComponent, TDSTagColorComponent],
      exports: [TDSTagComponent, TDSTagColorComponent]
    }]
  }], null, null);
})();

// node_modules/tds-ui/fesm2022/tds-ui-badges.mjs
var import_rxjs2 = __toESM(require_cjs(), 1);
var import_operators2 = __toESM(require_operators(), 1);

// node_modules/tds-ui/fesm2022/tds-ui-core-no-animation.mjs
var DISABLED_CLASSNAME = "tds-animate-disabled";
var TDSNoAnimationDirective = class _TDSNoAnimationDirective {
  constructor(element, renderer, animationType) {
    this.element = element;
    this.renderer = renderer;
    this.animationType = animationType;
    this.noAnimation = false;
  }
  ngOnChanges() {
    this.updateClass();
  }
  ngAfterViewInit() {
    this.updateClass();
  }
  updateClass() {
    const element = coerceElement(this.element);
    if (!element) {
      return;
    }
    if (this.noAnimation || this.animationType === "NoopAnimations") {
      this.renderer.addClass(element, DISABLED_CLASSNAME);
    } else {
      this.renderer.removeClass(element, DISABLED_CLASSNAME);
    }
  }
  static {
    this.ɵfac = function TDSNoAnimationDirective_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TDSNoAnimationDirective)(ɵɵdirectiveInject(ElementRef), ɵɵdirectiveInject(Renderer2), ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));
    };
  }
  static {
    this.ɵdir = ɵɵdefineDirective({
      type: _TDSNoAnimationDirective,
      selectors: [["", "noAnimation", ""]],
      inputs: {
        noAnimation: "noAnimation"
      },
      exportAs: ["noAnimation"],
      standalone: true,
      features: [ɵɵNgOnChangesFeature]
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TDSNoAnimationDirective, [{
    type: Directive,
    args: [{
      selector: "[noAnimation]",
      exportAs: "noAnimation",
      standalone: true
    }]
  }], () => [{
    type: ElementRef
  }, {
    type: Renderer2
  }, {
    type: void 0,
    decorators: [{
      type: Optional
    }, {
      type: Inject,
      args: [ANIMATION_MODULE_TYPE]
    }]
  }], {
    noAnimation: [{
      type: Input
    }]
  });
})();
var TDSNoAnimationModule = class _TDSNoAnimationModule {
  static {
    this.ɵfac = function TDSNoAnimationModule_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TDSNoAnimationModule)();
    };
  }
  static {
    this.ɵmod = ɵɵdefineNgModule({
      type: _TDSNoAnimationModule,
      imports: [TDSNoAnimationDirective],
      exports: [TDSNoAnimationDirective]
    });
  }
  static {
    this.ɵinj = ɵɵdefineInjector({});
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TDSNoAnimationModule, [{
    type: NgModule,
    args: [{
      imports: [TDSNoAnimationDirective],
      exports: [TDSNoAnimationDirective]
    }]
  }], null, null);
})();

// node_modules/tds-ui/fesm2022/tds-ui-badges.mjs
function TDSBadgeSupComponent_Conditional_0_For_1_Conditional_1_For_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "p", 3);
    ɵɵtext(1);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const p_r1 = ctx.$implicit;
    const ɵ$index_2_r2 = ɵɵnextContext(2).$index;
    const ctx_r2 = ɵɵnextContext(2);
    ɵɵclassProp("current", p_r1 === ctx_r2.countArray[ɵ$index_2_r2]);
    ɵɵadvance();
    ɵɵtextInterpolate1(" ", p_r1, " ");
  }
}
function TDSBadgeSupComponent_Conditional_0_For_1_Conditional_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵrepeaterCreate(0, TDSBadgeSupComponent_Conditional_0_For_1_Conditional_1_For_1_Template, 2, 3, "p", 2, ɵɵrepeaterTrackByIdentity);
  }
  if (rf & 2) {
    const ctx_r2 = ɵɵnextContext(3);
    ɵɵrepeater(ctx_r2.countSingleArray);
  }
}
function TDSBadgeSupComponent_Conditional_0_For_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "span", 1);
    ɵɵtemplate(1, TDSBadgeSupComponent_Conditional_0_For_1_Conditional_1_Template, 2, 0);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ɵ$index_2_r2 = ctx.$index;
    const ctx_r2 = ɵɵnextContext(2);
    ɵɵstyleProp("transform", "translateY(" + -ctx_r2.countArray[ɵ$index_2_r2] * 100 + "%)");
    ɵɵproperty("noAnimation", ctx_r2.noAnimation);
    ɵɵadvance();
    ɵɵconditional(!ctx_r2.dot && ctx_r2.countArray[ɵ$index_2_r2] !== void 0 ? 1 : -1);
  }
}
function TDSBadgeSupComponent_Conditional_0_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵrepeaterCreate(0, TDSBadgeSupComponent_Conditional_0_For_1_Template, 2, 4, "span", 0, ɵɵrepeaterTrackByIdentity);
  }
  if (rf & 2) {
    const ctx_r2 = ɵɵnextContext();
    ɵɵrepeater(ctx_r2.maxNumberArray);
  }
}
function TDSBadgeSupComponent_Conditional_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtext(0);
  }
  if (rf & 2) {
    const ctx_r2 = ɵɵnextContext();
    ɵɵtextInterpolate1(" ", ctx_r2.overflowCount, "+ ");
  }
}
var _c02 = ["*"];
function TDSBadgeComponent_Conditional_0_Conditional_1_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainerStart(0);
    ɵɵtext(1);
    ɵɵelementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(3);
    ɵɵadvance();
    ɵɵtextInterpolate(ctx_r0.text);
  }
}
function TDSBadgeComponent_Conditional_0_Conditional_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "span", 2);
    ɵɵtemplate(1, TDSBadgeComponent_Conditional_0_Conditional_1_ng_container_1_Template, 2, 1, "ng-container", 0);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(2);
    ɵɵadvance();
    ɵɵproperty("tdsStringTemplateOutlet", ctx_r0.text);
  }
}
function TDSBadgeComponent_Conditional_0_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelement(0, "span", 1);
    ɵɵtemplate(1, TDSBadgeComponent_Conditional_0_Conditional_1_Template, 2, 1, "span", 2);
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext();
    ɵɵstyleProp("background", !ctx_r0.presetColor && ctx_r0.color);
    ɵɵproperty("ngStyle", ctx_r0.tdsStyle);
    ɵɵadvance();
    ɵɵconditional(ctx_r0.text ? 1 : -1);
  }
}
function TDSBadgeComponent_ng_container_2_Conditional_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelement(0, "tds-badge-sup", 4);
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(2);
    ɵɵclassMap(ctx_r0.tdsClass);
    ɵɵproperty("offset", ctx_r0.offset)("title", ctx_r0.title)("tdsStyle", ctx_r0.tdsStyle)("dot", ctx_r0.dot)("size", ctx_r0.size)("overflowCount", ctx_r0.overflowCount)("disableAnimation", !!(ctx_r0.standalone || ctx_r0.status || ctx_r0.color || (ctx_r0.noAnimation == null ? null : ctx_r0.noAnimation.noAnimation)))("count", ctx_r0.count)("noAnimation", !!(ctx_r0.noAnimation == null ? null : ctx_r0.noAnimation.noAnimation))("standalone", ctx_r0.standalone)("placement", ctx_r0.placement);
  }
}
function TDSBadgeComponent_ng_container_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainerStart(0);
    ɵɵtemplate(1, TDSBadgeComponent_ng_container_2_Conditional_1_Template, 1, 13, "tds-badge-sup", 3);
    ɵɵelementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext();
    ɵɵadvance();
    ɵɵconditional(ctx_r0.showSup ? 1 : -1);
  }
}
function TDSRibbonComponent_ng_container_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainerStart(0);
    ɵɵtext(1);
    ɵɵelementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext();
    ɵɵadvance();
    ɵɵtextInterpolate(ctx_r0.text);
  }
}
var badgePresetColors = ["pink", "red", "yellow", "orange", "cyan", "green", "blue", "purple", "geekblue", "magenta", "volcano", "gold", "lime"];
var TDSBadgeSupComponent = class _TDSBadgeSupComponent {
  constructor() {
    this.tdsStyle = null;
    this.dot = false;
    this.overflowCount = 99;
    this.disableAnimation = false;
    this.noAnimation = false;
    this.standalone = false;
    this.size = "md";
    this.placement = "topRight";
    this._cls = "";
    this.maxNumberArray = [];
    this.countArray = [];
    this._count = 0;
    this.countSingleArray = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9];
  }
  generateMaxNumberArray() {
    this.maxNumberArray = this.overflowCount.toString().split("");
  }
  ngOnInit() {
    this.generateMaxNumberArray();
  }
  get cls() {
    return this._cls;
  }
  ngOnChanges(changes) {
    const {
      overflowCount,
      count,
      standalone,
      tdsStyle,
      tdsClass
    } = changes;
    if (count && typeof count.currentValue === "number") {
      this._count = Math.max(0, count.currentValue);
      this.countArray = this._count.toString().split("").map((item) => +item);
    }
    if (overflowCount) {
      this.generateMaxNumberArray();
    }
  }
  get styleCss() {
    let style = null;
    if (!this.standalone) {
      switch (this.placement) {
        case "topRight":
          style = {
            ["transform"]: "translate(50%,-50%)",
            ["transform-origin"]: "100% 0"
          };
          break;
        case "topLeft":
          style = {
            ["transform"]: "translate(-50%,-50%)",
            ["transform-origin"]: "0 0"
          };
          break;
        case "bottomLeft":
          style = {
            ["transform"]: "translate(-50%,50%)",
            ["transform-origin"]: "100% 0"
          };
          break;
        case "bottomRight":
          style = {
            ["transform"]: "translate(50%,50%)",
            ["transform-origin"]: "0 100%"
          };
          break;
        default:
          style = {
            ["transform"]: "translate(-50%,50%)",
            ["transform-origin"]: "100% 0"
          };
          break;
      }
    }
    if (!!this.tdsStyle) {
      if (style != null) return Object.assign({}, style, this.tdsStyle);
      return this.tdsStyle;
    }
    return style;
  }
  static {
    this.ɵfac = function TDSBadgeSupComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TDSBadgeSupComponent)();
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _TDSBadgeSupComponent,
      selectors: [["tds-badge-sup"]],
      hostAttrs: [1, "tds-scroll-number"],
      hostVars: 33,
      hostBindings: function TDSBadgeSupComponent_HostBindings(rf, ctx) {
        if (rf & 2) {
          ɵɵsyntheticHostProperty("@.disabled", ctx.disableAnimation)("@zoomBadgeMotion", void 0);
          ɵɵattribute("title", ctx.title === null ? "" : ctx.title || ctx.count);
          ɵɵstyleMap(ctx.styleCss);
          ɵɵstyleProp("right", ctx.offset && ctx.offset[0] ? -ctx.offset[0] : null, "px")("margin-top", ctx.offset && ctx.offset[1] ? ctx.offset[1] : null, "px");
          ɵɵclassProp("tds-badge-count", !ctx.dot)("tds-badge-sup-size-sm", ctx.size === "sm")("tds-badge-sup-size-md", ctx.size === "md")("tds-badge-sup-size-lg", ctx.size === "lg")("tds-badge-sup-size-xl", ctx.size === "xl")("tds-badge-dot", ctx.dot)("tds-badge-multiple-words", ctx.countArray.length >= 2)("tds-badge-sup-standalone", ctx.standalone)("tds-badge-sup-placement-top-left", ctx.placement === "topLeft")("tds-badge-sup-placement-top-right", ctx.placement === "topRight")("tds-badge-sup-placement-bottom-left", ctx.placement === "bottomLeft")("tds-badge-sup-placement-bottom-right", ctx.placement === "bottomRight");
        }
      },
      inputs: {
        offset: "offset",
        title: "title",
        tdsStyle: "tdsStyle",
        dot: "dot",
        overflowCount: "overflowCount",
        disableAnimation: "disableAnimation",
        count: "count",
        noAnimation: "noAnimation",
        standalone: "standalone",
        size: "size",
        placement: "placement"
      },
      exportAs: ["tdsBadgeSup"],
      standalone: true,
      features: [ɵɵNgOnChangesFeature, ɵɵStandaloneFeature],
      decls: 2,
      vars: 1,
      consts: [[1, "tds-scroll-number-only", 2, "transition", "all .3s cubic-bezier(.645,.045,.355,1)", 3, "noAnimation", "transform"], [1, "tds-scroll-number-only", 2, "transition", "all .3s cubic-bezier(.645,.045,.355,1)", 3, "noAnimation"], [1, "tds-scroll-number-only-unit", 3, "current"], [1, "tds-scroll-number-only-unit"]],
      template: function TDSBadgeSupComponent_Template(rf, ctx) {
        if (rf & 1) {
          ɵɵtemplate(0, TDSBadgeSupComponent_Conditional_0_Template, 2, 0)(1, TDSBadgeSupComponent_Conditional_1_Template, 1, 1);
        }
        if (rf & 2) {
          ɵɵconditional(ctx._count <= ctx.overflowCount ? 0 : 1);
        }
      },
      dependencies: [TDSNoAnimationDirective],
      encapsulation: 2,
      data: {
        animation: [zoomBadgeMotion]
      },
      changeDetection: 0
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TDSBadgeSupComponent, [{
    type: Component,
    args: [{
      selector: "tds-badge-sup",
      exportAs: "tdsBadgeSup",
      preserveWhitespaces: false,
      encapsulation: ViewEncapsulation$1.None,
      changeDetection: ChangeDetectionStrategy.OnPush,
      animations: [zoomBadgeMotion],
      standalone: true,
      imports: [TDSNoAnimationDirective],
      template: `
    @if (_count <= overflowCount) {
      @for (n of maxNumberArray; track n; let i = $index) {
        <span
          [noAnimation]="noAnimation"
          class="tds-scroll-number-only"
          [style.transform]="'translateY(' + -countArray[i] * 100 + '%)'"
          style="transition: all .3s cubic-bezier(.645,.045,.355,1)"
          >
          @if (!dot && countArray[i] !== undefined) {
            @for (p of countSingleArray; track p) {
              <p
                class="tds-scroll-number-only-unit"
                [class.current]="p === countArray[i]"
                >
                {{ p }}
              </p>
            }
          }
        </span>
      }
    } @else {
      {{ overflowCount }}+
    }
    `,
      host: {
        "[@.disabled]": `disableAnimation`,
        "[@zoomBadgeMotion]": "",
        "[attr.title]": `title === null ? '' : title || count`,
        "[style]": `styleCss`,
        "[style.right.px]": `offset && offset[0] ? -offset[0] : null`,
        "[style.margin-top.px]": `offset && offset[1] ? offset[1] : null`,
        "[class.tds-badge-count]": `!dot`,
        "[class.tds-badge-sup-size-sm]": `size === 'sm'`,
        "[class.tds-badge-sup-size-md]": `size === 'md'`,
        "[class.tds-badge-sup-size-lg]": `size === 'lg'`,
        "[class.tds-badge-sup-size-xl]": `size === 'xl'`,
        "[class.tds-badge-dot]": `dot`,
        "[class.tds-badge-multiple-words]": `countArray.length >= 2`,
        "[class.tds-badge-sup-standalone]": `standalone`,
        "[class.tds-badge-sup-placement-top-left]": `placement === 'topLeft'`,
        "[class.tds-badge-sup-placement-top-right]": `placement === 'topRight'`,
        "[class.tds-badge-sup-placement-bottom-left]": `placement === 'bottomLeft'`,
        "[class.tds-badge-sup-placement-bottom-right]": `placement === 'bottomRight'`,
        "class": "tds-scroll-number"
        // '[class]': "cls"
      }
    }]
  }], () => [], {
    offset: [{
      type: Input
    }],
    title: [{
      type: Input
    }],
    tdsStyle: [{
      type: Input
    }],
    dot: [{
      type: Input
    }],
    overflowCount: [{
      type: Input
    }],
    disableAnimation: [{
      type: Input
    }],
    count: [{
      type: Input
    }],
    noAnimation: [{
      type: Input
    }],
    standalone: [{
      type: Input
    }],
    size: [{
      type: Input
    }],
    placement: [{
      type: Input
    }]
  });
})();
var TDS_CONFIG_MODULE_NAME2 = "badge";
var TDSBadgeComponent = class _TDSBadgeComponent {
  constructor(tdsConfigService, renderer, cdr, elementRef, directionality) {
    this.tdsConfigService = tdsConfigService;
    this.renderer = renderer;
    this.cdr = cdr;
    this.elementRef = elementRef;
    this.directionality = directionality;
    this._tdsModuleName = TDS_CONFIG_MODULE_NAME2;
    this.showSup = false;
    this.presetColor = null;
    this.dir = "ltr";
    this.destroy$ = new import_rxjs2.Subject();
    this.showZero = false;
    this.showDot = true;
    this.standalone = false;
    this.dot = false;
    this.overflowCount = 99;
    this.color = void 0;
    this.tdsStyle = null;
    this.text = null;
    this.tdsClass = "";
    this.tdsTheme = "default";
    this.size = "md";
    this.placement = "topRight";
    this.noAnimation = inject(TDSNoAnimationDirective, {
      host: true,
      optional: true
    });
  }
  ngOnInit() {
    this.directionality.change?.pipe((0, import_operators2.takeUntil)(this.destroy$)).subscribe((direction) => {
      this.dir = direction;
      this.prepareBadgeForRtl();
      this.cdr.detectChanges();
    });
    this.dir = this.directionality.value;
    this.prepareBadgeForRtl();
  }
  ngOnChanges(changes) {
    const {
      color,
      showDot,
      dot,
      count,
      showZero
    } = changes;
    if (color) {
      this.presetColor = this.color && badgePresetColors.indexOf(this.color) !== -1 ? this.color : null;
    }
    if (showDot || dot || count || showZero) {
      this.showSup = this.showDot && this.dot || typeof this.count === "number" && this.count > 0 || typeof this.count === "number" && this.count <= 0 && this.showZero;
    }
  }
  prepareBadgeForRtl() {
    if (this.isRtlLayout) {
      this.renderer.addClass(this.elementRef.nativeElement, "tds-badge-rtl");
    } else {
      this.renderer.removeClass(this.elementRef.nativeElement, "tds-badge-rtl");
    }
  }
  get isRtlLayout() {
    return this.dir === "rtl";
  }
  get colorStatus() {
    if (!!this.status) {
      return this.convertClass();
    }
    return null;
  }
  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
  convertClass() {
    let result = null;
    switch (this.status) {
      case "primary":
        result = "bg-primary-1 dark:bg-d-primary-1";
        break;
      case "secondary":
        result = "bg-neutral-1-400 dark:bg-d-neutral-1-400";
        break;
      case "success":
        result = "bg-success-400 dark:bg-d-success-400";
        break;
      case "info":
        result = "bg-info-400 dark:bg-d-info-400";
        break;
      case "warning":
        result = "bg-warning-400 dark:bg-d-warning-400";
        break;
      case "error":
        result = "bg-error-400 dark:bg-d-error-400";
        break;
      default:
        result = this.status;
        break;
    }
    return result;
  }
  static {
    this.ɵfac = function TDSBadgeComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TDSBadgeComponent)(ɵɵdirectiveInject(TDSConfigService), ɵɵdirectiveInject(Renderer2), ɵɵdirectiveInject(ChangeDetectorRef), ɵɵdirectiveInject(ElementRef), ɵɵdirectiveInject(Directionality));
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _TDSBadgeComponent,
      selectors: [["tds-badge"]],
      hostAttrs: [1, "tds-badge"],
      hostVars: 30,
      hostBindings: function TDSBadgeComponent_HostBindings(rf, ctx) {
        if (rf & 2) {
          ɵɵclassProp("tds-badge-status", ctx.status)("tds-badge-not-a-wrapper", !!(ctx.standalone || ctx.status || ctx.color))("tds-badge-size-sm", ctx.size === "sm")("tds-badge-size-md", ctx.size === "md")("tds-badge-size-lg", ctx.size === "lg")("tds-badge-size-xl", ctx.size === "xl")("tds-badge-status-primary", ctx.status == "primary")("tds-badge-status-secondary", ctx.status == "secondary")("tds-badge-status-success", ctx.status == "success")("tds-badge-status-info", ctx.status == "info")("tds-badge-status-warning", ctx.status == "warning")("tds-badge-status-error", ctx.status == "error")("tds-badge-theme-default", ctx.tdsTheme === "default")("tds-badge-theme-light", ctx.tdsTheme === "light")("tds-badge-theme-dark", ctx.tdsTheme === "dark");
        }
      },
      inputs: {
        showZero: "showZero",
        showDot: "showDot",
        standalone: "standalone",
        dot: "dot",
        overflowCount: "overflowCount",
        color: "color",
        tdsStyle: "tdsStyle",
        text: "text",
        title: "title",
        status: "status",
        count: "count",
        offset: "offset",
        tdsClass: "tdsClass",
        tdsTheme: "tdsTheme",
        size: "size",
        placement: "placement"
      },
      exportAs: ["tdsBadge"],
      standalone: true,
      features: [ɵɵNgOnChangesFeature, ɵɵStandaloneFeature],
      ngContentSelectors: _c02,
      decls: 3,
      vars: 2,
      consts: [[4, "tdsStringTemplateOutlet"], [1, "tds-badge-status-dot", 3, "ngStyle"], [1, "tds-badge-status-text"], [3, "offset", "title", "tdsStyle", "dot", "size", "overflowCount", "disableAnimation", "count", "noAnimation", "standalone", "class", "placement"], [3, "offset", "title", "tdsStyle", "dot", "size", "overflowCount", "disableAnimation", "count", "noAnimation", "standalone", "placement"]],
      template: function TDSBadgeComponent_Template(rf, ctx) {
        if (rf & 1) {
          ɵɵprojectionDef();
          ɵɵtemplate(0, TDSBadgeComponent_Conditional_0_Template, 2, 4);
          ɵɵprojection(1);
          ɵɵtemplate(2, TDSBadgeComponent_ng_container_2_Template, 2, 1, "ng-container", 0);
        }
        if (rf & 2) {
          ɵɵconditional((ctx.status || ctx.color && !ctx.standalone) && !ctx.dot ? 0 : -1);
          ɵɵadvance(2);
          ɵɵproperty("tdsStringTemplateOutlet", ctx.count);
        }
      },
      dependencies: [NgStyle, TDSBadgeSupComponent, TDSOutletModule, TDSStringTemplateOutletDirective],
      encapsulation: 2,
      data: {
        animation: [zoomBadgeMotion]
      },
      changeDetection: 0
    });
  }
};
__decorate([InputBoolean()], TDSBadgeComponent.prototype, "showZero", void 0);
__decorate([InputBoolean()], TDSBadgeComponent.prototype, "showDot", void 0);
__decorate([InputBoolean()], TDSBadgeComponent.prototype, "standalone", void 0);
__decorate([InputBoolean()], TDSBadgeComponent.prototype, "dot", void 0);
__decorate([WithConfig()], TDSBadgeComponent.prototype, "overflowCount", void 0);
__decorate([WithConfig()], TDSBadgeComponent.prototype, "color", void 0);
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TDSBadgeComponent, [{
    type: Component,
    args: [{
      selector: "tds-badge",
      exportAs: "tdsBadge",
      preserveWhitespaces: false,
      encapsulation: ViewEncapsulation$1.None,
      changeDetection: ChangeDetectionStrategy.OnPush,
      animations: [zoomBadgeMotion],
      standalone: true,
      imports: [NgStyle, TDSBadgeSupComponent, TDSOutletModule],
      template: `
@if ((status || (color && !standalone)) && !dot) {
  <span
    class="tds-badge-status-dot"
    [style.background]="!presetColor && color"
    [ngStyle]="tdsStyle"
  ></span>
  @if (text) {
    <span class="tds-badge-status-text">
      <ng-container *tdsStringTemplateOutlet="text">{{ text }}</ng-container>
    </span>
  }
}
<ng-content></ng-content>
<ng-container *tdsStringTemplateOutlet="count">
  @if (showSup) {
    <tds-badge-sup
      [offset]="offset"
      [title]="title"
      [tdsStyle]="tdsStyle"
      [dot]="dot"
      [size]="size"
      [overflowCount]="overflowCount"
      [disableAnimation]="!!(standalone || status || color || noAnimation?.noAnimation)"
      [count]="count"
      [noAnimation]="!!noAnimation?.noAnimation"
      [standalone]="standalone"
      [class]="tdsClass"
      [placement]="placement"
    ></tds-badge-sup>
  }
</ng-container>



`,
      host: {
        "[class.tds-badge-status]": "status",
        "[class.tds-badge-not-a-wrapper]": "!!(standalone || status || color)",
        "[class.tds-badge-size-sm]": 'size === "sm"',
        "[class.tds-badge-size-md]": 'size === "md"',
        "[class.tds-badge-size-lg]": 'size === "lg"',
        "[class.tds-badge-size-xl]": 'size === "xl"',
        "[class.tds-badge-status-primary]": 'status =="primary"',
        "[class.tds-badge-status-secondary]": 'status =="secondary"',
        "[class.tds-badge-status-success]": 'status =="success"',
        "[class.tds-badge-status-info]": 'status =="info"',
        "[class.tds-badge-status-warning]": 'status =="warning"',
        "[class.tds-badge-status-error]": 'status =="error"',
        "[class.tds-badge-theme-default]": 'tdsTheme === "default"',
        "[class.tds-badge-theme-light]": 'tdsTheme === "light"',
        "[class.tds-badge-theme-dark]": 'tdsTheme === "dark"',
        "class": "tds-badge"
      }
    }]
  }], () => [{
    type: TDSConfigService
  }, {
    type: Renderer2
  }, {
    type: ChangeDetectorRef
  }, {
    type: ElementRef
  }, {
    type: Directionality
  }], {
    showZero: [{
      type: Input
    }],
    showDot: [{
      type: Input
    }],
    standalone: [{
      type: Input
    }],
    dot: [{
      type: Input
    }],
    overflowCount: [{
      type: Input
    }],
    color: [{
      type: Input
    }],
    tdsStyle: [{
      type: Input
    }],
    text: [{
      type: Input
    }],
    title: [{
      type: Input
    }],
    status: [{
      type: Input
    }],
    count: [{
      type: Input
    }],
    offset: [{
      type: Input
    }],
    tdsClass: [{
      type: Input
    }],
    tdsTheme: [{
      type: Input
    }],
    size: [{
      type: Input
    }],
    placement: [{
      type: Input
    }]
  });
})();
var TDSRibbonComponent = class _TDSRibbonComponent {
  constructor() {
    this.placement = "end";
    this.text = null;
    this.presetColor = null;
  }
  ngOnChanges(changes) {
    const {
      color
    } = changes;
    if (color) {
      this.presetColor = this.color && badgePresetColors.indexOf(this.color) !== -1 ? this.color : null;
    }
  }
  static {
    this.ɵfac = function TDSRibbonComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TDSRibbonComponent)();
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _TDSRibbonComponent,
      selectors: [["tds-ribbon"]],
      hostAttrs: [1, "tds-ribbon-wrapper"],
      inputs: {
        color: "color",
        placement: "placement",
        text: "text"
      },
      exportAs: ["tdsRibbon"],
      standalone: true,
      features: [ɵɵNgOnChangesFeature, ɵɵStandaloneFeature],
      ngContentSelectors: _c02,
      decls: 4,
      vars: 11,
      consts: [[1, "tds-ribbon"], [4, "tdsStringTemplateOutlet"], [1, "tds-ribbon-corner"]],
      template: function TDSRibbonComponent_Template(rf, ctx) {
        if (rf & 1) {
          ɵɵprojectionDef();
          ɵɵprojection(0);
          ɵɵelementStart(1, "div", 0);
          ɵɵtemplate(2, TDSRibbonComponent_ng_container_2_Template, 2, 1, "ng-container", 1);
          ɵɵelement(3, "div", 2);
          ɵɵelementEnd();
        }
        if (rf & 2) {
          ɵɵadvance();
          ɵɵclassMap(ctx.presetColor && "tds-ribbon-color-" + ctx.presetColor);
          ɵɵstyleProp("background-color", !ctx.presetColor && ctx.color);
          ɵɵclassProp("tds-ribbon-placement-end", ctx.placement === "end")("tds-ribbon-placement-start", ctx.placement === "start");
          ɵɵadvance();
          ɵɵproperty("tdsStringTemplateOutlet", ctx.text);
          ɵɵadvance();
          ɵɵstyleProp("color", !ctx.presetColor && ctx.color);
        }
      },
      dependencies: [TDSOutletModule, TDSStringTemplateOutletDirective],
      encapsulation: 2,
      changeDetection: 0
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TDSRibbonComponent, [{
    type: Component,
    args: [{
      selector: "tds-ribbon",
      exportAs: "tdsRibbon",
      preserveWhitespaces: false,
      standalone: true,
      imports: [TDSOutletModule],
      encapsulation: ViewEncapsulation$1.None,
      changeDetection: ChangeDetectionStrategy.OnPush,
      template: `
    <ng-content></ng-content>
    <div
      class="tds-ribbon"
      [class]="presetColor && 'tds-ribbon-color-' + presetColor"
      [class.tds-ribbon-placement-end]="placement === 'end'"
      [class.tds-ribbon-placement-start]="placement === 'start'"
      [style.background-color]="!presetColor && color"
    >
      <ng-container *tdsStringTemplateOutlet="text">{{ text }}</ng-container>
      <div class="tds-ribbon-corner" [style.color]="!presetColor && color"></div>
    </div>
  `,
      host: {
        "class": "tds-ribbon-wrapper"
      }
    }]
  }], () => [], {
    color: [{
      type: Input
    }],
    placement: [{
      type: Input
    }],
    text: [{
      type: Input
    }]
  });
})();
var TDSBadgeModule = class _TDSBadgeModule {
  static {
    this.ɵfac = function TDSBadgeModule_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TDSBadgeModule)();
    };
  }
  static {
    this.ɵmod = ɵɵdefineNgModule({
      type: _TDSBadgeModule,
      imports: [TDSBadgeComponent, TDSBadgeSupComponent, TDSRibbonComponent],
      exports: [TDSBadgeComponent, TDSRibbonComponent, TDSBadgeSupComponent]
    });
  }
  static {
    this.ɵinj = ɵɵdefineInjector({
      imports: [TDSBadgeComponent, TDSRibbonComponent]
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TDSBadgeModule, [{
    type: NgModule,
    args: [{
      imports: [TDSBadgeComponent, TDSBadgeSupComponent, TDSRibbonComponent],
      exports: [TDSBadgeComponent, TDSRibbonComponent, TDSBadgeSupComponent]
    }]
  }], null, null);
})();

// node_modules/tds-ui/fesm2022/tds-ui-tooltip.mjs
var import_rxjs3 = __toESM(require_cjs(), 1);
var import_operators3 = __toESM(require_operators(), 1);
var _c03 = ["overlay"];
function TDSToolTipComponent_ng_template_0_ng_container_5_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainerStart(0);
    ɵɵtext(1);
    ɵɵelementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r1 = ɵɵnextContext(2);
    ɵɵadvance();
    ɵɵtextInterpolate(ctx_r1.title);
  }
}
function TDSToolTipComponent_ng_template_0_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "div", 2)(1, "div", 3)(2, "div", 4);
    ɵɵelement(3, "span", 5);
    ɵɵelementEnd();
    ɵɵelementStart(4, "div", 6);
    ɵɵtemplate(5, TDSToolTipComponent_ng_template_0_ng_container_5_Template, 2, 1, "ng-container", 7);
    ɵɵelementEnd()()();
  }
  if (rf & 2) {
    const ctx_r1 = ɵɵnextContext();
    ɵɵclassProp("tds-tooltip-custom", ctx_r1.tooltipArrowCss || ctx_r1.tooltipContentCss)("tds-tooltip-default", !ctx_r1.color)("tds-tooltip-info", ctx_r1.color == "info")("tds-tooltip-success", ctx_r1.color == "success")("tds-tooltip-error", ctx_r1.color == "error")("tds-tooltip-warning", ctx_r1.color == "warning");
    ɵɵproperty("ngClass", ctx_r1._classMap)("ngStyle", ctx_r1.overlayStyle)("@zoomBigMotion", "active");
    ɵɵadvance(2);
    ɵɵproperty("ngStyle", ctx_r1._contentStyleArrow);
    ɵɵadvance();
    ɵɵproperty("ngClass", ctx_r1.tooltipArrowCss)("ngStyle", ctx_r1._contentStyleArrowContent);
    ɵɵadvance();
    ɵɵproperty("ngClass", ctx_r1.tooltipContentCss);
    ɵɵadvance();
    ɵɵproperty("tdsStringTemplateOutlet", ctx_r1.title);
  }
}
var TDSTooltipBaseDirective = class _TDSTooltipBaseDirective {
  /**
   * This true title that would be used in other parts on this component.
   */
  get _title() {
    return this.title || this.directiveTitle || null;
  }
  get _content() {
    return this.content || this.directiveContent || null;
  }
  get _footer() {
    return this.footer || this.directiveFooter || null;
  }
  get _trigger() {
    return typeof this.trigger !== "undefined" ? this.trigger : "hover";
  }
  get _placement() {
    const p = this.placement;
    return Array.isArray(p) && p.length > 0 ? p : typeof p === "string" && p ? [p] : ["top"];
  }
  get _visible() {
    return (typeof this.visible !== "undefined" ? this.visible : this.internalVisible) || false;
  }
  get _mouseEnterDelay() {
    return this.mouseEnterDelay || 0.15;
  }
  get _mouseLeaveDelay() {
    return this.mouseLeaveDelay || 0.1;
  }
  get _overlayClassName() {
    return this.overlayClassName || null;
  }
  get _overlayStyle() {
    return this.overlayStyle || null;
  }
  get _autoClose() {
    return this.autoClose != null ? this.autoClose : true;
  }
  getProxyPropertyMap() {
    return {
      noAnimation: ["noAnimation", () => true]
    };
  }
  constructor(elementRef, hostView, renderer) {
    this.elementRef = elementRef;
    this.hostView = hostView;
    this.renderer = renderer;
    this.autoClose = null;
    this.visibleChange = new EventEmitter();
    this.internalVisible = false;
    this.destroy$ = new import_rxjs3.Subject();
    this.triggerDisposables = [];
  }
  ngOnChanges(changes) {
    const {
      trigger
    } = changes;
    if (trigger && !trigger.isFirstChange()) {
      this.registerTriggers();
    }
    if (this.component) {
      this.updatePropertiesByChanges(changes);
    }
  }
  ngAfterViewInit() {
    this.createComponent();
    this.registerTriggers();
  }
  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
    this.clearTogglingTimer();
    this.removeTriggerListeners();
  }
  show() {
    this.component?.show();
  }
  hide() {
    this.component?.hide();
  }
  /**
   * Force the component to update its position.
   */
  updatePosition() {
    if (this.component) {
      this.component.updatePosition();
    }
  }
  /**
   * Create a dynamic tooltip component. This method can be override.
   */
  createComponent() {
    const componentRef = this.componentRef;
    this.component = componentRef.instance;
    this.renderer.removeChild(this.renderer.parentNode(this.elementRef.nativeElement), componentRef.location.nativeElement);
    this.component.setOverlayOrigin(this.origin || this.elementRef);
    this.initProperties();
    const ngVisibleChange$ = this.component.visibleChange.pipe((0, import_operators3.distinctUntilChanged)());
    ngVisibleChange$.pipe((0, import_operators3.takeUntil)(this.destroy$)).subscribe((visible) => {
      this.internalVisible = visible;
      this.visibleChange.emit(visible);
    });
    ngVisibleChange$.pipe((0, import_operators3.filter)((visible) => visible), (0, import_operators3.delay)(0, import_rxjs3.asapScheduler), (0, import_operators3.filter)(() => Boolean(this.component?.overlay?.overlayRef)), (0, import_operators3.takeUntil)(this.destroy$)).subscribe(() => {
      this.component?.updatePosition();
    });
  }
  registerTriggers() {
    const el = this.elementRef.nativeElement;
    const trigger = this.trigger;
    this.removeTriggerListeners();
    if (trigger === "hover") {
      let overlayElement;
      this.triggerDisposables.push(this.renderer.listen(el, "mouseenter", () => {
        this.delayEnterLeave(true, true, this._mouseEnterDelay);
      }));
      this.triggerDisposables.push(this.renderer.listen(el, "mouseleave", () => {
        this.delayEnterLeave(true, false, this._mouseLeaveDelay);
        if (this.component?.overlay.overlayRef && !overlayElement) {
          overlayElement = this.component.overlay.overlayRef.overlayElement;
          this.triggerDisposables.push(this.renderer.listen(overlayElement, "mouseenter", () => {
            this.delayEnterLeave(false, true, this._mouseEnterDelay);
          }));
          this.triggerDisposables.push(this.renderer.listen(overlayElement, "mouseleave", () => {
            this.delayEnterLeave(false, false, this._mouseLeaveDelay);
          }));
        }
      }));
    } else if (trigger === "focus") {
      this.triggerDisposables.push(this.renderer.listen(el, "focus", () => this.show()));
      this.triggerDisposables.push(this.renderer.listen(el, "blur", () => this.hide()));
    } else if (trigger === "click") {
      this.triggerDisposables.push(this.renderer.listen(el, "click", (e) => {
        e.preventDefault();
        this.show();
      }));
    }
  }
  updatePropertiesByChanges(changes) {
    this.updatePropertiesByKeys(Object.keys(changes));
  }
  updatePropertiesByKeys(keys) {
    const mappingProperties = __spreadValues({
      // common mappings
      title: ["title", () => this._title],
      directiveTitle: ["title", () => this._title],
      content: ["content", () => this._content],
      directiveContent: ["content", () => this._content],
      footer: ["footer", () => this._footer],
      directiveFooter: ["footer", () => this._footer],
      trigger: ["trigger", () => this._trigger],
      placement: ["placement", () => this._placement],
      visible: ["visible", () => this._visible],
      mouseEnterDelay: ["mouseEnterDelay", () => this._mouseEnterDelay],
      mouseLeaveDelay: ["mouseLeaveDelay", () => this._mouseLeaveDelay],
      overlayClassName: ["overlayClassName", () => this._overlayClassName],
      overlayStyle: ["overlayStyle", () => this._overlayStyle],
      autoClose: ["autoClose", () => this._autoClose]
    }, this.getProxyPropertyMap());
    (keys || Object.keys(mappingProperties).filter((key) => !key.startsWith("directive"))).forEach((property) => {
      if (mappingProperties[property]) {
        const [name, valueFn] = mappingProperties[property];
        this.updateComponentValue(name, valueFn());
      }
    });
    this.component?.updateByDirective();
  }
  initProperties() {
    this.updatePropertiesByKeys();
  }
  updateComponentValue(key, value) {
    if (typeof value !== "undefined") {
      this.component[key] = value;
    }
  }
  delayEnterLeave(isOrigin, isEnter, delay2 = -1) {
    if (this.delayTimer) {
      this.clearTogglingTimer();
    } else if (delay2 > 0) {
      this.delayTimer = setTimeout(() => {
        this.delayTimer = void 0;
        isEnter ? this.show() : this.hide();
      }, delay2 * 1e3);
    } else {
      isEnter && isOrigin ? this.show() : this.hide();
    }
  }
  removeTriggerListeners() {
    this.triggerDisposables.forEach((dispose) => dispose());
    this.triggerDisposables.length = 0;
  }
  clearTogglingTimer() {
    if (this.delayTimer) {
      clearTimeout(this.delayTimer);
      this.delayTimer = void 0;
    }
  }
  static {
    this.ɵfac = function TDSTooltipBaseDirective_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TDSTooltipBaseDirective)(ɵɵdirectiveInject(ElementRef), ɵɵdirectiveInject(ViewContainerRef), ɵɵdirectiveInject(Renderer2));
    };
  }
  static {
    this.ɵdir = ɵɵdefineDirective({
      type: _TDSTooltipBaseDirective,
      features: [ɵɵNgOnChangesFeature]
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TDSTooltipBaseDirective, [{
    type: Directive
  }], () => [{
    type: ElementRef
  }, {
    type: ViewContainerRef
  }, {
    type: Renderer2
  }], null);
})();
var TDSTooltipBaseComponent = class _TDSTooltipBaseComponent {
  set visible(value) {
    const visible = toBoolean(value);
    if (this._visible !== visible) {
      this._visible = visible;
      this.visibleChange.next(visible);
    }
  }
  get visible() {
    return this._visible;
  }
  set trigger(value) {
    this._trigger = value;
  }
  get trigger() {
    return this._trigger;
  }
  set placement(value) {
    const preferredPosition = value.map((placement) => POSITION_MAP[placement]);
    this._positions = [...preferredPosition, ...DEFAULT_TOOLTIP_POSITIONS];
  }
  constructor(cdr) {
    this.cdr = cdr;
    this.title = null;
    this.content = null;
    this.footer = null;
    this.overlayStyle = {};
    this.backdrop = false;
    this.autoClose = true;
    this.visibleChange = new import_rxjs3.Subject();
    this._visible = false;
    this._trigger = "hover";
    this.preferredPlacement = "top";
    this.dir = "ltr";
    this._classMap = {};
    this._prefix = "tds-tooltip";
    this._positions = [...DEFAULT_TOOLTIP_POSITIONS];
    this.destroy$ = new import_rxjs3.Subject();
  }
  ngOnInit() {
  }
  ngOnDestroy() {
    this.visibleChange.complete();
    this.destroy$.next();
    this.destroy$.complete();
  }
  show() {
    if (this.visible) {
      return;
    }
    if (!this.isEmpty()) {
      this.visible = true;
      this.visibleChange.next(true);
      this.cdr.detectChanges();
    }
  }
  hide() {
    if (!this.visible) {
      return;
    }
    this.visible = false;
    this.visibleChange.next(false);
    this.cdr.detectChanges();
  }
  updateByDirective() {
    this.updateStyles();
    this.cdr.detectChanges();
    Promise.resolve().then(() => {
      this.updatePosition();
      this.updateVisibilityByTitle();
    });
  }
  /**
   * Force the component to update its position.
   */
  updatePosition() {
    if (this.origin && this.overlay && this.overlay.overlayRef) {
      this.overlay.overlayRef.updatePosition();
    }
  }
  onPositionChange(position) {
    this.preferredPlacement = getPlacementName(position);
    this.updateStyles();
    this.cdr.detectChanges();
  }
  updateStyles() {
    this._classMap = {
      [this.overlayClassName]: true,
      [`${this._prefix}-placement-${this.preferredPlacement}`]: true
    };
  }
  setOverlayOrigin(origin) {
    this.origin = origin;
    this.cdr.markForCheck();
  }
  onClickOutside(event) {
    const target = _getEventTarget(event);
    if (!this.origin.nativeElement.contains(target) && this.trigger !== null) {
      this.hide();
    }
  }
  /**
   * Hide the component while the content is empty.
   */
  updateVisibilityByTitle() {
    if (this.isEmpty()) {
      this.hide();
    }
  }
  static {
    this.ɵfac = function TDSTooltipBaseComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TDSTooltipBaseComponent)(ɵɵdirectiveInject(ChangeDetectorRef));
    };
  }
  static {
    this.ɵdir = ɵɵdefineDirective({
      type: _TDSTooltipBaseComponent,
      viewQuery: function TDSTooltipBaseComponent_Query(rf, ctx) {
        if (rf & 1) {
          ɵɵviewQuery(_c03, 5);
        }
        if (rf & 2) {
          let _t;
          ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.overlay = _t.first);
        }
      }
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TDSTooltipBaseComponent, [{
    type: Directive
  }], () => [{
    type: ChangeDetectorRef
  }], {
    overlay: [{
      type: ViewChild,
      args: ["overlay", {
        static: false
      }]
    }]
  });
})();
function isTooltipEmpty(value) {
  return value instanceof TemplateRef ? false : value === "" || !TDSHelperObject.hasValue(value);
}
var TDSTooltipDirective = class _TDSTooltipDirective extends TDSTooltipBaseDirective {
  constructor(elementRef, hostView, renderer) {
    super(elementRef, hostView, renderer);
    this.trigger = "hover";
    this.placement = "top";
    this.visibleChange = new EventEmitter();
    this.componentRef = this.hostView.createComponent(TDSToolTipComponent);
  }
  getProxyPropertyMap() {
    return {
      tooltipCss: ["tooltipContentCss", () => this.tooltipContentCss],
      tooltipArrowCss: ["tooltipArrowCss", () => this.tooltipArrowCss],
      tooltipColor: ["color", () => this.tooltipColor]
    };
  }
  static {
    this.ɵfac = function TDSTooltipDirective_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TDSTooltipDirective)(ɵɵdirectiveInject(ElementRef), ɵɵdirectiveInject(ViewContainerRef), ɵɵdirectiveInject(Renderer2));
    };
  }
  static {
    this.ɵdir = ɵɵdefineDirective({
      type: _TDSTooltipDirective,
      selectors: [["", "tds-tooltip", ""]],
      hostVars: 2,
      hostBindings: function TDSTooltipDirective_HostBindings(rf, ctx) {
        if (rf & 2) {
          ɵɵclassProp("tds-tooltip-open", ctx.visible);
        }
      },
      inputs: {
        title: [0, "tooltipTitle", "title"],
        directiveTitle: [0, "tds-tooltip", "directiveTitle"],
        trigger: [0, "tooltipTrigger", "trigger"],
        placement: [0, "tooltipPlacement", "placement"],
        origin: [0, "tooltipOrigin", "origin"],
        visible: [0, "tooltipVisible", "visible"],
        mouseEnterDelay: [0, "tooltipMouseEnterDelay", "mouseEnterDelay"],
        mouseLeaveDelay: [0, "tooltipMouseLeaveDelay", "mouseLeaveDelay"],
        overlayClassName: [0, "tooltipOverlayClassName", "overlayClassName"],
        overlayStyle: [0, "tooltipOverlayStyle", "overlayStyle"],
        tooltipColor: "tooltipColor",
        tooltipContentCss: "tooltipContentCss",
        tooltipArrowCss: "tooltipArrowCss"
      },
      outputs: {
        visibleChange: "tooltipVisibleChange"
      },
      exportAs: ["tdsTooltip"],
      standalone: true,
      features: [ɵɵInheritDefinitionFeature]
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TDSTooltipDirective, [{
    type: Directive,
    args: [{
      selector: "[tds-tooltip]",
      exportAs: "tdsTooltip",
      host: {
        "[class.tds-tooltip-open]": "visible"
      },
      standalone: true
    }]
  }], () => [{
    type: ElementRef
  }, {
    type: ViewContainerRef
  }, {
    type: Renderer2
  }], {
    title: [{
      type: Input,
      args: ["tooltipTitle"]
    }],
    directiveTitle: [{
      type: Input,
      args: ["tds-tooltip"]
    }],
    trigger: [{
      type: Input,
      args: ["tooltipTrigger"]
    }],
    placement: [{
      type: Input,
      args: ["tooltipPlacement"]
    }],
    origin: [{
      type: Input,
      args: ["tooltipOrigin"]
    }],
    visible: [{
      type: Input,
      args: ["tooltipVisible"]
    }],
    mouseEnterDelay: [{
      type: Input,
      args: ["tooltipMouseEnterDelay"]
    }],
    mouseLeaveDelay: [{
      type: Input,
      args: ["tooltipMouseLeaveDelay"]
    }],
    overlayClassName: [{
      type: Input,
      args: ["tooltipOverlayClassName"]
    }],
    overlayStyle: [{
      type: Input,
      args: ["tooltipOverlayStyle"]
    }],
    tooltipColor: [{
      type: Input
    }],
    tooltipContentCss: [{
      type: Input
    }],
    tooltipArrowCss: [{
      type: Input
    }],
    visibleChange: [{
      type: Output,
      args: ["tooltipVisibleChange"]
    }]
  });
})();
var TDSToolTipComponent = class _TDSToolTipComponent extends TDSTooltipBaseComponent {
  constructor(cdr, directionality) {
    super(cdr);
    this.title = null;
    this.tooltipContentCss = null;
    this.tooltipArrowCss = null;
    this._contentStyleMap = {};
    this._contentStyleArrowContent = {};
    this._contentStyleArrow = {};
  }
  isEmpty() {
    return isTooltipEmpty(this.title);
  }
  updateStyles() {
    const isColorPreset = this.color;
    this._classMap = {
      [this.overlayClassName]: true,
      [`${this._prefix}-placement-${this.preferredPlacement}`]: true
    };
    this.p_StyleArrow();
  }
  p_StyleArrow() {
    let borderTransparent = "4px solid transparent";
    if (this.preferredPlacement.indexOf("top") > -1) {
      this._contentStyleArrowContent = {
        width: "0",
        height: "0",
        borderLeft: borderTransparent,
        borderRight: borderTransparent,
        borderBottom: 0
        // borderTop: borderColor,
      };
    }
    if (this.preferredPlacement.indexOf("bottom") > -1) {
      this._contentStyleArrowContent = {
        width: "0",
        height: "0",
        borderLeft: borderTransparent,
        borderRight: borderTransparent,
        borderTop: 0
        // borderBottom: borderColor,        
      };
    }
    if (this.preferredPlacement.indexOf("left") > -1) {
      this._contentStyleArrowContent = {
        width: "0",
        height: "0",
        borderTop: borderTransparent,
        // borderLeft : borderColor,
        borderBottom: borderTransparent,
        borderRight: 0
      };
    }
    if (this.preferredPlacement.indexOf("right") > -1) {
      this._contentStyleArrowContent = {
        width: "0",
        height: "0",
        borderTop: borderTransparent,
        // borderRight : borderColor,
        borderBottom: borderTransparent,
        borderLeft: 0
      };
    }
    switch (this.preferredPlacement) {
      case "top":
        this._contentStyleArrow = {
          bottom: "0",
          left: "50%",
          transform: "translateX(-50%)"
        };
        break;
      case "topLeft":
        this._contentStyleArrow = {
          bottom: "0",
          left: "10px"
        };
        break;
      case "topRight":
        this._contentStyleArrow = {
          bottom: "0",
          right: "10px"
        };
        break;
      case "bottom":
        this._contentStyleArrow = {
          top: "0",
          left: "50%",
          transform: "translateX(-50%)"
        };
        break;
      case "bottomLeft":
        this._contentStyleArrow = {
          top: "0",
          left: "10px"
        };
        break;
      case "bottomRight":
        this._contentStyleArrow = {
          top: "0",
          right: "10px"
        };
        break;
      case "left":
        this._contentStyleArrow = {
          right: "0px",
          top: "50%",
          transform: "translateY(-50%)"
        };
        break;
      case "leftTop":
        this._contentStyleArrow = {
          right: "0px",
          top: "10px"
        };
        break;
      case "leftBottom":
        this._contentStyleArrow = {
          right: "0px",
          bottom: "10px"
        };
        break;
      case "right":
        this._contentStyleArrow = {
          left: "0px",
          top: "50%",
          transform: "translateY(-50%)"
        };
        break;
      case "rightTop":
        this._contentStyleArrow = {
          left: "0px",
          top: "10px"
        };
        break;
      case "rightBottom":
        this._contentStyleArrow = {
          left: "0px",
          bottom: "10px"
        };
        break;
      default:
        this._contentStyleArrow = {
          bottom: "0",
          left: "50%",
          transform: "translateX(-50%)"
        };
        break;
    }
  }
  static {
    this.ɵfac = function TDSToolTipComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TDSToolTipComponent)(ɵɵdirectiveInject(ChangeDetectorRef), ɵɵdirectiveInject(Directionality));
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _TDSToolTipComponent,
      selectors: [["tds-tooltip"]],
      exportAs: ["TDSTooltipComponent"],
      standalone: true,
      features: [ɵɵInheritDefinitionFeature, ɵɵStandaloneFeature],
      decls: 2,
      vars: 4,
      consts: [["overlay", "cdkConnectedOverlay"], ["cdkConnectedOverlay", "", 3, "overlayOutsideClick", "detach", "positionChange", "cdkConnectedOverlayOrigin", "cdkConnectedOverlayOpen", "cdkConnectedOverlayPositions", "cdkConnectedOverlayPush"], [1, "tds-tooltip", 3, "ngClass", "ngStyle"], [1, "tds-tooltip-content"], [1, "tds-tooltip-arrow", 3, "ngStyle"], [1, "tds-tooltip-arrow-content", 3, "ngClass", "ngStyle"], [1, "tds-tooltip-inner", 3, "ngClass"], [4, "tdsStringTemplateOutlet"]],
      template: function TDSToolTipComponent_Template(rf, ctx) {
        if (rf & 1) {
          const _r1 = ɵɵgetCurrentView();
          ɵɵtemplate(0, TDSToolTipComponent_ng_template_0_Template, 6, 20, "ng-template", 1, 0, ɵɵtemplateRefExtractor);
          ɵɵlistener("overlayOutsideClick", function TDSToolTipComponent_Template_ng_template_overlayOutsideClick_0_listener($event) {
            ɵɵrestoreView(_r1);
            return ɵɵresetView(ctx.onClickOutside($event));
          })("detach", function TDSToolTipComponent_Template_ng_template_detach_0_listener() {
            ɵɵrestoreView(_r1);
            return ɵɵresetView(ctx.hide());
          })("positionChange", function TDSToolTipComponent_Template_ng_template_positionChange_0_listener($event) {
            ɵɵrestoreView(_r1);
            return ɵɵresetView(ctx.onPositionChange($event));
          });
        }
        if (rf & 2) {
          ɵɵproperty("cdkConnectedOverlayOrigin", ctx.origin)("cdkConnectedOverlayOpen", ctx._visible)("cdkConnectedOverlayPositions", ctx._positions)("cdkConnectedOverlayPush", true);
        }
      },
      dependencies: [NgClass, NgStyle, OverlayModule, CdkConnectedOverlay, TDSOutletModule, TDSStringTemplateOutletDirective, TDSOverlayModule],
      encapsulation: 2,
      data: {
        animation: [zoomBigMotion]
      },
      changeDetection: 0
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TDSToolTipComponent, [{
    type: Component,
    args: [{
      selector: "tds-tooltip",
      exportAs: "TDSTooltipComponent",
      changeDetection: ChangeDetectionStrategy.OnPush,
      encapsulation: ViewEncapsulation$1.None,
      animations: [zoomBigMotion],
      template: `
    <ng-template
      #overlay="cdkConnectedOverlay"
      cdkConnectedOverlay
      [cdkConnectedOverlayOrigin]="origin"
      [cdkConnectedOverlayOpen]="_visible"
      [cdkConnectedOverlayPositions]="_positions"
      [cdkConnectedOverlayPush]="true"
      (overlayOutsideClick)="onClickOutside($event)"
      (detach)="hide()"
      (positionChange)="onPositionChange($event)"
    >
      <div
        class="tds-tooltip"
        [class.tds-tooltip-custom]='tooltipArrowCss || tooltipContentCss'
        [class.tds-tooltip-default]='!color'
        [class.tds-tooltip-info]='color == "info"'
        [class.tds-tooltip-success]='color == "success"' 
        [class.tds-tooltip-error]='color == "error"'         
        [class.tds-tooltip-warning]='color == "warning"' 
        [ngClass]="_classMap"
        [ngStyle]="overlayStyle"     
        [@zoomBigMotion]="'active'"
      >
        <div class="tds-tooltip-content ">
          <div class="tds-tooltip-arrow "
          [ngStyle]="_contentStyleArrow" >
            <span class="tds-tooltip-arrow-content" [ngClass]='tooltipArrowCss!' [ngStyle]="_contentStyleArrowContent"></span>
          </div>
          <div class="tds-tooltip-inner" [ngClass]="tooltipContentCss!">
            <ng-container *tdsStringTemplateOutlet="title">{{ title }}</ng-container>
          </div>
        </div>
      </div>
    </ng-template>
  `,
      preserveWhitespaces: false,
      standalone: true,
      imports: [NgClass, NgStyle, OverlayModule, TDSNoAnimationDirective, TDSOutletModule, TDSOverlayModule]
    }]
  }], () => [{
    type: ChangeDetectorRef
  }, {
    type: Directionality
  }], null);
})();
var TDSToolTipModule = class _TDSToolTipModule {
  static {
    this.ɵfac = function TDSToolTipModule_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TDSToolTipModule)();
    };
  }
  static {
    this.ɵmod = ɵɵdefineNgModule({
      type: _TDSToolTipModule,
      imports: [TDSToolTipComponent, TDSTooltipDirective],
      exports: [TDSToolTipComponent, TDSTooltipDirective]
    });
  }
  static {
    this.ɵinj = ɵɵdefineInjector({
      imports: [TDSToolTipComponent]
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TDSToolTipModule, [{
    type: NgModule,
    args: [{
      imports: [TDSToolTipComponent, TDSTooltipDirective],
      exports: [TDSToolTipComponent, TDSTooltipDirective]
    }]
  }], null, null);
})();

// node_modules/tds-ui/fesm2022/tds-ui-nav-bar.mjs
var import_rxjs4 = __toESM(require_cjs(), 1);
var import_operators4 = __toESM(require_operators(), 1);
var _c04 = ["tds-subnavbar-title", ""];
var _c1 = ["*"];
function TDSSubNavBarTitleComponent_Conditional_0_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelement(0, "span", 5);
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext();
    ɵɵclassMap(ctx_r0.tdsIcon);
  }
}
function TDSSubNavBarTitleComponent_Conditional_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelement(0, "span", 1);
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext();
    ɵɵproperty("innerHtml", ctx_r0.tdsHtmlIcon, ɵɵsanitizeHtml);
  }
}
function TDSSubNavBarTitleComponent_ng_container_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainerStart(0);
    ɵɵelementStart(1, "span", 6);
    ɵɵtext(2);
    ɵɵelementEnd();
    ɵɵelementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext();
    ɵɵadvance(2);
    ɵɵtextInterpolate(ctx_r0.tdsTitle);
  }
}
function TDSSubNavBarTitleComponent_Conditional_4_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelement(0, "span", 3);
  }
}
function TDSSubNavBarTitleComponent_Conditional_5_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelement(0, "span", 4);
  }
}
var _c2 = ["tds-subnavbar-inline-child", ""];
function TDSSubNavBarInlineChildComponent_ng_template_0_Template(rf, ctx) {
}
var _c3 = ["tds-subnavbar-none-inline-child", ""];
function TDSSubNavBarNoneInlineChildComponent_ng_template_1_Template(rf, ctx) {
}
var _c4 = ["tds-subnavbar", ""];
var _c5 = [[["", "title", ""]], "*"];
var _c6 = ["[title]", "*"];
function TDSSubNavBarComponent_Conditional_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵprojection(0);
  }
}
function TDSSubNavBarComponent_Conditional_3_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelement(0, "div", 3);
  }
  if (rf & 2) {
    const ctx_r1 = ɵɵnextContext();
    const subMenuTemplate_r3 = ɵɵreference(6);
    ɵɵproperty("mode", ctx_r1.mode)("tdsOpen", ctx_r1.tdsOpen)("navBarClass", ctx_r1.tdsNavBarClassName)("templateOutlet", subMenuTemplate_r3);
  }
}
function TDSSubNavBarComponent_Conditional_4_ng_template_0_Template(rf, ctx) {
  if (rf & 1) {
    const _r5 = ɵɵgetCurrentView();
    ɵɵelementStart(0, "div", 6);
    ɵɵlistener("subMenuMouseState", function TDSSubNavBarComponent_Conditional_4_ng_template_0_Template_div_subMenuMouseState_0_listener($event) {
      ɵɵrestoreView(_r5);
      const ctx_r1 = ɵɵnextContext(2);
      return ɵɵresetView(ctx_r1.setMouseEnterState($event));
    });
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = ɵɵnextContext(2);
    const subMenuTemplate_r3 = ɵɵreference(6);
    ɵɵproperty("theme", ctx_r1.theme)("mode", ctx_r1.mode)("tdsOpen", ctx_r1.tdsOpen)("position", ctx_r1.position)("tdsDisabled", ctx_r1.tdsDisabled)("isNavBarInsideDropDown", ctx_r1.isNavBarInsideDropDown)("templateOutlet", subMenuTemplate_r3)("navBarClass", ctx_r1.tdsNavBarClassName);
  }
}
function TDSSubNavBarComponent_Conditional_4_Template(rf, ctx) {
  if (rf & 1) {
    const _r4 = ɵɵgetCurrentView();
    ɵɵtemplate(0, TDSSubNavBarComponent_Conditional_4_ng_template_0_Template, 1, 8, "ng-template", 5);
    ɵɵlistener("positionChange", function TDSSubNavBarComponent_Conditional_4_Template_ng_template_positionChange_0_listener($event) {
      ɵɵrestoreView(_r4);
      const ctx_r1 = ɵɵnextContext();
      return ɵɵresetView(ctx_r1.onPositionChange($event));
    });
  }
  if (rf & 2) {
    const ctx_r1 = ɵɵnextContext();
    const origin_r6 = ɵɵreference(1);
    ɵɵproperty("cdkConnectedOverlayPositions", ctx_r1.overlayPositions)("cdkConnectedOverlayOrigin", origin_r6)("cdkConnectedOverlayWidth", ctx_r1.triggerWidth)("cdkConnectedOverlayOpen", ctx_r1.tdsOpen)("cdkConnectedOverlayTransformOriginOn", ".tds-navbar-subnavbar");
  }
}
function TDSSubNavBarComponent_ng_template_5_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵprojection(0, 1);
  }
}
var _c7 = ["titleElement"];
var _c8 = ["tds-navbar-group", ""];
var _c9 = ["*", [["", "title", ""]]];
var _c10 = ["*", "[title]"];
function TDSNavBarGroupComponent_ng_container_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainerStart(0);
    ɵɵtext(1);
    ɵɵelementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext();
    ɵɵadvance();
    ɵɵtextInterpolate(ctx_r0.tdsTitle);
  }
}
function TDSNavBarGroupComponent_Conditional_3_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵprojection(0, 1);
  }
}
var TDSIsNavBarInsideDropDownToken = new InjectionToken("TDSIsInDropDownNavBarToken");
var TDSNavBarServiceLocalToken = new InjectionToken("TDSNavBarServiceLocalToken");
var NavBarService = class _NavBarService {
  constructor() {
    this.descendantMenuItemClick$ = new import_rxjs4.Subject();
    this.childMenuItemClick$ = new import_rxjs4.Subject();
    this.theme$ = new import_rxjs4.BehaviorSubject("light");
    this.mode$ = new import_rxjs4.BehaviorSubject("vertical");
    this.inlineIndent$ = new import_rxjs4.BehaviorSubject(24);
    this.isChildSubMenuOpen$ = new import_rxjs4.BehaviorSubject(false);
  }
  onDescendantMenuItemClick(navbar) {
    this.descendantMenuItemClick$.next(navbar);
  }
  onChildMenuItemClick(navbar) {
    this.childMenuItemClick$.next(navbar);
  }
  setMode(mode) {
    this.mode$.next(mode);
  }
  setTheme(theme) {
    this.theme$.next(theme);
  }
  setInlineIndent(indent) {
    this.inlineIndent$.next(indent);
  }
  static {
    this.ɵfac = function NavBarService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _NavBarService)();
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _NavBarService,
      factory: _NavBarService.ɵfac
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(NavBarService, [{
    type: Injectable
  }], null, null);
})();
var TDSSubNavBarService = class _TDSSubNavBarService {
  /**
   * navbar item inside submenu clicked
   *
   * @param navbar
   */
  onChildMenuItemClick(navbar) {
    this.childMenuItemClick$.next(navbar);
  }
  setOpenStateWithoutDebounce(value) {
    this.isCurrentSubMenuOpen$.next(value);
  }
  setMouseEnterTitleOrOverlayState(value) {
    this.isMouseEnterTitleOrOverlay$.next(value);
  }
  constructor(nzHostSubmenuService, tdsNavBarService, isNavBarInsideDropDown) {
    this.nzHostSubmenuService = nzHostSubmenuService;
    this.tdsNavBarService = tdsNavBarService;
    this.isNavBarInsideDropDown = isNavBarInsideDropDown;
    this.mode$ = this.tdsNavBarService.mode$.pipe((0, import_operators4.map)((mode) => {
      if (mode === "inline") {
        return "inline";
      } else if (mode === "vertical" || this.nzHostSubmenuService) {
        return "vertical";
      } else {
        return "horizontal";
      }
    }));
    this.level = 1;
    this.isCurrentSubMenuOpen$ = new import_rxjs4.BehaviorSubject(false);
    this.isChildSubMenuOpen$ = new import_rxjs4.BehaviorSubject(false);
    this.isMouseEnterTitleOrOverlay$ = new import_rxjs4.Subject();
    this.childMenuItemClick$ = new import_rxjs4.Subject();
    this.destroy$ = new import_rxjs4.Subject();
    if (this.nzHostSubmenuService) {
      this.level = this.nzHostSubmenuService.level + 1;
    }
    const isClosedByMenuItemClick = this.childMenuItemClick$.pipe((0, import_operators4.mergeMap)(() => this.mode$), (0, import_operators4.filter)((mode) => mode !== "inline" || this.isNavBarInsideDropDown), (0, import_operators4.mapTo)(false));
    const isCurrentSubmenuOpen$ = (0, import_rxjs4.merge)(this.isMouseEnterTitleOrOverlay$, isClosedByMenuItemClick);
    const isSubMenuOpenWithDebounce$ = (0, import_rxjs4.combineLatest)([this.isChildSubMenuOpen$, isCurrentSubmenuOpen$]).pipe((0, import_operators4.map)(([isChildSubMenuOpen, isCurrentSubmenuOpen]) => isChildSubMenuOpen || isCurrentSubmenuOpen), (0, import_operators4.auditTime)(150), (0, import_operators4.distinctUntilChanged)(), (0, import_operators4.takeUntil)(this.destroy$));
    isSubMenuOpenWithDebounce$.pipe((0, import_operators4.distinctUntilChanged)()).subscribe((data) => {
      this.setOpenStateWithoutDebounce(data);
      if (this.nzHostSubmenuService) {
        this.nzHostSubmenuService.isChildSubMenuOpen$.next(data);
      } else {
        this.tdsNavBarService.isChildSubMenuOpen$.next(data);
      }
    });
  }
  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
  static {
    this.ɵfac = function TDSSubNavBarService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TDSSubNavBarService)(ɵɵinject(_TDSSubNavBarService, 12), ɵɵinject(NavBarService), ɵɵinject(TDSIsNavBarInsideDropDownToken));
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _TDSSubNavBarService,
      factory: _TDSSubNavBarService.ɵfac
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TDSSubNavBarService, [{
    type: Injectable
  }], () => [{
    type: TDSSubNavBarService,
    decorators: [{
      type: SkipSelf
    }, {
      type: Optional
    }]
  }, {
    type: NavBarService
  }, {
    type: void 0,
    decorators: [{
      type: Inject,
      args: [TDSIsNavBarInsideDropDownToken]
    }]
  }], null);
})();
var TDSNavBarItemDirective = class _TDSNavBarItemDirective {
  /** clear all item selected status except this */
  clickMenuItem(e) {
    if (this.tdsDisabled) {
      e.preventDefault();
      e.stopPropagation();
    } else {
      this.tdsNavBarService.onDescendantMenuItemClick(this);
      if (this.tdsSubNavBarService) {
        this.tdsSubNavBarService.onChildMenuItemClick(this);
      } else {
        this.tdsNavBarService.onChildMenuItemClick(this);
      }
    }
  }
  setSelectedState(value) {
    this.tdsSelected = value;
    this.selected$.next(value);
  }
  updateRouterActive() {
    if (!this.listOfRouterLink || !this.listOfRouterLinkWithHref || !this.router || !this.router.navigated || !this.tdsMatchRouter) {
      return;
    }
    Promise.resolve().then(() => {
      const hasActiveLinks = this.hasActiveLinks();
      if (this.tdsSelected !== hasActiveLinks) {
        this.tdsSelected = hasActiveLinks;
        this.setSelectedState(this.tdsSelected);
        this.cdr.markForCheck();
      }
    });
  }
  hasActiveLinks() {
    const isActiveCheckFn = this.isLinkActive(this.router);
    return this.routerLink && isActiveCheckFn(this.routerLink) || this.routerLinkWithHref && isActiveCheckFn(this.routerLinkWithHref) || this.listOfRouterLink.some(isActiveCheckFn) || this.listOfRouterLinkWithHref.some(isActiveCheckFn);
  }
  isLinkActive(router) {
    return (link) => router.isActive(link.urlTree || "", this.tdsMatchOptions || {
      paths: this.tdsMatchRouterExact ? "exact" : "subset",
      queryParams: this.tdsMatchRouterExact ? "exact" : "subset",
      fragment: "ignored",
      matrixParams: "ignored"
    });
  }
  constructor(tdsNavBarService, cdr, tdsSubNavBarService, isNavBarInsideDropDown, directionality, routerLink, routerLinkWithHref, router) {
    this.tdsNavBarService = tdsNavBarService;
    this.cdr = cdr;
    this.tdsSubNavBarService = tdsSubNavBarService;
    this.isNavBarInsideDropDown = isNavBarInsideDropDown;
    this.directionality = directionality;
    this.routerLink = routerLink;
    this.routerLinkWithHref = routerLinkWithHref;
    this.router = router;
    this.destroy$ = new import_rxjs4.Subject();
    this.level = this.tdsSubNavBarService ? this.tdsSubNavBarService.level + 1 : 1;
    this.selected$ = new import_rxjs4.Subject();
    this.inlinePaddingLeft = null;
    this.dir = "ltr";
    this.tdsDisabled = false;
    this.tdsSelected = false;
    this.tdsDanger = false;
    this.tdsMatchRouterExact = false;
    this.tdsMatchRouter = false;
    if (router) {
      this.router.events.pipe((0, import_operators4.takeUntil)(this.destroy$), (0, import_operators4.filter)((e) => e instanceof NavigationEnd)).subscribe(() => {
        this.updateRouterActive();
      });
    }
  }
  ngOnInit() {
    (0, import_rxjs4.combineLatest)([this.tdsNavBarService.mode$, this.tdsNavBarService.inlineIndent$]).pipe((0, import_operators4.takeUntil)(this.destroy$)).subscribe(([mode, inlineIndent]) => {
      this.inlinePaddingLeft = mode === "inline" ? this.level * inlineIndent : null;
    });
    this.dir = this.directionality.value;
    this.directionality.change?.pipe((0, import_operators4.takeUntil)(this.destroy$)).subscribe((direction) => {
      this.dir = direction;
    });
  }
  ngAfterContentInit() {
    this.listOfRouterLink.changes.pipe((0, import_operators4.takeUntil)(this.destroy$)).subscribe(() => this.updateRouterActive());
    this.listOfRouterLinkWithHref.changes.pipe((0, import_operators4.takeUntil)(this.destroy$)).subscribe(() => this.updateRouterActive());
    this.updateRouterActive();
  }
  ngOnChanges(changes) {
    if (changes.tdsSelected) {
      this.setSelectedState(this.tdsSelected);
    }
  }
  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
  static {
    this.ɵfac = function TDSNavBarItemDirective_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TDSNavBarItemDirective)(ɵɵdirectiveInject(NavBarService), ɵɵdirectiveInject(ChangeDetectorRef), ɵɵdirectiveInject(TDSSubNavBarService, 8), ɵɵdirectiveInject(TDSIsNavBarInsideDropDownToken), ɵɵdirectiveInject(Directionality, 8), ɵɵdirectiveInject(RouterLink, 8), ɵɵdirectiveInject(RouterLink, 8), ɵɵdirectiveInject(Router, 8));
    };
  }
  static {
    this.ɵdir = ɵɵdefineDirective({
      type: _TDSNavBarItemDirective,
      selectors: [["", "tds-navbar-item", ""]],
      contentQueries: function TDSNavBarItemDirective_ContentQueries(rf, ctx, dirIndex) {
        if (rf & 1) {
          ɵɵcontentQuery(dirIndex, RouterLink, 5);
          ɵɵcontentQuery(dirIndex, RouterLink, 5);
        }
        if (rf & 2) {
          let _t;
          ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.listOfRouterLink = _t);
          ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.listOfRouterLinkWithHref = _t);
        }
      },
      hostVars: 20,
      hostBindings: function TDSNavBarItemDirective_HostBindings(rf, ctx) {
        if (rf & 1) {
          ɵɵlistener("click", function TDSNavBarItemDirective_click_HostBindingHandler($event) {
            return ctx.clickMenuItem($event);
          });
        }
        if (rf & 2) {
          ɵɵstyleProp("padding-left", ctx.dir === "rtl" ? null : ctx.tdsPaddingLeft || ctx.inlinePaddingLeft, "px")("padding-right", ctx.dir === "rtl" ? ctx.tdsPaddingLeft || ctx.inlinePaddingLeft : null, "px");
          ɵɵclassProp("tds-dropdown-navbar-item", ctx.isNavBarInsideDropDown)("tds-dropdown-navbar-item-selected", ctx.isNavBarInsideDropDown && ctx.tdsSelected)("tds-dropdown-navbar-item-danger", ctx.isNavBarInsideDropDown && ctx.tdsDanger)("tds-dropdown-navbar-item-disabled", ctx.isNavBarInsideDropDown && ctx.tdsDisabled)("tds-navbar-item", !ctx.isNavBarInsideDropDown)("tds-navbar-item-selected", !ctx.isNavBarInsideDropDown && ctx.tdsSelected)("tds-navbar-item-danger", !ctx.isNavBarInsideDropDown && ctx.tdsDanger)("tds-navbar-item-disabled", !ctx.isNavBarInsideDropDown && ctx.tdsDisabled);
        }
      },
      inputs: {
        tdsPaddingLeft: "tdsPaddingLeft",
        tdsDisabled: "tdsDisabled",
        tdsSelected: "tdsSelected",
        tdsDanger: "tdsDanger",
        tdsMatchRouterExact: "tdsMatchRouterExact",
        tdsMatchRouter: "tdsMatchRouter",
        tdsMatchOptions: "tdsMatchOptions"
      },
      exportAs: ["tdsNavBarItem"],
      standalone: true,
      features: [ɵɵNgOnChangesFeature]
    });
  }
};
__decorate([InputBoolean()], TDSNavBarItemDirective.prototype, "tdsDisabled", void 0);
__decorate([InputBoolean()], TDSNavBarItemDirective.prototype, "tdsSelected", void 0);
__decorate([InputBoolean()], TDSNavBarItemDirective.prototype, "tdsDanger", void 0);
__decorate([InputBoolean()], TDSNavBarItemDirective.prototype, "tdsMatchRouterExact", void 0);
__decorate([InputBoolean()], TDSNavBarItemDirective.prototype, "tdsMatchRouter", void 0);
__decorate([InputBoolean()], TDSNavBarItemDirective.prototype, "tdsMatchOptions", void 0);
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TDSNavBarItemDirective, [{
    type: Directive,
    args: [{
      selector: "[tds-navbar-item]",
      exportAs: "tdsNavBarItem",
      host: {
        "[class.tds-dropdown-navbar-item]": `isNavBarInsideDropDown`,
        "[class.tds-dropdown-navbar-item-selected]": `isNavBarInsideDropDown && tdsSelected`,
        "[class.tds-dropdown-navbar-item-danger]": `isNavBarInsideDropDown && tdsDanger`,
        "[class.tds-dropdown-navbar-item-disabled]": `isNavBarInsideDropDown && tdsDisabled`,
        "[class.tds-navbar-item]": `!isNavBarInsideDropDown`,
        "[class.tds-navbar-item-selected]": `!isNavBarInsideDropDown && tdsSelected`,
        "[class.tds-navbar-item-danger]": `!isNavBarInsideDropDown && tdsDanger`,
        "[class.tds-navbar-item-disabled]": `!isNavBarInsideDropDown && tdsDisabled`,
        "[style.paddingLeft.px]": `dir === 'rtl' ? null : tdsPaddingLeft || inlinePaddingLeft`,
        "[style.paddingRight.px]": `dir === 'rtl' ? tdsPaddingLeft || inlinePaddingLeft : null`,
        "(click)": "clickMenuItem($event)"
      },
      standalone: true
    }]
  }], () => [{
    type: NavBarService
  }, {
    type: ChangeDetectorRef
  }, {
    type: TDSSubNavBarService,
    decorators: [{
      type: Optional
    }]
  }, {
    type: void 0,
    decorators: [{
      type: Inject,
      args: [TDSIsNavBarInsideDropDownToken]
    }]
  }, {
    type: Directionality,
    decorators: [{
      type: Optional
    }]
  }, {
    type: RouterLink,
    decorators: [{
      type: Optional
    }]
  }, {
    type: RouterLink,
    decorators: [{
      type: Optional
    }]
  }, {
    type: Router,
    decorators: [{
      type: Optional
    }]
  }], {
    tdsPaddingLeft: [{
      type: Input
    }],
    tdsDisabled: [{
      type: Input
    }],
    tdsSelected: [{
      type: Input
    }],
    tdsDanger: [{
      type: Input
    }],
    tdsMatchRouterExact: [{
      type: Input
    }],
    tdsMatchRouter: [{
      type: Input
    }],
    tdsMatchOptions: [{
      type: Input
    }],
    listOfRouterLink: [{
      type: ContentChildren,
      args: [RouterLink, {
        descendants: true
      }]
    }],
    listOfRouterLinkWithHref: [{
      type: ContentChildren,
      args: [RouterLink, {
        descendants: true
      }]
    }]
  });
})();
var TDSSubNavBarTitleComponent = class _TDSSubNavBarTitleComponent {
  constructor(cdr, directionality) {
    this.cdr = cdr;
    this.directionality = directionality;
    this.tdsIcon = null;
    this.tdsHtmlIcon = null;
    this.tdsTitle = null;
    this.isNavBarInsideDropDown = false;
    this.tdsDisabled = false;
    this.paddingLeft = null;
    this.mode = "vertical";
    this.toggleSubMenu = new EventEmitter();
    this.subMenuMouseState = new EventEmitter();
    this.dir = "ltr";
    this.destroy$ = new import_rxjs4.Subject();
  }
  ngOnInit() {
    this.dir = this.directionality.value;
    this.directionality.change?.pipe((0, import_operators4.takeUntil)(this.destroy$)).subscribe((direction) => {
      this.dir = direction;
      this.cdr.detectChanges();
    });
  }
  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
  setMouseState(state) {
    if (!this.tdsDisabled) {
      this.subMenuMouseState.next(state);
    }
  }
  clickTitle() {
    if (this.mode === "inline" && !this.tdsDisabled) {
      this.toggleSubMenu.emit();
    }
  }
  static {
    this.ɵfac = function TDSSubNavBarTitleComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TDSSubNavBarTitleComponent)(ɵɵdirectiveInject(ChangeDetectorRef), ɵɵdirectiveInject(Directionality, 8));
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _TDSSubNavBarTitleComponent,
      selectors: [["", "tds-subnavbar-title", ""]],
      hostVars: 8,
      hostBindings: function TDSSubNavBarTitleComponent_HostBindings(rf, ctx) {
        if (rf & 1) {
          ɵɵlistener("click", function TDSSubNavBarTitleComponent_click_HostBindingHandler() {
            return ctx.clickTitle();
          })("mouseenter", function TDSSubNavBarTitleComponent_mouseenter_HostBindingHandler() {
            return ctx.setMouseState(true);
          })("mouseleave", function TDSSubNavBarTitleComponent_mouseleave_HostBindingHandler() {
            return ctx.setMouseState(false);
          });
        }
        if (rf & 2) {
          ɵɵstyleProp("padding-left", ctx.dir === "rtl" ? null : ctx.paddingLeft, "px")("padding-right", ctx.dir === "rtl" ? ctx.paddingLeft : null, "px");
          ɵɵclassProp("tds-dropdown-navbar-subnavbar-title", ctx.isNavBarInsideDropDown)("tds-navbar-subnavbar-title", !ctx.isNavBarInsideDropDown);
        }
      },
      inputs: {
        tdsIcon: "tdsIcon",
        tdsHtmlIcon: "tdsHtmlIcon",
        tdsTitle: "tdsTitle",
        isNavBarInsideDropDown: "isNavBarInsideDropDown",
        tdsDisabled: "tdsDisabled",
        paddingLeft: "paddingLeft",
        mode: "mode"
      },
      outputs: {
        toggleSubMenu: "toggleSubMenu",
        subMenuMouseState: "subMenuMouseState"
      },
      exportAs: ["tdsSubNavBarTitle"],
      standalone: true,
      features: [ɵɵStandaloneFeature],
      attrs: _c04,
      ngContentSelectors: _c1,
      decls: 6,
      vars: 4,
      consts: [[1, "tds-subnavbar-title-icon", 3, "class"], [1, "tds-subnavbar-title-html-icon", 3, "innerHtml"], [4, "tdsStringTemplateOutlet"], [1, "tds-dropdown-navbar-subnavbar-expand-icon"], [1, "tds-navbar-subnavbar-arrow", "tdsi-chevron-right-fill"], [1, "tds-subnavbar-title-icon"], [1, "tds-subnavbar-title-name"]],
      template: function TDSSubNavBarTitleComponent_Template(rf, ctx) {
        if (rf & 1) {
          ɵɵprojectionDef();
          ɵɵtemplate(0, TDSSubNavBarTitleComponent_Conditional_0_Template, 1, 2, "span", 0)(1, TDSSubNavBarTitleComponent_Conditional_1_Template, 1, 1, "span", 1)(2, TDSSubNavBarTitleComponent_ng_container_2_Template, 3, 1, "ng-container", 2);
          ɵɵprojection(3);
          ɵɵtemplate(4, TDSSubNavBarTitleComponent_Conditional_4_Template, 1, 0, "span", 3)(5, TDSSubNavBarTitleComponent_Conditional_5_Template, 1, 0, "span", 4);
        }
        if (rf & 2) {
          ɵɵconditional(ctx.tdsIcon ? 0 : -1);
          ɵɵadvance();
          ɵɵconditional(!ctx.tdsIcon && ctx.tdsHtmlIcon ? 1 : -1);
          ɵɵadvance();
          ɵɵproperty("tdsStringTemplateOutlet", ctx.tdsTitle);
          ɵɵadvance(2);
          ɵɵconditional(ctx.isNavBarInsideDropDown ? 4 : 5);
        }
      },
      dependencies: [TDSOutletModule, TDSStringTemplateOutletDirective],
      encapsulation: 2,
      changeDetection: 0
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TDSSubNavBarTitleComponent, [{
    type: Component,
    args: [{
      selector: "[tds-subnavbar-title]",
      exportAs: "tdsSubNavBarTitle",
      encapsulation: ViewEncapsulation$1.None,
      changeDetection: ChangeDetectionStrategy.OnPush,
      template: `
    @if (tdsIcon) {
      <span class="tds-subnavbar-title-icon"  [class]="tdsIcon"></span>
    }
    @if (!tdsIcon && tdsHtmlIcon) {
      <span class="tds-subnavbar-title-html-icon" [innerHtml]="tdsHtmlIcon"></span>
    }
    <ng-container *tdsStringTemplateOutlet="tdsTitle">
      <span class="tds-subnavbar-title-name">{{ tdsTitle }}</span>
    </ng-container>
    <ng-content></ng-content>
    @if (isNavBarInsideDropDown) {
      <span
        class="tds-dropdown-navbar-subnavbar-expand-icon"
        >
        @switch (dir) {
          <!-- <span *ngSwitchCase="'rtl'"  class="tds-dropdown-navbar-subnavbar-arrow-icon"></span>
          <span *ngSwitchDefault  class="tds-dropdown-navbar-subnavbar-arrow-icon"></span> -->
        }
      </span>
    } @else {
      <span class="tds-navbar-subnavbar-arrow tdsi-chevron-right-fill"></span>
    }
    `,
      host: {
        "[class.tds-dropdown-navbar-subnavbar-title]": "isNavBarInsideDropDown",
        "[class.tds-navbar-subnavbar-title]": "!isNavBarInsideDropDown",
        "[style.paddingLeft.px]": `dir === 'rtl' ? null : paddingLeft `,
        "[style.paddingRight.px]": `dir === 'rtl' ? paddingLeft : null`,
        "(click)": "clickTitle()",
        "(mouseenter)": "setMouseState(true)",
        "(mouseleave)": "setMouseState(false)"
      },
      standalone: true,
      imports: [TDSOutletModule]
    }]
  }], () => [{
    type: ChangeDetectorRef
  }, {
    type: Directionality,
    decorators: [{
      type: Optional
    }]
  }], {
    tdsIcon: [{
      type: Input
    }],
    tdsHtmlIcon: [{
      type: Input
    }],
    tdsTitle: [{
      type: Input
    }],
    isNavBarInsideDropDown: [{
      type: Input
    }],
    tdsDisabled: [{
      type: Input
    }],
    paddingLeft: [{
      type: Input
    }],
    mode: [{
      type: Input
    }],
    toggleSubMenu: [{
      type: Output
    }],
    subMenuMouseState: [{
      type: Output
    }]
  });
})();
var TDSSubNavBarInlineChildComponent = class _TDSSubNavBarInlineChildComponent {
  constructor(elementRef, renderer, directionality) {
    this.elementRef = elementRef;
    this.renderer = renderer;
    this.directionality = directionality;
    this.templateOutlet = null;
    this.navBarClass = "";
    this.mode = "vertical";
    this.tdsOpen = false;
    this.listOfCacheClassName = [];
    this.expandState = "collapsed";
    this.dir = "ltr";
    this.destroy$ = new import_rxjs4.Subject();
  }
  calcMotionState() {
    if (this.tdsOpen) {
      this.expandState = "expanded";
    } else {
      this.expandState = "collapsed";
    }
  }
  ngOnInit() {
    this.calcMotionState();
    this.dir = this.directionality.value;
    this.directionality.change?.pipe((0, import_operators4.takeUntil)(this.destroy$)).subscribe((direction) => {
      this.dir = direction;
    });
  }
  ngOnChanges(changes) {
    const {
      mode,
      tdsOpen,
      navBarClass
    } = changes;
    if (mode || tdsOpen) {
      this.calcMotionState();
    }
    if (navBarClass) {
      if (this.listOfCacheClassName.length) {
        this.listOfCacheClassName.filter((item) => !!item).forEach((className) => {
          this.renderer.removeClass(this.elementRef.nativeElement, className);
        });
      }
      if (this.navBarClass) {
        this.listOfCacheClassName = this.navBarClass.split(" ");
        this.listOfCacheClassName.filter((item) => !!item).forEach((className) => {
          this.renderer.addClass(this.elementRef.nativeElement, className);
        });
      }
    }
  }
  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
  static {
    this.ɵfac = function TDSSubNavBarInlineChildComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TDSSubNavBarInlineChildComponent)(ɵɵdirectiveInject(ElementRef), ɵɵdirectiveInject(Renderer2), ɵɵdirectiveInject(Directionality, 8));
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _TDSSubNavBarInlineChildComponent,
      selectors: [["", "tds-subnavbar-inline-child", ""]],
      hostAttrs: [1, "tds-navbar", "tds-navbar-inline", "tds-navbar-sub"],
      hostVars: 3,
      hostBindings: function TDSSubNavBarInlineChildComponent_HostBindings(rf, ctx) {
        if (rf & 2) {
          ɵɵsyntheticHostProperty("@collapseMotion", ctx.expandState);
          ɵɵclassProp("tds-navbar-rtl", ctx.dir === "rtl");
        }
      },
      inputs: {
        templateOutlet: "templateOutlet",
        navBarClass: "navBarClass",
        mode: "mode",
        tdsOpen: "tdsOpen"
      },
      exportAs: ["tdsSubNavBarInlineChild"],
      standalone: true,
      features: [ɵɵNgOnChangesFeature, ɵɵStandaloneFeature],
      attrs: _c2,
      decls: 1,
      vars: 1,
      consts: [[3, "ngTemplateOutlet"]],
      template: function TDSSubNavBarInlineChildComponent_Template(rf, ctx) {
        if (rf & 1) {
          ɵɵtemplate(0, TDSSubNavBarInlineChildComponent_ng_template_0_Template, 0, 0, "ng-template", 0);
        }
        if (rf & 2) {
          ɵɵproperty("ngTemplateOutlet", ctx.templateOutlet);
        }
      },
      dependencies: [NgTemplateOutlet],
      encapsulation: 2,
      data: {
        animation: [collapseMotion]
      },
      changeDetection: 0
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TDSSubNavBarInlineChildComponent, [{
    type: Component,
    args: [{
      selector: "[tds-subnavbar-inline-child]",
      animations: [collapseMotion],
      exportAs: "tdsSubNavBarInlineChild",
      encapsulation: ViewEncapsulation$1.None,
      changeDetection: ChangeDetectionStrategy.OnPush,
      template: ` <ng-template [ngTemplateOutlet]="templateOutlet"></ng-template> `,
      host: {
        class: "tds-navbar tds-navbar-inline tds-navbar-sub",
        "[class.tds-navbar-rtl]": `dir === 'rtl'`,
        "[@collapseMotion]": "expandState"
      },
      standalone: true,
      imports: [NgTemplateOutlet]
    }]
  }], () => [{
    type: ElementRef
  }, {
    type: Renderer2
  }, {
    type: Directionality,
    decorators: [{
      type: Optional
    }]
  }], {
    templateOutlet: [{
      type: Input
    }],
    navBarClass: [{
      type: Input
    }],
    mode: [{
      type: Input
    }],
    tdsOpen: [{
      type: Input
    }]
  });
})();
var TDSSubNavBarNoneInlineChildComponent = class _TDSSubNavBarNoneInlineChildComponent {
  constructor(directionality) {
    this.directionality = directionality;
    this.navBarClass = "";
    this.theme = "light";
    this.templateOutlet = null;
    this.isNavBarInsideDropDown = false;
    this.mode = "vertical";
    this.position = "right";
    this.tdsDisabled = false;
    this.tdsOpen = false;
    this.subMenuMouseState = new EventEmitter();
    this.expandState = "collapsed";
    this.dir = "ltr";
    this.destroy$ = new import_rxjs4.Subject();
  }
  setMouseState(state) {
    if (!this.tdsDisabled) {
      this.subMenuMouseState.next(state);
    }
  }
  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
  calcMotionState() {
    if (this.tdsOpen) {
      if (this.mode === "horizontal") {
        this.expandState = "bottom";
      } else if (this.mode === "vertical") {
        this.expandState = "active";
      }
    } else {
      this.expandState = "collapsed";
    }
  }
  ngOnInit() {
    this.calcMotionState();
    this.dir = this.directionality.value;
    this.directionality.change?.pipe((0, import_operators4.takeUntil)(this.destroy$)).subscribe((direction) => {
      this.dir = direction;
    });
  }
  ngOnChanges(changes) {
    const {
      mode,
      tdsOpen
    } = changes;
    if (mode || tdsOpen) {
      this.calcMotionState();
    }
  }
  static {
    this.ɵfac = function TDSSubNavBarNoneInlineChildComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TDSSubNavBarNoneInlineChildComponent)(ɵɵdirectiveInject(Directionality, 8));
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _TDSSubNavBarNoneInlineChildComponent,
      selectors: [["", "tds-subnavbar-none-inline-child", ""]],
      hostAttrs: [1, "tds-navbar-subnavbar", "tds-navbar-subnavbar-popup"],
      hostVars: 14,
      hostBindings: function TDSSubNavBarNoneInlineChildComponent_HostBindings(rf, ctx) {
        if (rf & 1) {
          ɵɵlistener("mouseenter", function TDSSubNavBarNoneInlineChildComponent_mouseenter_HostBindingHandler() {
            return ctx.setMouseState(true);
          })("mouseleave", function TDSSubNavBarNoneInlineChildComponent_mouseleave_HostBindingHandler() {
            return ctx.setMouseState(false);
          });
        }
        if (rf & 2) {
          ɵɵsyntheticHostProperty("@slideMotion", ctx.expandState)("@zoomBigMotion", ctx.expandState);
          ɵɵclassProp("tds-navbar-light", ctx.theme === "light")("tds-navbar-dark", ctx.theme === "dark")("tds-navbar-subnavbar-placement-bottom", ctx.mode === "horizontal")("tds-navbar-subnavbar-placement-right", ctx.mode === "vertical" && ctx.position === "right")("tds-navbar-subnavbar-placement-left", ctx.mode === "vertical" && ctx.position === "left")("tds-navbar-subnavbar-rtl", ctx.dir === "rtl");
        }
      },
      inputs: {
        navBarClass: "navBarClass",
        theme: "theme",
        templateOutlet: "templateOutlet",
        isNavBarInsideDropDown: "isNavBarInsideDropDown",
        mode: "mode",
        position: "position",
        tdsDisabled: "tdsDisabled",
        tdsOpen: "tdsOpen"
      },
      outputs: {
        subMenuMouseState: "subMenuMouseState"
      },
      exportAs: ["tdsSubNavBarNoneInlineChild"],
      standalone: true,
      features: [ɵɵNgOnChangesFeature, ɵɵStandaloneFeature],
      attrs: _c3,
      decls: 2,
      vars: 16,
      consts: [[3, "ngClass"], [3, "ngTemplateOutlet"]],
      template: function TDSSubNavBarNoneInlineChildComponent_Template(rf, ctx) {
        if (rf & 1) {
          ɵɵelementStart(0, "div", 0);
          ɵɵtemplate(1, TDSSubNavBarNoneInlineChildComponent_ng_template_1_Template, 0, 0, "ng-template", 1);
          ɵɵelementEnd();
        }
        if (rf & 2) {
          ɵɵclassProp("tds-dropdown-navbar", ctx.isNavBarInsideDropDown)("tds-navbar", !ctx.isNavBarInsideDropDown)("tds-dropdown-navbar-vertical", ctx.isNavBarInsideDropDown)("tds-navbar-vertical", !ctx.isNavBarInsideDropDown)("tds-dropdown-navbar-sub", ctx.isNavBarInsideDropDown)("tds-navbar-sub", !ctx.isNavBarInsideDropDown)("tds-navbar-rtl", ctx.dir === "rtl");
          ɵɵproperty("ngClass", ctx.navBarClass);
          ɵɵadvance();
          ɵɵproperty("ngTemplateOutlet", ctx.templateOutlet);
        }
      },
      dependencies: [NgClass, NgTemplateOutlet],
      encapsulation: 2,
      data: {
        animation: [zoomBigMotion, slideMotion]
      },
      changeDetection: 0
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TDSSubNavBarNoneInlineChildComponent, [{
    type: Component,
    args: [{
      selector: "[tds-subnavbar-none-inline-child]",
      exportAs: "tdsSubNavBarNoneInlineChild",
      encapsulation: ViewEncapsulation$1.None,
      animations: [zoomBigMotion, slideMotion],
      changeDetection: ChangeDetectionStrategy.OnPush,
      template: `
    <div
      [class.tds-dropdown-navbar]="isNavBarInsideDropDown"
      [class.tds-navbar]="!isNavBarInsideDropDown"
      [class.tds-dropdown-navbar-vertical]="isNavBarInsideDropDown"
      [class.tds-navbar-vertical]="!isNavBarInsideDropDown"
      [class.tds-dropdown-navbar-sub]="isNavBarInsideDropDown"
      [class.tds-navbar-sub]="!isNavBarInsideDropDown"
      [class.tds-navbar-rtl]="dir === 'rtl'"
      [ngClass]="navBarClass"
    >
      <ng-template [ngTemplateOutlet]="templateOutlet"></ng-template>
    </div>
  `,
      host: {
        class: "tds-navbar-subnavbar tds-navbar-subnavbar-popup",
        "[class.tds-navbar-light]": "theme === 'light'",
        "[class.tds-navbar-dark]": "theme === 'dark'",
        "[class.tds-navbar-subnavbar-placement-bottom]": "mode === 'horizontal'",
        "[class.tds-navbar-subnavbar-placement-right]": "mode === 'vertical' && position === 'right'",
        "[class.tds-navbar-subnavbar-placement-left]": "mode === 'vertical' && position === 'left'",
        "[class.tds-navbar-subnavbar-rtl]": 'dir ==="rtl"',
        "[@slideMotion]": "expandState",
        "[@zoomBigMotion]": "expandState",
        "(mouseenter)": "setMouseState(true)",
        "(mouseleave)": "setMouseState(false)"
      },
      standalone: true,
      imports: [NgClass, NgTemplateOutlet]
    }]
  }], () => [{
    type: Directionality,
    decorators: [{
      type: Optional
    }]
  }], {
    navBarClass: [{
      type: Input
    }],
    theme: [{
      type: Input
    }],
    templateOutlet: [{
      type: Input
    }],
    isNavBarInsideDropDown: [{
      type: Input
    }],
    mode: [{
      type: Input
    }],
    position: [{
      type: Input
    }],
    tdsDisabled: [{
      type: Input
    }],
    tdsOpen: [{
      type: Input
    }],
    subMenuMouseState: [{
      type: Output
    }]
  });
})();
var listOfVerticalPositions = [POSITION_MAP.rightTop, POSITION_MAP.right, POSITION_MAP.rightBottom, POSITION_MAP.leftTop, POSITION_MAP.left, POSITION_MAP.leftBottom];
var listOfHorizontalPositions = [POSITION_MAP.bottomLeft, POSITION_MAP.bottomRight, POSITION_MAP.topRight, POSITION_MAP.topLeft];
var TDSSubNavBarComponent = class _TDSSubNavBarComponent {
  /** set the subnavbar host open status directly **/
  setOpenStateWithoutDebounce(open) {
    this.tdsSubNavBarService.setOpenStateWithoutDebounce(open);
  }
  toggleSubMenu() {
    this.setOpenStateWithoutDebounce(!this.tdsOpen);
  }
  setMouseEnterState(value) {
    this.isActive = value;
    if (this.mode !== "inline") {
      this.tdsSubNavBarService.setMouseEnterTitleOrOverlayState(value);
    }
  }
  setTriggerWidth() {
    if (this.mode === "horizontal" && this.platform.isBrowser && this.cdkOverlayOrigin && this.tdsPlacement === "bottomLeft") {
      this.triggerWidth = this.cdkOverlayOrigin.nativeElement.getBoundingClientRect().width;
    }
  }
  onPositionChange(position) {
    const placement = getPlacementName(position);
    if (placement === "rightTop" || placement === "rightBottom" || placement === "right") {
      this.position = "right";
    } else if (placement === "leftTop" || placement === "leftBottom" || placement === "left") {
      this.position = "left";
    }
  }
  constructor(tdsNavBarService, cdr, tdsSubNavBarService, platform, isNavBarInsideDropDown, directionality, noAnimation) {
    this.tdsNavBarService = tdsNavBarService;
    this.cdr = cdr;
    this.tdsSubNavBarService = tdsSubNavBarService;
    this.platform = platform;
    this.isNavBarInsideDropDown = isNavBarInsideDropDown;
    this.directionality = directionality;
    this.noAnimation = noAnimation;
    this.tdsNavBarClassName = "";
    this.tdsPaddingLeft = null;
    this.tdsTitle = null;
    this.tdsIcon = null;
    this.tdsHtmlIcon = null;
    this.tdsOpen = false;
    this.tdsDisabled = false;
    this.tdsPlacement = "bottomLeft";
    this.tdsOpenChange = new EventEmitter();
    this.cdkOverlayOrigin = null;
    this.listOfTDSSubNavBarComponent = null;
    this.level = this.tdsSubNavBarService.level;
    this.destroy$ = new import_rxjs4.Subject();
    this.position = "right";
    this.triggerWidth = null;
    this.theme = "light";
    this.mode = "vertical";
    this.inlinePaddingLeft = null;
    this.overlayPositions = listOfVerticalPositions;
    this.isSelected = false;
    this.isActive = false;
    this.dir = "ltr";
  }
  ngOnInit() {
    this.tdsNavBarService.theme$.pipe((0, import_operators4.takeUntil)(this.destroy$)).subscribe((theme) => {
      this.theme = theme;
      this.cdr.markForCheck();
    });
    this.tdsSubNavBarService.mode$.pipe((0, import_operators4.takeUntil)(this.destroy$)).subscribe((mode) => {
      this.mode = mode;
      if (mode === "horizontal") {
        this.overlayPositions = [POSITION_MAP[this.tdsPlacement], ...listOfHorizontalPositions];
      } else if (mode === "vertical") {
        this.overlayPositions = listOfVerticalPositions;
      }
      this.cdr.markForCheck();
    });
    (0, import_rxjs4.combineLatest)([this.tdsSubNavBarService.mode$, this.tdsNavBarService.inlineIndent$]).pipe((0, import_operators4.takeUntil)(this.destroy$)).subscribe(([mode, inlineIndent]) => {
      this.inlinePaddingLeft = mode === "inline" ? this.level * inlineIndent : null;
      this.cdr.markForCheck();
    });
    this.tdsSubNavBarService.isCurrentSubMenuOpen$.pipe((0, import_operators4.takeUntil)(this.destroy$)).subscribe((open) => {
      this.isActive = open;
      if (open !== this.tdsOpen) {
        this.setTriggerWidth();
        this.tdsOpen = open;
        this.tdsOpenChange.emit(this.tdsOpen);
        this.cdr.markForCheck();
      }
    });
    this.dir = this.directionality.value;
    this.directionality.change?.pipe((0, import_operators4.takeUntil)(this.destroy$)).subscribe((direction) => {
      this.dir = direction;
      this.cdr.markForCheck();
    });
  }
  ngAfterContentInit() {
    this.setTriggerWidth();
    const listOfTDSNavBarItemDirective = this.listOfTDSNavBarItemDirective;
    const changes = listOfTDSNavBarItemDirective.changes;
    const mergedObservable = (0, import_rxjs4.merge)(...[changes, ...listOfTDSNavBarItemDirective.map((navbar) => navbar.selected$)]);
    changes.pipe((0, import_operators4.startWith)(listOfTDSNavBarItemDirective), (0, import_operators4.switchMap)(() => mergedObservable), (0, import_operators4.startWith)(true), (0, import_operators4.map)(() => listOfTDSNavBarItemDirective.some((e) => e.tdsSelected)), (0, import_operators4.takeUntil)(this.destroy$)).subscribe((selected) => {
      this.isSelected = selected;
      this.cdr.markForCheck();
    });
  }
  ngOnChanges(changes) {
    const {
      tdsOpen
    } = changes;
    if (tdsOpen) {
      this.tdsSubNavBarService.setOpenStateWithoutDebounce(this.tdsOpen);
      this.setTriggerWidth();
    }
  }
  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
  static {
    this.ɵfac = function TDSSubNavBarComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TDSSubNavBarComponent)(ɵɵdirectiveInject(NavBarService), ɵɵdirectiveInject(ChangeDetectorRef), ɵɵdirectiveInject(TDSSubNavBarService), ɵɵdirectiveInject(Platform), ɵɵdirectiveInject(TDSIsNavBarInsideDropDownToken), ɵɵdirectiveInject(Directionality, 8), ɵɵdirectiveInject(TDSNoAnimationDirective, 9));
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _TDSSubNavBarComponent,
      selectors: [["", "tds-subnavbar", ""]],
      contentQueries: function TDSSubNavBarComponent_ContentQueries(rf, ctx, dirIndex) {
        if (rf & 1) {
          ɵɵcontentQuery(dirIndex, _TDSSubNavBarComponent, 5);
          ɵɵcontentQuery(dirIndex, TDSNavBarItemDirective, 5);
        }
        if (rf & 2) {
          let _t;
          ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.listOfTDSSubNavBarComponent = _t);
          ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.listOfTDSNavBarItemDirective = _t);
        }
      },
      viewQuery: function TDSSubNavBarComponent_Query(rf, ctx) {
        if (rf & 1) {
          ɵɵviewQuery(CdkOverlayOrigin, 7, ElementRef);
        }
        if (rf & 2) {
          let _t;
          ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.cdkOverlayOrigin = _t.first);
        }
      },
      hostVars: 34,
      hostBindings: function TDSSubNavBarComponent_HostBindings(rf, ctx) {
        if (rf & 2) {
          ɵɵclassProp("tds-dropdown-navbar-subnavbar", ctx.isNavBarInsideDropDown)("tds-dropdown-navbar-subnavbar-disabled", ctx.isNavBarInsideDropDown && ctx.tdsDisabled)("tds-dropdown-navbar-subnavbar-open", ctx.isNavBarInsideDropDown && ctx.tdsOpen)("tds-dropdown-navbar-subnavbar-selected", ctx.isNavBarInsideDropDown && ctx.isSelected)("tds-dropdown-navbar-subnavbar-vertical", ctx.isNavBarInsideDropDown && ctx.mode === "vertical")("tds-dropdown-navbar-subnavbar-horizontal", ctx.isNavBarInsideDropDown && ctx.mode === "horizontal")("tds-dropdown-navbar-subnavbar-inline", ctx.isNavBarInsideDropDown && ctx.mode === "inline")("tds-dropdown-navbar-subnavbar-active", ctx.isNavBarInsideDropDown && ctx.isActive)("tds-navbar-subnavbar", !ctx.isNavBarInsideDropDown)("tds-navbar-subnavbar-disabled", !ctx.isNavBarInsideDropDown && ctx.tdsDisabled)("tds-navbar-subnavbar-open", !ctx.isNavBarInsideDropDown && ctx.tdsOpen)("tds-navbar-subnavbar-selected", !ctx.isNavBarInsideDropDown && ctx.isSelected)("tds-navbar-subnavbar-vertical", !ctx.isNavBarInsideDropDown && ctx.mode === "vertical")("tds-navbar-subnavbar-horizontal", !ctx.isNavBarInsideDropDown && ctx.mode === "horizontal")("tds-navbar-subnavbar-inline", !ctx.isNavBarInsideDropDown && ctx.mode === "inline")("tds-navbar-subnavbar-active", !ctx.isNavBarInsideDropDown && ctx.isActive)("tds-navbar-subnavbar-rtl", ctx.dir === "rtl");
        }
      },
      inputs: {
        tdsNavBarClassName: "tdsNavBarClassName",
        tdsPaddingLeft: "tdsPaddingLeft",
        tdsTitle: "tdsTitle",
        tdsIcon: "tdsIcon",
        tdsHtmlIcon: "tdsHtmlIcon",
        tdsOpen: "tdsOpen",
        tdsDisabled: "tdsDisabled",
        tdsPlacement: "tdsPlacement"
      },
      outputs: {
        tdsOpenChange: "tdsOpenChange"
      },
      exportAs: ["tdsSubNavBar"],
      standalone: true,
      features: [ɵɵProvidersFeature([TDSSubNavBarService]), ɵɵNgOnChangesFeature, ɵɵStandaloneFeature],
      attrs: _c4,
      ngContentSelectors: _c6,
      decls: 7,
      vars: 9,
      consts: [["origin", "cdkOverlayOrigin"], ["subMenuTemplate", ""], ["tds-subnavbar-title", "", "cdkOverlayOrigin", "", 3, "subMenuMouseState", "toggleSubMenu", "tdsIcon", "tdsHtmlIcon", "tdsTitle", "mode", "tdsDisabled", "isNavBarInsideDropDown", "paddingLeft"], ["tds-subnavbar-inline-child", "", 3, "mode", "tdsOpen", "navBarClass", "templateOutlet"], ["cdkConnectedOverlay", "", 3, "cdkConnectedOverlayPositions", "cdkConnectedOverlayOrigin", "cdkConnectedOverlayWidth", "cdkConnectedOverlayOpen", "cdkConnectedOverlayTransformOriginOn"], ["cdkConnectedOverlay", "", 3, "positionChange", "cdkConnectedOverlayPositions", "cdkConnectedOverlayOrigin", "cdkConnectedOverlayWidth", "cdkConnectedOverlayOpen", "cdkConnectedOverlayTransformOriginOn"], ["tds-subnavbar-none-inline-child", "", 3, "subMenuMouseState", "theme", "mode", "tdsOpen", "position", "tdsDisabled", "isNavBarInsideDropDown", "templateOutlet", "navBarClass"]],
      template: function TDSSubNavBarComponent_Template(rf, ctx) {
        if (rf & 1) {
          const _r1 = ɵɵgetCurrentView();
          ɵɵprojectionDef(_c5);
          ɵɵelementStart(0, "div", 2, 0);
          ɵɵlistener("subMenuMouseState", function TDSSubNavBarComponent_Template_div_subMenuMouseState_0_listener($event) {
            ɵɵrestoreView(_r1);
            return ɵɵresetView(ctx.setMouseEnterState($event));
          })("toggleSubMenu", function TDSSubNavBarComponent_Template_div_toggleSubMenu_0_listener() {
            ɵɵrestoreView(_r1);
            return ɵɵresetView(ctx.toggleSubMenu());
          });
          ɵɵtemplate(2, TDSSubNavBarComponent_Conditional_2_Template, 1, 0);
          ɵɵelementEnd();
          ɵɵtemplate(3, TDSSubNavBarComponent_Conditional_3_Template, 1, 4, "div", 3)(4, TDSSubNavBarComponent_Conditional_4_Template, 1, 5, null, 4)(5, TDSSubNavBarComponent_ng_template_5_Template, 1, 0, "ng-template", null, 1, ɵɵtemplateRefExtractor);
        }
        if (rf & 2) {
          ɵɵproperty("tdsIcon", ctx.tdsIcon)("tdsHtmlIcon", ctx.tdsHtmlIcon)("tdsTitle", ctx.tdsTitle)("mode", ctx.mode)("tdsDisabled", ctx.tdsDisabled)("isNavBarInsideDropDown", ctx.isNavBarInsideDropDown)("paddingLeft", ctx.tdsPaddingLeft || ctx.inlinePaddingLeft);
          ɵɵadvance(2);
          ɵɵconditional(!ctx.tdsTitle ? 2 : -1);
          ɵɵadvance();
          ɵɵconditional(ctx.mode === "inline" ? 3 : 4);
        }
      },
      dependencies: [OverlayModule, CdkConnectedOverlay, CdkOverlayOrigin, TDSSubNavBarTitleComponent, TDSSubNavBarInlineChildComponent, TDSSubNavBarNoneInlineChildComponent],
      encapsulation: 2,
      changeDetection: 0
    });
  }
};
__decorate([InputBoolean()], TDSSubNavBarComponent.prototype, "tdsOpen", void 0);
__decorate([InputBoolean()], TDSSubNavBarComponent.prototype, "tdsDisabled", void 0);
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TDSSubNavBarComponent, [{
    type: Component,
    args: [{
      selector: "[tds-subnavbar]",
      exportAs: "tdsSubNavBar",
      providers: [TDSSubNavBarService],
      encapsulation: ViewEncapsulation$1.None,
      changeDetection: ChangeDetectionStrategy.OnPush,
      preserveWhitespaces: false,
      template: `
    <div
      tds-subnavbar-title
      cdkOverlayOrigin
      #origin="cdkOverlayOrigin"
      [tdsIcon]="tdsIcon"
      [tdsHtmlIcon]="tdsHtmlIcon"
      [tdsTitle]="tdsTitle"
      [mode]="mode"
      [tdsDisabled]="tdsDisabled"
      [isNavBarInsideDropDown]="isNavBarInsideDropDown"
      [paddingLeft]="tdsPaddingLeft || inlinePaddingLeft"
      (subMenuMouseState)="setMouseEnterState($event)"
      (toggleSubMenu)="toggleSubMenu()"
      >
      @if (!tdsTitle) {
        <ng-content select="[title]"></ng-content>
      }
    </div>
    @if (mode === 'inline') {
      <div
        tds-subnavbar-inline-child
        [mode]="mode"
        [tdsOpen]="tdsOpen"
        [navBarClass]="tdsNavBarClassName"
        [templateOutlet]="subMenuTemplate"
      ></div>
    } @else {
      <ng-template
        cdkConnectedOverlay
        (positionChange)="onPositionChange($event)"
        [cdkConnectedOverlayPositions]="overlayPositions"
        [cdkConnectedOverlayOrigin]="origin"
        [cdkConnectedOverlayWidth]="triggerWidth!"
        [cdkConnectedOverlayOpen]="tdsOpen"
        [cdkConnectedOverlayTransformOriginOn]="'.tds-navbar-subnavbar'"
        >
        <div
          tds-subnavbar-none-inline-child
          [theme]="theme"
          [mode]="mode"
          [tdsOpen]="tdsOpen"
          [position]="position"
          [tdsDisabled]="tdsDisabled"
          [isNavBarInsideDropDown]="isNavBarInsideDropDown"
          [templateOutlet]="subMenuTemplate"
          [navBarClass]="tdsNavBarClassName"
          (subMenuMouseState)="setMouseEnterState($event)"
        ></div>
      </ng-template>
    }
    
    <ng-template #subMenuTemplate>
      <ng-content></ng-content>
    </ng-template>
    `,
      host: {
        "[class.tds-dropdown-navbar-subnavbar]": `isNavBarInsideDropDown`,
        "[class.tds-dropdown-navbar-subnavbar-disabled]": `isNavBarInsideDropDown && tdsDisabled`,
        "[class.tds-dropdown-navbar-subnavbar-open]": `isNavBarInsideDropDown && tdsOpen`,
        "[class.tds-dropdown-navbar-subnavbar-selected]": `isNavBarInsideDropDown && isSelected`,
        "[class.tds-dropdown-navbar-subnavbar-vertical]": `isNavBarInsideDropDown && mode === 'vertical'`,
        "[class.tds-dropdown-navbar-subnavbar-horizontal]": `isNavBarInsideDropDown && mode === 'horizontal'`,
        "[class.tds-dropdown-navbar-subnavbar-inline]": `isNavBarInsideDropDown && mode === 'inline'`,
        "[class.tds-dropdown-navbar-subnavbar-active]": `isNavBarInsideDropDown && isActive`,
        "[class.tds-navbar-subnavbar]": `!isNavBarInsideDropDown`,
        "[class.tds-navbar-subnavbar-disabled]": `!isNavBarInsideDropDown && tdsDisabled`,
        "[class.tds-navbar-subnavbar-open]": `!isNavBarInsideDropDown && tdsOpen`,
        "[class.tds-navbar-subnavbar-selected]": `!isNavBarInsideDropDown && isSelected`,
        "[class.tds-navbar-subnavbar-vertical]": `!isNavBarInsideDropDown && mode === 'vertical'`,
        "[class.tds-navbar-subnavbar-horizontal]": `!isNavBarInsideDropDown && mode === 'horizontal'`,
        "[class.tds-navbar-subnavbar-inline]": `!isNavBarInsideDropDown && mode === 'inline'`,
        "[class.tds-navbar-subnavbar-active]": `!isNavBarInsideDropDown && isActive`,
        "[class.tds-navbar-subnavbar-rtl]": `dir === 'rtl'`
      },
      standalone: true,
      imports: [OverlayModule, TDSSubNavBarTitleComponent, TDSSubNavBarInlineChildComponent, TDSSubNavBarNoneInlineChildComponent]
    }]
  }], () => [{
    type: NavBarService
  }, {
    type: ChangeDetectorRef
  }, {
    type: TDSSubNavBarService
  }, {
    type: Platform
  }, {
    type: void 0,
    decorators: [{
      type: Inject,
      args: [TDSIsNavBarInsideDropDownToken]
    }]
  }, {
    type: Directionality,
    decorators: [{
      type: Optional
    }]
  }, {
    type: TDSNoAnimationDirective,
    decorators: [{
      type: Host
    }, {
      type: Optional
    }]
  }], {
    tdsNavBarClassName: [{
      type: Input
    }],
    tdsPaddingLeft: [{
      type: Input
    }],
    tdsTitle: [{
      type: Input
    }],
    tdsIcon: [{
      type: Input
    }],
    tdsHtmlIcon: [{
      type: Input
    }],
    tdsOpen: [{
      type: Input
    }],
    tdsDisabled: [{
      type: Input
    }],
    tdsPlacement: [{
      type: Input
    }],
    tdsOpenChange: [{
      type: Output
    }],
    cdkOverlayOrigin: [{
      type: ViewChild,
      args: [CdkOverlayOrigin, {
        static: true,
        read: ElementRef
      }]
    }],
    listOfTDSSubNavBarComponent: [{
      type: ContentChildren,
      args: [forwardRef(() => TDSSubNavBarComponent), {
        descendants: true
      }]
    }],
    listOfTDSNavBarItemDirective: [{
      type: ContentChildren,
      args: [TDSNavBarItemDirective, {
        descendants: true
      }]
    }]
  });
})();
function NavBarServiceFactory(serviceInsideDropDown, serviceOutsideDropDown) {
  return serviceInsideDropDown ? serviceInsideDropDown : serviceOutsideDropDown;
}
function NavBarDropDownTokenFactory(isMenuInsideDropDownToken) {
  return isMenuInsideDropDownToken ? isMenuInsideDropDownToken : false;
}
var TDSNavBarDirective = class _TDSNavBarDirective {
  setInlineCollapsed(inlineCollapsed) {
    this.tdsInlineCollapsed = inlineCollapsed;
    this.inlineCollapsed$.next(inlineCollapsed);
  }
  updateInlineCollapse() {
    if (this.listOfTDSNavBarItemDirective) {
      if (this.tdsInlineCollapsed) {
        this.listOfOpenedTDSSubNavBarComponent = this.listOfTDSSubNavBarComponent.filter((submenu) => submenu.tdsOpen);
        this.listOfTDSSubNavBarComponent.forEach((submenu) => submenu.setOpenStateWithoutDebounce(false));
      } else {
        this.listOfOpenedTDSSubNavBarComponent.forEach((submenu) => submenu.setOpenStateWithoutDebounce(true));
        this.listOfOpenedTDSSubNavBarComponent = [];
      }
    }
  }
  constructor(tdsNavBarService, isNavBarInsideDropDown, cdr, directionality) {
    this.tdsNavBarService = tdsNavBarService;
    this.isNavBarInsideDropDown = isNavBarInsideDropDown;
    this.cdr = cdr;
    this.directionality = directionality;
    this.tdsInlineIndent = 24;
    this.tdsTheme = "light";
    this.tdsMode = "vertical";
    this.tdsInlineCollapsed = false;
    this.tdsSelectable = !this.isNavBarInsideDropDown;
    this.tdsClick = new EventEmitter();
    this.actualMode = "vertical";
    this.dir = "ltr";
    this.inlineCollapsed$ = new import_rxjs4.BehaviorSubject(this.tdsInlineCollapsed);
    this.mode$ = new import_rxjs4.BehaviorSubject(this.tdsMode);
    this.destroy$ = new import_rxjs4.Subject();
    this.listOfOpenedTDSSubNavBarComponent = [];
  }
  ngOnInit() {
    (0, import_rxjs4.combineLatest)([this.inlineCollapsed$, this.mode$]).pipe((0, import_operators4.takeUntil)(this.destroy$)).subscribe(([inlineCollapsed, mode]) => {
      this.actualMode = inlineCollapsed ? "vertical" : mode;
      this.tdsNavBarService.setMode(this.actualMode);
      this.cdr.markForCheck();
    });
    this.tdsNavBarService.descendantMenuItemClick$.pipe((0, import_operators4.takeUntil)(this.destroy$)).subscribe((navbar) => {
      this.tdsClick.emit(navbar);
      if (this.tdsSelectable && !navbar.tdsMatchRouter) {
        this.listOfTDSNavBarItemDirective.forEach((item) => item.setSelectedState(item === navbar));
      }
    });
    this.dir = this.directionality.value;
    this.directionality.change?.pipe((0, import_operators4.takeUntil)(this.destroy$)).subscribe((direction) => {
      this.dir = direction;
      this.tdsNavBarService.setMode(this.actualMode);
      this.cdr.markForCheck();
    });
  }
  ngAfterContentInit() {
    this.inlineCollapsed$.pipe((0, import_operators4.takeUntil)(this.destroy$)).subscribe(() => {
      this.updateInlineCollapse();
      this.cdr.markForCheck();
    });
  }
  ngOnChanges(changes) {
    const {
      tdsInlineCollapsed,
      tdsInlineIndent,
      tdsTheme,
      tdsMode
    } = changes;
    if (tdsInlineCollapsed) {
      this.inlineCollapsed$.next(this.tdsInlineCollapsed);
    }
    if (tdsInlineIndent) {
      this.tdsNavBarService.setInlineIndent(this.tdsInlineIndent);
    }
    if (tdsTheme) {
      this.tdsNavBarService.setTheme(this.tdsTheme);
    }
    if (tdsMode) {
      this.mode$.next(this.tdsMode);
      if (!changes.tdsMode.isFirstChange() && this.listOfTDSSubNavBarComponent) {
        this.listOfTDSSubNavBarComponent.forEach((submenu) => submenu.setOpenStateWithoutDebounce(false));
      }
    }
  }
  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
  static {
    this.ɵfac = function TDSNavBarDirective_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TDSNavBarDirective)(ɵɵdirectiveInject(NavBarService), ɵɵdirectiveInject(TDSIsNavBarInsideDropDownToken), ɵɵdirectiveInject(ChangeDetectorRef), ɵɵdirectiveInject(Directionality, 8));
    };
  }
  static {
    this.ɵdir = ɵɵdefineDirective({
      type: _TDSNavBarDirective,
      selectors: [["", "tds-navbar", ""]],
      contentQueries: function TDSNavBarDirective_ContentQueries(rf, ctx, dirIndex) {
        if (rf & 1) {
          ɵɵcontentQuery(dirIndex, TDSNavBarItemDirective, 5);
          ɵɵcontentQuery(dirIndex, TDSSubNavBarComponent, 5);
        }
        if (rf & 2) {
          let _t;
          ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.listOfTDSNavBarItemDirective = _t);
          ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.listOfTDSSubNavBarComponent = _t);
        }
      },
      hostVars: 34,
      hostBindings: function TDSNavBarDirective_HostBindings(rf, ctx) {
        if (rf & 2) {
          ɵɵclassProp("tds-dropdown-navbar", ctx.isNavBarInsideDropDown)("tds-dropdown-navbar-root", ctx.isNavBarInsideDropDown)("tds-dropdown-navbar-light", ctx.isNavBarInsideDropDown && ctx.tdsTheme === "light")("tds-dropdown-navbar-dark", ctx.isNavBarInsideDropDown && ctx.tdsTheme === "dark")("tds-dropdown-navbar-vertical", ctx.isNavBarInsideDropDown && ctx.actualMode === "vertical")("tds-dropdown-navbar-horizontal", ctx.isNavBarInsideDropDown && ctx.actualMode === "horizontal")("tds-dropdown-navbar-inline", ctx.isNavBarInsideDropDown && ctx.actualMode === "inline")("tds-dropdown-navbar-inline-collapsed", ctx.isNavBarInsideDropDown && ctx.tdsInlineCollapsed)("tds-navbar", !ctx.isNavBarInsideDropDown)("tds-navbar-root", !ctx.isNavBarInsideDropDown)("tds-navbar-light", !ctx.isNavBarInsideDropDown && ctx.tdsTheme === "light")("tds-navbar-dark", !ctx.isNavBarInsideDropDown && ctx.tdsTheme === "dark")("tds-navbar-vertical", !ctx.isNavBarInsideDropDown && ctx.actualMode === "vertical")("tds-navbar-horizontal", !ctx.isNavBarInsideDropDown && ctx.actualMode === "horizontal")("tds-navbar-inline", !ctx.isNavBarInsideDropDown && ctx.actualMode === "inline")("tds-navbar-inline-collapsed", !ctx.isNavBarInsideDropDown && ctx.tdsInlineCollapsed)("tds-navbar-rtl", ctx.dir === "rtl");
        }
      },
      inputs: {
        tdsInlineIndent: "tdsInlineIndent",
        tdsTheme: "tdsTheme",
        tdsMode: "tdsMode",
        tdsInlineCollapsed: "tdsInlineCollapsed",
        tdsSelectable: "tdsSelectable"
      },
      outputs: {
        tdsClick: "tdsClick"
      },
      exportAs: ["tdsNavBar"],
      standalone: true,
      features: [ɵɵProvidersFeature([
        {
          provide: TDSNavBarServiceLocalToken,
          useClass: NavBarService
        },
        /** use the top level service **/
        {
          provide: NavBarService,
          useFactory: NavBarServiceFactory,
          deps: [[new SkipSelf(), new Optional(), NavBarService], TDSNavBarServiceLocalToken]
        },
        /** check if navbar inside dropdown-navbar component **/
        {
          provide: TDSIsNavBarInsideDropDownToken,
          useFactory: NavBarDropDownTokenFactory,
          deps: [[new SkipSelf(), new Optional(), TDSIsNavBarInsideDropDownToken]]
        }
      ]), ɵɵNgOnChangesFeature]
    });
  }
};
__decorate([InputBoolean()], TDSNavBarDirective.prototype, "tdsInlineCollapsed", void 0);
__decorate([InputBoolean()], TDSNavBarDirective.prototype, "tdsSelectable", void 0);
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TDSNavBarDirective, [{
    type: Directive,
    args: [{
      selector: "[tds-navbar]",
      exportAs: "tdsNavBar",
      providers: [
        {
          provide: TDSNavBarServiceLocalToken,
          useClass: NavBarService
        },
        /** use the top level service **/
        {
          provide: NavBarService,
          useFactory: NavBarServiceFactory,
          deps: [[new SkipSelf(), new Optional(), NavBarService], TDSNavBarServiceLocalToken]
        },
        /** check if navbar inside dropdown-navbar component **/
        {
          provide: TDSIsNavBarInsideDropDownToken,
          useFactory: NavBarDropDownTokenFactory,
          deps: [[new SkipSelf(), new Optional(), TDSIsNavBarInsideDropDownToken]]
        }
      ],
      host: {
        "[class.tds-dropdown-navbar]": `isNavBarInsideDropDown`,
        "[class.tds-dropdown-navbar-root]": `isNavBarInsideDropDown`,
        "[class.tds-dropdown-navbar-light]": `isNavBarInsideDropDown && tdsTheme === 'light'`,
        "[class.tds-dropdown-navbar-dark]": `isNavBarInsideDropDown && tdsTheme === 'dark'`,
        "[class.tds-dropdown-navbar-vertical]": `isNavBarInsideDropDown && actualMode === 'vertical'`,
        "[class.tds-dropdown-navbar-horizontal]": `isNavBarInsideDropDown && actualMode === 'horizontal'`,
        "[class.tds-dropdown-navbar-inline]": `isNavBarInsideDropDown && actualMode === 'inline'`,
        "[class.tds-dropdown-navbar-inline-collapsed]": `isNavBarInsideDropDown && tdsInlineCollapsed`,
        "[class.tds-navbar]": `!isNavBarInsideDropDown`,
        "[class.tds-navbar-root]": `!isNavBarInsideDropDown`,
        "[class.tds-navbar-light]": `!isNavBarInsideDropDown && tdsTheme === 'light'`,
        "[class.tds-navbar-dark]": `!isNavBarInsideDropDown && tdsTheme === 'dark'`,
        "[class.tds-navbar-vertical]": `!isNavBarInsideDropDown && actualMode === 'vertical'`,
        "[class.tds-navbar-horizontal]": `!isNavBarInsideDropDown && actualMode === 'horizontal'`,
        "[class.tds-navbar-inline]": `!isNavBarInsideDropDown && actualMode === 'inline'`,
        "[class.tds-navbar-inline-collapsed]": `!isNavBarInsideDropDown && tdsInlineCollapsed`,
        "[class.tds-navbar-rtl]": `dir === 'rtl'`
      },
      standalone: true
    }]
  }], () => [{
    type: NavBarService
  }, {
    type: void 0,
    decorators: [{
      type: Inject,
      args: [TDSIsNavBarInsideDropDownToken]
    }]
  }, {
    type: ChangeDetectorRef
  }, {
    type: Directionality,
    decorators: [{
      type: Optional
    }]
  }], {
    listOfTDSNavBarItemDirective: [{
      type: ContentChildren,
      args: [TDSNavBarItemDirective, {
        descendants: true
      }]
    }],
    listOfTDSSubNavBarComponent: [{
      type: ContentChildren,
      args: [TDSSubNavBarComponent, {
        descendants: true
      }]
    }],
    tdsInlineIndent: [{
      type: Input
    }],
    tdsTheme: [{
      type: Input
    }],
    tdsMode: [{
      type: Input
    }],
    tdsInlineCollapsed: [{
      type: Input
    }],
    tdsSelectable: [{
      type: Input
    }],
    tdsClick: [{
      type: Output
    }]
  });
})();
function NavBarGroupFactory(isMenuInsideDropDownToken) {
  return isMenuInsideDropDownToken ? isMenuInsideDropDownToken : false;
}
var TDSNavBarGroupComponent = class _TDSNavBarGroupComponent {
  constructor(elementRef, renderer, isNavBarInsideDropDown) {
    this.elementRef = elementRef;
    this.renderer = renderer;
    this.isNavBarInsideDropDown = isNavBarInsideDropDown;
    const className = this.isNavBarInsideDropDown ? "tds-dropdown-navbar-item-group" : "tds-navbar-item-group";
    this.renderer.addClass(elementRef.nativeElement, className);
  }
  ngAfterViewInit() {
    const ulElement = this.titleElement.nativeElement.nextElementSibling;
    if (ulElement) {
      const className = this.isNavBarInsideDropDown ? "tds-dropdown-navbar-item-group-list" : "tds-navbar-item-group-list";
      this.renderer.addClass(ulElement, className);
    }
  }
  static {
    this.ɵfac = function TDSNavBarGroupComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TDSNavBarGroupComponent)(ɵɵdirectiveInject(ElementRef), ɵɵdirectiveInject(Renderer2), ɵɵdirectiveInject(TDSIsNavBarInsideDropDownToken));
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _TDSNavBarGroupComponent,
      selectors: [["", "tds-navbar-group", ""]],
      viewQuery: function TDSNavBarGroupComponent_Query(rf, ctx) {
        if (rf & 1) {
          ɵɵviewQuery(_c7, 5);
        }
        if (rf & 2) {
          let _t;
          ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.titleElement = _t.first);
        }
      },
      inputs: {
        tdsTitle: "tdsTitle"
      },
      exportAs: ["tdsNavBarGroup"],
      standalone: true,
      features: [ɵɵProvidersFeature([
        /** check if navbar inside dropdown-navbar component **/
        {
          provide: TDSIsNavBarInsideDropDownToken,
          useFactory: NavBarGroupFactory,
          deps: [[new SkipSelf(), new Optional(), TDSIsNavBarInsideDropDownToken]]
        }
      ]), ɵɵStandaloneFeature],
      attrs: _c8,
      ngContentSelectors: _c10,
      decls: 5,
      vars: 6,
      consts: [["titleElement", ""], [4, "tdsStringTemplateOutlet"]],
      template: function TDSNavBarGroupComponent_Template(rf, ctx) {
        if (rf & 1) {
          ɵɵprojectionDef(_c9);
          ɵɵelementStart(0, "div", null, 0);
          ɵɵtemplate(2, TDSNavBarGroupComponent_ng_container_2_Template, 2, 1, "ng-container", 1)(3, TDSNavBarGroupComponent_Conditional_3_Template, 1, 0);
          ɵɵelementEnd();
          ɵɵprojection(4);
        }
        if (rf & 2) {
          ɵɵclassProp("tds-navbar-item-group-title", !ctx.isNavBarInsideDropDown)("tds-dropdown-navbar-item-group-title", ctx.isNavBarInsideDropDown);
          ɵɵadvance(2);
          ɵɵproperty("tdsStringTemplateOutlet", ctx.tdsTitle);
          ɵɵadvance();
          ɵɵconditional(!ctx.tdsTitle ? 3 : -1);
        }
      },
      dependencies: [TDSOutletModule, TDSStringTemplateOutletDirective],
      encapsulation: 2,
      changeDetection: 0
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TDSNavBarGroupComponent, [{
    type: Component,
    args: [{
      selector: "[tds-navbar-group]",
      exportAs: "tdsNavBarGroup",
      changeDetection: ChangeDetectionStrategy.OnPush,
      providers: [
        /** check if navbar inside dropdown-navbar component **/
        {
          provide: TDSIsNavBarInsideDropDownToken,
          useFactory: NavBarGroupFactory,
          deps: [[new SkipSelf(), new Optional(), TDSIsNavBarInsideDropDownToken]]
        }
      ],
      encapsulation: ViewEncapsulation$1.None,
      template: `
    <div
      [class.tds-navbar-item-group-title]="!isNavBarInsideDropDown"
      [class.tds-dropdown-navbar-item-group-title]="isNavBarInsideDropDown"
      #titleElement
      >
      <ng-container *tdsStringTemplateOutlet="tdsTitle">{{ tdsTitle }}</ng-container>
      @if (!tdsTitle) {
        <ng-content select="[title]"></ng-content>
      }
    </div>
    <ng-content></ng-content>
    `,
      preserveWhitespaces: false,
      standalone: true,
      imports: [TDSOutletModule]
    }]
  }], () => [{
    type: ElementRef
  }, {
    type: Renderer2
  }, {
    type: void 0,
    decorators: [{
      type: Inject,
      args: [TDSIsNavBarInsideDropDownToken]
    }]
  }], {
    tdsTitle: [{
      type: Input
    }],
    titleElement: [{
      type: ViewChild,
      args: ["titleElement"]
    }]
  });
})();
var TDSNavBarDividerDirective = class _TDSNavBarDividerDirective {
  constructor(elementRef, renderer) {
    this.elementRef = elementRef;
    this.renderer = renderer;
    this.renderer.addClass(elementRef.nativeElement, "tds-dropdown-navbar-item-divider");
  }
  static {
    this.ɵfac = function TDSNavBarDividerDirective_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TDSNavBarDividerDirective)(ɵɵdirectiveInject(ElementRef), ɵɵdirectiveInject(Renderer2));
    };
  }
  static {
    this.ɵdir = ɵɵdefineDirective({
      type: _TDSNavBarDividerDirective,
      selectors: [["", "tds-navbar-divider", ""]],
      exportAs: ["tdsNavBarDivider"],
      standalone: true
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TDSNavBarDividerDirective, [{
    type: Directive,
    args: [{
      selector: "[tds-navbar-divider]",
      exportAs: "tdsNavBarDivider",
      standalone: true
    }]
  }], () => [{
    type: ElementRef
  }, {
    type: Renderer2
  }], null);
})();
var TDSNavBarModule = class _TDSNavBarModule {
  static {
    this.ɵfac = function TDSNavBarModule_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TDSNavBarModule)();
    };
  }
  static {
    this.ɵmod = ɵɵdefineNgModule({
      type: _TDSNavBarModule,
      imports: [TDSNavBarDirective, TDSNavBarItemDirective, TDSSubNavBarComponent, TDSNavBarDividerDirective, TDSNavBarGroupComponent, TDSSubNavBarTitleComponent, TDSSubNavBarInlineChildComponent, TDSSubNavBarNoneInlineChildComponent],
      exports: [TDSNavBarDirective, TDSNavBarItemDirective, TDSSubNavBarComponent, TDSNavBarDividerDirective, TDSNavBarGroupComponent]
    });
  }
  static {
    this.ɵinj = ɵɵdefineInjector({
      imports: [TDSSubNavBarComponent, TDSNavBarGroupComponent, TDSSubNavBarTitleComponent]
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TDSNavBarModule, [{
    type: NgModule,
    args: [{
      // imports: [BidiModule,
      //   CommonModule,
      //   PlatformModule,
      //   OverlayModule,
      //   TDSNoAnimationModule,
      //   TDSOutletModule],
      imports: [TDSNavBarDirective, TDSNavBarItemDirective, TDSSubNavBarComponent, TDSNavBarDividerDirective, TDSNavBarGroupComponent, TDSSubNavBarTitleComponent, TDSSubNavBarInlineChildComponent, TDSSubNavBarNoneInlineChildComponent],
      exports: [TDSNavBarDirective, TDSNavBarItemDirective, TDSSubNavBarComponent, TDSNavBarDividerDirective, TDSNavBarGroupComponent]
    }]
  }], null, null);
})();

// node_modules/tds-ui/fesm2022/tds-ui-menu.mjs
var _c05 = (a0) => [a0];
var _c12 = () => [];
function TDSMenuItemComponent_Conditional_4_Conditional_3_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelement(0, "tds-badge", 9);
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(2);
    ɵɵproperty("standalone", true)("count", ctx_r0.item.badge.count)("tdsStyle", ctx_r0.item.badge.tdsStyle)("tdsTheme", ctx_r0.tdsTheme);
  }
}
function TDSMenuItemComponent_Conditional_4_Conditional_4_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "tds-tag", 10);
    ɵɵtext(1);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(2);
    ɵɵproperty("status", ctx_r0.item.tag.status)("rounded", ctx_r0.item.tag.rounded)("type", ctx_r0.item.tag.type)("tdsTheme", ctx_r0.tdsTheme);
    ɵɵadvance();
    ɵɵtextInterpolate(ctx_r0.item.tag.text);
  }
}
function TDSMenuItemComponent_Conditional_4_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "a", 4)(1, "span", 8);
    ɵɵtext(2);
    ɵɵelementEnd();
    ɵɵtemplate(3, TDSMenuItemComponent_Conditional_4_Conditional_3_Template, 1, 4, "tds-badge", 9)(4, TDSMenuItemComponent_Conditional_4_Conditional_4_Template, 2, 5, "tds-tag", 10);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext();
    ɵɵproperty("routerLink", ɵɵpureFunction1(12, _c05, ctx_r0.item.link))("queryParams", ctx_r0.item.linkProps == null ? null : ctx_r0.item.linkProps.queryParams)("fragment", ctx_r0.item.linkProps == null ? null : ctx_r0.item.linkProps.fragment)("queryParamsHandling", ctx_r0.item.linkProps == null ? null : ctx_r0.item.linkProps.queryParamsHandling)("preserveFragment", ctx_r0.item.linkProps == null ? null : ctx_r0.item.linkProps.preserveFragment)("skipLocationChange", ctx_r0.item.linkProps == null ? null : ctx_r0.item.linkProps.skipLocationChange)("replaceUrl", ctx_r0.item.linkProps == null ? null : ctx_r0.item.linkProps.replaceUrl)("state", ctx_r0.item.linkProps == null ? null : ctx_r0.item.linkProps.state)("routerLinkActive", (ctx_r0.item.linkProps == null ? null : ctx_r0.item.linkProps.routerLinkActive) || ɵɵpureFunction0(14, _c12));
    ɵɵadvance(2);
    ɵɵtextInterpolate(ctx_r0.item.name);
    ɵɵadvance();
    ɵɵconditional(ctx_r0.item.badge ? 3 : -1);
    ɵɵadvance();
    ɵɵconditional(ctx_r0.item.tag ? 4 : -1);
  }
}
function TDSMenuItemComponent_Conditional_5_Conditional_3_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelement(0, "tds-badge", 9);
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(2);
    ɵɵproperty("standalone", true)("count", ctx_r0.item.badge.count)("tdsStyle", ctx_r0.item.badge.tdsStyle)("tdsTheme", ctx_r0.tdsTheme);
  }
}
function TDSMenuItemComponent_Conditional_5_Conditional_4_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "tds-tag", 10);
    ɵɵtext(1);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(2);
    ɵɵproperty("status", ctx_r0.item.tag.status)("rounded", ctx_r0.item.tag.rounded)("type", ctx_r0.item.tag.type)("tdsTheme", ctx_r0.tdsTheme);
    ɵɵadvance();
    ɵɵtextInterpolate(ctx_r0.item.tag.text);
  }
}
function TDSMenuItemComponent_Conditional_5_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "span", 5)(1, "span", 8);
    ɵɵtext(2);
    ɵɵelementEnd();
    ɵɵtemplate(3, TDSMenuItemComponent_Conditional_5_Conditional_3_Template, 1, 4, "tds-badge", 9)(4, TDSMenuItemComponent_Conditional_5_Conditional_4_Template, 2, 5, "tds-tag", 10);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext();
    ɵɵadvance(2);
    ɵɵtextInterpolate(ctx_r0.item.name);
    ɵɵadvance();
    ɵɵconditional(ctx_r0.item.badge ? 3 : -1);
    ɵɵadvance();
    ɵɵconditional(ctx_r0.item.tag ? 4 : -1);
  }
}
function TDSMenuItemComponent_Conditional_6_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "div", 6);
    ɵɵelement(1, "span", 11);
    ɵɵelementEnd();
  }
}
function TDSMenuItemComponent_Conditional_7_For_4_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelement(0, "tds-menu-item", 13);
  }
  if (rf & 2) {
    const menu_r2 = ctx.$implicit;
    const ctx_r0 = ɵɵnextContext(2);
    ɵɵproperty("options", ctx_r0.options)("item", menu_r2)("isSelected", menu_r2.isSelected)("matchRouterExact", ctx_r0.matchRouterExact)("matchRouter", ctx_r0.matchRouter)("tdsTheme", ctx_r0.tdsTheme);
  }
}
function TDSMenuItemComponent_Conditional_7_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "div", 7);
    ɵɵpipe(1, "tdsMapper");
    ɵɵelementStart(2, "div", 12);
    ɵɵrepeaterCreate(3, TDSMenuItemComponent_Conditional_7_For_4_Template, 1, 6, "tds-menu-item", 13, ɵɵrepeaterTrackByIdentity);
    ɵɵelementEnd()();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext();
    ɵɵproperty("@menuCollapseMotion", ɵɵpipeBind3(1, 1, ctx_r0.item.isOpen, ctx_r0.mapperExpandState, ctx_r0.hasAllHidden));
    ɵɵadvance(3);
    ɵɵrepeater(ctx_r0.item.listChild);
  }
}
var _c22 = ["*"];
function TDSMenuGroupInlineComponent_Conditional_0_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "div", 0);
    ɵɵtext(1);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext();
    ɵɵadvance();
    ɵɵtextInterpolate1(" ", ctx_r0.item.groupTitle, "\n");
  }
}
function TDSMenuGroupInlineComponent_Conditional_2_Conditional_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelement(0, "div", 9);
    ɵɵpipe(1, "tdsSanitizer");
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(2);
    ɵɵproperty("innerHTML", ɵɵpipeBind2(1, 1, ctx_r0.item.htmlIcon, "html"), ɵɵsanitizeHtml);
  }
}
function TDSMenuGroupInlineComponent_Conditional_2_Conditional_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelement(0, "span", 10);
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(2);
    ɵɵproperty("ngClass", ctx_r0.item.icon);
  }
}
function TDSMenuGroupInlineComponent_Conditional_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "div", 8);
    ɵɵtemplate(1, TDSMenuGroupInlineComponent_Conditional_2_Conditional_1_Template, 2, 4, "div", 9)(2, TDSMenuGroupInlineComponent_Conditional_2_Conditional_2_Template, 1, 1, "span", 10);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext();
    ɵɵclassProp("menu-active", ctx_r0.isSelected);
    ɵɵadvance();
    ɵɵconditional(ctx_r0.item.htmlIcon ? 1 : -1);
    ɵɵadvance();
    ɵɵconditional(ctx_r0.hasIcon && !ctx_r0.item.htmlIcon ? 2 : -1);
  }
}
function TDSMenuGroupInlineComponent_Conditional_4_Conditional_3_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelement(0, "tds-badge", 13);
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(2);
    ɵɵproperty("standalone", true)("overflowCount", ctx_r0.item.badge.overflowCount)("count", ctx_r0.item.badge.count)("tdsStyle", ctx_r0.item.badge.tdsStyle)("tdsTheme", ctx_r0.tdsTheme);
  }
}
function TDSMenuGroupInlineComponent_Conditional_4_Conditional_4_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "tds-tag", 14);
    ɵɵtext(1);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(2);
    ɵɵproperty("status", ctx_r0.item.tag.status)("rounded", ctx_r0.item.tag.rounded)("type", ctx_r0.item.tag.type)("tdsTheme", ctx_r0.tdsTheme);
    ɵɵadvance();
    ɵɵtextInterpolate(ctx_r0.item.tag.text);
  }
}
function TDSMenuGroupInlineComponent_Conditional_4_Template(rf, ctx) {
  if (rf & 1) {
    const _r2 = ɵɵgetCurrentView();
    ɵɵelementStart(0, "a", 11);
    ɵɵlistener("click", function TDSMenuGroupInlineComponent_Conditional_4_Template_a_click_0_listener($event) {
      ɵɵrestoreView(_r2);
      const ctx_r0 = ɵɵnextContext();
      return ɵɵresetView(ctx_r0.onClickItem($event));
    });
    ɵɵelementStart(1, "span", 12);
    ɵɵtext(2);
    ɵɵelementEnd();
    ɵɵtemplate(3, TDSMenuGroupInlineComponent_Conditional_4_Conditional_3_Template, 1, 5, "tds-badge", 13)(4, TDSMenuGroupInlineComponent_Conditional_4_Conditional_4_Template, 2, 5, "tds-tag", 14);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext();
    ɵɵproperty("routerLink", ɵɵpureFunction1(12, _c05, ctx_r0.item.link))("queryParams", ctx_r0.item.linkProps == null ? null : ctx_r0.item.linkProps.queryParams)("fragment", ctx_r0.item.linkProps == null ? null : ctx_r0.item.linkProps.fragment)("queryParamsHandling", ctx_r0.item.linkProps == null ? null : ctx_r0.item.linkProps.queryParamsHandling)("preserveFragment", ctx_r0.item.linkProps == null ? null : ctx_r0.item.linkProps.preserveFragment)("skipLocationChange", ctx_r0.item.linkProps == null ? null : ctx_r0.item.linkProps.skipLocationChange)("replaceUrl", ctx_r0.item.linkProps == null ? null : ctx_r0.item.linkProps.replaceUrl)("state", ctx_r0.item.linkProps == null ? null : ctx_r0.item.linkProps.state)("routerLinkActive", (ctx_r0.item.linkProps == null ? null : ctx_r0.item.linkProps.routerLinkActive) || ɵɵpureFunction0(14, _c12));
    ɵɵadvance(2);
    ɵɵtextInterpolate(ctx_r0.item.name);
    ɵɵadvance();
    ɵɵconditional(ctx_r0.item.badge ? 3 : -1);
    ɵɵadvance();
    ɵɵconditional(ctx_r0.item.tag ? 4 : -1);
  }
}
function TDSMenuGroupInlineComponent_Conditional_5_Conditional_3_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelement(0, "tds-badge", 13);
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(2);
    ɵɵproperty("standalone", true)("overflowCount", ctx_r0.item.badge.overflowCount)("count", ctx_r0.item.badge.count)("tdsStyle", ctx_r0.item.badge.tdsStyle)("tdsTheme", ctx_r0.tdsTheme);
  }
}
function TDSMenuGroupInlineComponent_Conditional_5_Conditional_4_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "tds-tag", 14);
    ɵɵtext(1);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(2);
    ɵɵproperty("status", ctx_r0.item.tag.status)("rounded", ctx_r0.item.tag.rounded)("type", ctx_r0.item.tag.type)("tdsTheme", ctx_r0.tdsTheme);
    ɵɵadvance();
    ɵɵtextInterpolate(ctx_r0.item.tag.text);
  }
}
function TDSMenuGroupInlineComponent_Conditional_5_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "span", 5)(1, "span", 12);
    ɵɵtext(2);
    ɵɵelementEnd();
    ɵɵtemplate(3, TDSMenuGroupInlineComponent_Conditional_5_Conditional_3_Template, 1, 5, "tds-badge", 13)(4, TDSMenuGroupInlineComponent_Conditional_5_Conditional_4_Template, 2, 5, "tds-tag", 14);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext();
    ɵɵadvance(2);
    ɵɵtextInterpolate(ctx_r0.item.name);
    ɵɵadvance();
    ɵɵconditional(ctx_r0.item.badge ? 3 : -1);
    ɵɵadvance();
    ɵɵconditional(ctx_r0.item.tag ? 4 : -1);
  }
}
function TDSMenuGroupInlineComponent_Conditional_6_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "div", 6);
    ɵɵelement(1, "span", 15);
    ɵɵelementEnd();
  }
}
function TDSMenuGroupInlineComponent_Conditional_7_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "div", 7);
    ɵɵpipe(1, "tdsMapper");
    ɵɵelementStart(2, "div", 16);
    ɵɵprojection(3);
    ɵɵelementEnd()();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext();
    ɵɵproperty("@menuCollapseMotion", ɵɵpipeBind3(1, 1, ctx_r0.item.isOpen, ctx_r0.mapperExpandState, ctx_r0.hasAllHidden));
  }
}
var _c32 = (a0) => ({
  "hidden": a0
});
var _c42 = (a0) => ({
  $implicit: a0
});
var _c52 = (a0, a1, a2) => ({
  "tds-menu-light": a0,
  "tds-menu-dark": a1,
  "tds-menu-default": a2
});
function TDSMenuGroupPopupComponent_Conditional_2_Conditional_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r4 = ɵɵgetCurrentView();
    ɵɵelementStart(0, "div", 7)(1, "a", 8);
    ɵɵlistener("click", function TDSMenuGroupPopupComponent_Conditional_2_Conditional_1_Template_a_click_1_listener($event) {
      ɵɵrestoreView(_r4);
      const ctx_r2 = ɵɵnextContext(2);
      return ɵɵresetView(ctx_r2.onClickItem($event));
    });
    ɵɵelementContainer(2, 9);
    ɵɵelementEnd()();
  }
  if (rf & 2) {
    const ctx_r2 = ɵɵnextContext(2);
    const iconTmpl_r5 = ɵɵreference(6);
    ɵɵadvance();
    ɵɵproperty("routerLink", ɵɵpureFunction1(11, _c05, ctx_r2.item.link))("queryParams", ctx_r2.item.linkProps == null ? null : ctx_r2.item.linkProps.queryParams)("fragment", ctx_r2.item.linkProps == null ? null : ctx_r2.item.linkProps.fragment)("queryParamsHandling", ctx_r2.item.linkProps == null ? null : ctx_r2.item.linkProps.queryParamsHandling)("preserveFragment", ctx_r2.item.linkProps == null ? null : ctx_r2.item.linkProps.preserveFragment)("skipLocationChange", ctx_r2.item.linkProps == null ? null : ctx_r2.item.linkProps.skipLocationChange)("replaceUrl", ctx_r2.item.linkProps == null ? null : ctx_r2.item.linkProps.replaceUrl)("state", ctx_r2.item.linkProps == null ? null : ctx_r2.item.linkProps.state)("routerLinkActive", (ctx_r2.item.linkProps == null ? null : ctx_r2.item.linkProps.routerLinkActive) || ɵɵpureFunction0(13, _c12));
    ɵɵadvance();
    ɵɵproperty("ngTemplateOutlet", iconTmpl_r5)("ngTemplateOutletContext", ɵɵpureFunction1(14, _c42, ctx_r2.item));
  }
}
function TDSMenuGroupPopupComponent_Conditional_2_Template(rf, ctx) {
  if (rf & 1) {
    const _r2 = ɵɵgetCurrentView();
    ɵɵelementStart(0, "div", 6);
    ɵɵlistener("click", function TDSMenuGroupPopupComponent_Conditional_2_Template_div_click_0_listener($event) {
      ɵɵrestoreView(_r2);
      const ctx_r2 = ɵɵnextContext();
      return ɵɵresetView(ctx_r2.onClickItem($event));
    });
    ɵɵtemplate(1, TDSMenuGroupPopupComponent_Conditional_2_Conditional_1_Template, 3, 16, "div", 7);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r2 = ɵɵnextContext();
    ɵɵproperty("tooltipTitle", ctx_r2.item.name);
    ɵɵadvance();
    ɵɵconditional(ctx_r2.hasIcon ? 1 : -1);
  }
}
function TDSMenuGroupPopupComponent_Conditional_3_Conditional_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "div", 7);
    ɵɵelementContainer(1, 9);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r2 = ɵɵnextContext(2);
    const iconTmpl_r5 = ɵɵreference(6);
    ɵɵadvance();
    ɵɵproperty("ngTemplateOutlet", iconTmpl_r5)("ngTemplateOutletContext", ɵɵpureFunction1(2, _c42, ctx_r2.item));
  }
}
function TDSMenuGroupPopupComponent_Conditional_3_Template(rf, ctx) {
  if (rf & 1) {
    const _r6 = ɵɵgetCurrentView();
    ɵɵelementStart(0, "div", 10);
    ɵɵlistener("click", function TDSMenuGroupPopupComponent_Conditional_3_Template_div_click_0_listener($event) {
      ɵɵrestoreView(_r6);
      const ctx_r2 = ɵɵnextContext();
      return ɵɵresetView(ctx_r2.onClickItem($event));
    });
    ɵɵtemplate(1, TDSMenuGroupPopupComponent_Conditional_3_Conditional_1_Template, 2, 4, "div", 7);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r2 = ɵɵnextContext();
    ɵɵadvance();
    ɵɵconditional(ctx_r2.hasIcon ? 1 : -1);
  }
}
function TDSMenuGroupPopupComponent_ng_template_4_Conditional_0_Template(rf, ctx) {
  if (rf & 1) {
    const _r7 = ɵɵgetCurrentView();
    ɵɵelementStart(0, "div", 12);
    ɵɵlistener("mouseenter", function TDSMenuGroupPopupComponent_ng_template_4_Conditional_0_Template_div_mouseenter_0_listener() {
      ɵɵrestoreView(_r7);
      const ctx_r2 = ɵɵnextContext(2);
      return ɵɵresetView(ctx_r2.setMouseState(true));
    })("mouseleave", function TDSMenuGroupPopupComponent_ng_template_4_Conditional_0_Template_div_mouseleave_0_listener() {
      ɵɵrestoreView(_r7);
      const ctx_r2 = ɵɵnextContext(2);
      return ɵɵresetView(ctx_r2.setMouseState(false));
    });
    ɵɵelementStart(1, "div", 13)(2, "div", 14)(3, "div", 15);
    ɵɵprojection(4);
    ɵɵelementEnd()()()();
  }
  if (rf & 2) {
    const ctx_r2 = ɵɵnextContext(2);
    ɵɵstyleProp("margin-top", ctx_r2.position == "rightTop" ? -5 : 0, "px");
    ɵɵproperty("ngClass", ɵɵpureFunction3(4, _c52, ctx_r2.mode == "light", ctx_r2.mode == "dark", ctx_r2.mode == "default"))("@slideMotion", void 0);
  }
}
function TDSMenuGroupPopupComponent_ng_template_4_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, TDSMenuGroupPopupComponent_ng_template_4_Conditional_0_Template, 5, 8, "div", 11);
  }
  if (rf & 2) {
    const ctx_r2 = ɵɵnextContext();
    ɵɵconditional(ctx_r2.hasListChild ? 0 : -1);
  }
}
function TDSMenuGroupPopupComponent_ng_template_5_Conditional_0_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "tds-badge", 16);
    ɵɵelement(1, "span", 17);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const item_r8 = ɵɵnextContext().$implicit;
    const ctx_r2 = ɵɵnextContext();
    ɵɵproperty("status", item_r8.dot.status)("dot", true)("tdsTheme", ctx_r2.tdsTheme);
    ɵɵadvance();
    ɵɵproperty("ngClass", item_r8.icon);
  }
}
function TDSMenuGroupPopupComponent_ng_template_5_Conditional_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelement(0, "span", 17);
  }
  if (rf & 2) {
    const item_r8 = ɵɵnextContext().$implicit;
    ɵɵproperty("ngClass", item_r8.icon);
  }
}
function TDSMenuGroupPopupComponent_ng_template_5_Conditional_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelement(0, "span", 18);
    ɵɵpipe(1, "tdsSanitizer");
  }
  if (rf & 2) {
    const item_r8 = ɵɵnextContext().$implicit;
    ɵɵproperty("innerHTML", ɵɵpipeBind2(1, 1, item_r8.htmlIcon, "html"), ɵɵsanitizeHtml);
  }
}
function TDSMenuGroupPopupComponent_ng_template_5_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, TDSMenuGroupPopupComponent_ng_template_5_Conditional_0_Template, 2, 4, "tds-badge", 16)(1, TDSMenuGroupPopupComponent_ng_template_5_Conditional_1_Template, 1, 1, "span", 17)(2, TDSMenuGroupPopupComponent_ng_template_5_Conditional_2_Template, 2, 4, "span", 18);
  }
  if (rf & 2) {
    const item_r8 = ctx.$implicit;
    ɵɵconditional(item_r8.dot ? 0 : -1);
    ɵɵadvance();
    ɵɵconditional(!item_r8.dot && !item_r8.htmlIcon ? 1 : -1);
    ɵɵadvance();
    ɵɵconditional(!item_r8.dot && item_r8.htmlIcon ? 2 : -1);
  }
}
var _c62 = ["logo-text"];
var _c72 = [[["", "logo-text", ""]], [["", "logo", ""]]];
var _c82 = ["[logo-text]", "[logo]"];
var _c92 = (a0) => ({
  " overflow-x-hidden": a0
});
var _c102 = (a0) => ({
  "flex-col": a0
});
var _c11 = (a0) => ({
  "tds-menu-footer-icon-inline-collapsed": a0
});
var _c122 = (a0, a1) => ({
  "tdsi-chevron-right-fill": a0,
  "tdsi-chevron-left-fill": a1
});
function TDSMenuComponent_Conditional_1_Conditional_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵprojection(0);
  }
}
function TDSMenuComponent_Conditional_1_Conditional_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵprojection(0, 1);
  }
}
function TDSMenuComponent_Conditional_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "div", 1);
    ɵɵtemplate(1, TDSMenuComponent_Conditional_1_Conditional_1_Template, 1, 0)(2, TDSMenuComponent_Conditional_1_Conditional_2_Template, 1, 0);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext();
    ɵɵadvance();
    ɵɵconditional(!ctx_r0.inlineCollapsed ? 1 : -1);
    ɵɵadvance();
    ɵɵconditional(ctx_r0.inlineCollapsed ? 2 : -1);
  }
}
function TDSMenuComponent_Conditional_3_For_1_Conditional_0_For_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelement(0, "tds-menu-item", 6);
  }
  if (rf & 2) {
    const children_r2 = ctx.$implicit;
    const ctx_r0 = ɵɵnextContext(4);
    ɵɵclassMap(ɵɵpureFunction3(9, _c52, ctx_r0.mode == "light", ctx_r0.mode == "dark", ctx_r0.mode == "default"));
    ɵɵproperty("options", ctx_r0.options)("item", children_r2)("isSelected", children_r2.isSelected)("parentIsGroup", true)("matchRouterExact", ctx_r0.matchRouterExact)("matchRouter", ctx_r0.matchRouter)("tdsTheme", ctx_r0.mode);
  }
}
function TDSMenuComponent_Conditional_3_For_1_Conditional_0_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "tds-menu-group-popup", 4);
    ɵɵrepeaterCreate(1, TDSMenuComponent_Conditional_3_For_1_Conditional_0_For_2_Template, 1, 13, "tds-menu-item", 5, ɵɵrepeaterTrackByIdentity);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const item_r3 = ɵɵnextContext().$implicit;
    const ctx_r0 = ɵɵnextContext(2);
    ɵɵproperty("options", ctx_r0.options)("item", item_r3)("tdsTheme", ctx_r0.mode);
    ɵɵadvance();
    ɵɵrepeater(item_r3.listChild);
  }
}
function TDSMenuComponent_Conditional_3_For_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, TDSMenuComponent_Conditional_3_For_1_Conditional_0_Template, 3, 3, "tds-menu-group-popup", 4);
  }
  if (rf & 2) {
    const item_r3 = ctx.$implicit;
    ɵɵconditional(!item_r3.hidden ? 0 : -1);
  }
}
function TDSMenuComponent_Conditional_3_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵrepeaterCreate(0, TDSMenuComponent_Conditional_3_For_1_Template, 1, 1, null, null, ɵɵrepeaterTrackByIdentity);
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext();
    ɵɵrepeater(ctx_r0.listMenu);
  }
}
function TDSMenuComponent_Conditional_4_For_1_Conditional_0_For_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelement(0, "tds-menu-item", 9);
  }
  if (rf & 2) {
    const children_r4 = ctx.$implicit;
    const ctx_r0 = ɵɵnextContext(4);
    ɵɵclassMap(ɵɵpureFunction3(10, _c52, ctx_r0.mode == "light", ctx_r0.mode == "dark", ctx_r0.mode == "default"));
    ɵɵproperty("options", ctx_r0.options)("showIcon", ctx_r0.hasIcon)("item", children_r4)("isSelected", children_r4.isSelected)("parentIsGroup", true)("matchRouterExact", ctx_r0.matchRouterExact)("matchRouter", ctx_r0.matchRouter)("tdsTheme", ctx_r0.mode);
  }
}
function TDSMenuComponent_Conditional_4_For_1_Conditional_0_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "tds-menu-group-inline", 7);
    ɵɵrepeaterCreate(1, TDSMenuComponent_Conditional_4_For_1_Conditional_0_For_2_Template, 1, 14, "tds-menu-item", 8, ɵɵrepeaterTrackByIdentity);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const item_r5 = ɵɵnextContext().$implicit;
    const ctx_r0 = ɵɵnextContext(2);
    ɵɵproperty("options", ctx_r0.options)("showIcon", ctx_r0.hasIcon)("item", item_r5)("tdsTheme", ctx_r0.mode);
    ɵɵadvance();
    ɵɵrepeater(item_r5.listChild);
  }
}
function TDSMenuComponent_Conditional_4_For_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, TDSMenuComponent_Conditional_4_For_1_Conditional_0_Template, 3, 4, "tds-menu-group-inline", 7);
  }
  if (rf & 2) {
    const item_r5 = ctx.$implicit;
    ɵɵconditional(!item_r5.hidden ? 0 : -1);
  }
}
function TDSMenuComponent_Conditional_4_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵrepeaterCreate(0, TDSMenuComponent_Conditional_4_For_1_Template, 1, 1, null, null, ɵɵrepeaterTrackByIdentity);
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext();
    ɵɵrepeater(ctx_r0.listMenu);
  }
}
function TDSMenuComponent_Conditional_5_Conditional_1_ng_container_0_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainer(0);
  }
}
function TDSMenuComponent_Conditional_5_Conditional_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, TDSMenuComponent_Conditional_5_Conditional_1_ng_container_0_Template, 1, 0, "ng-container", 10);
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(2);
    ɵɵproperty("ngTemplateOutlet", ctx_r0.menuFooter)("ngTemplateOutletContext", ɵɵpureFunction1(2, _c42, ctx_r0.inlineCollapsed));
  }
}
function TDSMenuComponent_Conditional_5_Conditional_2_Template(rf, ctx) {
  if (rf & 1) {
    const _r6 = ɵɵgetCurrentView();
    ɵɵelementStart(0, "div", 11);
    ɵɵnamespaceSVG();
    ɵɵelementStart(1, "svg", 12);
    ɵɵelement(2, "path", 13)(3, "path", 14);
    ɵɵelementEnd()();
    ɵɵnamespaceHTML();
    ɵɵelementStart(4, "div", 15);
    ɵɵlistener("click", function TDSMenuComponent_Conditional_5_Conditional_2_Template_div_click_4_listener() {
      ɵɵrestoreView(_r6);
      const ctx_r0 = ɵɵnextContext(2);
      return ɵɵresetView(ctx_r0.onClickInlineCollapsed());
    });
    ɵɵelement(5, "span", 16);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(2);
    ɵɵadvance(4);
    ɵɵproperty("ngClass", ɵɵpureFunction1(2, _c11, ctx_r0.inlineCollapsed));
    ɵɵadvance();
    ɵɵproperty("ngClass", ɵɵpureFunction2(4, _c122, ctx_r0.inlineCollapsed, !ctx_r0.inlineCollapsed));
  }
}
function TDSMenuComponent_Conditional_5_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "div", 3);
    ɵɵtemplate(1, TDSMenuComponent_Conditional_5_Conditional_1_Template, 1, 4, "ng-container")(2, TDSMenuComponent_Conditional_5_Conditional_2_Template, 6, 7);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext();
    ɵɵproperty("ngClass", ɵɵpureFunction1(2, _c102, ctx_r0.inlineCollapsed));
    ɵɵadvance();
    ɵɵconditional(ctx_r0.menuFooter ? 1 : 2);
  }
}
var TDSMenuService = class _TDSMenuService {
  constructor() {
    this.mode$ = new import_rxjs5.BehaviorSubject("dark");
    this.isChildSubMenuOpen$ = new import_rxjs5.BehaviorSubject(false);
    this.descendantMenuItemClick$ = new import_rxjs5.Subject();
    this.groupMenuOpen$ = new import_rxjs5.BehaviorSubject(null);
  }
  onDescendantMenuItemClick(menu) {
    this.descendantMenuItemClick$.next(menu);
  }
  onModeChange(mode) {
    this.mode$.next(mode);
  }
  static {
    this.ɵfac = function TDSMenuService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TDSMenuService)();
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _TDSMenuService,
      factory: _TDSMenuService.ɵfac
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TDSMenuService, [{
    type: Injectable
  }], null, null);
})();
var TDSMenuItemComponent = class _TDSMenuItemComponent {
  get uid() {
    return this.item ? this.item.uid : "";
  }
  constructor(_cdr, tdsMenuService, routerLink, routerLinkWithHref, router) {
    this._cdr = _cdr;
    this.tdsMenuService = tdsMenuService;
    this.routerLink = routerLink;
    this.routerLinkWithHref = routerLinkWithHref;
    this.router = router;
    this.destroy$ = new import_rxjs5.Subject();
    this.showIcon = true;
    this.isSelected = false;
    this.parentIsGroup = false;
    this.inlineCollapsed = false;
    this.matchRouterExact = false;
    this.matchRouter = false;
    this.tdsTheme = "default";
    this.options = {
      background: "bg-white dark:bg-d-neutral-3-200",
      backgroundItem: "bg-white dark:bg-d-neutral-3-200",
      backgroundItemSelected: "bg-white dark:bg-d-neutral-3-200",
      backgroundItemHover: "dark:hover:bg-d-neutral-3-300  hover:bg-neutral-3-50"
    };
    this.selected$ = new import_rxjs5.Subject();
    this.isOpenPopup = false;
    this.cdkOverlayOrigin = null;
    this.mapperExpandState = (isOpen, hasAllHidden) => {
      return isOpen && !hasAllHidden ? "expanded" : "collapsed";
    };
    if (router) {
      this.router.events.pipe((0, import_operators5.takeUntil)(this.destroy$), (0, import_operators5.filter)((e) => e instanceof NavigationEnd)).subscribe(() => {
        this.updateRouterActive();
      });
    }
  }
  ngOnInit() {
  }
  ngAfterViewInit() {
    this.listenItemChangeSelected();
  }
  ngOnChanges(changes) {
    if (changes["item"]) {
      this._cdr.markForCheck();
    }
    if (changes.isSelected) {
      this.setSelectedState(this.isSelected);
    }
  }
  onClickItem(e) {
    if (this.item.disabled) {
      e.stopPropagation();
      e.preventDefault();
    }
    if (this.hasListChild) {
      this.item.isOpen = !this.item.isOpen;
    } else {
      if (this.hasLink) {
        this.p_NavigateByUrl();
      }
      this.tdsMenuService.onDescendantMenuItemClick(this);
    }
  }
  get hasListChild() {
    return TDSHelperArray.hasListValue(this.item.listChild);
  }
  get hasAllHidden() {
    let childs = this.item.listChild?.find((f) => {
      return !f.hidden;
    });
    return childs == void 0;
  }
  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
  setSelectedState(value) {
    this.isSelected = value;
    this.item.isSelected = value;
    this.selected$.next(value);
    if (this.isSelected) {
      if (!this.item.isOpen) {
        this.item.isOpen = true;
      }
    }
    this._cdr.markForCheck();
  }
  updateRouterActive() {
    if (this.hasListChild || !this.listOfRouterLink || !this.listOfRouterLinkWithHref || !this.router || !this.router.navigated || !this.matchRouter) {
      return;
    }
    Promise.resolve().then(() => {
      const hasActiveLinks = this.hasActiveLinks();
      if (!this.hasListChild) {
        if (this.isSelected !== hasActiveLinks) {
          this.isSelected = hasActiveLinks;
          this.setSelectedState(this.isSelected);
        }
      }
    });
  }
  hasActiveLinks() {
    const isActiveCheckFn = this.isLinkActive(this.router);
    return this.routerLink && isActiveCheckFn(this.routerLink) || this.routerLinkWithHref && isActiveCheckFn(this.routerLinkWithHref) || this.listOfRouterLink.some(isActiveCheckFn) || this.listOfRouterLinkWithHref.some(isActiveCheckFn);
  }
  isLinkActive(router) {
    const isActiveMatchOptions = this.item.linkProps?.routerLinkActiveOptions || {
      paths: this.matchRouterExact ? "exact" : "subset",
      queryParams: this.matchRouterExact ? "exact" : "subset",
      fragment: "ignored",
      matrixParams: "ignored"
    };
    return (link) => router.isActive(link.urlTree, isActiveMatchOptions);
  }
  setSelectedStateListChildren(uid) {
    if (TDSHelperArray.hasListValue(this.listItem)) {
      this.listItem.forEach((f) => {
        f.setSelectedStateListChildren(uid);
      });
    } else {
      this.setSelectedState(uid === this.uid);
    }
  }
  get hasLink() {
    return !this.item.disabled && !this.hasListChild && TDSHelperObject.hasValue(this.item.link);
  }
  get hasIcon() {
    return this.item && (TDSHelperString.hasValueString(this.item.icon) || TDSHelperString.hasValueString(this.item.htmlIcon));
  }
  listenItemChangeSelected() {
    if (this.listOfRouterLink) this.listOfRouterLink.changes.pipe((0, import_operators5.takeUntil)(this.destroy$)).subscribe(() => this.updateRouterActive());
    if (this.listOfRouterLinkWithHref) this.listOfRouterLinkWithHref.changes.pipe((0, import_operators5.takeUntil)(this.destroy$)).subscribe(() => this.updateRouterActive());
    if (TDSHelperArray.hasListValue(this.listItem)) {
      const listOfTDSMenuItemComponent = this.listItem;
      const changes = listOfTDSMenuItemComponent.changes;
      const mergedObservable = (0, import_rxjs5.merge)(...[changes, ...listOfTDSMenuItemComponent.map((menu) => menu.selected$)]);
      changes.pipe((0, import_operators5.startWith)(listOfTDSMenuItemComponent), (0, import_operators5.switchMap)(() => mergedObservable), (0, import_operators5.startWith)(true), (0, import_operators5.map)(() => listOfTDSMenuItemComponent.some((e) => e.isSelected)), (0, import_operators5.debounceTime)(100), (0, import_operators5.distinctUntilChanged)(), (0, import_operators5.takeUntil)(this.destroy$)).subscribe((selected) => {
        this.setSelectedState(selected);
      });
    }
    this.updateRouterActive();
  }
  setOpen(value) {
    if (this.item.isOpen != value) {
      this.item.isOpen = value;
      this._cdr.markForCheck();
    }
  }
  setOpenChildren(value) {
    if (this.listItem.length) {
      this.listItem.forEach((f) => {
        f.setOpenChildren(value);
      });
      this.setOpen(value);
    }
    this._cdr.markForCheck();
  }
  p_NavigateByUrl() {
    if (!TDSHelperObject.hasValue(this.item.linkProps)) {
      this.router?.navigateByUrl(this.item.link);
    } else {
      this.router?.navigate([this.item.link], {
        queryParams: this.item.linkProps?.queryParams,
        fragment: this.item.linkProps?.fragment,
        queryParamsHandling: this.item.linkProps?.queryParamsHandling,
        preserveFragment: this.item.linkProps?.preserveFragment,
        skipLocationChange: this.item.linkProps?.skipLocationChange,
        replaceUrl: this.item.linkProps?.replaceUrl,
        state: this.item.linkProps?.state
      });
    }
  }
  static {
    this.ɵfac = function TDSMenuItemComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TDSMenuItemComponent)(ɵɵdirectiveInject(ChangeDetectorRef), ɵɵdirectiveInject(TDSMenuService), ɵɵdirectiveInject(RouterLink, 8), ɵɵdirectiveInject(RouterLink, 8), ɵɵdirectiveInject(Router, 8));
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _TDSMenuItemComponent,
      selectors: [["tds-menu-item"]],
      viewQuery: function TDSMenuItemComponent_Query(rf, ctx) {
        if (rf & 1) {
          ɵɵviewQuery(CdkOverlayOrigin, 7, ElementRef);
          ɵɵviewQuery(_TDSMenuItemComponent, 5);
          ɵɵviewQuery(RouterLink, 5);
          ɵɵviewQuery(RouterLink, 5);
        }
        if (rf & 2) {
          let _t;
          ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.cdkOverlayOrigin = _t.first);
          ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.listItem = _t);
          ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.listOfRouterLink = _t);
          ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.listOfRouterLinkWithHref = _t);
        }
      },
      hostVars: 16,
      hostBindings: function TDSMenuItemComponent_HostBindings(rf, ctx) {
        if (rf & 2) {
          ɵɵclassProp("tds-menu-item-disabled", ctx.item.disabled)("tds-menu-item-hidden", ctx.item.hidden)("tds-menu-item-active", ctx.isSelected)("tds-menu-item-opened", !!ctx.item.isOpen && ctx.hasListChild)("tds-menu-item-show-icon", ctx.showIcon)("tds-menu-item-parent-is-group", ctx.parentIsGroup)("tds-menu-item-hidden-icon", !ctx.showIcon)("tds-menu-item-parent-is-item", !ctx.parentIsGroup);
        }
      },
      inputs: {
        showIcon: "showIcon",
        item: "item",
        isSelected: "isSelected",
        parentIsGroup: "parentIsGroup",
        inlineCollapsed: "inlineCollapsed",
        matchRouterExact: "matchRouterExact",
        matchRouter: "matchRouter",
        tdsTheme: "tdsTheme",
        options: "options",
        isOpenPopup: "isOpenPopup"
      },
      standalone: true,
      features: [ɵɵNgOnChangesFeature, ɵɵStandaloneFeature],
      decls: 8,
      vars: 7,
      consts: [[1, "tds-menu-item-wrapper", 3, "click"], [1, "tds-menu-item-badge"], ["status", "primary", 3, "tdsTheme"], [1, "tds-menu-item-title"], [1, "tds-menu-item-has-link", 3, "routerLink", "queryParams", "fragment", "queryParamsHandling", "preserveFragment", "skipLocationChange", "replaceUrl", "state", "routerLinkActive"], [1, "tds-menu-item-text"], [1, "tds-menu-item-arrow"], [1, "tds-menu-item-list-child"], [1, "truncate"], [3, "standalone", "count", "tdsStyle", "tdsTheme"], [3, "status", "rounded", "type", "tdsTheme"], [1, "tdsi-chevron-right-fill"], [1, "w-full", "flex", "flex-col"], [3, "options", "item", "isSelected", "matchRouterExact", "matchRouter", "tdsTheme"]],
      template: function TDSMenuItemComponent_Template(rf, ctx) {
        if (rf & 1) {
          ɵɵelementStart(0, "div", 0);
          ɵɵlistener("click", function TDSMenuItemComponent_Template_div_click_0_listener($event) {
            return ctx.onClickItem($event);
          });
          ɵɵelementStart(1, "div", 1);
          ɵɵelement(2, "tds-badge", 2);
          ɵɵelementEnd();
          ɵɵelementStart(3, "div", 3);
          ɵɵtemplate(4, TDSMenuItemComponent_Conditional_4_Template, 5, 15, "a", 4)(5, TDSMenuItemComponent_Conditional_5_Template, 5, 3, "span", 5);
          ɵɵelementEnd();
          ɵɵtemplate(6, TDSMenuItemComponent_Conditional_6_Template, 2, 0, "div", 6);
          ɵɵelementEnd();
          ɵɵtemplate(7, TDSMenuItemComponent_Conditional_7_Template, 5, 5, "div", 7);
        }
        if (rf & 2) {
          ɵɵadvance(2);
          ɵɵclassProp("invisible", !(ctx.isSelected && !ctx.hasListChild));
          ɵɵproperty("tdsTheme", ctx.tdsTheme);
          ɵɵadvance(2);
          ɵɵconditional(ctx.hasLink ? 4 : -1);
          ɵɵadvance();
          ɵɵconditional(!ctx.hasLink ? 5 : -1);
          ɵɵadvance();
          ɵɵconditional(ctx.hasListChild && !ctx.hasAllHidden ? 6 : -1);
          ɵɵadvance();
          ɵɵconditional(ctx.hasListChild ? 7 : -1);
        }
      },
      dependencies: [_TDSMenuItemComponent, RouterModule, RouterLink, RouterLinkActive, TDSTagModule, TDSTagComponent, TDSBadgeModule, TDSBadgeComponent, TDSMapperPipeModule, TDSMapperPipe],
      encapsulation: 2,
      data: {
        animation: [menuCollapseMotion]
      },
      changeDetection: 0
    });
  }
};
__decorate([InputBoolean()], TDSMenuItemComponent.prototype, "parentIsGroup", void 0);
__decorate([InputBoolean()], TDSMenuItemComponent.prototype, "inlineCollapsed", void 0);
__decorate([InputBoolean()], TDSMenuItemComponent.prototype, "matchRouterExact", void 0);
__decorate([InputBoolean()], TDSMenuItemComponent.prototype, "matchRouter", void 0);
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TDSMenuItemComponent, [{
    type: Component,
    args: [{
      selector: "tds-menu-item",
      changeDetection: ChangeDetectionStrategy.OnPush,
      encapsulation: ViewEncapsulation$1.None,
      animations: [menuCollapseMotion],
      host: {
        "[class.tds-menu-item-disabled]": "item.disabled",
        "[class.tds-menu-item-hidden]": "item.hidden",
        "[class.tds-menu-item-active]": "isSelected",
        "[class.tds-menu-item-opened]": "!!item.isOpen && hasListChild",
        "[class.tds-menu-item-show-icon]": "showIcon",
        "[class.tds-menu-item-parent-is-group]": "parentIsGroup",
        "[class.tds-menu-item-hidden-icon]": "!showIcon",
        "[class.tds-menu-item-parent-is-item]": "!parentIsGroup"
      },
      standalone: true,
      imports: [RouterModule, TDSTagModule, TDSBadgeModule, TDSMapperPipeModule],
      template: `<div class="tds-menu-item-wrapper"  (click)="onClickItem($event)">\r
  <div class="tds-menu-item-badge">\r
    <tds-badge [class.invisible]="!(isSelected && !hasListChild)" status="primary" [tdsTheme]="tdsTheme" >\r
    </tds-badge>\r
  </div>\r
  <div class="tds-menu-item-title">\r
    @if (hasLink) {\r
      <a class="tds-menu-item-has-link" [routerLink]="[item.link]"\r
        [queryParams]="item.linkProps?.queryParams" [fragment]="item.linkProps?.fragment"\r
        [queryParamsHandling]="item.linkProps?.queryParamsHandling"\r
        [preserveFragment]="item.linkProps?.preserveFragment!"\r
        [skipLocationChange]="item.linkProps?.skipLocationChange!" [replaceUrl]="item.linkProps?.replaceUrl!"\r
        [state]="item.linkProps?.state" [routerLinkActive]="item.linkProps?.routerLinkActive || []">\r
        <span class="truncate">{{item.name}}</span>\r
        @if (item.badge) {\r
          <tds-badge [standalone]="true" [count]="item.badge.count"\r
          [tdsStyle]="item.badge.tdsStyle!" [tdsTheme]="tdsTheme"></tds-badge>\r
        }\r
        @if (item.tag) {\r
          <tds-tag [status]='item.tag.status!' [rounded]="item.tag.rounded!"\r
          [type]="item.tag.type!" [tdsTheme]="tdsTheme">{{item.tag.text}}</tds-tag>\r
        }\r
      </a>\r
    }\r
    @if (!hasLink) {\r
      <span class="tds-menu-item-text">\r
        <span class="truncate">{{item.name}}</span>\r
        @if (item.badge) {\r
          <tds-badge [standalone]="true" [count]="item.badge.count!"\r
          [tdsStyle]="item.badge.tdsStyle!" [tdsTheme]="tdsTheme"></tds-badge>\r
        }\r
        @if (item.tag) {\r
          <tds-tag [status]='item.tag.status!' [rounded]="item.tag.rounded!"\r
          [type]="item.tag.type!" [tdsTheme]="tdsTheme">{{item.tag.text!}}</tds-tag>\r
        }\r
      </span>\r
    }\r
  </div>\r
  @if (hasListChild && !hasAllHidden) {\r
    <div class="tds-menu-item-arrow">\r
      <span class="tdsi-chevron-right-fill"></span>\r
    </div>\r
  }\r
</div>\r
@if (hasListChild) {\r
  <div class="tds-menu-item-list-child"\r
    [@menuCollapseMotion]="item.isOpen  | tdsMapper:mapperExpandState:hasAllHidden ">\r
    <div class="w-full flex flex-col">\r
      @for (menu of item.listChild; track menu) {\r
        <tds-menu-item [options]="options" [item]="menu" [isSelected]="menu.isSelected!"\r
          [matchRouterExact]="matchRouterExact" [matchRouter]="matchRouter" [tdsTheme]="tdsTheme">\r
        </tds-menu-item>\r
      }\r
    </div>\r
  </div>\r
}`
    }]
  }], () => [{
    type: ChangeDetectorRef
  }, {
    type: TDSMenuService
  }, {
    type: RouterLink,
    decorators: [{
      type: Optional
    }]
  }, {
    type: RouterLink,
    decorators: [{
      type: Optional
    }]
  }, {
    type: Router,
    decorators: [{
      type: Optional
    }]
  }], {
    showIcon: [{
      type: Input
    }],
    item: [{
      type: Input
    }],
    isSelected: [{
      type: Input
    }],
    parentIsGroup: [{
      type: Input
    }],
    inlineCollapsed: [{
      type: Input
    }],
    matchRouterExact: [{
      type: Input
    }],
    matchRouter: [{
      type: Input
    }],
    tdsTheme: [{
      type: Input
    }],
    options: [{
      type: Input
    }],
    isOpenPopup: [{
      type: Input
    }],
    listItem: [{
      type: ViewChildren,
      args: [TDSMenuItemComponent]
    }],
    cdkOverlayOrigin: [{
      type: ViewChild,
      args: [CdkOverlayOrigin, {
        static: true,
        read: ElementRef
      }]
    }],
    listOfRouterLink: [{
      type: ViewChildren,
      args: [RouterLink]
    }],
    listOfRouterLinkWithHref: [{
      type: ViewChildren,
      args: [RouterLink]
    }]
  });
})();
var TDSMenuGroupInlineComponent = class _TDSMenuGroupInlineComponent {
  get uid() {
    return this.item ? this.item.uid : "";
  }
  constructor(_cdr, tdsMenuService, routerLink, routerLinkWithHref, router) {
    this._cdr = _cdr;
    this.tdsMenuService = tdsMenuService;
    this.routerLink = routerLink;
    this.routerLinkWithHref = routerLinkWithHref;
    this.router = router;
    this.destroy$ = new import_rxjs5.Subject();
    this.showIcon = true;
    this.isSelected = false;
    this.options = {
      background: "bg-white dark:bg-d-neutral-3-200",
      backgroundItem: "bg-white dark:bg-d-neutral-3-200",
      backgroundItemSelected: "bg-neutral-3-50 dark:bg-d-neutral-3-300",
      backgroundItemHover: "dark:hover:bg-d-neutral-3-300  hover:bg-neutral-3-50"
    };
    this.tdsTheme = "default";
    this.cdkOverlayOrigin = null;
    this.mode = "dark";
    this.IsActiveMatchOptions = {
      paths: "subset",
      matrixParams: "ignored",
      queryParams: "ignored",
      fragment: "ignored"
    };
    this.ngStyleItem = {};
    this.mapperExpandState = (isOpen, hasAllHidden) => {
      return isOpen && !hasAllHidden ? "expanded" : "collapsed";
    };
    if (router) {
      this.router.events.pipe((0, import_operators5.takeUntil)(this.destroy$), (0, import_operators5.filter)((e) => e instanceof NavigationEnd)).subscribe(() => {
        this.updateRouterActive();
      });
    }
  }
  ngOnInit() {
    this.ngStyleItem = this.getStyleItem();
  }
  ngAfterContentInit() {
    this.listenItemChangeSelected();
    this.tdsMenuService.mode$.pipe((0, import_operators5.takeUntil)(this.destroy$)).subscribe((res) => {
      this.mode = res;
      this._cdr.markForCheck();
    });
  }
  ngAfterViewInit() {
    if (!this.hasListChild || this.hasAllHidden) {
      this.updateRouterActive();
    }
  }
  ngOnChanges(changes) {
    if (changes["item"]) {
      this._cdr.markForCheck();
    }
    if (changes.isSelected) {
      this.setSelectedState(this.isSelected);
    }
  }
  onClickItem(e) {
    if (this.item.disabled) {
      e.stopPropagation();
      e.preventDefault();
    }
    if (this.hasListChild && !this.hasAllHidden) {
      this.tdsMenuService.groupMenuOpen$.next(this);
    } else {
      if (!this.isSelected) this.tdsMenuService.onDescendantMenuItemClick(this);
      if (this.hasLink) this.p_NavigateByUrl();
    }
  }
  get hasListChild() {
    return TDSHelperArray.hasListValue(this.item.listChild);
  }
  get hasAllHidden() {
    let childs = this.item.listChild?.find((f) => {
      return !f.hidden;
    });
    return childs == void 0;
  }
  get hasGroupTitle() {
    return this.item && TDSHelperString.hasValueString(this.item.groupTitle);
  }
  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
  setSelectedState(value) {
    this.isSelected = value;
    this.item.isSelected = value;
    if (this.isSelected) {
      if (!this.item.isOpen) {
        this.item.isOpen = true;
      }
    }
    this.ngStyleItem = this.getStyleItem();
    this._cdr.markForCheck();
  }
  updateRouterActive() {
    if (this.hasListChild || !this.listOfRouterLink || !this.listOfRouterLinkWithHref || !this.router || !this.router.navigated) {
      return;
    }
    Promise.resolve().then(() => {
      const hasActiveLinks = this.hasActiveLinks();
      if (this.isSelected !== hasActiveLinks) {
        this.setSelectedState(hasActiveLinks);
      }
    });
  }
  hasActiveLinks() {
    const isActiveCheckFn = this.isLinkActive(this.router);
    return this.routerLink && isActiveCheckFn(this.routerLink) || this.routerLinkWithHref && isActiveCheckFn(this.routerLinkWithHref) || this.listOfRouterLink.some(isActiveCheckFn) || this.listOfRouterLinkWithHref.some(isActiveCheckFn);
  }
  isLinkActive(router) {
    return (link) => router.isActive(link.urlTree, this.item.linkProps?.routerLinkActiveOptions || this.IsActiveMatchOptions);
  }
  setSelectedStateListChildren(uid) {
    if (TDSHelperArray.hasListValue(this.listItem)) {
      this.listItem.forEach((f) => {
        f.setSelectedStateListChildren(uid);
      });
    } else {
      this.setSelectedState(uid === this.uid);
    }
  }
  get hasLink() {
    return !this.item.disabled && (!this.hasListChild || this.hasAllHidden) && TDSHelperObject.hasValue(this.item.link);
  }
  get hasIcon() {
    return this.showIcon && this.item && (TDSHelperString.hasValueString(this.item.icon) || TDSHelperString.hasValueString(this.item.htmlIcon));
  }
  listenItemChangeSelected() {
    if (TDSHelperArray.hasListValue(this.listItem)) {
      const listOfTDSMenuItemComponent = this.listItem;
      const changes = listOfTDSMenuItemComponent.changes;
      const mergedObservable = (0, import_rxjs5.merge)(...[changes, ...listOfTDSMenuItemComponent.map((menu) => menu.selected$)]);
      changes.pipe(
        (0, import_operators5.startWith)(listOfTDSMenuItemComponent),
        (0, import_operators5.switchMap)(() => mergedObservable),
        (0, import_operators5.startWith)(true),
        // debounceTime(200),
        (0, import_operators5.map)(() => listOfTDSMenuItemComponent.some((e) => e.isSelected)),
        (0, import_operators5.takeUntil)(this.destroy$)
      ).subscribe((selected) => {
        if (selected != this.isSelected) {
          if (!this.hasAllHidden) {
            this.setSelectedState(selected);
          } else {
            const hasActiveLinks = this.hasActiveLinks();
            this.setSelectedState(hasActiveLinks || selected);
          }
        }
      });
    }
  }
  setOpenStateListChildren(value) {
    if (TDSHelperArray.hasListValue(this.listItem)) {
      this.listItem.forEach((f) => {
        if (!f.hasListChild) f.setOpenChildren(value);
      });
      this.item.isOpen = value;
      this._cdr.markForCheck();
    }
  }
  getStyleItem() {
    return {
      [`${this.options.backgroundItemSelected}`]: this.isSelected,
      [`${this.options.backgroundItem}`]: !this.isSelected,
      [`${this.options.backgroundItemHover}`]: true,
      [`cursor-pointer`]: !this.item.disabled,
      ["py-2.5"]: this.showIcon,
      ["py-2"]: !this.showIcon
    };
  }
  p_NavigateByUrl() {
    if (!TDSHelperObject.hasValue(this.item.linkProps)) {
      this.router?.navigateByUrl(this.item.link);
    } else {
      this.router?.navigate([this.item.link], {
        queryParams: this.item.linkProps?.queryParams,
        fragment: this.item.linkProps?.fragment,
        queryParamsHandling: this.item.linkProps?.queryParamsHandling,
        preserveFragment: this.item.linkProps?.preserveFragment,
        skipLocationChange: this.item.linkProps?.skipLocationChange,
        replaceUrl: this.item.linkProps?.replaceUrl,
        state: this.item.linkProps?.state
      });
    }
  }
  static {
    this.ɵfac = function TDSMenuGroupInlineComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TDSMenuGroupInlineComponent)(ɵɵdirectiveInject(ChangeDetectorRef), ɵɵdirectiveInject(TDSMenuService), ɵɵdirectiveInject(RouterLink, 8), ɵɵdirectiveInject(RouterLink, 8), ɵɵdirectiveInject(Router, 8));
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _TDSMenuGroupInlineComponent,
      selectors: [["tds-menu-group-inline"]],
      contentQueries: function TDSMenuGroupInlineComponent_ContentQueries(rf, ctx, dirIndex) {
        if (rf & 1) {
          ɵɵcontentQuery(dirIndex, TDSMenuItemComponent, 5);
        }
        if (rf & 2) {
          let _t;
          ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.listItem = _t);
        }
      },
      viewQuery: function TDSMenuGroupInlineComponent_Query(rf, ctx) {
        if (rf & 1) {
          ɵɵviewQuery(CdkOverlayOrigin, 7, ElementRef);
          ɵɵviewQuery(RouterLink, 5);
          ɵɵviewQuery(RouterLink, 5);
        }
        if (rf & 2) {
          let _t;
          ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.cdkOverlayOrigin = _t.first);
          ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.listOfRouterLink = _t);
          ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.listOfRouterLinkWithHref = _t);
        }
      },
      hostVars: 14,
      hostBindings: function TDSMenuGroupInlineComponent_HostBindings(rf, ctx) {
        if (rf & 2) {
          ɵɵclassProp("tds-menu-group-inline-opened", !!ctx.item.isOpen && ctx.hasListChild)("tds-menu-group-inline-hidden", !!ctx.item.hidden)("tds-menu-group-inline-show-icon", ctx.showIcon)("tds-menu-group-active", ctx.isSelected)("tds-menu-light", ctx.mode == "light")("tds-menu-dark", ctx.mode == "dark")("tds-menu-default", ctx.mode == "default");
        }
      },
      inputs: {
        showIcon: "showIcon",
        item: "item",
        isSelected: "isSelected",
        options: "options",
        tdsTheme: "tdsTheme"
      },
      standalone: true,
      features: [ɵɵNgOnChangesFeature, ɵɵStandaloneFeature],
      ngContentSelectors: _c22,
      decls: 8,
      vars: 6,
      consts: [[1, "tds-menu-group-inline-group-title"], [1, "tds-menu-group-inline-item", 3, "click"], [1, "tds-menu-group-inline-icon", 3, "menu-active"], [1, "tds-menu-group-inline-content-wrapper"], [1, "tds-menu-group-inline-has-link", 3, "routerLink", "queryParams", "fragment", "queryParamsHandling", "preserveFragment", "skipLocationChange", "replaceUrl", "state", "routerLinkActive"], [1, "tds-menu-group-inline-text"], [1, "tds-menu-group-inline-arrow"], [1, "tds-menu-list-child"], [1, "tds-menu-group-inline-icon"], [1, "tds-menu-html-icon", 3, "innerHTML"], [1, "tds-menu-group-inline-font-icon", 3, "ngClass"], [1, "tds-menu-group-inline-has-link", 3, "click", "routerLink", "queryParams", "fragment", "queryParamsHandling", "preserveFragment", "skipLocationChange", "replaceUrl", "state", "routerLinkActive"], [1, "tds-menu-name"], [3, "standalone", "overflowCount", "count", "tdsStyle", "tdsTheme"], [3, "status", "rounded", "type", "tdsTheme"], [1, "tdsi-chevron-right-fill"], [1, "tds-menu-list-child-wrapper"]],
      template: function TDSMenuGroupInlineComponent_Template(rf, ctx) {
        if (rf & 1) {
          ɵɵprojectionDef();
          ɵɵtemplate(0, TDSMenuGroupInlineComponent_Conditional_0_Template, 2, 1, "div", 0);
          ɵɵelementStart(1, "div", 1);
          ɵɵlistener("click", function TDSMenuGroupInlineComponent_Template_div_click_1_listener($event) {
            return ctx.onClickItem($event);
          });
          ɵɵtemplate(2, TDSMenuGroupInlineComponent_Conditional_2_Template, 3, 4, "div", 2);
          ɵɵelementStart(3, "div", 3);
          ɵɵtemplate(4, TDSMenuGroupInlineComponent_Conditional_4_Template, 5, 15, "a", 4)(5, TDSMenuGroupInlineComponent_Conditional_5_Template, 5, 3, "span", 5);
          ɵɵelementEnd();
          ɵɵtemplate(6, TDSMenuGroupInlineComponent_Conditional_6_Template, 2, 0, "div", 6);
          ɵɵelementEnd();
          ɵɵtemplate(7, TDSMenuGroupInlineComponent_Conditional_7_Template, 4, 5, "div", 7);
        }
        if (rf & 2) {
          ɵɵconditional(ctx.hasGroupTitle ? 0 : -1);
          ɵɵadvance(2);
          ɵɵconditional(ctx.showIcon ? 2 : -1);
          ɵɵadvance(2);
          ɵɵconditional(ctx.hasLink ? 4 : -1);
          ɵɵadvance();
          ɵɵconditional(!ctx.hasLink ? 5 : -1);
          ɵɵadvance();
          ɵɵconditional(ctx.hasListChild && !ctx.hasAllHidden ? 6 : -1);
          ɵɵadvance();
          ɵɵconditional(ctx.hasListChild ? 7 : -1);
        }
      },
      dependencies: [NgClass, TDSPipesModule, TDSSanitizerPipe, RouterModule, RouterLink, RouterLinkActive, TDSBadgeModule, TDSBadgeComponent, TDSTagModule, TDSTagComponent, TDSMapperPipeModule, TDSMapperPipe],
      encapsulation: 2,
      data: {
        animation: [menuCollapseMotion]
      },
      changeDetection: 0
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TDSMenuGroupInlineComponent, [{
    type: Component,
    args: [{
      selector: "tds-menu-group-inline",
      changeDetection: ChangeDetectionStrategy.OnPush,
      encapsulation: ViewEncapsulation$1.None,
      animations: [menuCollapseMotion],
      host: {
        "[class.tds-menu-group-inline-opened]": "!!item.isOpen && hasListChild",
        "[class.tds-menu-group-inline-hidden]": "!!item.hidden ",
        "[class.tds-menu-group-inline-show-icon]": "showIcon",
        "[class.tds-menu-group-active]": "isSelected",
        "[class.tds-menu-light]": 'mode=="light"',
        "[class.tds-menu-dark]": 'mode=="dark"',
        "[class.tds-menu-default]": 'mode=="default"'
      },
      standalone: true,
      imports: [NgClass, TDSPipesModule, RouterModule, TDSBadgeModule, TDSTagModule, TDSMapperPipeModule],
      template: `@if (hasGroupTitle) {\r
<div class="tds-menu-group-inline-group-title">\r
  {{item.groupTitle}}\r
</div>\r
}\r
<div class="tds-menu-group-inline-item" (click)="onClickItem($event)">\r
  @if (showIcon) {\r
  <div class="tds-menu-group-inline-icon" [class.menu-active]="isSelected">\r
    @if (item.htmlIcon) {\r
    <div class="tds-menu-html-icon" [innerHTML]="item.htmlIcon | tdsSanitizer:'html'"></div>\r
    }\r
    @if (hasIcon && !item.htmlIcon) {\r
    <span class="tds-menu-group-inline-font-icon" [ngClass]="item.icon!"></span>\r
    }\r
  </div>\r
  }\r
  <div class="tds-menu-group-inline-content-wrapper">\r
    @if (hasLink) {\r
    <a class="tds-menu-group-inline-has-link" (click)="onClickItem($event)" [routerLink]="[item.link]"\r
      [queryParams]="item.linkProps?.queryParams" [fragment]="item.linkProps?.fragment"\r
      [queryParamsHandling]="item.linkProps?.queryParamsHandling" [preserveFragment]="item.linkProps?.preserveFragment!"\r
      [skipLocationChange]="item.linkProps?.skipLocationChange!" [replaceUrl]="item.linkProps?.replaceUrl!"\r
      [state]="item.linkProps?.state" [routerLinkActive]="item.linkProps?.routerLinkActive || []">\r
      <span class="tds-menu-name">{{item.name}}</span>\r
      @if (item.badge) {\r
      <tds-badge [standalone]="true" [overflowCount]="item.badge.overflowCount!" [count]="item.badge.count"\r
        [tdsStyle]="item.badge.tdsStyle!" [tdsTheme]="tdsTheme"></tds-badge>\r
      }\r
      @if (item.tag) {\r
      <tds-tag [status]='item.tag.status!' [rounded]="item.tag.rounded!" [type]="item.tag.type!"\r
        [tdsTheme]="tdsTheme">{{item.tag.text}}</tds-tag>\r
      }\r
    </a>\r
    }\r
    @if (!hasLink) {\r
    <span class="tds-menu-group-inline-text">\r
      <span class="tds-menu-name">{{item.name}}</span>\r
      @if (item.badge) {\r
      <tds-badge [standalone]="true" [overflowCount]="item.badge.overflowCount" [count]="item.badge.count"\r
        [tdsStyle]="item.badge.tdsStyle!" [tdsTheme]="tdsTheme"></tds-badge>\r
      }\r
      @if (item.tag) {\r
      <tds-tag [status]='item.tag.status!' [rounded]="item.tag.rounded!" [type]="item.tag.type!"\r
        [tdsTheme]="tdsTheme">{{item.tag.text}}</tds-tag>\r
      }\r
    </span>\r
    }\r
  </div>\r
  @if (hasListChild && !hasAllHidden) {\r
  <div class="tds-menu-group-inline-arrow">\r
    <span class="tdsi-chevron-right-fill"></span>\r
  </div>\r
  }\r
</div>\r
@if (hasListChild) {\r
<div class="tds-menu-list-child" [@menuCollapseMotion]="item.isOpen  | tdsMapper:mapperExpandState:hasAllHidden">\r
  <div class="tds-menu-list-child-wrapper">\r
    <ng-content></ng-content>\r
  </div>\r
</div>\r
}`
    }]
  }], () => [{
    type: ChangeDetectorRef
  }, {
    type: TDSMenuService
  }, {
    type: RouterLink,
    decorators: [{
      type: Optional
    }]
  }, {
    type: RouterLink,
    decorators: [{
      type: Optional
    }]
  }, {
    type: Router,
    decorators: [{
      type: Optional
    }]
  }], {
    showIcon: [{
      type: Input
    }],
    item: [{
      type: Input
    }],
    isSelected: [{
      type: Input
    }],
    options: [{
      type: Input
    }],
    tdsTheme: [{
      type: Input
    }],
    listItem: [{
      type: ContentChildren,
      args: [TDSMenuItemComponent, {
        descendants: true
      }]
    }],
    cdkOverlayOrigin: [{
      type: ViewChild,
      args: [CdkOverlayOrigin, {
        static: true,
        read: ElementRef
      }]
    }],
    listOfRouterLink: [{
      type: ViewChildren,
      args: [RouterLink]
    }],
    listOfRouterLinkWithHref: [{
      type: ViewChildren,
      args: [RouterLink]
    }]
  });
})();
var listOfVerticalPositions2 = [
  POSITION_MAP.rightTop,
  POSITION_MAP.right,
  POSITION_MAP.rightBottom,
  // POSITION_MAP.leftTop,
  POSITION_MAP.left
  // POSITION_MAP.leftBottom
];
var TDSMenuGroupPopupComponent = class _TDSMenuGroupPopupComponent {
  get uid() {
    return this.item ? this.item.uid : "";
  }
  constructor(_cdr, tdsMenuService, routerLink, routerLinkWithHref, router) {
    this._cdr = _cdr;
    this.tdsMenuService = tdsMenuService;
    this.routerLink = routerLink;
    this.routerLinkWithHref = routerLinkWithHref;
    this.router = router;
    this.destroy$ = new import_rxjs5.Subject();
    this.isSelected = false;
    this.inlineCollapsed = false;
    this.options = {
      background: "bg-white dark:bg-d-neutral-3-200",
      backgroundItem: "bg-white dark:bg-d-neutral-3-200",
      backgroundItemSelected: "bg-neutral-3-50 dark:bg-d-neutral-3-300",
      backgroundItemHover: "dark:hover:bg-d-neutral-3-300  hover:bg-neutral-3-50"
    };
    this.tdsTheme = "default";
    this.open$ = new import_rxjs5.Subject();
    this.cdkOverlayOrigin = null;
    this.IsActiveMatchOptions = {
      paths: "subset",
      matrixParams: "ignored",
      queryParams: "ignored",
      fragment: "ignored"
    };
    this.position = "rightTop";
    this.triggerWidth = null;
    this.overlayPositions = listOfVerticalPositions2;
    this.mode = "dark";
    if (router) {
      this.router.events.pipe((0, import_operators5.takeUntil)(this.destroy$), (0, import_operators5.filter)((e) => e instanceof NavigationEnd)).subscribe(() => {
        if (!this.hasListChild) {
          this.updateRouterActive();
        }
      });
    }
  }
  ngOnInit() {
    this.open$.pipe((0, import_operators5.debounceTime)(100), (0, import_operators5.distinctUntilChanged)(), (0, import_operators5.takeUntil)(this.destroy$)).subscribe((res) => {
      this.item.isOpen = res;
      this._cdr.markForCheck();
    });
    this.tdsMenuService.mode$.subscribe((mode) => {
      if (TDSHelperObject.hasValue(mode) && this.mode !== mode) {
        this.mode = mode;
        this._cdr.markForCheck();
      }
    });
  }
  ngAfterContentInit() {
    this.listenItemChangeSelected();
  }
  ngAfterViewInit() {
    if (!this.hasListChild) {
      this.updateRouterActive();
    }
  }
  ngOnChanges(changes) {
    if (changes["item"]) {
      this._cdr.markForCheck();
    }
  }
  onClickItem(e) {
    if (this.item.disabled) {
      e.stopPropagation();
      e.preventDefault();
    }
    if (!this.hasListChild || !this.hasAllHidden) {
      this.tdsMenuService.onDescendantMenuItemClick(this);
      if (this.hasLink) this.p_NavigateByUrl();
    }
  }
  get hasListChild() {
    return TDSHelperArray.hasListValue(this.item.listChild);
  }
  get hasAllHidden() {
    let childs = this.item.listChild?.find((f) => {
      return !f.hidden;
    });
    return childs == void 0;
  }
  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
  setSelectedState(value) {
    this.isSelected = value;
    this.item.isSelected = value;
    this._cdr.markForCheck();
  }
  updateRouterActive() {
    if (this.hasListChild || !this.listOfRouterLink || !this.listOfRouterLinkWithHref || !this.router || !this.router.navigated) {
      return;
    }
    Promise.resolve().then(() => {
      const hasActiveLinks = this.hasActiveLinks();
      if (this.isSelected !== hasActiveLinks) {
        this.setSelectedState(hasActiveLinks);
      }
    });
  }
  hasActiveLinks() {
    const isActiveCheckFn = this.isLinkActive(this.router);
    return this.routerLink && isActiveCheckFn(this.routerLink) || this.routerLinkWithHref && isActiveCheckFn(this.routerLinkWithHref) || this.listOfRouterLink.some(isActiveCheckFn) || this.listOfRouterLinkWithHref.some(isActiveCheckFn);
  }
  isLinkActive(router) {
    return (link) => router.isActive(link.urlTree, this.item.linkProps?.routerLinkActiveOptions || this.IsActiveMatchOptions);
  }
  setSelectedStateListChildren(uid) {
    if (TDSHelperArray.hasListValue(this.listItem)) {
      this.listItem.forEach((f) => {
        f.setSelectedStateListChildren(uid);
      });
    } else {
      this.setSelectedState(uid === this.uid);
    }
  }
  get hasLink() {
    return !this.item.disabled && (!this.hasListChild || this.hasAllHidden) && TDSHelperObject.hasValue(this.item.link);
  }
  get hasIcon() {
    return this.item && (TDSHelperString.hasValueString(this.item.icon) || TDSHelperString.hasValueString(this.item.htmlIcon));
  }
  listenItemChangeSelected() {
    if (TDSHelperArray.hasListValue(this.listItem) && this.listItem.length > 0) {
      const listOfTDSMenuItemComponent = this.listItem;
      const changes = listOfTDSMenuItemComponent.changes;
      const mergedObservable = (0, import_rxjs5.merge)(...[changes, ...listOfTDSMenuItemComponent.map((menu) => menu.selected$)]);
      changes.pipe((0, import_operators5.startWith)(listOfTDSMenuItemComponent), (0, import_operators5.switchMap)(() => mergedObservable), (0, import_operators5.startWith)(true), (0, import_operators5.map)(() => listOfTDSMenuItemComponent.some((e) => e.isSelected)), (0, import_operators5.takeUntil)(this.destroy$)).subscribe((selected) => {
        if (selected != this.isSelected) if (!this.hasAllHidden) this.setSelectedState(selected);
        else {
          const hasActiveLinks = this.hasActiveLinks();
          this.setSelectedState(hasActiveLinks || selected);
        }
      });
    }
  }
  setTriggerWidth() {
    if (this.cdkOverlayOrigin) {
      this.triggerWidth = this.cdkOverlayOrigin.nativeElement.getBoundingClientRect().width;
    }
  }
  onPositionChange(position) {
    const placement = getPlacementName(position);
    this.position = placement;
    this._cdr.markForCheck();
  }
  setOpenStateListChildren(value) {
    if (TDSHelperArray.hasListValue(this.listItem)) {
      this.listItem.forEach((f) => {
        f.setOpenChildren(value);
      });
      this.item.isOpen = value;
      this._cdr.markForCheck();
    }
  }
  setMouseState(value) {
    this.open$.next(value);
  }
  p_NavigateByUrl() {
    if (!TDSHelperObject.hasValue(this.item.linkProps)) {
      this.router?.navigateByUrl(this.item.link);
    } else {
      this.router?.navigate([this.item.link], {
        queryParams: this.item.linkProps?.queryParams,
        fragment: this.item.linkProps?.fragment,
        queryParamsHandling: this.item.linkProps?.queryParamsHandling,
        preserveFragment: this.item.linkProps?.preserveFragment,
        skipLocationChange: this.item.linkProps?.skipLocationChange,
        replaceUrl: this.item.linkProps?.replaceUrl,
        state: this.item.linkProps?.state
      });
    }
  }
  static {
    this.ɵfac = function TDSMenuGroupPopupComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TDSMenuGroupPopupComponent)(ɵɵdirectiveInject(ChangeDetectorRef), ɵɵdirectiveInject(TDSMenuService), ɵɵdirectiveInject(RouterLink, 8), ɵɵdirectiveInject(RouterLink, 8), ɵɵdirectiveInject(Router, 8));
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _TDSMenuGroupPopupComponent,
      selectors: [["tds-menu-group-popup"]],
      contentQueries: function TDSMenuGroupPopupComponent_ContentQueries(rf, ctx, dirIndex) {
        if (rf & 1) {
          ɵɵcontentQuery(dirIndex, TDSMenuItemComponent, 5);
        }
        if (rf & 2) {
          let _t;
          ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.listItem = _t);
        }
      },
      viewQuery: function TDSMenuGroupPopupComponent_Query(rf, ctx) {
        if (rf & 1) {
          ɵɵviewQuery(CdkOverlayOrigin, 7, ElementRef);
          ɵɵviewQuery(RouterLink, 5);
          ɵɵviewQuery(RouterLink, 5);
        }
        if (rf & 2) {
          let _t;
          ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.cdkOverlayOrigin = _t.first);
          ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.listOfRouterLink = _t);
          ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.listOfRouterLinkWithHref = _t);
        }
      },
      hostVars: 8,
      hostBindings: function TDSMenuGroupPopupComponent_HostBindings(rf, ctx) {
        if (rf & 1) {
          ɵɵlistener("mouseenter", function TDSMenuGroupPopupComponent_mouseenter_HostBindingHandler() {
            return ctx.setMouseState(true);
          })("mouseleave", function TDSMenuGroupPopupComponent_mouseleave_HostBindingHandler() {
            return ctx.setMouseState(false);
          });
        }
        if (rf & 2) {
          ɵɵclassProp("tds-menu-group-active", ctx.isSelected)("tds-menu-light", ctx.mode == "light")("tds-menu-dark", ctx.mode == "dark")("tds-menu-default", ctx.mode == "default");
        }
      },
      inputs: {
        item: "item",
        isSelected: "isSelected",
        inlineCollapsed: "inlineCollapsed",
        options: "options",
        tdsTheme: "tdsTheme"
      },
      standalone: true,
      features: [ɵɵNgOnChangesFeature, ɵɵStandaloneFeature],
      ngContentSelectors: _c22,
      decls: 7,
      vars: 10,
      consts: [["origin", "cdkOverlayOrigin"], ["iconTmpl", ""], ["cdkOverlayOrigin", "", 1, "tds-menu-group-popup-wrapper", 3, "ngClass"], ["tooltipPlacement", "right", "tds-tooltip", "", 1, "tds-menu-group-popup-wrapper-link", 3, "tooltipTitle"], [1, "tds-menu-group-popup-wrapper-link"], ["cdkConnectedOverlay", "", "cdkConnectedOverlayPanelClass", "bottom-2.5", 3, "positionChange", "cdkConnectedOverlayPositions", "cdkConnectedOverlayOrigin", "cdkConnectedOverlayWidth", "cdkConnectedOverlayOpen", "cdkConnectedOverlayTransformOriginOn"], ["tooltipPlacement", "right", "tds-tooltip", "", 1, "tds-menu-group-popup-wrapper-link", 3, "click", "tooltipTitle"], [1, "h-5", "w-5", "flex", "items-center"], [1, "flex", "items-center", 3, "click", "routerLink", "queryParams", "fragment", "queryParamsHandling", "preserveFragment", "skipLocationChange", "replaceUrl", "state", "routerLinkActive"], [3, "ngTemplateOutlet", "ngTemplateOutletContext"], [1, "tds-menu-group-popup-wrapper-link", 3, "click"], [1, "h-full", 2, "min-width", "244px", 3, "ngClass", "margin-top"], [1, "h-full", 2, "min-width", "244px", 3, "mouseenter", "mouseleave", "ngClass"], [1, "w-full", "h-full", "flex", "flex-col", "relative"], [1, "absolute", "inset-0", "overflow-y-auto", "overflow-x-hidden", "no-scrollbar", "px-1", "py-1.5"], [1, "tds-menu-panel"], [3, "status", "dot", "tdsTheme"], [1, "tds-menu-font-icon", 3, "ngClass"], [1, "tds-menu-html-icon", 3, "innerHTML"]],
      template: function TDSMenuGroupPopupComponent_Template(rf, ctx) {
        if (rf & 1) {
          const _r1 = ɵɵgetCurrentView();
          ɵɵprojectionDef();
          ɵɵelementStart(0, "div", 2, 0);
          ɵɵtemplate(2, TDSMenuGroupPopupComponent_Conditional_2_Template, 2, 2, "div", 3)(3, TDSMenuGroupPopupComponent_Conditional_3_Template, 2, 1, "div", 4);
          ɵɵelementEnd();
          ɵɵtemplate(4, TDSMenuGroupPopupComponent_ng_template_4_Template, 1, 1, "ng-template", 5);
          ɵɵlistener("positionChange", function TDSMenuGroupPopupComponent_Template_ng_template_positionChange_4_listener($event) {
            ɵɵrestoreView(_r1);
            return ɵɵresetView(ctx.onPositionChange($event));
          });
          ɵɵtemplate(5, TDSMenuGroupPopupComponent_ng_template_5_Template, 3, 3, "ng-template", null, 1, ɵɵtemplateRefExtractor);
        }
        if (rf & 2) {
          const origin_r9 = ɵɵreference(1);
          ɵɵproperty("ngClass", ɵɵpureFunction1(8, _c32, ctx.item.hidden));
          ɵɵadvance(2);
          ɵɵconditional(ctx.hasLink ? 2 : -1);
          ɵɵadvance();
          ɵɵconditional(!ctx.hasLink ? 3 : -1);
          ɵɵadvance();
          ɵɵproperty("cdkConnectedOverlayPositions", ctx.overlayPositions)("cdkConnectedOverlayOrigin", origin_r9)("cdkConnectedOverlayWidth", ctx.triggerWidth)("cdkConnectedOverlayOpen", ctx.item.isOpen && !ctx.hasAllHidden)("cdkConnectedOverlayTransformOriginOn", ".tds-menu-submenu");
        }
      },
      dependencies: [NgClass, TDSToolTipModule, TDSTooltipDirective, RouterModule, RouterLink, RouterLinkActive, NgTemplateOutlet, OverlayModule, CdkConnectedOverlay, CdkOverlayOrigin, TDSBadgeModule, TDSBadgeComponent, TDSPipesModule, TDSSanitizerPipe],
      encapsulation: 2,
      data: {
        animation: [slideMotion]
      },
      changeDetection: 0
    });
  }
};
__decorate([InputBoolean()], TDSMenuGroupPopupComponent.prototype, "inlineCollapsed", void 0);
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TDSMenuGroupPopupComponent, [{
    type: Component,
    args: [{
      selector: "tds-menu-group-popup",
      changeDetection: ChangeDetectionStrategy.OnPush,
      encapsulation: ViewEncapsulation$1.None,
      host: {
        "[class.tds-menu-group-active]": "isSelected",
        "[class.tds-menu-light]": 'mode=="light"',
        "[class.tds-menu-dark]": 'mode=="dark"',
        "[class.tds-menu-default]": 'mode=="default"',
        "(mouseenter)": "setMouseState(true)",
        "(mouseleave)": "setMouseState(false)"
      },
      animations: [slideMotion],
      standalone: true,
      imports: [NgClass, TDSToolTipModule, RouterModule, NgTemplateOutlet, OverlayModule, TDSBadgeModule, TDSPipesModule],
      template: `<div class="tds-menu-group-popup-wrapper" [ngClass]="{'hidden':item.hidden}" cdkOverlayOrigin\r
  #origin="cdkOverlayOrigin">\r
  @if (hasLink) {\r
    <div class="tds-menu-group-popup-wrapper-link" (click)="onClickItem($event)"\r
      [tooltipTitle]="item.name" tooltipPlacement="right" tds-tooltip>\r
      @if (hasIcon) {\r
        <div class="h-5 w-5 flex items-center">\r
          <a class="flex items-center" (click)="onClickItem($event)" [routerLink]="[item.link]"\r
            [queryParams]="item.linkProps?.queryParams" [fragment]="item.linkProps?.fragment"\r
            [queryParamsHandling]="item.linkProps?.queryParamsHandling"\r
            [preserveFragment]="item.linkProps?.preserveFragment!"\r
            [skipLocationChange]="item.linkProps?.skipLocationChange!" [replaceUrl]="item.linkProps?.replaceUrl!"\r
            [state]="item.linkProps?.state" [routerLinkActive]="item.linkProps?.routerLinkActive || []">\r
            <ng-container [ngTemplateOutlet]="iconTmpl" [ngTemplateOutletContext]="{ $implicit: item}">\r
            </ng-container>\r
          </a>\r
        </div>\r
      }\r
    </div>\r
  }\r
  @if (!hasLink) {\r
    <div class="tds-menu-group-popup-wrapper-link" (click)="onClickItem($event)">\r
      @if (hasIcon) {\r
        <div class="h-5 w-5 flex items-center">\r
          <ng-container [ngTemplateOutlet]="iconTmpl" [ngTemplateOutletContext]="{ $implicit: item}">\r
          </ng-container>\r
        </div>\r
      }\r
    </div>\r
  }\r
</div>\r
\r
<ng-template cdkConnectedOverlay (positionChange)="onPositionChange($event)" cdkConnectedOverlayPanelClass="bottom-2.5"\r
  [cdkConnectedOverlayPositions]="overlayPositions" [cdkConnectedOverlayOrigin]="origin"\r
  [cdkConnectedOverlayWidth]="triggerWidth!" [cdkConnectedOverlayOpen]="item.isOpen! && !hasAllHidden"\r
  [cdkConnectedOverlayTransformOriginOn]="'.tds-menu-submenu'">\r
  @if (hasListChild) {\r
    <div\r
      [ngClass]="{'tds-menu-light': mode=='light','tds-menu-dark': mode=='dark','tds-menu-default': mode=='default'}"\r
      class="h-full" @slideMotion style="min-width: 244px;" [style.margin-top.px]="position == 'rightTop'? -5 : 0"\r
      (mouseenter)='setMouseState(true)' (mouseleave)='setMouseState(false)'>\r
      <div class="w-full h-full flex flex-col relative ">\r
        <div class="absolute inset-0 overflow-y-auto overflow-x-hidden no-scrollbar  px-1 py-1.5">\r
          <div class="tds-menu-panel">\r
            <ng-content></ng-content>\r
          </div>\r
        </div>\r
      </div>\r
    </div>\r
  }\r
</ng-template>\r
\r
<ng-template #iconTmpl let-item>\r
  @if (item.dot) {\r
    <tds-badge [status]="item.dot.status" [dot]="true" [tdsTheme]="tdsTheme">\r
      <span [ngClass]="item.icon!" class="tds-menu-font-icon">\r
      </span>\r
    </tds-badge>\r
  }\r
  @if (!item.dot && !item.htmlIcon) {\r
    <span [ngClass]="item.icon!" class="tds-menu-font-icon">\r
    </span>\r
  }\r
  @if (!item.dot && item.htmlIcon) {\r
    <span class="tds-menu-html-icon"\r
    [innerHTML]="item.htmlIcon | tdsSanitizer:'html'"></span>\r
  }\r
</ng-template>`
    }]
  }], () => [{
    type: ChangeDetectorRef
  }, {
    type: TDSMenuService
  }, {
    type: RouterLink,
    decorators: [{
      type: Optional
    }]
  }, {
    type: RouterLink,
    decorators: [{
      type: Optional
    }]
  }, {
    type: Router,
    decorators: [{
      type: Optional
    }]
  }], {
    item: [{
      type: Input
    }],
    isSelected: [{
      type: Input
    }],
    inlineCollapsed: [{
      type: Input
    }],
    options: [{
      type: Input
    }],
    tdsTheme: [{
      type: Input
    }],
    listItem: [{
      type: ContentChildren,
      args: [TDSMenuItemComponent, {
        descendants: true
      }]
    }],
    cdkOverlayOrigin: [{
      type: ViewChild,
      args: [CdkOverlayOrigin, {
        static: true,
        read: ElementRef
      }]
    }],
    listOfRouterLink: [{
      type: ViewChildren,
      args: [RouterLink]
    }],
    listOfRouterLinkWithHref: [{
      type: ViewChildren,
      args: [RouterLink]
    }]
  });
})();
var TDSMenuFooterDirective = class _TDSMenuFooterDirective {
  static {
    this.ɵfac = function TDSMenuFooterDirective_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TDSMenuFooterDirective)();
    };
  }
  static {
    this.ɵdir = ɵɵdefineDirective({
      type: _TDSMenuFooterDirective,
      selectors: [["", "tdsMenuFooter", ""]],
      exportAs: ["tdsMenuFooter"],
      standalone: true
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TDSMenuFooterDirective, [{
    type: Directive,
    args: [{
      selector: "[tdsMenuFooter]",
      exportAs: "tdsMenuFooter",
      standalone: true
    }]
  }], null, null);
})();
function TDSMenuServiceFactory(serviceInsideDropDown, serviceOutsideDropDown) {
  return serviceInsideDropDown ? serviceInsideDropDown : serviceOutsideDropDown;
}
var TDSMenuComponent = class _TDSMenuComponent {
  get menuFooter() {
    return this.tdsMenuFooter || this.tdsMenuFooterChild;
  }
  constructor(element, renderer, _cdr, tdsMenuService) {
    this.element = element;
    this.renderer = renderer;
    this._cdr = _cdr;
    this.tdsMenuService = tdsMenuService;
    this.destroy$ = new import_rxjs5.Subject();
    this.listMenu = [];
    this.showLogo = false;
    this.data = [];
    this.mode = "dark";
    this.showIcon = true;
    this.showFooter = true;
    this.inlineCollapsed = false;
    this.matchRouterExact = false;
    this.matchRouter = true;
    this.options = {
      background: "bg-white dark:bg-d-neutral-3-200",
      backgroundItem: "bg-white dark:bg-d-neutral-3-200",
      backgroundItemSelected: "bg-neutral-3-50 dark:bg-d-neutral-3-300",
      backgroundItemHover: "dark:hover:bg-d-neutral-3-300  hover:bg-neutral-3-50"
    };
    this.onClickItem = new EventEmitter();
    this.onOpenChange = new EventEmitter();
    this.inlineCollapsed$ = new import_rxjs5.BehaviorSubject(this.inlineCollapsed);
    this.mapHasIcon = () => {
      if (!this.showIcon) {
        return false;
      }
      const lstIcon = this.listMenu.filter((f) => TDSHelperString.hasValueString(f.icon) || TDSHelperString.hasValueString(f.htmlIcon));
      return lstIcon.length > 0;
    };
    this.mapItemHasIcon = (item) => {
      if (!this.showIcon) {
        return false;
      }
      return TDSHelperString.hasValueString(item.icon);
    };
    this.mapItemHasIconHtml = (item) => {
      if (!this.showIcon) {
        return false;
      }
      return !TDSHelperString.hasValueString(item.icon) && TDSHelperString.hasValueString(item.htmlIcon);
    };
  }
  ngOnInit() {
    this.tdsMenuService.descendantMenuItemClick$.pipe((0, import_operators5.takeUntil)(this.destroy$)).subscribe((menu) => {
      this.onClickItem.emit(menu.item);
      if (TDSHelperArray.hasListValue(this.listOfTDSMenuGroupInlineComponent)) {
        this.listOfTDSMenuGroupInlineComponent.forEach((group) => {
          group.setSelectedStateListChildren(menu.uid);
        });
      }
      if (TDSHelperArray.hasListValue(this.listOfTDSMenuGroupPopupComponent)) {
        this.listOfTDSMenuGroupPopupComponent.forEach((group) => {
          group.setSelectedStateListChildren(menu.uid);
        });
      }
    });
    this.tdsMenuService.groupMenuOpen$.pipe((0, import_operators5.takeUntil)(this.destroy$)).subscribe((menu) => {
      if (TDSHelperArray.hasListValue(this.listOfTDSMenuGroupInlineComponent)) {
        this.listOfTDSMenuGroupInlineComponent.forEach((group) => {
          if (group.hasListChild) group.setOpenStateListChildren(group == menu && !group.item.isOpen);
        });
      }
    });
  }
  ngOnChanges(changes) {
    const {
      data,
      inlineCollapsed,
      mode
    } = changes;
    if (data) {
      this.p_mapData(this.data);
    }
    if (inlineCollapsed) {
      this.inlineCollapsed$.next(this.inlineCollapsed);
    }
    if (mode) {
      this.tdsMenuService.onModeChange(this.mode);
    }
    this._cdr.markForCheck();
  }
  p_mapData(data) {
    if (!TDSHelperArray.hasListValue(data)) {
      this.listMenu = [];
    } else {
      this.listMenu = data.map((item) => {
        return this.p_mapItem(item);
      });
    }
  }
  p_mapItem(data) {
    let listChild = [];
    if (TDSHelperArray.hasListValue(data.listChild)) {
      listChild = data.listChild.map((item) => {
        return this.p_mapItem(item);
      });
    }
    return {
      name: data.name,
      icon: data.icon,
      disabled: data.disabled,
      isSelected: data.isSelected || false,
      isOpen: data.isOpen || false,
      link: data.link,
      listChild,
      uid: TDSHelperString.genid(),
      hidden: data.hidden,
      groupTitle: data.groupTitle,
      htmlIcon: data.htmlIcon,
      linkProps: data.linkProps,
      badge: data.badge,
      tag: data.tag,
      dot: data.dot
    };
  }
  ngAfterViewInit() {
    this.inlineCollapsed$.pipe((0, import_operators5.takeUntil)(this.destroy$)).subscribe(() => {
      this.updateInlineCollapse();
      this._cdr.markForCheck();
    });
  }
  updateInlineCollapse() {
    if (this.inlineCollapsed) {
      if (this.listOfTDSMenuGroupInlineComponent.length) {
        this.listOfTDSMenuGroupInlineComponent.forEach((group) => {
          group.setOpenStateListChildren(false);
        });
      }
    } else {
      if (this.listOfTDSMenuGroupPopupComponent.length) {
        this.listOfTDSMenuGroupPopupComponent.forEach((group) => {
          group.setOpenStateListChildren(false);
        });
      }
    }
  }
  setInlineCollapsed(inlineCollapsed) {
    this.inlineCollapsed = inlineCollapsed;
    this.inlineCollapsed$.next(inlineCollapsed);
  }
  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
  onClickInlineCollapsed() {
    this.inlineCollapsed = !this.inlineCollapsed;
    this.onOpenChange.emit(this.inlineCollapsed);
    this.inlineCollapsed$.next(this.inlineCollapsed);
  }
  get hasIcon() {
    if (!this.showIcon) {
      return false;
    }
    const lstIcon = this.listMenu.filter((f) => TDSHelperString.hasValueString(f.icon) || TDSHelperString.hasValueString(f.htmlIcon));
    return lstIcon.length > 0;
  }
  // TDSLayoutSiderComponent dùng cập nhật class css cho menu
  addKlass(kClass) {
    if (!this.hasKlass(kClass)) this.renderer.addClass(this.element.nativeElement, kClass);
  }
  removeKlass(kClass) {
    if (this.hasKlass(kClass)) this.renderer.removeClass(this.element.nativeElement, kClass);
  }
  hasKlass(kClass) {
    return this.element.nativeElement.classList.contains(kClass);
  }
  onSubNavbarOpen(value, child, parent) {
    if (value) {
      if (TDSHelperObject.hasValue(parent)) {
        const childOpen = parent?.listChild?.filter((f) => !!f.isOpen && f.uid !== child.uid);
        childOpen?.forEach((element) => {
          element.isOpen = false;
        });
      } else {
        const childOpen = this.listMenu?.filter((f) => !!f.isOpen && f.uid !== child.uid);
        childOpen?.forEach((element) => {
          element.isOpen = false;
        });
      }
    }
  }
  static {
    this.ɵfac = function TDSMenuComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TDSMenuComponent)(ɵɵdirectiveInject(ElementRef), ɵɵdirectiveInject(Renderer2), ɵɵdirectiveInject(ChangeDetectorRef), ɵɵdirectiveInject(TDSMenuService));
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _TDSMenuComponent,
      selectors: [["tds-menu"]],
      contentQueries: function TDSMenuComponent_ContentQueries(rf, ctx, dirIndex) {
        if (rf & 1) {
          ɵɵcontentQuery(dirIndex, _c62, 5);
          ɵɵcontentQuery(dirIndex, TDSMenuFooterDirective, 5, TemplateRef);
        }
        if (rf & 2) {
          let _t;
          ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.logoText = _t.first);
          ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.tdsMenuFooterChild = _t.first);
        }
      },
      viewQuery: function TDSMenuComponent_Query(rf, ctx) {
        if (rf & 1) {
          ɵɵviewQuery(TDSMenuGroupInlineComponent, 5);
          ɵɵviewQuery(TDSMenuGroupPopupComponent, 5);
        }
        if (rf & 2) {
          let _t;
          ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.listOfTDSMenuGroupInlineComponent = _t);
          ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.listOfTDSMenuGroupPopupComponent = _t);
        }
      },
      hostVars: 10,
      hostBindings: function TDSMenuComponent_HostBindings(rf, ctx) {
        if (rf & 2) {
          ɵɵclassProp("w-full", !ctx.inlineCollapsed)("tds-menu-collapsed", ctx.inlineCollapsed)("tds-menu-light", ctx.mode === "light")("tds-menu-dark", ctx.mode === "dark")("tds-menu-default", ctx.mode === "default");
        }
      },
      inputs: {
        showLogo: "showLogo",
        data: "data",
        mode: "mode",
        showIcon: "showIcon",
        showFooter: "showFooter",
        inlineCollapsed: "inlineCollapsed",
        matchRouterExact: "matchRouterExact",
        matchRouter: "matchRouter",
        options: "options",
        tdsMenuFooter: "tdsMenuFooter"
      },
      outputs: {
        onClickItem: "onClickItem",
        onOpenChange: "onOpenChange"
      },
      standalone: true,
      features: [ɵɵProvidersFeature([
        /** use the top level service **/
        TDSMenuService
      ]), ɵɵNgOnChangesFeature, ɵɵStandaloneFeature],
      ngContentSelectors: _c82,
      decls: 6,
      vars: 8,
      consts: [[1, "tds-menu-wrapper", 3, "ngClass"], [1, "w-full", "tds-menu-header"], ["cdkScrollable", "", 1, "tds-menu-body", 3, "ngClass"], [1, "tds-menu-footer", 3, "ngClass"], [3, "options", "item", "tdsTheme"], [3, "class", "options", "item", "isSelected", "parentIsGroup", "matchRouterExact", "matchRouter", "tdsTheme"], [3, "options", "item", "isSelected", "parentIsGroup", "matchRouterExact", "matchRouter", "tdsTheme"], [3, "options", "showIcon", "item", "tdsTheme"], [3, "class", "options", "showIcon", "item", "isSelected", "parentIsGroup", "matchRouterExact", "matchRouter", "tdsTheme"], [3, "options", "showIcon", "item", "isSelected", "parentIsGroup", "matchRouterExact", "matchRouter", "tdsTheme"], [4, "ngTemplateOutlet", "ngTemplateOutletContext"], [1, "tds-menu-footer-svg"], ["width", "38", "height", "38", "viewBox", "0 0 38 38", "fill", "none", "xmlns", "http://www.w3.org/2000/svg"], ["d", "M19 38C29.4934 38 38 29.4934 38 19C38 8.50658 29.4934 0 19 0C8.50662 0 3.04774e-05 8.50658 3.04774e-05 19L0 33.7778C0 36.3111 1.68889 38 4.22221 38H19Z", 1, "fill-current"], ["fill-rule", "evenodd", "clip-rule", "evenodd", "d", "M31.2241 13.2812C31.0776 12.3206 30.6631 11.4174 30.0256 10.6695C29.388 9.92167 28.5518 9.35776 27.6077 9.03903C26.6636 8.72029 25.6477 8.65889 24.6702 8.86151C23.6927 9.06413 22.791 9.52301 22.063 10.1884C21.5477 10.6357 21.1308 11.1797 20.8375 11.7876C20.5441 12.3956 20.3803 13.0548 20.356 13.7258C20.2959 15.5879 20.2823 17.4511 20.2687 19.3141C20.265 19.829 20.2612 20.3439 20.2565 20.8588C20.2547 20.9262 20.2679 20.9932 20.2954 21.0551C20.3229 21.117 20.3639 21.1724 20.4156 21.2173C20.4821 21.2335 20.5513 21.2353 20.6185 21.2226C20.6857 21.2099 20.7493 21.1831 20.8046 21.1439C21.7749 20.4924 22.7398 19.8342 23.6995 19.1693C23.7546 19.1262 23.8207 19.0984 23.8907 19.0889C23.9608 19.0794 24.0322 19.0884 24.0974 19.1152C24.9539 19.4042 25.8733 19.4708 26.7644 19.3085C27.7408 19.1524 28.6531 18.7352 29.399 18.1035C30.1449 17.4718 30.6949 16.6507 30.9873 15.7323C31.1136 15.3068 31.2132 14.8743 31.2858 14.4372V13.6736L31.2826 13.6506C31.2657 13.5284 31.2484 13.4031 31.2241 13.2812ZM26.0507 15.4521H25.1335C25.1335 15.0142 25.1748 14.7126 25.2575 14.5473C25.3401 14.3821 25.5632 14.1383 25.9268 13.8161C26.0755 13.6921 26.1994 13.5578 26.2986 13.413C26.3977 13.2686 26.4473 13.1138 26.4473 12.9485C26.4473 12.775 26.3812 12.6158 26.249 12.4711C26.1168 12.3266 25.9185 12.2544 25.6541 12.2544C25.3566 12.2544 25.1357 12.3412 24.9912 12.5147C24.8465 12.6882 24.7534 12.8493 24.7121 12.9981L23.8941 12.6758C24.0098 12.3288 24.2144 12.0313 24.5079 11.7835C24.801 11.5356 25.1831 11.4116 25.6541 11.4116C26.1168 11.4116 26.5175 11.5356 26.8563 11.7835C27.1951 12.0313 27.3645 12.3866 27.3645 12.8493C27.3645 13.122 27.3025 13.3554 27.1786 13.5494C27.0546 13.7437 26.8852 13.9483 26.6704 14.1631C26.3812 14.4358 26.2057 14.6506 26.1439 14.8076C26.0818 14.9646 26.0507 15.1794 26.0507 15.4521ZM26.02 17.1808C25.9 17.3008 25.7532 17.3608 25.5797 17.3608C25.4062 17.3608 25.2595 17.3008 25.1395 17.1808C25.0198 17.0612 24.96 16.9146 24.96 16.7411C24.96 16.5676 25.0198 16.421 25.1395 16.3013C25.2595 16.1814 25.4062 16.1214 25.5797 16.1214C25.7532 16.1214 25.9 16.1814 26.02 16.3013C26.1396 16.421 26.1994 16.5676 26.1994 16.7411C26.1994 16.9146 26.1396 17.0612 26.02 17.1808ZM12.3957 20.7207C12.4807 21.4003 12.8138 22.0278 13.3345 22.4891C13.8551 22.9505 14.5288 23.2151 15.2332 23.235C15.9376 23.2549 16.6259 23.0287 17.1733 22.5975C17.7206 22.1663 18.0907 21.5587 18.2162 20.885C18.2992 20.3939 18.3991 19.9056 18.4989 19.4175C18.5295 19.2678 18.5601 19.1181 18.5903 18.9684C18.7099 18.4734 18.6948 17.9566 18.5464 17.469C18.3979 16.9814 18.1213 16.5398 17.7437 16.1877C17.4238 15.8868 17.0443 15.6524 16.6282 15.4985C16.2121 15.3446 15.7682 15.2746 15.3233 15.2927C15.2183 15.3058 15.1127 15.3165 15.0072 15.3273C14.7787 15.3505 14.5504 15.3736 14.3286 15.4212C13.5736 15.6001 12.9171 16.0513 12.4921 16.6833C12.0672 17.3154 11.9057 18.0809 12.0405 18.8244L12.0722 18.9823C12.1883 19.56 12.3045 20.1387 12.3957 20.7207ZM22.1044 26.2182C22.0049 25.7562 21.8786 25.3328 21.3394 25.155C20.4947 24.8805 19.6563 24.5859 18.8179 24.2913L18.5252 24.1885C18.4642 24.161 18.3954 24.1544 18.33 24.1697C18.2647 24.1849 18.2065 24.2212 18.165 24.2726C17.8939 24.54 17.6187 24.8038 17.3435 25.0676C16.6674 25.7156 15.9911 26.3638 15.3746 27.0677L15.2543 27.0629C14.4452 26.2688 13.325 25.155 12.4838 24.2783C12.4433 24.2265 12.3862 24.1893 12.3214 24.1728C12.2566 24.1564 12.188 24.1615 12.1266 24.1875C11.1769 24.5238 10.2246 24.8554 9.26957 25.182C9.10862 25.2297 8.96474 25.3204 8.85442 25.4438C8.7441 25.5672 8.67181 25.7184 8.64584 25.8799C8.49729 26.5722 8.34396 27.2637 8.19064 27.9551C8.10165 28.3565 8.01266 28.7578 7.92461 29.1593C7.85105 29.495 7.77977 29.8312 7.70706 30.1741C7.67259 30.3367 7.6378 30.5008 7.60229 30.6671L23.0555 30.6593L23.0477 30.6175C23.0334 30.5398 23.0212 30.4741 23.0077 30.41C22.7066 29.0124 22.4055 27.6151 22.1044 26.2182Z", "fill", "white"], [1, "tds-menu-footer-icon", "tds-menu-footer-icon-inline-collapsed", 3, "click", "ngClass"], [3, "ngClass"]],
      template: function TDSMenuComponent_Template(rf, ctx) {
        if (rf & 1) {
          ɵɵprojectionDef(_c72);
          ɵɵelementStart(0, "div", 0);
          ɵɵtemplate(1, TDSMenuComponent_Conditional_1_Template, 3, 2, "div", 1);
          ɵɵelementStart(2, "div", 2);
          ɵɵtemplate(3, TDSMenuComponent_Conditional_3_Template, 2, 0)(4, TDSMenuComponent_Conditional_4_Template, 2, 0);
          ɵɵelementEnd();
          ɵɵtemplate(5, TDSMenuComponent_Conditional_5_Template, 3, 4, "div", 3);
          ɵɵelementEnd();
        }
        if (rf & 2) {
          ɵɵproperty("ngClass", ctx.mode);
          ɵɵadvance();
          ɵɵconditional(ctx.showLogo ? 1 : -1);
          ɵɵadvance();
          ɵɵproperty("ngClass", ɵɵpureFunction1(6, _c92, ctx.inlineCollapsed));
          ɵɵadvance();
          ɵɵconditional(ctx.inlineCollapsed ? 3 : -1);
          ɵɵadvance();
          ɵɵconditional(!ctx.inlineCollapsed ? 4 : -1);
          ɵɵadvance();
          ɵɵconditional(ctx.hasIcon && ctx.showFooter ? 5 : -1);
        }
      },
      dependencies: [NgClass, TDSOutletModule, TDSBadgeModule, TDSToolTipModule, TDSPipesModule, TDSNavBarModule, TDSMapperPipeModule, TDSMenuItemComponent, TDSMenuGroupPopupComponent, TDSMenuGroupInlineComponent, NgTemplateOutlet],
      encapsulation: 2,
      changeDetection: 0
    });
  }
};
__decorate([InputBoolean()], TDSMenuComponent.prototype, "showLogo", void 0);
__decorate([InputBoolean()], TDSMenuComponent.prototype, "inlineCollapsed", void 0);
__decorate([InputBoolean()], TDSMenuComponent.prototype, "matchRouterExact", void 0);
__decorate([InputBoolean()], TDSMenuComponent.prototype, "matchRouter", void 0);
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TDSMenuComponent, [{
    type: Component,
    args: [{
      selector: "tds-menu",
      changeDetection: ChangeDetectionStrategy.OnPush,
      encapsulation: ViewEncapsulation$1.None,
      host: {
        "[class.w-full]": `!inlineCollapsed`,
        "[class.tds-menu-collapsed]": `inlineCollapsed`,
        "[class.tds-menu-light]": 'mode === "light"',
        "[class.tds-menu-dark]": 'mode === "dark"',
        "[class.tds-menu-default]": 'mode === "default"'
      },
      providers: [
        /** use the top level service **/
        TDSMenuService
      ],
      standalone: true,
      imports: [NgClass, TDSOutletModule, TDSBadgeModule, TDSToolTipModule, TDSPipesModule, TDSNavBarModule, TDSMapperPipeModule, TDSMenuItemComponent, TDSMenuGroupPopupComponent, TDSMenuGroupInlineComponent, NgTemplateOutlet],
      template: `<div class="tds-menu-wrapper" [ngClass]="mode">\r
  <!-- header -->\r
  @if (showLogo) {\r
    <div class="w-full tds-menu-header">\r
      @if (!inlineCollapsed) {\r
        <ng-content select="[logo-text]">\r
        </ng-content>\r
      }\r
      @if (inlineCollapsed) {\r
        <ng-content select="[logo]">\r
        </ng-content>\r
      }\r
    </div>\r
  }\r
  <!-- body -->\r
  <div class="tds-menu-body" [ngClass]="{' overflow-x-hidden':inlineCollapsed}" cdkScrollable>\r
    <!-- menu item  -->\r
    @if (inlineCollapsed) {\r
      @for (item of listMenu; track item) {\r
        @if (!item.hidden) {\r
          <tds-menu-group-popup [options]="options" [item]="item" [tdsTheme]="mode">\r
            @for (children of item.listChild; track children) {\r
              <tds-menu-item\r
                [class]="{'tds-menu-light':mode=='light','tds-menu-dark':mode=='dark','tds-menu-default':mode=='default'}"\r
                [options]="options" [item]="children" [isSelected]="children.isSelected!"\r
                [parentIsGroup]="true" [matchRouterExact]="matchRouterExact" [matchRouter]="matchRouter" [tdsTheme]="mode">\r
              </tds-menu-item>\r
            }\r
          </tds-menu-group-popup>\r
        }\r
      }\r
    }\r
    @if (!inlineCollapsed) {\r
      @for (item of listMenu; track item) {\r
        @if (!item.hidden) {\r
          <tds-menu-group-inline [options]="options" [showIcon]="hasIcon" [item]="item" [tdsTheme]="mode">\r
            @for (children of item.listChild; track children) {\r
              <tds-menu-item\r
                [class]="{'tds-menu-light':mode=='light','tds-menu-dark':mode=='dark','tds-menu-default':mode=='default'}"\r
                [options]="options" [showIcon]="hasIcon" [item]="children"\r
                [isSelected]="children.isSelected!" [parentIsGroup]="true"\r
                [matchRouterExact]="matchRouterExact" [matchRouter]="matchRouter" [tdsTheme]="mode">\r
              </tds-menu-item>\r
            }\r
          </tds-menu-group-inline>\r
        }\r
      }\r
    }\r
\r
  </div>\r
  <!-- footer -->\r
  @if (hasIcon && showFooter) {\r
    <div class="tds-menu-footer" [ngClass]="{'flex-col':inlineCollapsed}">\r
      @if (menuFooter) {\r
        <ng-container *ngTemplateOutlet="$any(menuFooter); context: { $implicit: inlineCollapsed }">\r
        </ng-container>\r
      } @else {\r
        <div class="tds-menu-footer-svg">\r
          <svg width="38" height="38" viewBox="0 0 38 38" fill="none" xmlns="http://www.w3.org/2000/svg">\r
            <path\r
              d="M19 38C29.4934 38 38 29.4934 38 19C38 8.50658 29.4934 0 19 0C8.50662 0 3.04774e-05 8.50658 3.04774e-05 19L0 33.7778C0 36.3111 1.68889 38 4.22221 38H19Z"\r
              class="fill-current" />\r
              <path fill-rule="evenodd" clip-rule="evenodd"\r
                d="M31.2241 13.2812C31.0776 12.3206 30.6631 11.4174 30.0256 10.6695C29.388 9.92167 28.5518 9.35776 27.6077 9.03903C26.6636 8.72029 25.6477 8.65889 24.6702 8.86151C23.6927 9.06413 22.791 9.52301 22.063 10.1884C21.5477 10.6357 21.1308 11.1797 20.8375 11.7876C20.5441 12.3956 20.3803 13.0548 20.356 13.7258C20.2959 15.5879 20.2823 17.4511 20.2687 19.3141C20.265 19.829 20.2612 20.3439 20.2565 20.8588C20.2547 20.9262 20.2679 20.9932 20.2954 21.0551C20.3229 21.117 20.3639 21.1724 20.4156 21.2173C20.4821 21.2335 20.5513 21.2353 20.6185 21.2226C20.6857 21.2099 20.7493 21.1831 20.8046 21.1439C21.7749 20.4924 22.7398 19.8342 23.6995 19.1693C23.7546 19.1262 23.8207 19.0984 23.8907 19.0889C23.9608 19.0794 24.0322 19.0884 24.0974 19.1152C24.9539 19.4042 25.8733 19.4708 26.7644 19.3085C27.7408 19.1524 28.6531 18.7352 29.399 18.1035C30.1449 17.4718 30.6949 16.6507 30.9873 15.7323C31.1136 15.3068 31.2132 14.8743 31.2858 14.4372V13.6736L31.2826 13.6506C31.2657 13.5284 31.2484 13.4031 31.2241 13.2812ZM26.0507 15.4521H25.1335C25.1335 15.0142 25.1748 14.7126 25.2575 14.5473C25.3401 14.3821 25.5632 14.1383 25.9268 13.8161C26.0755 13.6921 26.1994 13.5578 26.2986 13.413C26.3977 13.2686 26.4473 13.1138 26.4473 12.9485C26.4473 12.775 26.3812 12.6158 26.249 12.4711C26.1168 12.3266 25.9185 12.2544 25.6541 12.2544C25.3566 12.2544 25.1357 12.3412 24.9912 12.5147C24.8465 12.6882 24.7534 12.8493 24.7121 12.9981L23.8941 12.6758C24.0098 12.3288 24.2144 12.0313 24.5079 11.7835C24.801 11.5356 25.1831 11.4116 25.6541 11.4116C26.1168 11.4116 26.5175 11.5356 26.8563 11.7835C27.1951 12.0313 27.3645 12.3866 27.3645 12.8493C27.3645 13.122 27.3025 13.3554 27.1786 13.5494C27.0546 13.7437 26.8852 13.9483 26.6704 14.1631C26.3812 14.4358 26.2057 14.6506 26.1439 14.8076C26.0818 14.9646 26.0507 15.1794 26.0507 15.4521ZM26.02 17.1808C25.9 17.3008 25.7532 17.3608 25.5797 17.3608C25.4062 17.3608 25.2595 17.3008 25.1395 17.1808C25.0198 17.0612 24.96 16.9146 24.96 16.7411C24.96 16.5676 25.0198 16.421 25.1395 16.3013C25.2595 16.1814 25.4062 16.1214 25.5797 16.1214C25.7532 16.1214 25.9 16.1814 26.02 16.3013C26.1396 16.421 26.1994 16.5676 26.1994 16.7411C26.1994 16.9146 26.1396 17.0612 26.02 17.1808ZM12.3957 20.7207C12.4807 21.4003 12.8138 22.0278 13.3345 22.4891C13.8551 22.9505 14.5288 23.2151 15.2332 23.235C15.9376 23.2549 16.6259 23.0287 17.1733 22.5975C17.7206 22.1663 18.0907 21.5587 18.2162 20.885C18.2992 20.3939 18.3991 19.9056 18.4989 19.4175C18.5295 19.2678 18.5601 19.1181 18.5903 18.9684C18.7099 18.4734 18.6948 17.9566 18.5464 17.469C18.3979 16.9814 18.1213 16.5398 17.7437 16.1877C17.4238 15.8868 17.0443 15.6524 16.6282 15.4985C16.2121 15.3446 15.7682 15.2746 15.3233 15.2927C15.2183 15.3058 15.1127 15.3165 15.0072 15.3273C14.7787 15.3505 14.5504 15.3736 14.3286 15.4212C13.5736 15.6001 12.9171 16.0513 12.4921 16.6833C12.0672 17.3154 11.9057 18.0809 12.0405 18.8244L12.0722 18.9823C12.1883 19.56 12.3045 20.1387 12.3957 20.7207ZM22.1044 26.2182C22.0049 25.7562 21.8786 25.3328 21.3394 25.155C20.4947 24.8805 19.6563 24.5859 18.8179 24.2913L18.5252 24.1885C18.4642 24.161 18.3954 24.1544 18.33 24.1697C18.2647 24.1849 18.2065 24.2212 18.165 24.2726C17.8939 24.54 17.6187 24.8038 17.3435 25.0676C16.6674 25.7156 15.9911 26.3638 15.3746 27.0677L15.2543 27.0629C14.4452 26.2688 13.325 25.155 12.4838 24.2783C12.4433 24.2265 12.3862 24.1893 12.3214 24.1728C12.2566 24.1564 12.188 24.1615 12.1266 24.1875C11.1769 24.5238 10.2246 24.8554 9.26957 25.182C9.10862 25.2297 8.96474 25.3204 8.85442 25.4438C8.7441 25.5672 8.67181 25.7184 8.64584 25.8799C8.49729 26.5722 8.34396 27.2637 8.19064 27.9551C8.10165 28.3565 8.01266 28.7578 7.92461 29.1593C7.85105 29.495 7.77977 29.8312 7.70706 30.1741C7.67259 30.3367 7.6378 30.5008 7.60229 30.6671L23.0555 30.6593L23.0477 30.6175C23.0334 30.5398 23.0212 30.4741 23.0077 30.41C22.7066 29.0124 22.4055 27.6151 22.1044 26.2182Z"\r
                fill="white" />\r
              </svg>\r
            </div>\r
            <div class="tds-menu-footer-icon tds-menu-footer-icon-inline-collapsed"\r
              [ngClass]="{'tds-menu-footer-icon-inline-collapsed':inlineCollapsed}" (click)="onClickInlineCollapsed()">\r
              <span [ngClass]="{'tdsi-chevron-right-fill':inlineCollapsed, 'tdsi-chevron-left-fill':!inlineCollapsed }"\r
              ></span>\r
            </div>\r
          }\r
        </div>\r
      }\r
    </div>\r
\r
`
    }]
  }], () => [{
    type: ElementRef
  }, {
    type: Renderer2
  }, {
    type: ChangeDetectorRef
  }, {
    type: TDSMenuService
  }], {
    showLogo: [{
      type: Input
    }],
    data: [{
      type: Input
    }],
    mode: [{
      type: Input
    }],
    showIcon: [{
      type: Input
    }],
    showFooter: [{
      type: Input
    }],
    inlineCollapsed: [{
      type: Input
    }],
    matchRouterExact: [{
      type: Input
    }],
    matchRouter: [{
      type: Input
    }],
    options: [{
      type: Input
    }],
    onClickItem: [{
      type: Output
    }],
    onOpenChange: [{
      type: Output
    }],
    listOfTDSMenuGroupInlineComponent: [{
      type: ViewChildren,
      args: [TDSMenuGroupInlineComponent]
    }],
    listOfTDSMenuGroupPopupComponent: [{
      type: ViewChildren,
      args: [TDSMenuGroupPopupComponent]
    }],
    logoText: [{
      type: ContentChild,
      args: ["logo-text"]
    }],
    tdsMenuFooter: [{
      type: Input
    }],
    tdsMenuFooterChild: [{
      type: ContentChild,
      args: [TDSMenuFooterDirective, {
        static: false,
        read: TemplateRef
      }]
    }]
  });
})();
var TDSMenuModule = class _TDSMenuModule {
  static {
    this.ɵfac = function TDSMenuModule_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TDSMenuModule)();
    };
  }
  static {
    this.ɵmod = ɵɵdefineNgModule({
      type: _TDSMenuModule,
      imports: [TDSMenuComponent, TDSMenuItemComponent, TDSMenuGroupInlineComponent, TDSMenuGroupPopupComponent, TDSMenuFooterDirective],
      exports: [TDSMenuComponent, TDSMenuItemComponent, TDSMenuFooterDirective, TDSMenuGroupInlineComponent, TDSMenuGroupPopupComponent]
    });
  }
  static {
    this.ɵinj = ɵɵdefineInjector({
      imports: [TDSMenuComponent, TDSMenuItemComponent, TDSMenuGroupInlineComponent, TDSMenuGroupPopupComponent]
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TDSMenuModule, [{
    type: NgModule,
    args: [{
      imports: [TDSMenuComponent, TDSMenuItemComponent, TDSMenuGroupInlineComponent, TDSMenuGroupPopupComponent, TDSMenuFooterDirective],
      exports: [TDSMenuComponent, TDSMenuItemComponent, TDSMenuFooterDirective, TDSMenuGroupInlineComponent, TDSMenuGroupPopupComponent]
    }]
  }], null, null);
})();

export {
  TDSNoAnimationDirective,
  TDSMenuService,
  TDSMenuItemComponent,
  TDSMenuGroupInlineComponent,
  TDSMenuGroupPopupComponent,
  TDSMenuFooterDirective,
  TDSMenuServiceFactory,
  TDSMenuComponent,
  TDSMenuModule
};
//# sourceMappingURL=chunk-5CVR7W7N.js.map
