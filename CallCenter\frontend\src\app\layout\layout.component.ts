import { Component } from '@angular/core';
import { TDSMenuDTO } from 'tds-ui/menu';
import { TDSSafeAny } from 'tds-ui/shared/utility';
import { Router } from '@angular/router';

@Component({
  selector: 'app-layout',
  standalone: false,

  templateUrl: './layout.component.html',
  styleUrl: './layout.component.scss',
})
export class LayoutComponent {
  constructor(private router: Router) { }

  isCollapsed = false;
  activeTab = 1;
  active = 1;
  active1 = 'top';
  lstMenu: Array<TDSMenuDTO> = [{
    "name": "Dashboard",
    "link": "/dashboard",
    htmlIcon: `<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M1.78742 0.400124H7.78772C8.15564 0.400124 8.5085 0.546282 8.76866 0.806445C9.02883 1.06661 9.17499 1.41947 9.17499 1.78739V5.38757C9.17499 5.75529 9.02899 6.10797 8.76909 6.3681C8.50918 6.62823 8.15664 6.77452 7.78892 6.77484H1.78862C1.60644 6.775 1.42601 6.73927 1.25764 6.6697C1.08927 6.60013 0.936249 6.49808 0.807317 6.36937C0.678386 6.24066 0.576068 6.08782 0.506206 5.91957C0.436343 5.75132 0.400305 5.57095 0.400147 5.38877V1.78859C0.399989 1.60631 0.435756 1.42579 0.505402 1.25734C0.575048 1.08889 0.677209 0.935817 0.806044 0.80687C0.93488 0.677923 1.08786 0.57563 1.25625 0.505838C1.42464 0.436046 1.60514 0.400124 1.78742 0.400124V0.400124Z" class='tds-icon-secondary' />
        <path d="M12.2125 13.2252H18.2128C18.5807 13.2252 18.9335 13.3713 19.1937 13.6315C19.4539 13.8917 19.6 14.2445 19.6 14.6124V18.2126C19.6 18.5803 19.454 18.933 19.1941 19.1931C18.9342 19.4533 18.5817 19.5996 18.214 19.5999H12.2137C12.0315 19.6 11.8511 19.5643 11.6827 19.4947C11.5143 19.4252 11.3613 19.3231 11.2324 19.1944C11.1034 19.0657 11.0011 18.9129 10.9313 18.7446C10.8614 18.5764 10.8254 18.396 10.8252 18.2138V14.6136C10.825 14.4314 10.8608 14.2508 10.9305 14.0824C11.0001 13.9139 11.1023 13.7609 11.2311 13.6319C11.3599 13.503 11.5129 13.4007 11.6813 13.3309C11.8497 13.2611 12.0302 13.2252 12.2125 13.2252V13.2252Z" class='tds-icon-secondary'/>
        <path d="M1.78742 8.42493H7.78772C8.15564 8.42493 8.5085 8.57109 8.76866 8.83125C9.02883 9.09141 9.17499 9.44427 9.17499 9.8122V18.2126C9.17499 18.5803 9.02899 18.933 8.76909 19.1931C8.50918 19.4533 8.15664 19.5996 7.78892 19.5999H1.78862C1.60644 19.6 1.42601 19.5643 1.25764 19.4947C1.08927 19.4252 0.936249 19.3231 0.807317 19.1944C0.678386 19.0657 0.576068 18.9129 0.506206 18.7446C0.436343 18.5764 0.400305 18.396 0.400147 18.2138V9.8134C0.399989 9.63112 0.435756 9.45059 0.505402 9.28214C0.575048 9.11369 0.677209 8.96062 0.806044 8.83167C0.93488 8.70273 1.08786 8.60043 1.25625 8.53064C1.42464 8.46085 1.60514 8.42493 1.78742 8.42493V8.42493Z"  class="tds-icon-primary"/>
        <path d="M12.2125 0.400124H18.2128C18.5807 0.400124 18.9335 0.546282 19.1937 0.806445C19.4539 1.06661 19.6 1.41947 19.6 1.78739V10.1878C19.6 10.5555 19.454 10.9082 19.1941 11.1683C18.9342 11.4285 18.5817 11.5748 18.214 11.5751H12.2137C12.0315 11.5752 11.8511 11.5395 11.6827 11.4699C11.5143 11.4004 11.3613 11.2983 11.2324 11.1696C11.1034 11.0409 11.0011 10.8881 10.9313 10.7198C10.8614 10.5516 10.8254 10.3712 10.8252 10.189V1.78859C10.825 1.60631 10.8608 1.42579 10.9305 1.25734C11.0001 1.08889 11.1023 0.935817 11.2311 0.80687C11.3599 0.677923 11.5129 0.57563 11.6813 0.505838C11.8497 0.436046 12.0302 0.400124 12.2125 0.400124V0.400124Z" class="tds-icon-primary"/>
        </svg>`
  },
  {
    "name": "Cuộc gọi",
    "htmlIcon": `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M2.40015 12.6648C2.40015 8.56661 6.10953 5.24484 10.6806 5.24484C15.2468 5.24484 18.9562 8.56661 18.9562 12.6648C18.9562 16.763 15.2468 20.0848 10.6758 20.0848C9.46852 20.0873 8.27298 19.8482 7.15958 19.3815C5.67246 20.1663 4.06327 20.6938 2.40015 20.9416C3.27822 19.7173 3.75647 18.2516 3.76941 16.745C2.88648 15.5674 2.40635 14.1367 2.40015 12.6648Z" class="tds-icon-primary"/>
        <path d="M21.6015 9.82211C21.6015 5.72391 17.8957 2.40214 13.3211 2.40214C11.8083 2.36891 10.3148 2.74529 8.99844 3.49145C7.68212 4.2376 6.59201 5.32576 5.84351 6.64075C7.2857 5.7202 8.96282 5.23519 10.6737 5.24388C15.2448 5.24388 18.9542 8.56565 18.9542 12.6639C18.9481 14.1943 18.4312 15.6789 17.4853 16.8821C18.7975 17.4687 20.1822 17.8775 21.6027 18.0977C20.7264 16.8753 20.2486 15.4123 20.2346 13.9083C21.1179 12.7287 21.5973 11.2957 21.6015 9.82211Z" class="tds-icon-secondary"/>
        </svg>`,
    "link": "/calls"
  },
  {
    "name": "Đánh giá chất lượng",
    "htmlIcon": `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M11.4002 12.6011V4.72525H10.8381C9.16926 4.72525 7.53786 5.22013 6.15024 6.14731C4.76262 7.07449 3.68111 8.39232 3.04245 9.93417C2.4038 11.476 2.2367 13.1726 2.56228 14.8094C2.88787 16.4462 3.69151 17.9497 4.87158 19.1298C6.05165 20.3099 7.55516 21.1135 9.19197 21.4391C10.8288 21.7647 12.5254 21.5976 14.0672 20.9589C15.6091 20.3203 16.9269 19.2388 17.8541 17.8511C18.7812 16.4635 19.2761 14.8321 19.2761 13.1632V12.6011H11.4002Z" class="tds-icon-primary"/>
        <path d="M13.163 2.40012H12.6008V11.4002H21.6009V10.8381C21.5986 8.60094 20.7088 6.45607 19.1269 4.87415C17.545 3.29224 15.4001 2.40248 13.163 2.40012V2.40012Z" class="tds-icon-secondary"/>
        </svg>`,
    "listChild": [
      {
        "name": "Đánh giá cuộc gọi",
        "link": "/qa/qa-assessment"
      },
      {
        "name": "Quản lý tiêu chí",
        "link": "/qa/qa-procedure-management"
      }
    ]
  },
  {
    "name": "Báo cáo",
    "htmlIcon": `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M18.4834 10.4149C19.6793 10.4149 20.6487 9.44544 20.6487 8.24957C20.6487 7.0537 19.6793 6.08426 18.4834 6.08426C17.2876 6.08426 16.3181 7.0537 16.3181 8.24957C16.3181 9.44544 17.2876 10.4149 18.4834 10.4149Z" class="tds-icon-secondary"/>
        <path d="M5.52566 10.4149C6.72153 10.4149 7.69097 9.44544 7.69097 8.24957C7.69097 7.0537 6.72153 6.08426 5.52566 6.08426C4.32979 6.08426 3.36035 7.0537 3.36035 8.24957C3.36035 9.44544 4.32979 10.4149 5.52566 10.4149Z" class="tds-icon-secondary"/>
        <path d="M7.68311 12.2145C6.83077 11.5162 6.05885 11.6086 5.07332 11.6086C3.59934 11.6086 2.40015 12.8007 2.40015 14.2656V18.5652C2.40015 19.2014 2.91943 19.7187 3.558 19.7187C6.31487 19.7187 5.98275 19.7686 5.98275 19.5998C5.98275 16.5531 5.62189 14.3189 7.68311 12.2145Z" class="tds-icon-secondary"/>
        <path d="M12.936 11.4081C11.2146 11.2645 9.71834 11.4097 8.42777 12.475C6.26809 14.2049 6.68371 16.5341 6.68371 19.3835C6.68371 20.1374 7.29709 20.7622 8.06243 20.7622C16.3726 20.7622 16.7033 21.0303 17.1961 19.939C17.3577 19.5699 17.3134 19.6872 17.3134 16.1568C17.3134 13.3527 14.8854 11.4081 12.936 11.4081Z" class="tds-icon-primary"/>
        <path d="M18.9279 11.6085C17.937 11.6085 17.1693 11.517 16.3181 12.2144C18.3639 14.3032 18.0185 16.385 18.0185 19.5997C18.0185 19.7695 17.7428 19.7186 20.4019 19.7186C21.0633 19.7186 21.6011 19.1828 21.6011 18.5241V14.2655C21.6011 12.8006 20.4019 11.6085 18.9279 11.6085Z" class="tds-icon-secondary"/>
        </svg>`,
    "link": "/reports"
  },
  {
    "name": "Cấu hình",
    "htmlIcon": `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M12.0007 9.18799C11.4443 9.18799 10.9005 9.35296 10.4379 9.66205C9.97532 9.97114 9.61476 10.4105 9.40186 10.9245C9.18895 11.4385 9.13326 12.004 9.2418 12.5497C9.35034 13.0954 9.61823 13.5966 10.0116 13.99C10.405 14.3834 10.9062 14.6513 11.4519 14.7598C11.9976 14.8684 12.5632 14.8126 13.0772 14.5997C13.5911 14.3868 14.0305 14.0263 14.3396 13.5637C14.6487 13.1011 14.8136 12.5573 14.8136 12.0009C14.813 11.2551 14.5164 10.54 13.989 10.0126C13.4616 9.4852 12.7465 9.18862 12.0007 9.18799Z" class="tds-icon-secondary"/>
        <path d="M21.6011 13.2787V10.7226C21.6011 10.6356 21.571 10.5514 21.5158 10.4842C21.4607 10.4171 21.3839 10.3711 21.2987 10.3541L18.9982 9.90172C18.8564 9.42916 18.6666 8.97236 18.4317 8.53845L19.7134 6.61836C19.7615 6.5463 19.7832 6.45979 19.7746 6.37357C19.7661 6.28735 19.7279 6.20676 19.6666 6.14553L17.8581 4.33464C17.7969 4.27334 17.7163 4.23517 17.6301 4.22664C17.5439 4.2181 17.4574 4.23973 17.3853 4.28784L15.4652 5.56951C15.0313 5.33466 14.5745 5.14486 14.1019 5.00308L13.6471 2.70256C13.6304 2.61721 13.5845 2.54034 13.5172 2.48516C13.45 2.42998 13.3657 2.39992 13.2787 2.40015H10.7226C10.6356 2.40015 10.5514 2.43029 10.4842 2.48543C10.4171 2.54058 10.3711 2.61731 10.3541 2.70256L9.90172 5.00308C9.42916 5.14487 8.97236 5.33467 8.53845 5.56951L6.61836 4.28784C6.5463 4.23973 6.45979 4.2181 6.37357 4.22664C6.28735 4.23517 6.20676 4.27334 6.14553 4.33464L4.33464 6.14313C4.27334 6.20436 4.23517 6.28495 4.22663 6.37117C4.2181 6.45739 4.23973 6.5439 4.28784 6.61596L5.5695 8.53605C5.33466 8.96995 5.14486 9.42676 5.00308 9.89932L2.70256 10.3541C2.61731 10.3711 2.54058 10.4171 2.48543 10.4842C2.43029 10.5514 2.40015 10.6356 2.40015 10.7226V13.2787C2.40015 13.3656 2.43029 13.4498 2.48543 13.517C2.54058 13.5842 2.61731 13.6302 2.70256 13.6471L5.00308 14.0995C5.14487 14.5721 5.33467 15.0289 5.5695 15.4628L4.28784 17.3829C4.23973 17.455 4.2181 17.5415 4.22663 17.6277C4.23517 17.7139 4.27334 17.7945 4.33464 17.8557L6.14313 19.6642C6.20436 19.7255 6.28495 19.7637 6.37117 19.7722C6.45739 19.7808 6.5439 19.7591 6.61596 19.711L8.53605 18.4293C8.96995 18.6642 9.42676 18.854 9.89932 18.9958L10.3517 21.2963C10.3683 21.3824 10.4144 21.46 10.4821 21.5157C10.5498 21.5713 10.6349 21.6016 10.7226 21.6011H13.2787C13.3656 21.6011 13.4498 21.571 13.517 21.5158C13.5842 21.4607 13.6302 21.3839 13.6471 21.2987L14.0995 18.9982C14.5721 18.8564 15.0289 18.6666 15.4628 18.4317L17.3829 19.7134C17.455 19.7615 17.5415 19.7832 17.6277 19.7746C17.7139 19.7661 17.7945 19.7279 17.8557 19.6666L19.6642 17.8581C19.7255 17.7969 19.7637 17.7163 19.7722 17.6301C19.7808 17.5439 19.7591 17.4574 19.711 17.3853L18.4293 15.4652C18.6642 15.0313 18.854 14.5745 18.9958 14.1019L21.2963 13.6495C21.3825 13.6332 21.4602 13.5871 21.5159 13.5194C21.5716 13.4516 21.6018 13.3664 21.6011 13.2787Z" class="tds-icon-primary"/>
        </svg>`,
    "listChild": [
      {
        "name": "Cấu hình chung",
        "link": "/configuration/general"
      },
      {
        "name": "Quản lý người dùng",
        "link": "/configuration/user"
      }
    ]
  }
  ]



  setActiveTab(event: TDSSafeAny) {
    this.activeTab = event;
  }


  ngOnInit(): void {
  }

  toggleCollapsed(): void {
    this.isCollapsed = !this.isCollapsed;
  }

  onOpenChange(e: boolean) {
    this.isCollapsed = e;
  }

  // onMenuItemClick(item: TDSMenuDTO) {
  //   if (item && item.link) {
  //     this.router.navigate([item.link]);
  //   }
  // }
}
