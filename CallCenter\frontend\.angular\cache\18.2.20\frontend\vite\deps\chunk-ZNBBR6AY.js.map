{"version": 3, "sources": ["../../../../../../node_modules/tds-ui/fesm2022/tds-ui-cdk-pipes-mapper.mjs", "../../../../../../node_modules/tds-ui/fesm2022/tds-ui-core-config.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Pipe, NgModule } from '@angular/core';\nclass TDSMapperPipe {\n  /**\n   * Maps object to an arbitrary result through a mapper function.\n   *  **Recommendation:** It is advisable to use an arrow function for this purpose.\n   * @param value an item to transform\n   * @param mapper a mapping function\n   * @param args arbitrary number of additional arguments     *\n   */\n  transform(value, mapper, ...args) {\n    return mapper(value, ...args);\n  }\n  static {\n    this.ɵfac = function TDSMapperPipe_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSMapperPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"tdsMapper\",\n      type: TDSMapperPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSMapperPipe, [{\n    type: Pipe,\n    args: [{\n      name: `tdsMapper`,\n      standalone: true\n    }]\n  }], null, null);\n})();\nclass TDSMapperPipeModule {\n  static {\n    this.ɵfac = function TDSMapperPipeModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSMapperPipeModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: TDSMapperPipeModule,\n      imports: [TDSMapperPipe],\n      exports: [TDSMapperPipe]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSMapperPipeModule, [{\n    type: NgModule,\n    args: [{\n      imports: [TDSMapperPipe],\n      exports: [TDSMapperPipe]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TDSMapperPipe, TDSMapperPipeModule };\n", "import * as i0 from '@angular/core';\nimport { Injectable, Optional, Inject, InjectionToken } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport { filter, mapTo } from 'rxjs/operators';\nconst TDS_LIST_THEME_COLOR = ['blue', 'green', 'orange'];\nconst isDefined = function (value) {\n  return value !== undefined;\n};\nclass TDSConfigService {\n  constructor(defaultConfig) {\n    this.configUpdated$ = new Subject();\n    this.config = defaultConfig || {};\n  }\n  getConfig() {\n    return this.config;\n  }\n  getConfigForComponent(componentName) {\n    return this.config[componentName];\n  }\n  getConfigChangeEventForComponent(componentName) {\n    return this.configUpdated$.pipe(filter(n => n === componentName), mapTo(undefined));\n  }\n  set(componentName, value) {\n    this.config[componentName] = {\n      ...this.config[componentName],\n      ...value\n    };\n    this.configUpdated$.next(componentName);\n  }\n  static {\n    this.ɵfac = function TDSConfigService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSConfigService)(i0.ɵɵinject(TDS_CONFIG, 8));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: TDSConfigService,\n      factory: TDSConfigService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSConfigService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [TDS_CONFIG]\n    }]\n  }], null);\n})();\nfunction WithConfig() {\n  return function ConfigDecorator(target, propName, originalDescriptor) {\n    const privatePropName = `$$__tdsuiConfigDecorator__${propName}`;\n    Object.defineProperty(target, privatePropName, {\n      configurable: true,\n      writable: true,\n      enumerable: false\n    });\n    return {\n      get() {\n        const originalValue = originalDescriptor?.get ? originalDescriptor.get.bind(this)() : this[privatePropName];\n        const assignedByUser = (this.propertyAssignCounter?.[propName] || 0) > 1;\n        const configValue = this.tdsConfigService.getConfigForComponent(this._tdsModuleName)?.[propName];\n        if (assignedByUser && isDefined(originalValue)) {\n          return originalValue;\n        } else {\n          return isDefined(configValue) ? configValue : originalValue;\n        }\n      },\n      set(value) {\n        this.propertyAssignCounter = this.propertyAssignCounter || {};\n        this.propertyAssignCounter[propName] = (this.propertyAssignCounter[propName] || 0) + 1;\n        if (originalDescriptor?.set) {\n          originalDescriptor.set.bind(this)(value);\n        } else {\n          this[privatePropName] = value;\n        }\n      },\n      configurable: true,\n      enumerable: true\n    };\n  };\n}\nconst TDS_CONFIG = new InjectionToken('tds-config');\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TDSConfigService, TDS_CONFIG, TDS_LIST_THEME_COLOR, WithConfig };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAEA,IAAM,gBAAN,MAAM,eAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQlB,UAAU,OAAO,WAAW,MAAM;AAChC,WAAO,OAAO,OAAO,GAAG,IAAI;AAAA,EAC9B;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,sBAAsB,mBAAmB;AAC5D,aAAO,KAAK,qBAAqB,gBAAe;AAAA,IAClD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,aAAa;AAAA,MAC1C,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB,OAAO;AACL,SAAK,OAAO,SAAS,4BAA4B,mBAAmB;AAClE,aAAO,KAAK,qBAAqB,sBAAqB;AAAA,IACxD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,aAAa;AAAA,MACvB,SAAS,CAAC,aAAa;AAAA,IACzB,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB,CAAC,CAAC;AAAA,EACnD;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,aAAa;AAAA,MACvB,SAAS,CAAC,aAAa;AAAA,IACzB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;ACxDH,IAAM,YAAY,SAAU,OAAO;AACjC,SAAO,UAAU;AACnB;AACA,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,YAAY,eAAe;AACzB,SAAK,iBAAiB,IAAI,QAAQ;AAClC,SAAK,SAAS,iBAAiB,CAAC;AAAA,EAClC;AAAA,EACA,YAAY;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EACA,sBAAsB,eAAe;AACnC,WAAO,KAAK,OAAO,aAAa;AAAA,EAClC;AAAA,EACA,iCAAiC,eAAe;AAC9C,WAAO,KAAK,eAAe,KAAK,OAAO,OAAK,MAAM,aAAa,GAAG,MAAM,MAAS,CAAC;AAAA,EACpF;AAAA,EACA,IAAI,eAAe,OAAO;AACxB,SAAK,OAAO,aAAa,IAAI,kCACxB,KAAK,OAAO,aAAa,IACzB;AAEL,SAAK,eAAe,KAAK,aAAa;AAAA,EACxC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,mBAAmB;AAC/D,aAAO,KAAK,qBAAqB,mBAAqB,SAAS,YAAY,CAAC,CAAC;AAAA,IAC/E;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,kBAAiB;AAAA,MAC1B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,UAAU;AAAA,IACnB,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AACH,SAAS,aAAa;AACpB,SAAO,SAAS,gBAAgB,QAAQ,UAAU,oBAAoB;AACpE,UAAM,kBAAkB,6BAA6B,QAAQ;AAC7D,WAAO,eAAe,QAAQ,iBAAiB;AAAA,MAC7C,cAAc;AAAA,MACd,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AACD,WAAO;AAAA,MACL,MAAM;AACJ,cAAM,gBAAgB,oBAAoB,MAAM,mBAAmB,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK,eAAe;AAC1G,cAAM,kBAAkB,KAAK,wBAAwB,QAAQ,KAAK,KAAK;AACvE,cAAM,cAAc,KAAK,iBAAiB,sBAAsB,KAAK,cAAc,IAAI,QAAQ;AAC/F,YAAI,kBAAkB,UAAU,aAAa,GAAG;AAC9C,iBAAO;AAAA,QACT,OAAO;AACL,iBAAO,UAAU,WAAW,IAAI,cAAc;AAAA,QAChD;AAAA,MACF;AAAA,MACA,IAAI,OAAO;AACT,aAAK,wBAAwB,KAAK,yBAAyB,CAAC;AAC5D,aAAK,sBAAsB,QAAQ,KAAK,KAAK,sBAAsB,QAAQ,KAAK,KAAK;AACrF,YAAI,oBAAoB,KAAK;AAC3B,6BAAmB,IAAI,KAAK,IAAI,EAAE,KAAK;AAAA,QACzC,OAAO;AACL,eAAK,eAAe,IAAI;AAAA,QAC1B;AAAA,MACF;AAAA,MACA,cAAc;AAAA,MACd,YAAY;AAAA,IACd;AAAA,EACF;AACF;AACA,IAAM,aAAa,IAAI,eAAe,YAAY;", "names": []}