{"version": 3, "sources": ["../../../../../../node_modules/tds-ui/fesm2022/tds-ui-tinycolor.mjs", "../../../../../../node_modules/tds-ui/fesm2022/tds-ui-tag.mjs", "../../../../../../node_modules/tds-ui/fesm2022/tds-ui-core-no-animation.mjs", "../../../../../../node_modules/tds-ui/fesm2022/tds-ui-badges.mjs", "../../../../../../node_modules/tds-ui/fesm2022/tds-ui-tooltip.mjs", "../../../../../../node_modules/tds-ui/fesm2022/tds-ui-nav-bar.mjs", "../../../../../../node_modules/tds-ui/fesm2022/tds-ui-menu.mjs"], "sourcesContent": ["/**\n * Take input from [0, n] and return it as [0, 1]\n * @hidden\n */\nfunction bound01(n, max) {\n  if (isOnePointZero(n)) {\n    n = '100%';\n  }\n  const isPercent = isPercentage(n);\n  n = max === 360 ? n : Math.min(max, Math.max(0, parseFloat(n)));\n  // Automatically convert percentage into number\n  if (isPercent) {\n    n = parseInt(String(n * max), 10) / 100;\n  }\n  // Handle floating point rounding errors\n  if (Math.abs(n - max) < 0.000001) {\n    return 1;\n  }\n  // Convert into [0, 1] range if it isn't already\n  if (max === 360) {\n    // If n is a hue given in degrees,\n    // wrap around out-of-range values into [0, 360] range\n    // then convert into [0, 1].\n    n = (n < 0 ? n % max + max : n % max) / parseFloat(String(max));\n  } else {\n    // If n not a hue given in degrees\n    // Convert into [0, 1] range if it isn't already.\n    n = n % max / parseFloat(String(max));\n  }\n  return n;\n}\n/**\n * Force a number between 0 and 1\n * @hidden\n */\nfunction clamp01(val) {\n  return Math.min(1, Math.max(0, val));\n}\n/**\n * Need to handle 1.0 as 100%, since once it is a number, there is no difference between it and 1\n * <http://stackoverflow.com/questions/7422072/javascript-how-to-detect-number-as-a-decimal-including-1-0>\n * @hidden\n */\nfunction isOnePointZero(n) {\n  return typeof n === 'string' && n.indexOf('.') !== -1 && parseFloat(n) === 1;\n}\n/**\n * Check to see if string passed in is a percentage\n * @hidden\n */\nfunction isPercentage(n) {\n  return typeof n === 'string' && n.indexOf('%') !== -1;\n}\n/**\n * Return a valid alpha value [0,1] with all invalid values being set to 1\n * @hidden\n */\nfunction boundAlpha(a) {\n  a = parseFloat(a);\n  if (isNaN(a) || a < 0 || a > 1) {\n    a = 1;\n  }\n  return a;\n}\n/**\n * Replace a decimal with it's percentage value\n * @hidden\n */\nfunction convertToPercentage(n) {\n  if (typeof n == 'number' && n <= 1) {\n    return `${Number(n) * 100}%`;\n  }\n  return n;\n}\n/**\n * Force a hex value to have 2 characters\n * @hidden\n */\nfunction pad2(c) {\n  return c.length === 1 ? '0' + c : String(c);\n}\n\n// `rgbToHsl`, `rgbToHsv`, `hslToRgb`, `hsvToRgb` modified from:\n// <http://mjijackson.com/2008/02/rgb-to-hsl-and-rgb-to-hsv-color-model-conversion-algorithms-in-javascript>\n/**\n * Handle bounds / percentage checking to conform to CSS color spec\n * <http://www.w3.org/TR/css3-color/>\n * *Assumes:* r, g, b in [0, 255] or [0, 1]\n * *Returns:* { r, g, b } in [0, 255]\n */\nfunction rgbToRgb(r, g, b) {\n  return {\n    r: bound01(r, 255) * 255,\n    g: bound01(g, 255) * 255,\n    b: bound01(b, 255) * 255\n  };\n}\n/**\n * Converts an RGB color value to HSL.\n * *Assumes:* r, g, and b are contained in [0, 255] or [0, 1]\n * *Returns:* { h, s, l } in [0,1]\n */\nfunction rgbToHsl(r, g, b) {\n  r = bound01(r, 255);\n  g = bound01(g, 255);\n  b = bound01(b, 255);\n  const max = Math.max(r, g, b);\n  const min = Math.min(r, g, b);\n  let h = 0;\n  let s = 0;\n  const l = (max + min) / 2;\n  if (max === min) {\n    s = 0;\n    h = 0; // achromatic\n  } else {\n    const d = max - min;\n    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);\n    switch (max) {\n      case r:\n        h = (g - b) / d + (g < b ? 6 : 0);\n        break;\n      case g:\n        h = (b - r) / d + 2;\n        break;\n      case b:\n        h = (r - g) / d + 4;\n        break;\n      default:\n        break;\n    }\n    h /= 6;\n  }\n  return {\n    h,\n    s,\n    l\n  };\n}\nfunction hue2rgb(p, q, t) {\n  if (t < 0) {\n    t += 1;\n  }\n  if (t > 1) {\n    t -= 1;\n  }\n  if (t < 1 / 6) {\n    return p + (q - p) * (6 * t);\n  }\n  if (t < 1 / 2) {\n    return q;\n  }\n  if (t < 2 / 3) {\n    return p + (q - p) * (2 / 3 - t) * 6;\n  }\n  return p;\n}\n/**\n * Converts an HSL color value to RGB.\n *\n * *Assumes:* h is contained in [0, 1] or [0, 360] and s and l are contained [0, 1] or [0, 100]\n * *Returns:* { r, g, b } in the set [0, 255]\n */\nfunction hslToRgb(h, s, l) {\n  let r;\n  let g;\n  let b;\n  h = bound01(h, 360);\n  s = bound01(s, 100);\n  l = bound01(l, 100);\n  if (s === 0) {\n    // achromatic\n    g = l;\n    b = l;\n    r = l;\n  } else {\n    const q = l < 0.5 ? l * (1 + s) : l + s - l * s;\n    const p = 2 * l - q;\n    r = hue2rgb(p, q, h + 1 / 3);\n    g = hue2rgb(p, q, h);\n    b = hue2rgb(p, q, h - 1 / 3);\n  }\n  return {\n    r: r * 255,\n    g: g * 255,\n    b: b * 255\n  };\n}\n/**\n * Converts an RGB color value to HSV\n *\n * *Assumes:* r, g, and b are contained in the set [0, 255] or [0, 1]\n * *Returns:* { h, s, v } in [0,1]\n */\nfunction rgbToHsv(r, g, b) {\n  r = bound01(r, 255);\n  g = bound01(g, 255);\n  b = bound01(b, 255);\n  const max = Math.max(r, g, b);\n  const min = Math.min(r, g, b);\n  let h = 0;\n  const v = max;\n  const d = max - min;\n  const s = max === 0 ? 0 : d / max;\n  if (max === min) {\n    h = 0; // achromatic\n  } else {\n    switch (max) {\n      case r:\n        h = (g - b) / d + (g < b ? 6 : 0);\n        break;\n      case g:\n        h = (b - r) / d + 2;\n        break;\n      case b:\n        h = (r - g) / d + 4;\n        break;\n      default:\n        break;\n    }\n    h /= 6;\n  }\n  return {\n    h,\n    s,\n    v\n  };\n}\n/**\n * Converts an HSV color value to RGB.\n *\n * *Assumes:* h is contained in [0, 1] or [0, 360] and s and v are contained in [0, 1] or [0, 100]\n * *Returns:* { r, g, b } in the set [0, 255]\n */\nfunction hsvToRgb(h, s, v) {\n  h = bound01(h, 360) * 6;\n  s = bound01(s, 100);\n  v = bound01(v, 100);\n  const i = Math.floor(h);\n  const f = h - i;\n  const p = v * (1 - s);\n  const q = v * (1 - f * s);\n  const t = v * (1 - (1 - f) * s);\n  const mod = i % 6;\n  const r = [v, q, p, p, t, v][mod];\n  const g = [t, v, v, q, p, p][mod];\n  const b = [p, p, t, v, v, q][mod];\n  return {\n    r: r * 255,\n    g: g * 255,\n    b: b * 255\n  };\n}\n/**\n * Converts an RGB color to hex\n *\n * Assumes r, g, and b are contained in the set [0, 255]\n * Returns a 3 or 6 character hex\n */\nfunction rgbToHex(r, g, b, allow3Char) {\n  const hex = [pad2(Math.round(r).toString(16)), pad2(Math.round(g).toString(16)), pad2(Math.round(b).toString(16))];\n  // Return a 3 character hex if possible\n  if (allow3Char && hex[0].startsWith(hex[0].charAt(1)) && hex[1].startsWith(hex[1].charAt(1)) && hex[2].startsWith(hex[2].charAt(1))) {\n    return hex[0].charAt(0) + hex[1].charAt(0) + hex[2].charAt(0);\n  }\n  return hex.join('');\n}\n/**\n * Converts an RGBA color plus alpha transparency to hex\n *\n * Assumes r, g, b are contained in the set [0, 255] and\n * a in [0, 1]. Returns a 4 or 8 character rgba hex\n */\n// eslint-disable-next-line max-params\nfunction rgbaToHex(r, g, b, a, allow4Char) {\n  const hex = [pad2(Math.round(r).toString(16)), pad2(Math.round(g).toString(16)), pad2(Math.round(b).toString(16)), pad2(convertDecimalToHex(a))];\n  // Return a 4 character hex if possible\n  if (allow4Char && hex[0].startsWith(hex[0].charAt(1)) && hex[1].startsWith(hex[1].charAt(1)) && hex[2].startsWith(hex[2].charAt(1)) && hex[3].startsWith(hex[3].charAt(1))) {\n    return hex[0].charAt(0) + hex[1].charAt(0) + hex[2].charAt(0) + hex[3].charAt(0);\n  }\n  return hex.join('');\n}\n/**\n * Converts an RGBA color to an ARGB Hex8 string\n * Rarely used, but required for \"toFilter()\"\n */\nfunction rgbaToArgbHex(r, g, b, a) {\n  const hex = [pad2(convertDecimalToHex(a)), pad2(Math.round(r).toString(16)), pad2(Math.round(g).toString(16)), pad2(Math.round(b).toString(16))];\n  return hex.join('');\n}\n/** Converts a decimal to a hex value */\nfunction convertDecimalToHex(d) {\n  return Math.round(parseFloat(d) * 255).toString(16);\n}\n/** Converts a hex value to a decimal */\nfunction convertHexToDecimal(h) {\n  return parseIntFromHex(h) / 255;\n}\n/** Parse a base-16 hex value into a base-10 integer */\nfunction parseIntFromHex(val) {\n  return parseInt(val, 16);\n}\nfunction numberInputToObject(color) {\n  return {\n    r: color >> 16,\n    g: (color & 0xff00) >> 8,\n    b: color & 0xff\n  };\n}\n\n// https://github.com/bahamas10/css-color-names/blob/master/css-color-names.json\n/**\n * @hidden\n */\nconst names = {\n  aliceblue: '#f0f8ff',\n  antiquewhite: '#faebd7',\n  aqua: '#00ffff',\n  aquamarine: '#7fffd4',\n  azure: '#f0ffff',\n  beige: '#f5f5dc',\n  bisque: '#ffe4c4',\n  black: '#000000',\n  blanchedalmond: '#ffebcd',\n  blue: '#0000ff',\n  blueviolet: '#8a2be2',\n  brown: '#a52a2a',\n  burlywood: '#deb887',\n  cadetblue: '#5f9ea0',\n  chartreuse: '#7fff00',\n  chocolate: '#d2691e',\n  coral: '#ff7f50',\n  cornflowerblue: '#6495ed',\n  cornsilk: '#fff8dc',\n  crimson: '#dc143c',\n  cyan: '#00ffff',\n  darkblue: '#00008b',\n  darkcyan: '#008b8b',\n  darkgoldenrod: '#b8860b',\n  darkgray: '#a9a9a9',\n  darkgreen: '#006400',\n  darkgrey: '#a9a9a9',\n  darkkhaki: '#bdb76b',\n  darkmagenta: '#8b008b',\n  darkolivegreen: '#556b2f',\n  darkorange: '#ff8c00',\n  darkorchid: '#9932cc',\n  darkred: '#8b0000',\n  darksalmon: '#e9967a',\n  darkseagreen: '#8fbc8f',\n  darkslateblue: '#483d8b',\n  darkslategray: '#2f4f4f',\n  darkslategrey: '#2f4f4f',\n  darkturquoise: '#00ced1',\n  darkviolet: '#9400d3',\n  deeppink: '#ff1493',\n  deepskyblue: '#00bfff',\n  dimgray: '#696969',\n  dimgrey: '#696969',\n  dodgerblue: '#1e90ff',\n  firebrick: '#b22222',\n  floralwhite: '#fffaf0',\n  forestgreen: '#228b22',\n  fuchsia: '#ff00ff',\n  gainsboro: '#dcdcdc',\n  ghostwhite: '#f8f8ff',\n  goldenrod: '#daa520',\n  gold: '#ffd700',\n  gray: '#808080',\n  green: '#008000',\n  greenyellow: '#adff2f',\n  grey: '#808080',\n  honeydew: '#f0fff0',\n  hotpink: '#ff69b4',\n  indianred: '#cd5c5c',\n  indigo: '#4b0082',\n  ivory: '#fffff0',\n  khaki: '#f0e68c',\n  lavenderblush: '#fff0f5',\n  lavender: '#e6e6fa',\n  lawngreen: '#7cfc00',\n  lemonchiffon: '#fffacd',\n  lightblue: '#add8e6',\n  lightcoral: '#f08080',\n  lightcyan: '#e0ffff',\n  lightgoldenrodyellow: '#fafad2',\n  lightgray: '#d3d3d3',\n  lightgreen: '#90ee90',\n  lightgrey: '#d3d3d3',\n  lightpink: '#ffb6c1',\n  lightsalmon: '#ffa07a',\n  lightseagreen: '#20b2aa',\n  lightskyblue: '#87cefa',\n  lightslategray: '#778899',\n  lightslategrey: '#778899',\n  lightsteelblue: '#b0c4de',\n  lightyellow: '#ffffe0',\n  lime: '#00ff00',\n  limegreen: '#32cd32',\n  linen: '#faf0e6',\n  magenta: '#ff00ff',\n  maroon: '#800000',\n  mediumaquamarine: '#66cdaa',\n  mediumblue: '#0000cd',\n  mediumorchid: '#ba55d3',\n  mediumpurple: '#9370db',\n  mediumseagreen: '#3cb371',\n  mediumslateblue: '#7b68ee',\n  mediumspringgreen: '#00fa9a',\n  mediumturquoise: '#48d1cc',\n  mediumvioletred: '#c71585',\n  midnightblue: '#191970',\n  mintcream: '#f5fffa',\n  mistyrose: '#ffe4e1',\n  moccasin: '#ffe4b5',\n  navajowhite: '#ffdead',\n  navy: '#000080',\n  oldlace: '#fdf5e6',\n  olive: '#808000',\n  olivedrab: '#6b8e23',\n  orange: '#ffa500',\n  orangered: '#ff4500',\n  orchid: '#da70d6',\n  palegoldenrod: '#eee8aa',\n  palegreen: '#98fb98',\n  paleturquoise: '#afeeee',\n  palevioletred: '#db7093',\n  papayawhip: '#ffefd5',\n  peachpuff: '#ffdab9',\n  peru: '#cd853f',\n  pink: '#ffc0cb',\n  plum: '#dda0dd',\n  powderblue: '#b0e0e6',\n  purple: '#800080',\n  rebeccapurple: '#663399',\n  red: '#ff0000',\n  rosybrown: '#bc8f8f',\n  royalblue: '#4169e1',\n  saddlebrown: '#8b4513',\n  salmon: '#fa8072',\n  sandybrown: '#f4a460',\n  seagreen: '#2e8b57',\n  seashell: '#fff5ee',\n  sienna: '#a0522d',\n  silver: '#c0c0c0',\n  skyblue: '#87ceeb',\n  slateblue: '#6a5acd',\n  slategray: '#708090',\n  slategrey: '#708090',\n  snow: '#fffafa',\n  springgreen: '#00ff7f',\n  steelblue: '#4682b4',\n  tan: '#d2b48c',\n  teal: '#008080',\n  thistle: '#d8bfd8',\n  tomato: '#ff6347',\n  turquoise: '#40e0d0',\n  violet: '#ee82ee',\n  wheat: '#f5deb3',\n  white: '#ffffff',\n  whitesmoke: '#f5f5f5',\n  yellow: '#ffff00',\n  yellowgreen: '#9acd32'\n};\n\n/**\n * Given a string or object, convert that input to RGB\n *\n * Possible string inputs:\n * ```\n * \"red\"\n * \"#f00\" or \"f00\"\n * \"#ff0000\" or \"ff0000\"\n * \"#ff000000\" or \"ff000000\"\n * \"rgb 255 0 0\" or \"rgb (255, 0, 0)\"\n * \"rgb 1.0 0 0\" or \"rgb (1, 0, 0)\"\n * \"rgba (255, 0, 0, 1)\" or \"rgba 255, 0, 0, 1\"\n * \"rgba (1.0, 0, 0, 1)\" or \"rgba 1.0, 0, 0, 1\"\n * \"hsl(0, 100%, 50%)\" or \"hsl 0 100% 50%\"\n * \"hsla(0, 100%, 50%, 1)\" or \"hsla 0 100% 50%, 1\"\n * \"hsv(0, 100%, 100%)\" or \"hsv 0 100% 100%\"\n * ```\n */\nfunction inputToRGB(color) {\n  let rgb = {\n    r: 0,\n    g: 0,\n    b: 0\n  };\n  let a = 1;\n  let s = null;\n  let v = null;\n  let l = null;\n  let ok = false;\n  let format = false;\n  if (typeof color === 'string') {\n    color = stringInputToObject(color);\n  }\n  if (typeof color === 'object') {\n    if (isValidCSSUnit(color.r) && isValidCSSUnit(color.g) && isValidCSSUnit(color.b)) {\n      rgb = rgbToRgb(color.r, color.g, color.b);\n      ok = true;\n      format = String(color.r).substr(-1) === '%' ? 'prgb' : 'rgb';\n    } else if (isValidCSSUnit(color.h) && isValidCSSUnit(color.s) && isValidCSSUnit(color.v)) {\n      s = convertToPercentage(color.s);\n      v = convertToPercentage(color.v);\n      rgb = hsvToRgb(color.h, s, v);\n      ok = true;\n      format = 'hsv';\n    } else if (isValidCSSUnit(color.h) && isValidCSSUnit(color.s) && isValidCSSUnit(color.l)) {\n      s = convertToPercentage(color.s);\n      l = convertToPercentage(color.l);\n      rgb = hslToRgb(color.h, s, l);\n      ok = true;\n      format = 'hsl';\n    }\n    if (Object.prototype.hasOwnProperty.call(color, 'a')) {\n      a = color.a;\n    }\n  }\n  a = boundAlpha(a);\n  return {\n    ok,\n    format: color.format || format,\n    r: Math.min(255, Math.max(rgb.r, 0)),\n    g: Math.min(255, Math.max(rgb.g, 0)),\n    b: Math.min(255, Math.max(rgb.b, 0)),\n    a\n  };\n}\n// <http://www.w3.org/TR/css3-values/#integers>\nconst CSS_INTEGER = '[-\\\\+]?\\\\d+%?';\n// <http://www.w3.org/TR/css3-values/#number-value>\nconst CSS_NUMBER = '[-\\\\+]?\\\\d*\\\\.\\\\d+%?';\n// Allow positive/negative integer/number.  Don't capture the either/or, just the entire outcome.\nconst CSS_UNIT = `(?:${CSS_NUMBER})|(?:${CSS_INTEGER})`;\n// Actual matching.\n// Parentheses and commas are optional, but not required.\n// Whitespace can take the place of commas or opening paren\nconst PERMISSIVE_MATCH3 = `[\\\\s|\\\\(]+(${CSS_UNIT})[,|\\\\s]+(${CSS_UNIT})[,|\\\\s]+(${CSS_UNIT})\\\\s*\\\\)?`;\nconst PERMISSIVE_MATCH4 = `[\\\\s|\\\\(]+(${CSS_UNIT})[,|\\\\s]+(${CSS_UNIT})[,|\\\\s]+(${CSS_UNIT})[,|\\\\s]+(${CSS_UNIT})\\\\s*\\\\)?`;\nconst matchers = {\n  CSS_UNIT: new RegExp(CSS_UNIT),\n  rgb: new RegExp('rgb' + PERMISSIVE_MATCH3),\n  rgba: new RegExp('rgba' + PERMISSIVE_MATCH4),\n  hsl: new RegExp('hsl' + PERMISSIVE_MATCH3),\n  hsla: new RegExp('hsla' + PERMISSIVE_MATCH4),\n  hsv: new RegExp('hsv' + PERMISSIVE_MATCH3),\n  hsva: new RegExp('hsva' + PERMISSIVE_MATCH4),\n  hex3: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,\n  hex6: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,\n  hex4: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,\n  hex8: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/\n};\n/**\n * Permissive string parsing.  Take in a number of formats, and output an object\n * based on detected format.  Returns `{ r, g, b }` or `{ h, s, l }` or `{ h, s, v}`\n */\nfunction stringInputToObject(color) {\n  color = color.trim().toLowerCase();\n  if (color.length === 0) {\n    return false;\n  }\n  let named = false;\n  if (names[color]) {\n    color = names[color];\n    named = true;\n  } else if (color === 'transparent') {\n    return {\n      r: 0,\n      g: 0,\n      b: 0,\n      a: 0,\n      format: 'name'\n    };\n  }\n  // Try to match string input using regular expressions.\n  // Keep most of the number bounding out of this function - don't worry about [0,1] or [0,100] or [0,360]\n  // Just return an object and let the conversion functions handle that.\n  // This way the result will be the same whether the tinycolor is initialized with string or object.\n  let match = matchers.rgb.exec(color);\n  if (match) {\n    return {\n      r: match[1],\n      g: match[2],\n      b: match[3]\n    };\n  }\n  match = matchers.rgba.exec(color);\n  if (match) {\n    return {\n      r: match[1],\n      g: match[2],\n      b: match[3],\n      a: match[4]\n    };\n  }\n  match = matchers.hsl.exec(color);\n  if (match) {\n    return {\n      h: match[1],\n      s: match[2],\n      l: match[3]\n    };\n  }\n  match = matchers.hsla.exec(color);\n  if (match) {\n    return {\n      h: match[1],\n      s: match[2],\n      l: match[3],\n      a: match[4]\n    };\n  }\n  match = matchers.hsv.exec(color);\n  if (match) {\n    return {\n      h: match[1],\n      s: match[2],\n      v: match[3]\n    };\n  }\n  match = matchers.hsva.exec(color);\n  if (match) {\n    return {\n      h: match[1],\n      s: match[2],\n      v: match[3],\n      a: match[4]\n    };\n  }\n  match = matchers.hex8.exec(color);\n  if (match) {\n    return {\n      r: parseIntFromHex(match[1]),\n      g: parseIntFromHex(match[2]),\n      b: parseIntFromHex(match[3]),\n      a: convertHexToDecimal(match[4]),\n      format: named ? 'name' : 'hex8'\n    };\n  }\n  match = matchers.hex6.exec(color);\n  if (match) {\n    return {\n      r: parseIntFromHex(match[1]),\n      g: parseIntFromHex(match[2]),\n      b: parseIntFromHex(match[3]),\n      format: named ? 'name' : 'hex'\n    };\n  }\n  match = matchers.hex4.exec(color);\n  if (match) {\n    return {\n      r: parseIntFromHex(match[1] + match[1]),\n      g: parseIntFromHex(match[2] + match[2]),\n      b: parseIntFromHex(match[3] + match[3]),\n      a: convertHexToDecimal(match[4] + match[4]),\n      format: named ? 'name' : 'hex8'\n    };\n  }\n  match = matchers.hex3.exec(color);\n  if (match) {\n    return {\n      r: parseIntFromHex(match[1] + match[1]),\n      g: parseIntFromHex(match[2] + match[2]),\n      b: parseIntFromHex(match[3] + match[3]),\n      format: named ? 'name' : 'hex'\n    };\n  }\n  return false;\n}\n/**\n * Check to see if it looks like a CSS unit\n * (see `matchers` above for definition).\n */\nfunction isValidCSSUnit(color) {\n  return Boolean(matchers.CSS_UNIT.exec(String(color)));\n}\nconst getRoundNumber = value => Math.round(Number(value || 0));\nconst convertHsb2Hsv = color => {\n  if (color && typeof color === 'object' && 'h' in color && 'b' in color) {\n    const {\n      b,\n      ...resets\n    } = color;\n    return {\n      ...resets,\n      v: b\n    };\n  }\n  if (typeof color === 'string' && /hsb/.test(color)) {\n    return color.replace(/hsb/, 'hsv');\n  }\n  return color;\n};\nclass TinyColor {\n  constructor(color = '', opts = {}) {\n    color = convertHsb2Hsv(color);\n    // If input is already a tinycolor, return itself\n    if (color instanceof TinyColor) {\n      // eslint-disable-next-line no-constructor-return\n      return color;\n    }\n    if (typeof color === 'number') {\n      color = numberInputToObject(color);\n    }\n    this.originalInput = color;\n    const rgb = inputToRGB(color);\n    this.originalInput = color;\n    this.r = rgb.r;\n    this.g = rgb.g;\n    this.b = rgb.b;\n    this.a = rgb.a;\n    this.roundA = Math.round(100 * this.a) / 100;\n    this.format = opts.format ?? rgb.format;\n    this.gradientType = opts.gradientType;\n    // Don't let the range of [0,255] come back in [0,1].\n    // Potentially lose a little bit of precision here, but will fix issues where\n    // .5 gets interpreted as half of the total, instead of half of 1\n    // If it was supposed to be 128, this was already taken care of by `inputToRgb`\n    if (this.r < 1) {\n      this.r = Math.round(this.r);\n    }\n    if (this.g < 1) {\n      this.g = Math.round(this.g);\n    }\n    if (this.b < 1) {\n      this.b = Math.round(this.b);\n    }\n    this.isValid = rgb.ok;\n  }\n  isDark() {\n    return this.getBrightness() < 128;\n  }\n  isLight() {\n    return !this.isDark();\n  }\n  /**\n   * Returns the perceived brightness of the color, from 0-255.\n   */\n  getBrightness() {\n    // http://www.w3.org/TR/AERT#color-contrast\n    const rgb = this.toRgb();\n    return (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;\n  }\n  /**\n   * Returns the perceived luminance of a color, from 0-1.\n   */\n  getLuminance() {\n    // http://www.w3.org/TR/2008/REC-WCAG20-20081211/#relativeluminancedef\n    const rgb = this.toRgb();\n    let R;\n    let G;\n    let B;\n    const RsRGB = rgb.r / 255;\n    const GsRGB = rgb.g / 255;\n    const BsRGB = rgb.b / 255;\n    if (RsRGB <= 0.03928) {\n      R = RsRGB / 12.92;\n    } else {\n      // eslint-disable-next-line prefer-exponentiation-operator\n      R = Math.pow((RsRGB + 0.055) / 1.055, 2.4);\n    }\n    if (GsRGB <= 0.03928) {\n      G = GsRGB / 12.92;\n    } else {\n      // eslint-disable-next-line prefer-exponentiation-operator\n      G = Math.pow((GsRGB + 0.055) / 1.055, 2.4);\n    }\n    if (BsRGB <= 0.03928) {\n      B = BsRGB / 12.92;\n    } else {\n      // eslint-disable-next-line prefer-exponentiation-operator\n      B = Math.pow((BsRGB + 0.055) / 1.055, 2.4);\n    }\n    return 0.2126 * R + 0.7152 * G + 0.0722 * B;\n  }\n  /**\n   * Returns the alpha value of a color, from 0-1.\n   */\n  getAlpha() {\n    return this.a;\n  }\n  /**\n   * Sets the alpha value on the current color.\n   *\n   * @param alpha - The new alpha value. The accepted range is 0-1.\n   */\n  setAlpha(alpha) {\n    this.a = boundAlpha(alpha);\n    this.roundA = Math.round(100 * this.a) / 100;\n    return this;\n  }\n  /**\n   * Returns the object as a HSVA object.\n   */\n  toHsv() {\n    const hsv = rgbToHsv(this.r, this.g, this.b);\n    return {\n      h: hsv.h * 360,\n      s: hsv.s,\n      v: hsv.v,\n      a: this.a\n    };\n  }\n  /**\n   * Returns the hsva values interpolated into a string with the following format:\n   * \"hsva(xxx, xxx, xxx, xx)\".\n   */\n  toHsvString() {\n    const hsv = rgbToHsv(this.r, this.g, this.b);\n    const h = Math.round(hsv.h * 360);\n    const s = Math.round(hsv.s * 100);\n    const v = Math.round(hsv.v * 100);\n    return this.a === 1 ? `hsv(${h}, ${s}%, ${v}%)` : `hsva(${h}, ${s}%, ${v}%, ${this.roundA})`;\n  }\n  /**\n   * Returns the object as a HSLA object.\n   */\n  toHsl() {\n    const hsl = rgbToHsl(this.r, this.g, this.b);\n    return {\n      h: hsl.h * 360,\n      s: hsl.s,\n      l: hsl.l,\n      a: this.a\n    };\n  }\n  /**\n   * Returns the hsla values interpolated into a string with the following format:\n   * \"hsla(xxx, xxx, xxx, xx)\".\n   */\n  toHslString() {\n    const hsl = rgbToHsl(this.r, this.g, this.b);\n    const h = Math.round(hsl.h * 360);\n    const s = Math.round(hsl.s * 100);\n    const l = Math.round(hsl.l * 100);\n    return this.a === 1 ? `hsl(${h}, ${s}%, ${l}%)` : `hsla(${h}, ${s}%, ${l}%, ${this.roundA})`;\n  }\n  /**\n   * Returns the hex value of the color.\n   * @param allow3Char will shorten hex value to 3 char if possible\n   */\n  toHex(allow3Char = false) {\n    return rgbToHex(this.r, this.g, this.b, allow3Char);\n  }\n  /**\n   * Returns the hex value of the color -with a # appened.\n   * @param allow3Char will shorten hex value to 3 char if possible\n   */\n  toHexString(allow3Char = false) {\n    return '#' + this.toHex(allow3Char);\n  }\n  /**\n   * Returns the hex 8 value of the color.\n   * @param allow4Char will shorten hex value to 4 char if possible\n   */\n  toHex8(allow4Char = false) {\n    return rgbaToHex(this.r, this.g, this.b, this.a, allow4Char);\n  }\n  /**\n   * Returns the hex 8 value of the color -with a # appened.\n   * @param allow4Char will shorten hex value to 4 char if possible\n   */\n  toHex8String(allow4Char = false) {\n    return '#' + this.toHex8(allow4Char);\n  }\n  /**\n   * Returns the object as a RGBA object.\n   */\n  toRgb() {\n    return {\n      r: Math.round(this.r),\n      g: Math.round(this.g),\n      b: Math.round(this.b),\n      a: this.a\n    };\n  }\n  /**\n   * Returns the RGBA values interpolated into a string with the following format:\n   * \"RGBA(xxx, xxx, xxx, xx)\".\n   */\n  toRgbString() {\n    const r = Math.round(this.r);\n    const g = Math.round(this.g);\n    const b = Math.round(this.b);\n    return this.a === 1 ? `rgb(${r}, ${g}, ${b})` : `rgba(${r}, ${g}, ${b}, ${this.roundA})`;\n  }\n  /**\n   * Returns the object as a RGBA object.\n   */\n  toPercentageRgb() {\n    const fmt = x => `${Math.round(bound01(x, 255) * 100)}%`;\n    return {\n      r: fmt(this.r),\n      g: fmt(this.g),\n      b: fmt(this.b),\n      a: this.a\n    };\n  }\n  /**\n   * Returns the RGBA relative values interpolated into a string\n   */\n  toPercentageRgbString() {\n    const rnd = x => Math.round(bound01(x, 255) * 100);\n    return this.a === 1 ? `rgb(${rnd(this.r)}%, ${rnd(this.g)}%, ${rnd(this.b)}%)` : `rgba(${rnd(this.r)}%, ${rnd(this.g)}%, ${rnd(this.b)}%, ${this.roundA})`;\n  }\n  /**\n   * The 'real' name of the color -if there is one.\n   */\n  toName() {\n    if (this.a === 0) {\n      return 'transparent';\n    }\n    if (this.a < 1) {\n      return false;\n    }\n    const hex = '#' + rgbToHex(this.r, this.g, this.b, false);\n    for (const [key, value] of Object.entries(names)) {\n      if (hex === value) {\n        return key;\n      }\n    }\n    return false;\n  }\n  toString(format) {\n    const formatSet = Boolean(format);\n    format = format ?? this.format;\n    let formattedString = false;\n    const hasAlpha = this.a < 1 && this.a >= 0;\n    const needsAlphaFormat = !formatSet && hasAlpha && (format.startsWith('hex') || format === 'name');\n    if (needsAlphaFormat) {\n      // Special case for \"transparent\", all other non-alpha formats\n      // will return rgba when there is transparency.\n      if (format === 'name' && this.a === 0) {\n        return this.toName();\n      }\n      return this.toRgbString();\n    }\n    if (format === 'rgb') {\n      formattedString = this.toRgbString();\n    }\n    if (format === 'prgb') {\n      formattedString = this.toPercentageRgbString();\n    }\n    if (format === 'hex' || format === 'hex6') {\n      formattedString = this.toHexString();\n    }\n    if (format === 'hex3') {\n      formattedString = this.toHexString(true);\n    }\n    if (format === 'hex4') {\n      formattedString = this.toHex8String(true);\n    }\n    if (format === 'hex8') {\n      formattedString = this.toHex8String();\n    }\n    if (format === 'name') {\n      formattedString = this.toName();\n    }\n    if (format === 'hsl') {\n      formattedString = this.toHslString();\n    }\n    if (format === 'hsv') {\n      formattedString = this.toHsvString();\n    }\n    return formattedString || this.toHexString();\n  }\n  toNumber() {\n    return (Math.round(this.r) << 16) + (Math.round(this.g) << 8) + Math.round(this.b);\n  }\n  clone() {\n    return new TinyColor(this.toString());\n  }\n  /**\n   * Lighten the color a given amount. Providing 100 will always return white.\n   * @param amount - valid between 1-100\n   */\n  lighten(amount = 10) {\n    const hsl = this.toHsl();\n    hsl.l += amount / 100;\n    hsl.l = clamp01(hsl.l);\n    return new TinyColor(hsl);\n  }\n  /**\n   * Brighten the color a given amount, from 0 to 100.\n   * @param amount - valid between 1-100\n   */\n  brighten(amount = 10) {\n    const rgb = this.toRgb();\n    rgb.r = Math.max(0, Math.min(255, rgb.r - Math.round(255 * -(amount / 100))));\n    rgb.g = Math.max(0, Math.min(255, rgb.g - Math.round(255 * -(amount / 100))));\n    rgb.b = Math.max(0, Math.min(255, rgb.b - Math.round(255 * -(amount / 100))));\n    return new TinyColor(rgb);\n  }\n  /**\n   * Darken the color a given amount, from 0 to 100.\n   * Providing 100 will always return black.\n   * @param amount - valid between 1-100\n   */\n  darken(amount = 10) {\n    const hsl = this.toHsl();\n    hsl.l -= amount / 100;\n    hsl.l = clamp01(hsl.l);\n    return new TinyColor(hsl);\n  }\n  /**\n   * Mix the color with pure white, from 0 to 100.\n   * Providing 0 will do nothing, providing 100 will always return white.\n   * @param amount - valid between 1-100\n   */\n  tint(amount = 10) {\n    return this.mix('white', amount);\n  }\n  /**\n   * Mix the color with pure black, from 0 to 100.\n   * Providing 0 will do nothing, providing 100 will always return black.\n   * @param amount - valid between 1-100\n   */\n  shade(amount = 10) {\n    return this.mix('black', amount);\n  }\n  /**\n   * Desaturate the color a given amount, from 0 to 100.\n   * Providing 100 will is the same as calling greyscale\n   * @param amount - valid between 1-100\n   */\n  desaturate(amount = 10) {\n    const hsl = this.toHsl();\n    hsl.s -= amount / 100;\n    hsl.s = clamp01(hsl.s);\n    return new TinyColor(hsl);\n  }\n  /**\n   * Saturate the color a given amount, from 0 to 100.\n   * @param amount - valid between 1-100\n   */\n  saturate(amount = 10) {\n    const hsl = this.toHsl();\n    hsl.s += amount / 100;\n    hsl.s = clamp01(hsl.s);\n    return new TinyColor(hsl);\n  }\n  /**\n   * Completely desaturates a color into greyscale.\n   * Same as calling `desaturate(100)`\n   */\n  greyscale() {\n    return this.desaturate(100);\n  }\n  /**\n   * Spin takes a positive or negative amount within [-360, 360] indicating the change of hue.\n   * Values outside of this range will be wrapped into this range.\n   */\n  spin(amount) {\n    const hsl = this.toHsl();\n    const hue = (hsl.h + amount) % 360;\n    hsl.h = hue < 0 ? 360 + hue : hue;\n    return new TinyColor(hsl);\n  }\n  /**\n   * Mix the current color a given amount with another color, from 0 to 100.\n   * 0 means no mixing (return current color).\n   */\n  mix(color, amount = 50) {\n    const rgb1 = this.toRgb();\n    const rgb2 = new TinyColor(color).toRgb();\n    const p = amount / 100;\n    const rgba = {\n      r: (rgb2.r - rgb1.r) * p + rgb1.r,\n      g: (rgb2.g - rgb1.g) * p + rgb1.g,\n      b: (rgb2.b - rgb1.b) * p + rgb1.b,\n      a: (rgb2.a - rgb1.a) * p + rgb1.a\n    };\n    return new TinyColor(rgba);\n  }\n  analogous(results = 6, slices = 30) {\n    const hsl = this.toHsl();\n    const part = 360 / slices;\n    const ret = [this];\n    for (hsl.h = (hsl.h - (part * results >> 1) + 720) % 360; --results;) {\n      hsl.h = (hsl.h + part) % 360;\n      ret.push(new TinyColor(hsl));\n    }\n    return ret;\n  }\n  /**\n   * taken from https://github.com/infusion/jQuery-xcolor/blob/master/jquery.xcolor.js\n   */\n  complement() {\n    const hsl = this.toHsl();\n    hsl.h = (hsl.h + 180) % 360;\n    return new TinyColor(hsl);\n  }\n  monochromatic(results = 6) {\n    const hsv = this.toHsv();\n    const {\n      h\n    } = hsv;\n    const {\n      s\n    } = hsv;\n    let {\n      v\n    } = hsv;\n    const res = [];\n    const modification = 1 / results;\n    while (results--) {\n      res.push(new TinyColor({\n        h,\n        s,\n        v\n      }));\n      v = (v + modification) % 1;\n    }\n    return res;\n  }\n  splitcomplement() {\n    const hsl = this.toHsl();\n    const {\n      h\n    } = hsl;\n    return [this, new TinyColor({\n      h: (h + 72) % 360,\n      s: hsl.s,\n      l: hsl.l\n    }), new TinyColor({\n      h: (h + 216) % 360,\n      s: hsl.s,\n      l: hsl.l\n    })];\n  }\n  /**\n   * Compute how the color would appear on a background\n   */\n  onBackground(background) {\n    const fg = this.toRgb();\n    const bg = new TinyColor(background).toRgb();\n    return new TinyColor({\n      r: bg.r + (fg.r - bg.r) * fg.a,\n      g: bg.g + (fg.g - bg.g) * fg.a,\n      b: bg.b + (fg.b - bg.b) * fg.a\n    });\n  }\n  /**\n   * Alias for `polyad(3)`\n   */\n  triad() {\n    return this.polyad(3);\n  }\n  /**\n   * Alias for `polyad(4)`\n   */\n  tetrad() {\n    return this.polyad(4);\n  }\n  /**\n   * Get polyad colors, like (for 1, 2, 3, 4, 5, 6, 7, 8, etc...)\n   * monad, dyad, triad, tetrad, pentad, hexad, heptad, octad, etc...\n   */\n  polyad(n) {\n    const hsl = this.toHsl();\n    const {\n      h\n    } = hsl;\n    const result = [this];\n    const increment = 360 / n;\n    for (let i = 1; i < n; i++) {\n      result.push(new TinyColor({\n        h: (h + i * increment) % 360,\n        s: hsl.s,\n        l: hsl.l\n      }));\n    }\n    return result;\n  }\n  /**\n   * compare color vs current color\n   */\n  equals(color) {\n    return this.toRgbString() === new TinyColor(color).toRgbString();\n  }\n  /** Set or get custom data */\n  setCustomData(data) {\n    this.customData = data;\n  }\n  getCustomData() {\n    return this.customData;\n  }\n  toHsbString() {\n    const hsb = this.toHsb();\n    const saturation = getRoundNumber(hsb.s * 100);\n    const lightness = getRoundNumber(hsb.b * 100);\n    const hue = getRoundNumber(hsb.h);\n    const alpha = hsb.a;\n    const hsbString = `hsb(${hue}, ${saturation}%, ${lightness}%)`;\n    const hsbaString = `hsba(${hue}, ${saturation}%, ${lightness}%, ${alpha.toFixed(alpha === 0 ? 0 : 2)})`;\n    return alpha === 1 ? hsbString : hsbaString;\n  }\n  toHsb() {\n    let hsv = this.toHsv();\n    if (typeof this.originalInput === 'object' && this.originalInput) {\n      if ('h' in this.originalInput) {\n        hsv = this.originalInput;\n      }\n    }\n    const {\n      v,\n      ...resets\n    } = hsv;\n    return {\n      ...resets,\n      b: hsv.v\n    };\n  }\n}\n// kept for backwards compatability with v1\nfunction tinycolor(color = '', opts = {}) {\n  return new TinyColor(color, opts);\n}\n\n// Readability Functions\n// ---------------------\n// <http://www.w3.org/TR/2008/REC-WCAG20-20081211/#contrast-ratiodef (WCAG Version 2)\n/**\n * AKA `contrast`\n *\n * Analyze the 2 colors and returns the color contrast defined by (WCAG Version 2)\n */\nfunction readability(color1, color2) {\n  const c1 = new TinyColor(color1);\n  const c2 = new TinyColor(color2);\n  return (Math.max(c1.getLuminance(), c2.getLuminance()) + 0.05) / (Math.min(c1.getLuminance(), c2.getLuminance()) + 0.05);\n}\n/**\n * Ensure that foreground and background color combinations meet WCAG2 guidelines.\n * The third argument is an object.\n *      the 'level' property states 'AA' or 'AAA' - if missing or invalid, it defaults to 'AA';\n *      the 'size' property states 'large' or 'small' - if missing or invalid, it defaults to 'small'.\n * If the entire object is absent, isReadable defaults to {level:\"AA\",size:\"small\"}.\n *\n * Example\n * ```ts\n * new TinyColor().isReadable('#000', '#111') => false\n * new TinyColor().isReadable('#000', '#111', { level: 'AA', size: 'large' }) => false\n * ```\n */\nfunction isReadable(color1, color2, wcag2 = {\n  level: 'AA',\n  size: 'small'\n}) {\n  const readabilityLevel = readability(color1, color2);\n  switch ((wcag2.level ?? 'AA') + (wcag2.size ?? 'small')) {\n    case 'AAsmall':\n    case 'AAAlarge':\n      return readabilityLevel >= 4.5;\n    case 'AAlarge':\n      return readabilityLevel >= 3;\n    case 'AAAsmall':\n      return readabilityLevel >= 7;\n    default:\n      return false;\n  }\n}\n/**\n * Given a base color and a list of possible foreground or background\n * colors for that base, returns the most readable color.\n * Optionally returns Black or White if the most readable color is unreadable.\n *\n * @param baseColor - the base color.\n * @param colorList - array of colors to pick the most readable one from.\n * @param args - and object with extra arguments\n *\n * Example\n * ```ts\n * new TinyColor().mostReadable('#123', ['#124\", \"#125'], { includeFallbackColors: false }).toHexString(); // \"#112255\"\n * new TinyColor().mostReadable('#123', ['#124\", \"#125'],{ includeFallbackColors: true }).toHexString();  // \"#ffffff\"\n * new TinyColor().mostReadable('#a8015a', [\"#faf3f3\"], { includeFallbackColors:true, level: 'AAA', size: 'large' }).toHexString(); // \"#faf3f3\"\n * new TinyColor().mostReadable('#a8015a', [\"#faf3f3\"], { includeFallbackColors:true, level: 'AAA', size: 'small' }).toHexString(); // \"#ffffff\"\n * ```\n */\nfunction mostReadable(baseColor, colorList, args = {\n  includeFallbackColors: false,\n  level: 'AA',\n  size: 'small'\n}) {\n  let bestColor = null;\n  let bestScore = 0;\n  const {\n    includeFallbackColors,\n    level,\n    size\n  } = args;\n  for (const color of colorList) {\n    const score = readability(baseColor, color);\n    if (score > bestScore) {\n      bestScore = score;\n      bestColor = new TinyColor(color);\n    }\n  }\n  if (isReadable(baseColor, bestColor, {\n    level,\n    size\n  }) || !includeFallbackColors) {\n    return bestColor;\n  }\n  args.includeFallbackColors = false;\n  return mostReadable(baseColor, ['#fff', '#000'], args);\n}\n\n/**\n * Returns the color represented as a Microsoft filter for use in old versions of IE.\n */\nfunction toMsFilter(firstColor, secondColor) {\n  const color = new TinyColor(firstColor);\n  const hex8String = '#' + rgbaToArgbHex(color.r, color.g, color.b, color.a);\n  let secondHex8String = hex8String;\n  const gradientType = color.gradientType ? 'GradientType = 1, ' : '';\n  if (secondColor) {\n    const s = new TinyColor(secondColor);\n    secondHex8String = '#' + rgbaToArgbHex(s.r, s.g, s.b, s.a);\n  }\n  return `progid:DXImageTransform.Microsoft.gradient(${gradientType}startColorstr=${hex8String},endColorstr=${secondHex8String})`;\n}\n\n/**\n * If input is an object, force 1 into \"1.0\" to handle ratios properly\n * String input requires \"1.0\" as input, so 1 will be treated as 1\n */\nfunction fromRatio(ratio, opts) {\n  const newColor = {\n    r: convertToPercentage(ratio.r),\n    g: convertToPercentage(ratio.g),\n    b: convertToPercentage(ratio.b)\n  };\n  if (ratio.a !== undefined) {\n    newColor.a = Number(ratio.a);\n  }\n  return new TinyColor(newColor, opts);\n}\n/** old random function */\nfunction legacyRandom() {\n  return new TinyColor({\n    r: Math.random(),\n    g: Math.random(),\n    b: Math.random()\n  });\n}\n\n// randomColor by David Merfield under the CC0 license\n// https://github.com/davidmerfield/randomColor/\nfunction random(options = {}) {\n  // Check if we need to generate multiple colors\n  if (options.count !== undefined && options.count !== null) {\n    const totalColors = options.count;\n    const colors = [];\n    options.count = undefined;\n    while (totalColors > colors.length) {\n      // Since we're generating multiple colors,\n      // incremement the seed. Otherwise we'd just\n      // generate the same color each time...\n      options.count = null;\n      if (options.seed) {\n        options.seed += 1;\n      }\n      colors.push(random(options));\n    }\n    options.count = totalColors;\n    return colors;\n  }\n  // First we pick a hue (H)\n  const h = pickHue(options.hue, options.seed);\n  // Then use H to determine saturation (S)\n  const s = pickSaturation(h, options);\n  // Then use S and H to determine brightness (B).\n  const v = pickBrightness(h, s, options);\n  const res = {\n    h,\n    s,\n    v\n  };\n  if (options.alpha !== undefined) {\n    res.a = options.alpha;\n  }\n  // Then we return the HSB color in the desired format\n  return new TinyColor(res);\n}\nfunction pickHue(hue, seed) {\n  const hueRange = getHueRange(hue);\n  let res = randomWithin(hueRange, seed);\n  // Instead of storing red as two seperate ranges,\n  // we group them, using negative numbers\n  if (res < 0) {\n    res = 360 + res;\n  }\n  return res;\n}\nfunction pickSaturation(hue, options) {\n  if (options.hue === 'monochrome') {\n    return 0;\n  }\n  if (options.luminosity === 'random') {\n    return randomWithin([0, 100], options.seed);\n  }\n  const {\n    saturationRange\n  } = getColorInfo(hue);\n  let sMin = saturationRange[0];\n  let sMax = saturationRange[1];\n  switch (options.luminosity) {\n    case 'bright':\n      sMin = 55;\n      break;\n    case 'dark':\n      sMin = sMax - 10;\n      break;\n    case 'light':\n      sMax = 55;\n      break;\n    default:\n      break;\n  }\n  return randomWithin([sMin, sMax], options.seed);\n}\nfunction pickBrightness(H, S, options) {\n  let bMin = getMinimumBrightness(H, S);\n  let bMax = 100;\n  switch (options.luminosity) {\n    case 'dark':\n      bMax = bMin + 20;\n      break;\n    case 'light':\n      bMin = (bMax + bMin) / 2;\n      break;\n    case 'random':\n      bMin = 0;\n      bMax = 100;\n      break;\n    default:\n      break;\n  }\n  return randomWithin([bMin, bMax], options.seed);\n}\nfunction getMinimumBrightness(H, S) {\n  const {\n    lowerBounds\n  } = getColorInfo(H);\n  for (let i = 0; i < lowerBounds.length - 1; i++) {\n    const s1 = lowerBounds[i][0];\n    const v1 = lowerBounds[i][1];\n    const s2 = lowerBounds[i + 1][0];\n    const v2 = lowerBounds[i + 1][1];\n    if (S >= s1 && S <= s2) {\n      const m = (v2 - v1) / (s2 - s1);\n      const b = v1 - m * s1;\n      return m * S + b;\n    }\n  }\n  return 0;\n}\nfunction getHueRange(colorInput) {\n  const num = parseInt(colorInput, 10);\n  if (!Number.isNaN(num) && num < 360 && num > 0) {\n    return [num, num];\n  }\n  if (typeof colorInput === 'string') {\n    const namedColor = bounds.find(n => n.name === colorInput);\n    if (namedColor) {\n      const color = defineColor(namedColor);\n      if (color.hueRange) {\n        return color.hueRange;\n      }\n    }\n    const parsed = new TinyColor(colorInput);\n    if (parsed.isValid) {\n      const hue = parsed.toHsv().h;\n      return [hue, hue];\n    }\n  }\n  return [0, 360];\n}\nfunction getColorInfo(hue) {\n  // Maps red colors to make picking hue easier\n  if (hue >= 334 && hue <= 360) {\n    hue -= 360;\n  }\n  for (const bound of bounds) {\n    const color = defineColor(bound);\n    if (color.hueRange && hue >= color.hueRange[0] && hue <= color.hueRange[1]) {\n      return color;\n    }\n  }\n  throw Error('Color not found');\n}\nfunction randomWithin(range, seed) {\n  if (seed === undefined) {\n    return Math.floor(range[0] + Math.random() * (range[1] + 1 - range[0]));\n  }\n  // Seeded random algorithm from http://indiegamr.com/generate-repeatable-random-numbers-in-js/\n  const max = range[1] || 1;\n  const min = range[0] || 0;\n  seed = (seed * 9301 + 49297) % 233280;\n  const rnd = seed / 233280.0;\n  return Math.floor(min + rnd * (max - min));\n}\nfunction defineColor(bound) {\n  const sMin = bound.lowerBounds[0][0];\n  const sMax = bound.lowerBounds[bound.lowerBounds.length - 1][0];\n  const bMin = bound.lowerBounds[bound.lowerBounds.length - 1][1];\n  const bMax = bound.lowerBounds[0][1];\n  return {\n    name: bound.name,\n    hueRange: bound.hueRange,\n    lowerBounds: bound.lowerBounds,\n    saturationRange: [sMin, sMax],\n    brightnessRange: [bMin, bMax]\n  };\n}\n/**\n * @hidden\n */\nconst bounds = [{\n  name: 'monochrome',\n  hueRange: null,\n  lowerBounds: [[0, 0], [100, 0]]\n}, {\n  name: 'red',\n  hueRange: [-26, 18],\n  lowerBounds: [[20, 100], [30, 92], [40, 89], [50, 85], [60, 78], [70, 70], [80, 60], [90, 55], [100, 50]]\n}, {\n  name: 'orange',\n  hueRange: [19, 46],\n  lowerBounds: [[20, 100], [30, 93], [40, 88], [50, 86], [60, 85], [70, 70], [100, 70]]\n}, {\n  name: 'yellow',\n  hueRange: [47, 62],\n  lowerBounds: [[25, 100], [40, 94], [50, 89], [60, 86], [70, 84], [80, 82], [90, 80], [100, 75]]\n}, {\n  name: 'green',\n  hueRange: [63, 178],\n  lowerBounds: [[30, 100], [40, 90], [50, 85], [60, 81], [70, 74], [80, 64], [90, 50], [100, 40]]\n}, {\n  name: 'blue',\n  hueRange: [179, 257],\n  lowerBounds: [[20, 100], [30, 86], [40, 80], [50, 74], [60, 60], [70, 52], [80, 44], [90, 39], [100, 35]]\n}, {\n  name: 'purple',\n  hueRange: [258, 282],\n  lowerBounds: [[20, 100], [30, 87], [40, 79], [50, 70], [60, 65], [70, 59], [80, 52], [90, 45], [100, 42]]\n}, {\n  name: 'pink',\n  hueRange: [283, 334],\n  lowerBounds: [[20, 100], [30, 90], [40, 86], [60, 84], [80, 80], [90, 75], [100, 73]]\n}];\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TinyColor, bounds, convertDecimalToHex, convertHexToDecimal, fromRatio, getRoundNumber, hslToRgb, hsvToRgb, inputToRGB, isReadable, isValidCSSUnit, legacyRandom, mostReadable, names, numberInputToObject, parseIntFromHex, random, readability, rgbToHex, rgbToHsl, rgbToHsv, rgbToRgb, rgbaToArgbHex, rgbaToHex, stringInputToObject, tinycolor, toMsFilter };\n", "import { __decorate } from 'tslib';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, Output, Injectable, NgModule } from '@angular/core';\nimport { Subject, fromEvent } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { InputBoolean, toNumber, TDSHelperArray } from 'tds-ui/shared/utility';\nimport { rgbToHsv, rgbToHex, inputToRGB } from 'tds-ui/tinycolor';\nimport * as i3 from 'tds-ui/core/config';\nimport { WithConfig } from 'tds-ui/core/config';\nimport * as i2 from 'tds-ui/core/services';\nimport { TDSDestroyService } from 'tds-ui/core/services';\nconst _c0 = [\"*\"];\nfunction TDSTagComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 1);\n    i0.ɵɵlistener(\"click\", function TDSTagComponent_Conditional_1_Template_span_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onClickClose($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TDSTagColorComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 1, 0);\n    i0.ɵɵelement(2, \"span\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(ctx_r0.tdsIcon);\n    i0.ɵɵstyleProp(\"color\", ctx_r0.iconColor ? ctx_r0.iconColor : \"unset\");\n  }\n}\nclass TDSTagComponent {\n  constructor(_cdr, renderer, elementRef, ngZone) {\n    this._cdr = _cdr;\n    this.renderer = renderer;\n    this.elementRef = elementRef;\n    this.ngZone = ngZone;\n    this.mode = 'default';\n    this.type = 'default';\n    this.status = 'secondary';\n    this.checked = false;\n    this.size = \"md\";\n    this.disabled = false;\n    this.tdsTheme = 'default';\n    /**\n     * @deprecated không dùng từ bản 2.4.2\n     */\n    this.rounded = null;\n    this.close = new EventEmitter();\n    this.checkedChange = new EventEmitter();\n    this.cssClass = '';\n    this.destroy$ = new Subject();\n  }\n  ngOnInit() {\n    this.ngZone.runOutsideAngular(() => {\n      fromEvent(this.elementRef.nativeElement, 'click').pipe(takeUntil(this.destroy$)).subscribe(() => {\n        this.ngZone.run(() => this.updateCheckedStatus());\n      });\n    });\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  ngOnChanges(changes) {\n    this._cdr.markForCheck();\n  }\n  onClickClose(e) {\n    if (this.mode === 'closeable' && !this.disabled) {\n      this.close.emit(e);\n      if (!e.defaultPrevented) {\n        this.renderer.removeChild(this.renderer.parentNode(this.elementRef.nativeElement), this.elementRef.nativeElement);\n      }\n    }\n  }\n  updateCheckedStatus() {\n    if (this.mode === 'checkable' && !this.disabled) {\n      this.checked = !this.checked;\n      this.checkedChange.emit(this.checked);\n    }\n  }\n  static {\n    this.ɵfac = function TDSTagComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSTagComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TDSTagComponent,\n      selectors: [[\"tds-tag\"]],\n      hostVars: 50,\n      hostBindings: function TDSTagComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵstyleMap(ctx.tdsStyle);\n          i0.ɵɵclassMap(ctx.rounded);\n          i0.ɵɵclassProp(\"tds-tag-mode-default\", ctx.mode === \"default\")(\"tds-tag-mode-closeable\", ctx.mode === \"closeable\")(\"tds-tag-mode-checkable\", ctx.mode === \"checkable\")(\"tds-tag-mode-checkable-checked\", ctx.mode === \"checkable\" && ctx.checked)(\"tds-tag-secondary\", ctx.status === \"secondary\")(\"tds-tag-primary\", ctx.status === \"primary\")(\"tds-tag-success\", ctx.status === \"success\")(\"tds-tag-info\", ctx.status === \"info\")(\"tds-tag-error\", ctx.status === \"error\")(\"tds-tag-warning\", ctx.status === \"warning\")(\"tds-tag-neutral\", ctx.status === \"neutral\")(\"tds-tag-custom\", ctx.status === \"custom\")(\"tds-tag-default\", ctx.status === \"default\")(\"tds-tag-type-default\", ctx.type === \"default\")(\"tds-tag-type-outline\", ctx.type === \"outline\")(\"tds-tag-size-sm\", ctx.size === \"sm\")(\"tds-tag-size-md\", ctx.size === \"md\")(\"tds-tag-size-lg\", ctx.size === \"lg\")(\"tds-tag-size-xl\", ctx.size === \"xl\")(\"tds-tag-disabled\", ctx.disabled)(\"tds-tag-theme-default\", ctx.tdsTheme === \"default\")(\"tds-tag-theme-light\", ctx.tdsTheme === \"light\")(\"tds-tag-theme-dark\", ctx.tdsTheme === \"dark\");\n        }\n      },\n      inputs: {\n        mode: \"mode\",\n        type: \"type\",\n        status: \"status\",\n        tdsStyle: \"tdsStyle\",\n        checked: \"checked\",\n        size: \"size\",\n        disabled: \"disabled\",\n        tdsTheme: \"tdsTheme\",\n        rounded: \"rounded\"\n      },\n      outputs: {\n        close: \"close\",\n        checkedChange: \"checkedChange\"\n      },\n      exportAs: [\"tdsTag\"],\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 2,\n      vars: 1,\n      consts: [[\"tabindex\", \"-1\", 1, \"tds-tag-close-icon\", \"tdsi-close-fill\"], [\"tabindex\", \"-1\", 1, \"tds-tag-close-icon\", \"tdsi-close-fill\", 3, \"click\"]],\n      template: function TDSTagComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n          i0.ɵɵtemplate(1, TDSTagComponent_Conditional_1_Template, 1, 0, \"span\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx.mode === \"closeable\" ? 1 : -1);\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n__decorate([InputBoolean()], TDSTagComponent.prototype, \"checked\", void 0);\n__decorate([InputBoolean()], TDSTagComponent.prototype, \"disabled\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSTagComponent, [{\n    type: Component,\n    args: [{\n      selector: 'tds-tag',\n      exportAs: 'tdsTag',\n      template: `\n    <ng-content></ng-content>\n    @if (mode === 'closeable') {\n      <span class='tds-tag-close-icon tdsi-close-fill'\n        tabindex=\"-1\"\n      (click)=\"onClickClose($event)\"></span>\n    }\n    `,\n      preserveWhitespaces: false,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        '[class.tds-tag-mode-default]': 'mode === \"default\"',\n        '[class.tds-tag-mode-closeable]': 'mode === \"closeable\"',\n        '[class.tds-tag-mode-checkable]': 'mode === \"checkable\"',\n        '[class.tds-tag-mode-checkable-checked]': 'mode === \"checkable\" && checked',\n        '[class.tds-tag-secondary]': 'status === \"secondary\"',\n        '[class.tds-tag-primary]': 'status === \"primary\"',\n        '[class.tds-tag-success]': 'status === \"success\"',\n        '[class.tds-tag-info]': 'status === \"info\"',\n        '[class.tds-tag-error]': 'status === \"error\"',\n        '[class.tds-tag-warning]': 'status === \"warning\"',\n        '[class.tds-tag-neutral]': 'status === \"neutral\"',\n        '[class.tds-tag-custom]': 'status === \"custom\"',\n        '[class.tds-tag-default]': 'status === \"default\"',\n        '[class.tds-tag-type-default]': 'type === \"default\"',\n        '[class.tds-tag-type-outline]': 'type === \"outline\"',\n        '[class.tds-tag-size-sm]': 'size === \"sm\"',\n        '[class.tds-tag-size-md]': 'size === \"md\"',\n        '[class.tds-tag-size-lg]': 'size === \"lg\"',\n        '[class.tds-tag-size-xl]': 'size === \"xl\"',\n        '[class.tds-tag-disabled]': 'disabled',\n        '[class.tds-tag-theme-default]': 'tdsTheme === \"default\"',\n        '[class.tds-tag-theme-light]': 'tdsTheme === \"light\"',\n        '[class.tds-tag-theme-dark]': 'tdsTheme === \"dark\"',\n        '[style]': 'tdsStyle!',\n        '[class]': 'rounded'\n      },\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.NgZone\n  }], {\n    mode: [{\n      type: Input\n    }],\n    type: [{\n      type: Input\n    }],\n    status: [{\n      type: Input\n    }],\n    tdsStyle: [{\n      type: Input\n    }],\n    checked: [{\n      type: Input\n    }],\n    size: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    tdsTheme: [{\n      type: Input\n    }],\n    rounded: [{\n      type: Input\n    }],\n    close: [{\n      type: Output\n    }],\n    checkedChange: [{\n      type: Output\n    }]\n  });\n})();\nclass TDSTagGenarateColorService {\n  constructor() {\n    this._hueStep = 2;\n    this._saturationStep = 0.16;\n    this._saturationStep2 = 0.05;\n    this._brightnessStep1 = 0.05;\n    this._brightnessStep2 = 0.15;\n    this._lightColorCount = 5;\n    this._darkColorCount = 4;\n    this.darkColorMap = [{\n      index: 7,\n      opacity: 0.15\n    }, {\n      index: 6,\n      opacity: 0.25\n    }, {\n      index: 5,\n      opacity: 0.3\n    }, {\n      index: 5,\n      opacity: 0.45\n    }, {\n      index: 5,\n      opacity: 0.65\n    }, {\n      index: 5,\n      opacity: 0.85\n    }, {\n      index: 4,\n      opacity: 0.9\n    }, {\n      index: 3,\n      opacity: 0.95\n    }, {\n      index: 2,\n      opacity: 0.97\n    }, {\n      index: 1,\n      opacity: 0.98\n    }];\n  }\n  get hueStep() {\n    return this._hueStep;\n  }\n  set hueStep(hueStep) {\n    this._hueStep = toNumber(hueStep);\n  }\n  get saturationStep() {\n    return this._saturationStep;\n  }\n  set saturationStep(saturationStep) {\n    this._saturationStep = toNumber(saturationStep);\n  }\n  get saturationStep2() {\n    return this._saturationStep2;\n  }\n  set saturationStep2(saturationStep2) {\n    this._saturationStep2 = toNumber(saturationStep2);\n  }\n  get brightnessStep1() {\n    return this._brightnessStep1;\n  }\n  set brightnessStep1(brightnessStep1) {\n    this._brightnessStep1 = toNumber(brightnessStep1);\n  }\n  get brightnessStep2() {\n    return this._brightnessStep2;\n  }\n  set brightnessStep2(brightnessStep2) {\n    this._brightnessStep2 = toNumber(brightnessStep2);\n  }\n  get lightColorCount() {\n    return this._lightColorCount;\n  }\n  set lightColorCount(lightColorCount) {\n    this._lightColorCount = lightColorCount;\n  }\n  get darkColorCount() {\n    return this._darkColorCount;\n  }\n  set darkColorCount(darkColorCount) {\n    this._darkColorCount = darkColorCount;\n  }\n  // Wrapper function ported from TinyColor.prototype.toHsv\n  // Keep it here because of `hsv.h * 360`\n  toHsv({\n    r,\n    g,\n    b\n  }) {\n    const hsv = rgbToHsv(r, g, b);\n    return {\n      h: hsv.h * 360,\n      s: hsv.s,\n      v: hsv.v\n    };\n  }\n  // Wrapper function ported from TinyColor.prototype.toHexString\n  // Keep it here because of the prefix `#`\n  toHex({\n    r,\n    g,\n    b\n  }) {\n    return `#${rgbToHex(r, g, b, false)}`;\n  }\n  mix(rgb1, rgb2, amount) {\n    const p = amount / 100;\n    const rgb = {\n      r: (rgb2.r - rgb1.r) * p + rgb1.r,\n      g: (rgb2.g - rgb1.g) * p + rgb1.g,\n      b: (rgb2.b - rgb1.b) * p + rgb1.b\n    };\n    return rgb;\n  }\n  getHue(hsv, i, light) {\n    let hue;\n    if (Math.round(hsv.h) >= 60 && Math.round(hsv.h) <= 240) {\n      hue = light ? Math.round(hsv.h) - this.hueStep * i : Math.round(hsv.h) + this.hueStep * i;\n    } else {\n      hue = light ? Math.round(hsv.h) + this.hueStep * i : Math.round(hsv.h) - this.hueStep * i;\n    }\n    if (hue < 0) {\n      hue += 360;\n    } else if (hue >= 360) {\n      hue -= 360;\n    }\n    return hue;\n  }\n  getSaturation(hsv, i, light) {\n    // grey color don't change saturation\n    if (hsv.h === 0 && hsv.s === 0) {\n      return hsv.s;\n    }\n    let saturation;\n    if (light) {\n      saturation = hsv.s - this.saturationStep * i;\n    } else if (i === this.darkColorCount) {\n      saturation = hsv.s + this.saturationStep;\n    } else {\n      saturation = hsv.s + this.saturationStep2 * i;\n    }\n    // 边界值修正\n    if (saturation > 1) {\n      saturation = 1;\n    }\n    // 第一格的 s 限制在 0.06-0.1 之间\n    if (light && i === this.lightColorCount && saturation > 0.1) {\n      saturation = 0.1;\n    }\n    if (saturation < 0.06) {\n      saturation = 0.06;\n    }\n    return Number(saturation.toFixed(2));\n  }\n  getValue(hsv, i, light) {\n    let value;\n    if (light) {\n      value = hsv.v + this.brightnessStep1 * i;\n    } else {\n      value = hsv.v - this.brightnessStep2 * i;\n    }\n    if (value > 1) {\n      value = 1;\n    }\n    return Number(value.toFixed(2));\n  }\n  generate(color, opts = {}) {\n    const patterns = [];\n    const pColor = inputToRGB(color);\n    for (let i = this.lightColorCount; i > 0; i -= 1) {\n      const hsv = this.toHsv(pColor);\n      const colorString = this.toHex(inputToRGB({\n        h: this.getHue(hsv, i, true),\n        s: this.getSaturation(hsv, i, true),\n        v: this.getValue(hsv, i, true)\n      }));\n      patterns.push(colorString);\n    }\n    patterns.push(this.toHex(pColor));\n    for (let i = 1; i <= this.darkColorCount; i += 1) {\n      const hsv = this.toHsv(pColor);\n      const colorString = this.toHex(inputToRGB({\n        h: this.getHue(hsv, i),\n        s: this.getSaturation(hsv, i),\n        v: this.getValue(hsv, i)\n      }));\n      patterns.push(colorString);\n    }\n    // dark theme patterns\n    if (opts.theme === 'dark') {\n      return this.darkColorMap.map(({\n        index,\n        opacity\n      }) => {\n        const darkColorString = this.toHex(this.mix(inputToRGB(opts.backgroundColor), inputToRGB(patterns[index]), opacity * 100));\n        return darkColorString;\n      });\n    }\n    return patterns;\n  }\n  static {\n    this.ɵfac = function TDSTagGenarateColorService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSTagGenarateColorService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: TDSTagGenarateColorService,\n      factory: TDSTagGenarateColorService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSTagGenarateColorService, [{\n    type: Injectable\n  }], () => [], null);\n})();\nconst TDS_CONFIG_MODULE_NAME = 'tagMultiColor';\nclass TDSTagColorComponent {\n  constructor(element, genarateColorSevice, cdr, renderer, destroy$, tdsConfigService) {\n    this.element = element;\n    this.genarateColorSevice = genarateColorSevice;\n    this.cdr = cdr;\n    this.renderer = renderer;\n    this.destroy$ = destroy$;\n    this.tdsConfigService = tdsConfigService;\n    this._tdsModuleName = TDS_CONFIG_MODULE_NAME;\n    this.tdsTheme = 'default';\n    this.tdsColor = '';\n    this.tdsBgColor = '100';\n    this.tdsBorderColor = '100';\n    this.tdsIconColor = '500';\n    this.tdsSize = \"md\";\n    this.listColor = [];\n    this.rangeColor = ['50', '100', '200', '300', '400', '500', '600', '700', '800', '900'];\n  }\n  ngOnInit() {\n    this.genarate();\n    this.setUpColor();\n    this.tdsConfigService.getConfigChangeEventForComponent(TDS_CONFIG_MODULE_NAME).pipe(takeUntil(this.destroy$)).subscribe(() => {\n      this.genarate();\n      this.setUpColor();\n      this.cdr.markForCheck();\n    });\n  }\n  ngOnChanges(changes) {\n    const {\n      tdsTheme,\n      tdsColor\n    } = changes;\n    if (tdsTheme || tdsColor) {\n      this.genarate();\n    }\n    this.setUpColor();\n    this.cdr.markForCheck();\n  }\n  genarate() {\n    this.listColor = this.genarateColorSevice.generate(this.tdsColor, {\n      theme: this.tdsTheme,\n      backgroundColor: '#141414'\n    });\n  }\n  setUpColor() {\n    if (TDSHelperArray.hasListValue(this.listColor)) {\n      this.bgColor = this.listColor[this.rangeColor.findIndex(f => this.tdsBgColor === f) || 0];\n      this.borderColor = this.listColor[this.rangeColor.findIndex(f => this.tdsBorderColor === f) || 0];\n      this.iconColor = this.listColor[this.rangeColor.findIndex(f => this.tdsIconColor === f) || 0];\n      this.renderer.setStyle(this.element.nativeElement, 'border-color', this.borderColor ? this.borderColor : 'transparent');\n      this.renderer.setStyle(this.element.nativeElement, 'background', this.bgColor);\n      const brightness = this.getBrightness(inputToRGB(this.bgColor));\n      this.textColor = brightness < 125 ? 'white' : 'unset';\n    }\n  }\n  /**\n  * Returns the perceived brightness of the color, from 0-255.\n  */\n  getBrightness({\n    r,\n    g,\n    b\n  }) {\n    // http://www.w3.org/TR/AERT#color-contrast    \n    return (r * 299 + g * 587 + b * 114) / 1000;\n  }\n  static {\n    this.ɵfac = function TDSTagColorComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSTagColorComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(TDSTagGenarateColorService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i2.TDSDestroyService), i0.ɵɵdirectiveInject(i3.TDSConfigService));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TDSTagColorComponent,\n      selectors: [[\"tds-tag-multi-color\"]],\n      hostVars: 8,\n      hostBindings: function TDSTagColorComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"tds-tag-size-sm\", ctx.tdsSize === \"sm\")(\"tds-tag-size-md\", ctx.tdsSize === \"md\")(\"tds-tag-size-lg\", ctx.tdsSize === \"lg\")(\"tds-tag-size-xl\", ctx.tdsSize === \"xl\");\n        }\n      },\n      inputs: {\n        tdsTheme: \"tdsTheme\",\n        tdsColor: \"tdsColor\",\n        tdsIcon: \"tdsIcon\",\n        tdsBgColor: \"tdsBgColor\",\n        tdsBorderColor: \"tdsBorderColor\",\n        tdsIconColor: \"tdsIconColor\",\n        tdsSize: \"tdsSize\"\n      },\n      exportAs: [\"tdsTagMultiColor\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([TDSTagGenarateColorService, TDSDestroyService]), i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 4,\n      vars: 3,\n      consts: [[\"icon\", \"\"], [1, \"tds-tag-multi-color-icon\"], [1, \"tds-tag-multi-color-text\"]],\n      template: function TDSTagColorComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, TDSTagColorComponent_Conditional_0_Template, 3, 4, \"span\", 1);\n          i0.ɵɵelementStart(1, \"span\", 2);\n          i0.ɵɵprojection(2);\n          i0.ɵɵelement(3, \"span\");\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵconditional(ctx.tdsIcon ? 0 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵstyleProp(\"color\", ctx.textColor ? ctx.textColor : \"unset\");\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n__decorate([WithConfig()], TDSTagColorComponent.prototype, \"tdsTheme\", void 0);\n__decorate([WithConfig()], TDSTagColorComponent.prototype, \"tdsColor\", void 0);\n__decorate([WithConfig()], TDSTagColorComponent.prototype, \"tdsIcon\", void 0);\n__decorate([WithConfig()], TDSTagColorComponent.prototype, \"tdsBgColor\", void 0);\n__decorate([WithConfig()], TDSTagColorComponent.prototype, \"tdsBorderColor\", void 0);\n__decorate([WithConfig()], TDSTagColorComponent.prototype, \"tdsIconColor\", void 0);\n__decorate([WithConfig()], TDSTagColorComponent.prototype, \"tdsSize\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSTagColorComponent, [{\n    type: Component,\n    args: [{\n      selector: 'tds-tag-multi-color',\n      exportAs: 'tdsTagMultiColor',\n      template: `\n      @if (tdsIcon) {\n        <span class='tds-tag-multi-color-icon' #icon>\n          <span [class]='tdsIcon' [style.color]=\"iconColor ? iconColor : 'unset'\"></span>\n        </span>\n      }\n      <span class='tds-tag-multi-color-text'  [style.color]=\"textColor ? textColor : 'unset'\">\n        <ng-content></ng-content>\n        <span>\n      `,\n      preserveWhitespaces: false,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        '[class.tds-tag-size-sm]': 'tdsSize === \"sm\"',\n        '[class.tds-tag-size-md]': 'tdsSize === \"md\"',\n        '[class.tds-tag-size-lg]': 'tdsSize === \"lg\"',\n        '[class.tds-tag-size-xl]': 'tdsSize === \"xl\"'\n      },\n      providers: [TDSTagGenarateColorService, TDSDestroyService],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: TDSTagGenarateColorService\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i2.TDSDestroyService\n  }, {\n    type: i3.TDSConfigService\n  }], {\n    tdsTheme: [{\n      type: Input\n    }],\n    tdsColor: [{\n      type: Input\n    }],\n    tdsIcon: [{\n      type: Input\n    }],\n    tdsBgColor: [{\n      type: Input\n    }],\n    tdsBorderColor: [{\n      type: Input\n    }],\n    tdsIconColor: [{\n      type: Input\n    }],\n    tdsSize: [{\n      type: Input\n    }]\n  });\n})();\nclass TDSTagModule {\n  static {\n    this.ɵfac = function TDSTagModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSTagModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: TDSTagModule,\n      imports: [TDSTagComponent, TDSTagColorComponent],\n      exports: [TDSTagComponent, TDSTagColorComponent]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSTagModule, [{\n    type: NgModule,\n    args: [{\n      imports: [TDSTagComponent, TDSTagColorComponent],\n      exports: [TDSTagComponent, TDSTagColorComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TDSTagColorComponent, TDSTagComponent, TDSTagGenarateColorService, TDSTagModule };\n", "import * as i0 from '@angular/core';\nimport { Directive, Optional, Inject, Input, NgModule } from '@angular/core';\nimport { coerceElement } from '@angular/cdk/coercion';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\nconst DISABLED_CLASSNAME = 'tds-animate-disabled';\nclass TDSNoAnimationDirective {\n  constructor(element, renderer, animationType) {\n    this.element = element;\n    this.renderer = renderer;\n    this.animationType = animationType;\n    this.noAnimation = false;\n  }\n  ngOnChanges() {\n    this.updateClass();\n  }\n  ngAfterViewInit() {\n    this.updateClass();\n  }\n  updateClass() {\n    const element = coerceElement(this.element);\n    if (!element) {\n      return;\n    }\n    if (this.noAnimation || this.animationType === 'NoopAnimations') {\n      this.renderer.addClass(element, DISABLED_CLASSNAME);\n    } else {\n      this.renderer.removeClass(element, DISABLED_CLASSNAME);\n    }\n  }\n  static {\n    this.ɵfac = function TDSNoAnimationDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSNoAnimationDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TDSNoAnimationDirective,\n      selectors: [[\"\", \"noAnimation\", \"\"]],\n      inputs: {\n        noAnimation: \"noAnimation\"\n      },\n      exportAs: [\"noAnimation\"],\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSNoAnimationDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[noAnimation]',\n      exportAs: 'noAnimation',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [ANIMATION_MODULE_TYPE]\n    }]\n  }], {\n    noAnimation: [{\n      type: Input\n    }]\n  });\n})();\nclass TDSNoAnimationModule {\n  static {\n    this.ɵfac = function TDSNoAnimationModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSNoAnimationModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: TDSNoAnimationModule,\n      imports: [TDSNoAnimationDirective],\n      exports: [TDSNoAnimationDirective]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSNoAnimationModule, [{\n    type: NgModule,\n    args: [{\n      imports: [TDSNoAnimationDirective],\n      exports: [TDSNoAnimationDirective]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TDSNoAnimationDirective, TDSNoAnimationModule };\n", "import { __decorate } from 'tslib';\nimport * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, Input, inject, NgModule } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { InputBoolean } from 'tds-ui/shared/utility';\nimport * as i1 from 'tds-ui/core/config';\nimport { WithConfig } from 'tds-ui/core/config';\nimport { zoomBadgeMotion } from 'tds-ui/core/animation';\nimport { TDSNoAnimationDirective } from 'tds-ui/core/no-animation';\nimport { NgStyle } from '@angular/common';\nimport * as i3 from 'tds-ui/core/outlet';\nimport { TDSOutletModule } from 'tds-ui/core/outlet';\nimport * as i2 from '@angular/cdk/bidi';\nfunction TDSBadgeSupComponent_Conditional_0_For_1_Conditional_1_For_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 3);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const p_r1 = ctx.$implicit;\n    const ɵ$index_2_r2 = i0.ɵɵnextContext(2).$index;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"current\", p_r1 === ctx_r2.countArray[ɵ$index_2_r2]);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", p_r1, \" \");\n  }\n}\nfunction TDSBadgeSupComponent_Conditional_0_For_1_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵrepeaterCreate(0, TDSBadgeSupComponent_Conditional_0_For_1_Conditional_1_For_1_Template, 2, 3, \"p\", 2, i0.ɵɵrepeaterTrackByIdentity);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵrepeater(ctx_r2.countSingleArray);\n  }\n}\nfunction TDSBadgeSupComponent_Conditional_0_For_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 1);\n    i0.ɵɵtemplate(1, TDSBadgeSupComponent_Conditional_0_For_1_Conditional_1_Template, 2, 0);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ɵ$index_2_r2 = ctx.$index;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"transform\", \"translateY(\" + -ctx_r2.countArray[ɵ$index_2_r2] * 100 + \"%)\");\n    i0.ɵɵproperty(\"noAnimation\", ctx_r2.noAnimation);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(!ctx_r2.dot && ctx_r2.countArray[ɵ$index_2_r2] !== undefined ? 1 : -1);\n  }\n}\nfunction TDSBadgeSupComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵrepeaterCreate(0, TDSBadgeSupComponent_Conditional_0_For_1_Template, 2, 4, \"span\", 0, i0.ɵɵrepeaterTrackByIdentity);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵrepeater(ctx_r2.maxNumberArray);\n  }\n}\nfunction TDSBadgeSupComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.overflowCount, \"+ \");\n  }\n}\nconst _c0 = [\"*\"];\nfunction TDSBadgeComponent_Conditional_0_Conditional_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.text);\n  }\n}\nfunction TDSBadgeComponent_Conditional_0_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 2);\n    i0.ɵɵtemplate(1, TDSBadgeComponent_Conditional_0_Conditional_1_ng_container_1_Template, 2, 1, \"ng-container\", 0);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"tdsStringTemplateOutlet\", ctx_r0.text);\n  }\n}\nfunction TDSBadgeComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 1);\n    i0.ɵɵtemplate(1, TDSBadgeComponent_Conditional_0_Conditional_1_Template, 2, 1, \"span\", 2);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"background\", !ctx_r0.presetColor && ctx_r0.color);\n    i0.ɵɵproperty(\"ngStyle\", ctx_r0.tdsStyle);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r0.text ? 1 : -1);\n  }\n}\nfunction TDSBadgeComponent_ng_container_2_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tds-badge-sup\", 4);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(ctx_r0.tdsClass);\n    i0.ɵɵproperty(\"offset\", ctx_r0.offset)(\"title\", ctx_r0.title)(\"tdsStyle\", ctx_r0.tdsStyle)(\"dot\", ctx_r0.dot)(\"size\", ctx_r0.size)(\"overflowCount\", ctx_r0.overflowCount)(\"disableAnimation\", !!(ctx_r0.standalone || ctx_r0.status || ctx_r0.color || (ctx_r0.noAnimation == null ? null : ctx_r0.noAnimation.noAnimation)))(\"count\", ctx_r0.count)(\"noAnimation\", !!(ctx_r0.noAnimation == null ? null : ctx_r0.noAnimation.noAnimation))(\"standalone\", ctx_r0.standalone)(\"placement\", ctx_r0.placement);\n  }\n}\nfunction TDSBadgeComponent_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TDSBadgeComponent_ng_container_2_Conditional_1_Template, 1, 13, \"tds-badge-sup\", 3);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r0.showSup ? 1 : -1);\n  }\n}\nfunction TDSRibbonComponent_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.text);\n  }\n}\nconst badgePresetColors = ['pink', 'red', 'yellow', 'orange', 'cyan', 'green', 'blue', 'purple', 'geekblue', 'magenta', 'volcano', 'gold', 'lime'];\nclass TDSBadgeSupComponent {\n  constructor() {\n    this.tdsStyle = null;\n    this.dot = false;\n    this.overflowCount = 99;\n    this.disableAnimation = false;\n    this.noAnimation = false;\n    this.standalone = false;\n    this.size = 'md';\n    this.placement = 'topRight';\n    this._cls = '';\n    this.maxNumberArray = [];\n    this.countArray = [];\n    this._count = 0;\n    this.countSingleArray = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9];\n  }\n  generateMaxNumberArray() {\n    this.maxNumberArray = this.overflowCount.toString().split('');\n  }\n  ngOnInit() {\n    this.generateMaxNumberArray();\n  }\n  get cls() {\n    return this._cls;\n  }\n  ngOnChanges(changes) {\n    const {\n      overflowCount,\n      count,\n      standalone,\n      tdsStyle,\n      tdsClass\n    } = changes;\n    if (count && typeof count.currentValue === 'number') {\n      this._count = Math.max(0, count.currentValue);\n      this.countArray = this._count.toString().split('').map(item => +item);\n    }\n    if (overflowCount) {\n      this.generateMaxNumberArray();\n    }\n  }\n  get styleCss() {\n    let style = null;\n    if (!this.standalone) {\n      switch (this.placement) {\n        case 'topRight':\n          style = {\n            ['transform']: 'translate(50%,-50%)',\n            ['transform-origin']: '100% 0'\n          };\n          break;\n        case 'topLeft':\n          style = {\n            ['transform']: 'translate(-50%,-50%)',\n            ['transform-origin']: '0 0'\n          };\n          break;\n        case 'bottomLeft':\n          style = {\n            ['transform']: 'translate(-50%,50%)',\n            ['transform-origin']: '100% 0'\n          };\n          break;\n        case 'bottomRight':\n          style = {\n            ['transform']: 'translate(50%,50%)',\n            ['transform-origin']: '0 100%'\n          };\n          break;\n        default:\n          style = {\n            ['transform']: 'translate(-50%,50%)',\n            ['transform-origin']: '100% 0'\n          };\n          break;\n      }\n    }\n    if (!!this.tdsStyle) {\n      if (style != null) return Object.assign({}, style, this.tdsStyle);\n      return this.tdsStyle;\n    }\n    return style;\n  }\n  static {\n    this.ɵfac = function TDSBadgeSupComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSBadgeSupComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TDSBadgeSupComponent,\n      selectors: [[\"tds-badge-sup\"]],\n      hostAttrs: [1, \"tds-scroll-number\"],\n      hostVars: 33,\n      hostBindings: function TDSBadgeSupComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵsyntheticHostProperty(\"@.disabled\", ctx.disableAnimation)(\"@zoomBadgeMotion\", undefined);\n          i0.ɵɵattribute(\"title\", ctx.title === null ? \"\" : ctx.title || ctx.count);\n          i0.ɵɵstyleMap(ctx.styleCss);\n          i0.ɵɵstyleProp(\"right\", ctx.offset && ctx.offset[0] ? -ctx.offset[0] : null, \"px\")(\"margin-top\", ctx.offset && ctx.offset[1] ? ctx.offset[1] : null, \"px\");\n          i0.ɵɵclassProp(\"tds-badge-count\", !ctx.dot)(\"tds-badge-sup-size-sm\", ctx.size === \"sm\")(\"tds-badge-sup-size-md\", ctx.size === \"md\")(\"tds-badge-sup-size-lg\", ctx.size === \"lg\")(\"tds-badge-sup-size-xl\", ctx.size === \"xl\")(\"tds-badge-dot\", ctx.dot)(\"tds-badge-multiple-words\", ctx.countArray.length >= 2)(\"tds-badge-sup-standalone\", ctx.standalone)(\"tds-badge-sup-placement-top-left\", ctx.placement === \"topLeft\")(\"tds-badge-sup-placement-top-right\", ctx.placement === \"topRight\")(\"tds-badge-sup-placement-bottom-left\", ctx.placement === \"bottomLeft\")(\"tds-badge-sup-placement-bottom-right\", ctx.placement === \"bottomRight\");\n        }\n      },\n      inputs: {\n        offset: \"offset\",\n        title: \"title\",\n        tdsStyle: \"tdsStyle\",\n        dot: \"dot\",\n        overflowCount: \"overflowCount\",\n        disableAnimation: \"disableAnimation\",\n        count: \"count\",\n        noAnimation: \"noAnimation\",\n        standalone: \"standalone\",\n        size: \"size\",\n        placement: \"placement\"\n      },\n      exportAs: [\"tdsBadgeSup\"],\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 1,\n      consts: [[1, \"tds-scroll-number-only\", 2, \"transition\", \"all .3s cubic-bezier(.645,.045,.355,1)\", 3, \"noAnimation\", \"transform\"], [1, \"tds-scroll-number-only\", 2, \"transition\", \"all .3s cubic-bezier(.645,.045,.355,1)\", 3, \"noAnimation\"], [1, \"tds-scroll-number-only-unit\", 3, \"current\"], [1, \"tds-scroll-number-only-unit\"]],\n      template: function TDSBadgeSupComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, TDSBadgeSupComponent_Conditional_0_Template, 2, 0)(1, TDSBadgeSupComponent_Conditional_1_Template, 1, 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵconditional(ctx._count <= ctx.overflowCount ? 0 : 1);\n        }\n      },\n      dependencies: [TDSNoAnimationDirective],\n      encapsulation: 2,\n      data: {\n        animation: [zoomBadgeMotion]\n      },\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSBadgeSupComponent, [{\n    type: Component,\n    args: [{\n      selector: 'tds-badge-sup',\n      exportAs: 'tdsBadgeSup',\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      animations: [zoomBadgeMotion],\n      standalone: true,\n      imports: [TDSNoAnimationDirective],\n      template: `\n    @if (_count <= overflowCount) {\n      @for (n of maxNumberArray; track n; let i = $index) {\n        <span\n          [noAnimation]=\"noAnimation\"\n          class=\"tds-scroll-number-only\"\n          [style.transform]=\"'translateY(' + -countArray[i] * 100 + '%)'\"\n          style=\"transition: all .3s cubic-bezier(.645,.045,.355,1)\"\n          >\n          @if (!dot && countArray[i] !== undefined) {\n            @for (p of countSingleArray; track p) {\n              <p\n                class=\"tds-scroll-number-only-unit\"\n                [class.current]=\"p === countArray[i]\"\n                >\n                {{ p }}\n              </p>\n            }\n          }\n        </span>\n      }\n    } @else {\n      {{ overflowCount }}+\n    }\n    `,\n      host: {\n        '[@.disabled]': `disableAnimation`,\n        '[@zoomBadgeMotion]': '',\n        '[attr.title]': `title === null ? '' : title || count`,\n        '[style]': `styleCss`,\n        '[style.right.px]': `offset && offset[0] ? -offset[0] : null`,\n        '[style.margin-top.px]': `offset && offset[1] ? offset[1] : null`,\n        '[class.tds-badge-count]': `!dot`,\n        '[class.tds-badge-sup-size-sm]': `size === 'sm'`,\n        '[class.tds-badge-sup-size-md]': `size === 'md'`,\n        '[class.tds-badge-sup-size-lg]': `size === 'lg'`,\n        '[class.tds-badge-sup-size-xl]': `size === 'xl'`,\n        '[class.tds-badge-dot]': `dot`,\n        '[class.tds-badge-multiple-words]': `countArray.length >= 2`,\n        '[class.tds-badge-sup-standalone]': `standalone`,\n        '[class.tds-badge-sup-placement-top-left]': `placement === 'topLeft'`,\n        '[class.tds-badge-sup-placement-top-right]': `placement === 'topRight'`,\n        '[class.tds-badge-sup-placement-bottom-left]': `placement === 'bottomLeft'`,\n        '[class.tds-badge-sup-placement-bottom-right]': `placement === 'bottomRight'`,\n        'class': 'tds-scroll-number'\n        // '[class]': \"cls\"\n      }\n    }]\n  }], () => [], {\n    offset: [{\n      type: Input\n    }],\n    title: [{\n      type: Input\n    }],\n    tdsStyle: [{\n      type: Input\n    }],\n    dot: [{\n      type: Input\n    }],\n    overflowCount: [{\n      type: Input\n    }],\n    disableAnimation: [{\n      type: Input\n    }],\n    count: [{\n      type: Input\n    }],\n    noAnimation: [{\n      type: Input\n    }],\n    standalone: [{\n      type: Input\n    }],\n    size: [{\n      type: Input\n    }],\n    placement: [{\n      type: Input\n    }]\n  });\n})();\nconst TDS_CONFIG_MODULE_NAME = 'badge';\nclass TDSBadgeComponent {\n  constructor(tdsConfigService, renderer, cdr, elementRef, directionality) {\n    this.tdsConfigService = tdsConfigService;\n    this.renderer = renderer;\n    this.cdr = cdr;\n    this.elementRef = elementRef;\n    this.directionality = directionality;\n    this._tdsModuleName = TDS_CONFIG_MODULE_NAME;\n    this.showSup = false;\n    this.presetColor = null;\n    this.dir = 'ltr';\n    this.destroy$ = new Subject();\n    this.showZero = false;\n    this.showDot = true;\n    this.standalone = false;\n    this.dot = false;\n    this.overflowCount = 99;\n    this.color = undefined;\n    this.tdsStyle = null;\n    this.text = null;\n    this.tdsClass = \"\";\n    this.tdsTheme = 'default';\n    /**\n     * dùng cho status\n     */\n    this.size = 'md';\n    this.placement = 'topRight';\n    this.noAnimation = inject(TDSNoAnimationDirective, {\n      host: true,\n      optional: true\n    });\n  }\n  ngOnInit() {\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.prepareBadgeForRtl();\n      this.cdr.detectChanges();\n    });\n    this.dir = this.directionality.value;\n    this.prepareBadgeForRtl();\n  }\n  ngOnChanges(changes) {\n    const {\n      color,\n      showDot,\n      dot,\n      count,\n      showZero\n    } = changes;\n    if (color) {\n      this.presetColor = this.color && badgePresetColors.indexOf(this.color) !== -1 ? this.color : null;\n    }\n    if (showDot || dot || count || showZero) {\n      this.showSup = this.showDot && this.dot || typeof this.count === 'number' && this.count > 0 || typeof this.count === 'number' && this.count <= 0 && this.showZero;\n    }\n  }\n  prepareBadgeForRtl() {\n    if (this.isRtlLayout) {\n      this.renderer.addClass(this.elementRef.nativeElement, 'tds-badge-rtl');\n    } else {\n      this.renderer.removeClass(this.elementRef.nativeElement, 'tds-badge-rtl');\n    }\n  }\n  get isRtlLayout() {\n    return this.dir === 'rtl';\n  }\n  get colorStatus() {\n    if (!!this.status) {\n      return this.convertClass();\n    }\n    return null;\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  convertClass() {\n    let result = null;\n    switch (this.status) {\n      case 'primary':\n        result = 'bg-primary-1 dark:bg-d-primary-1';\n        break;\n      case 'secondary':\n        result = 'bg-neutral-1-400 dark:bg-d-neutral-1-400';\n        break;\n      case 'success':\n        result = 'bg-success-400 dark:bg-d-success-400';\n        break;\n      case 'info':\n        result = 'bg-info-400 dark:bg-d-info-400';\n        break;\n      case 'warning':\n        result = 'bg-warning-400 dark:bg-d-warning-400';\n        break;\n      case 'error':\n        result = 'bg-error-400 dark:bg-d-error-400';\n        break;\n      default:\n        result = this.status;\n        break;\n    }\n    return result;\n  }\n  static {\n    this.ɵfac = function TDSBadgeComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSBadgeComponent)(i0.ɵɵdirectiveInject(i1.TDSConfigService), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i2.Directionality));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TDSBadgeComponent,\n      selectors: [[\"tds-badge\"]],\n      hostAttrs: [1, \"tds-badge\"],\n      hostVars: 30,\n      hostBindings: function TDSBadgeComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"tds-badge-status\", ctx.status)(\"tds-badge-not-a-wrapper\", !!(ctx.standalone || ctx.status || ctx.color))(\"tds-badge-size-sm\", ctx.size === \"sm\")(\"tds-badge-size-md\", ctx.size === \"md\")(\"tds-badge-size-lg\", ctx.size === \"lg\")(\"tds-badge-size-xl\", ctx.size === \"xl\")(\"tds-badge-status-primary\", ctx.status == \"primary\")(\"tds-badge-status-secondary\", ctx.status == \"secondary\")(\"tds-badge-status-success\", ctx.status == \"success\")(\"tds-badge-status-info\", ctx.status == \"info\")(\"tds-badge-status-warning\", ctx.status == \"warning\")(\"tds-badge-status-error\", ctx.status == \"error\")(\"tds-badge-theme-default\", ctx.tdsTheme === \"default\")(\"tds-badge-theme-light\", ctx.tdsTheme === \"light\")(\"tds-badge-theme-dark\", ctx.tdsTheme === \"dark\");\n        }\n      },\n      inputs: {\n        showZero: \"showZero\",\n        showDot: \"showDot\",\n        standalone: \"standalone\",\n        dot: \"dot\",\n        overflowCount: \"overflowCount\",\n        color: \"color\",\n        tdsStyle: \"tdsStyle\",\n        text: \"text\",\n        title: \"title\",\n        status: \"status\",\n        count: \"count\",\n        offset: \"offset\",\n        tdsClass: \"tdsClass\",\n        tdsTheme: \"tdsTheme\",\n        size: \"size\",\n        placement: \"placement\"\n      },\n      exportAs: [\"tdsBadge\"],\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 3,\n      vars: 2,\n      consts: [[4, \"tdsStringTemplateOutlet\"], [1, \"tds-badge-status-dot\", 3, \"ngStyle\"], [1, \"tds-badge-status-text\"], [3, \"offset\", \"title\", \"tdsStyle\", \"dot\", \"size\", \"overflowCount\", \"disableAnimation\", \"count\", \"noAnimation\", \"standalone\", \"class\", \"placement\"], [3, \"offset\", \"title\", \"tdsStyle\", \"dot\", \"size\", \"overflowCount\", \"disableAnimation\", \"count\", \"noAnimation\", \"standalone\", \"placement\"]],\n      template: function TDSBadgeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, TDSBadgeComponent_Conditional_0_Template, 2, 4);\n          i0.ɵɵprojection(1);\n          i0.ɵɵtemplate(2, TDSBadgeComponent_ng_container_2_Template, 2, 1, \"ng-container\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵconditional((ctx.status || ctx.color && !ctx.standalone) && !ctx.dot ? 0 : -1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"tdsStringTemplateOutlet\", ctx.count);\n        }\n      },\n      dependencies: [NgStyle, TDSBadgeSupComponent, TDSOutletModule, i3.TDSStringTemplateOutletDirective],\n      encapsulation: 2,\n      data: {\n        animation: [zoomBadgeMotion]\n      },\n      changeDetection: 0\n    });\n  }\n}\n__decorate([InputBoolean()], TDSBadgeComponent.prototype, \"showZero\", void 0);\n__decorate([InputBoolean()], TDSBadgeComponent.prototype, \"showDot\", void 0);\n__decorate([InputBoolean()], TDSBadgeComponent.prototype, \"standalone\", void 0);\n__decorate([InputBoolean()], TDSBadgeComponent.prototype, \"dot\", void 0);\n__decorate([WithConfig()], TDSBadgeComponent.prototype, \"overflowCount\", void 0);\n__decorate([WithConfig()], TDSBadgeComponent.prototype, \"color\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSBadgeComponent, [{\n    type: Component,\n    args: [{\n      selector: 'tds-badge',\n      exportAs: 'tdsBadge',\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      animations: [zoomBadgeMotion],\n      standalone: true,\n      imports: [NgStyle, TDSBadgeSupComponent, TDSOutletModule],\n      template: `\n@if ((status || (color && !standalone)) && !dot) {\n  <span\n    class=\"tds-badge-status-dot\"\n    [style.background]=\"!presetColor && color\"\n    [ngStyle]=\"tdsStyle\"\n  ></span>\n  @if (text) {\n    <span class=\"tds-badge-status-text\">\n      <ng-container *tdsStringTemplateOutlet=\"text\">{{ text }}</ng-container>\n    </span>\n  }\n}\n<ng-content></ng-content>\n<ng-container *tdsStringTemplateOutlet=\"count\">\n  @if (showSup) {\n    <tds-badge-sup\n      [offset]=\"offset\"\n      [title]=\"title\"\n      [tdsStyle]=\"tdsStyle\"\n      [dot]=\"dot\"\n      [size]=\"size\"\n      [overflowCount]=\"overflowCount\"\n      [disableAnimation]=\"!!(standalone || status || color || noAnimation?.noAnimation)\"\n      [count]=\"count\"\n      [noAnimation]=\"!!noAnimation?.noAnimation\"\n      [standalone]=\"standalone\"\n      [class]=\"tdsClass\"\n      [placement]=\"placement\"\n    ></tds-badge-sup>\n  }\n</ng-container>\n\n\n\n`,\n      host: {\n        '[class.tds-badge-status]': 'status',\n        '[class.tds-badge-not-a-wrapper]': '!!(standalone || status || color)',\n        '[class.tds-badge-size-sm]': 'size === \"sm\"',\n        '[class.tds-badge-size-md]': 'size === \"md\"',\n        '[class.tds-badge-size-lg]': 'size === \"lg\"',\n        '[class.tds-badge-size-xl]': 'size === \"xl\"',\n        '[class.tds-badge-status-primary]': 'status ==\"primary\"',\n        '[class.tds-badge-status-secondary]': 'status ==\"secondary\"',\n        '[class.tds-badge-status-success]': 'status ==\"success\"',\n        '[class.tds-badge-status-info]': 'status ==\"info\"',\n        '[class.tds-badge-status-warning]': 'status ==\"warning\"',\n        '[class.tds-badge-status-error]': 'status ==\"error\"',\n        '[class.tds-badge-theme-default]': 'tdsTheme === \"default\"',\n        '[class.tds-badge-theme-light]': 'tdsTheme === \"light\"',\n        '[class.tds-badge-theme-dark]': 'tdsTheme === \"dark\"',\n        'class': \"tds-badge\"\n      }\n    }]\n  }], () => [{\n    type: i1.TDSConfigService\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i2.Directionality\n  }], {\n    showZero: [{\n      type: Input\n    }],\n    showDot: [{\n      type: Input\n    }],\n    standalone: [{\n      type: Input\n    }],\n    dot: [{\n      type: Input\n    }],\n    overflowCount: [{\n      type: Input\n    }],\n    color: [{\n      type: Input\n    }],\n    tdsStyle: [{\n      type: Input\n    }],\n    text: [{\n      type: Input\n    }],\n    title: [{\n      type: Input\n    }],\n    status: [{\n      type: Input\n    }],\n    count: [{\n      type: Input\n    }],\n    offset: [{\n      type: Input\n    }],\n    tdsClass: [{\n      type: Input\n    }],\n    tdsTheme: [{\n      type: Input\n    }],\n    size: [{\n      type: Input\n    }],\n    placement: [{\n      type: Input\n    }]\n  });\n})();\nclass TDSRibbonComponent {\n  constructor() {\n    this.placement = 'end';\n    this.text = null;\n    this.presetColor = null;\n  }\n  ngOnChanges(changes) {\n    const {\n      color\n    } = changes;\n    if (color) {\n      this.presetColor = this.color && badgePresetColors.indexOf(this.color) !== -1 ? this.color : null;\n    }\n  }\n  static {\n    this.ɵfac = function TDSRibbonComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSRibbonComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TDSRibbonComponent,\n      selectors: [[\"tds-ribbon\"]],\n      hostAttrs: [1, \"tds-ribbon-wrapper\"],\n      inputs: {\n        color: \"color\",\n        placement: \"placement\",\n        text: \"text\"\n      },\n      exportAs: [\"tdsRibbon\"],\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 4,\n      vars: 11,\n      consts: [[1, \"tds-ribbon\"], [4, \"tdsStringTemplateOutlet\"], [1, \"tds-ribbon-corner\"]],\n      template: function TDSRibbonComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n          i0.ɵɵelementStart(1, \"div\", 0);\n          i0.ɵɵtemplate(2, TDSRibbonComponent_ng_container_2_Template, 2, 1, \"ng-container\", 1);\n          i0.ɵɵelement(3, \"div\", 2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵclassMap(ctx.presetColor && \"tds-ribbon-color-\" + ctx.presetColor);\n          i0.ɵɵstyleProp(\"background-color\", !ctx.presetColor && ctx.color);\n          i0.ɵɵclassProp(\"tds-ribbon-placement-end\", ctx.placement === \"end\")(\"tds-ribbon-placement-start\", ctx.placement === \"start\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"tdsStringTemplateOutlet\", ctx.text);\n          i0.ɵɵadvance();\n          i0.ɵɵstyleProp(\"color\", !ctx.presetColor && ctx.color);\n        }\n      },\n      dependencies: [TDSOutletModule, i3.TDSStringTemplateOutletDirective],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSRibbonComponent, [{\n    type: Component,\n    args: [{\n      selector: 'tds-ribbon',\n      exportAs: 'tdsRibbon',\n      preserveWhitespaces: false,\n      standalone: true,\n      imports: [TDSOutletModule],\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    <ng-content></ng-content>\n    <div\n      class=\"tds-ribbon\"\n      [class]=\"presetColor && 'tds-ribbon-color-' + presetColor\"\n      [class.tds-ribbon-placement-end]=\"placement === 'end'\"\n      [class.tds-ribbon-placement-start]=\"placement === 'start'\"\n      [style.background-color]=\"!presetColor && color\"\n    >\n      <ng-container *tdsStringTemplateOutlet=\"text\">{{ text }}</ng-container>\n      <div class=\"tds-ribbon-corner\" [style.color]=\"!presetColor && color\"></div>\n    </div>\n  `,\n      host: {\n        'class': 'tds-ribbon-wrapper'\n      }\n    }]\n  }], () => [], {\n    color: [{\n      type: Input\n    }],\n    placement: [{\n      type: Input\n    }],\n    text: [{\n      type: Input\n    }]\n  });\n})();\nclass TDSBadgeModule {\n  static {\n    this.ɵfac = function TDSBadgeModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSBadgeModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: TDSBadgeModule,\n      imports: [TDSBadgeComponent, TDSBadgeSupComponent, TDSRibbonComponent],\n      exports: [TDSBadgeComponent, TDSRibbonComponent, TDSBadgeSupComponent]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [TDSBadgeComponent, TDSRibbonComponent]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSBadgeModule, [{\n    type: NgModule,\n    args: [{\n      imports: [TDSBadgeComponent, TDSBadgeSupComponent, TDSRibbonComponent],\n      exports: [TDSBadgeComponent, TDSRibbonComponent, TDSBadgeSupComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TDSBadgeComponent, TDSBadgeModule, TDSBadgeSupComponent, TDSRibbonComponent, badgePresetColors };\n", "import * as i0 from '@angular/core';\nimport { EventEmitter, Directive, ViewChild, TemplateRef, Input, Output, Component, ChangeDetectionStrategy, ViewEncapsulation, NgModule } from '@angular/core';\nimport { _getEventTarget } from '@angular/cdk/platform';\nimport { Subject, asapScheduler } from 'rxjs';\nimport { distinctUntilChanged, takeUntil, filter, delay } from 'rxjs/operators';\nimport { POSITION_MAP, DEFAULT_TOOLTIP_POSITIONS, getPlacementName, TDSOverlayModule } from 'tds-ui/core/overlay';\nimport { toBoolean, TDSHelperObject } from 'tds-ui/shared/utility';\nimport { zoomBigMotion } from 'tds-ui/core/animation';\nimport { NgClass, NgStyle } from '@angular/common';\nimport * as i2 from '@angular/cdk/overlay';\nimport { OverlayModule } from '@angular/cdk/overlay';\nimport { TDSNoAnimationDirective } from 'tds-ui/core/no-animation';\nimport * as i3 from 'tds-ui/core/outlet';\nimport { TDSOutletModule } from 'tds-ui/core/outlet';\nimport * as i1 from '@angular/cdk/bidi';\nconst _c0 = [\"overlay\"];\nfunction TDSToolTipComponent_ng_template_0_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.title);\n  }\n}\nfunction TDSToolTipComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4);\n    i0.ɵɵelement(3, \"span\", 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 6);\n    i0.ɵɵtemplate(5, TDSToolTipComponent_ng_template_0_ng_container_5_Template, 2, 1, \"ng-container\", 7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"tds-tooltip-custom\", ctx_r1.tooltipArrowCss || ctx_r1.tooltipContentCss)(\"tds-tooltip-default\", !ctx_r1.color)(\"tds-tooltip-info\", ctx_r1.color == \"info\")(\"tds-tooltip-success\", ctx_r1.color == \"success\")(\"tds-tooltip-error\", ctx_r1.color == \"error\")(\"tds-tooltip-warning\", ctx_r1.color == \"warning\");\n    i0.ɵɵproperty(\"ngClass\", ctx_r1._classMap)(\"ngStyle\", ctx_r1.overlayStyle)(\"@zoomBigMotion\", \"active\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", ctx_r1._contentStyleArrow);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.tooltipArrowCss)(\"ngStyle\", ctx_r1._contentStyleArrowContent);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.tooltipContentCss);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"tdsStringTemplateOutlet\", ctx_r1.title);\n  }\n}\nclass TDSTooltipBaseDirective {\n  /**\n   * This true title that would be used in other parts on this component.\n   */\n  get _title() {\n    return this.title || this.directiveTitle || null;\n  }\n  get _content() {\n    return this.content || this.directiveContent || null;\n  }\n  get _footer() {\n    return this.footer || this.directiveFooter || null;\n  }\n  get _trigger() {\n    return typeof this.trigger !== 'undefined' ? this.trigger : 'hover';\n  }\n  get _placement() {\n    const p = this.placement;\n    return Array.isArray(p) && p.length > 0 ? p : typeof p === 'string' && p ? [p] : ['top'];\n  }\n  get _visible() {\n    return (typeof this.visible !== 'undefined' ? this.visible : this.internalVisible) || false;\n  }\n  get _mouseEnterDelay() {\n    return this.mouseEnterDelay || 0.15;\n  }\n  get _mouseLeaveDelay() {\n    return this.mouseLeaveDelay || 0.1;\n  }\n  get _overlayClassName() {\n    return this.overlayClassName || null;\n  }\n  get _overlayStyle() {\n    return this.overlayStyle || null;\n  }\n  get _autoClose() {\n    return this.autoClose != null ? this.autoClose : true;\n  }\n  getProxyPropertyMap() {\n    return {\n      noAnimation: ['noAnimation', () => true]\n    };\n  }\n  constructor(elementRef, hostView, renderer) {\n    this.elementRef = elementRef;\n    this.hostView = hostView;\n    this.renderer = renderer;\n    this.autoClose = null;\n    this.visibleChange = new EventEmitter();\n    this.internalVisible = false;\n    this.destroy$ = new Subject();\n    this.triggerDisposables = [];\n  }\n  ngOnChanges(changes) {\n    const {\n      trigger\n    } = changes;\n    if (trigger && !trigger.isFirstChange()) {\n      this.registerTriggers();\n    }\n    if (this.component) {\n      this.updatePropertiesByChanges(changes);\n    }\n  }\n  ngAfterViewInit() {\n    this.createComponent();\n    this.registerTriggers();\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n    // Clear toggling timer. Issue #3875 #4317 #4386\n    this.clearTogglingTimer();\n    this.removeTriggerListeners();\n  }\n  show() {\n    this.component?.show();\n  }\n  hide() {\n    this.component?.hide();\n  }\n  /**\n   * Force the component to update its position.\n   */\n  updatePosition() {\n    if (this.component) {\n      this.component.updatePosition();\n    }\n  }\n  /**\n   * Create a dynamic tooltip component. This method can be override.\n   */\n  createComponent() {\n    const componentRef = this.componentRef;\n    this.component = componentRef.instance;\n    // Remove the component's DOM because it should be in the overlay container.\n    this.renderer.removeChild(this.renderer.parentNode(this.elementRef.nativeElement), componentRef.location.nativeElement);\n    this.component.setOverlayOrigin(this.origin || this.elementRef);\n    this.initProperties();\n    const ngVisibleChange$ = this.component.visibleChange.pipe(distinctUntilChanged());\n    ngVisibleChange$.pipe(takeUntil(this.destroy$)).subscribe(visible => {\n      this.internalVisible = visible;\n      this.visibleChange.emit(visible);\n    });\n    // In some cases, the rendering takes into account the height at which the `arrow` is in wrong place,\n    // so `cdk` sets the container position incorrectly.\n    // To avoid this, after placing the `arrow` in the correct position, we should `re-calculate` the position of the `overlay`.\n    ngVisibleChange$.pipe(filter(visible => visible), delay(0, asapScheduler), filter(() => Boolean(this.component?.overlay?.overlayRef)), takeUntil(this.destroy$)).subscribe(() => {\n      this.component?.updatePosition();\n    });\n  }\n  registerTriggers() {\n    // When the method gets invoked, all properties has been synced to the dynamic component.\n    // After removing the old API, we can just check the directive's own `trigger`.\n    const el = this.elementRef.nativeElement;\n    const trigger = this.trigger;\n    this.removeTriggerListeners();\n    if (trigger === 'hover') {\n      let overlayElement;\n      this.triggerDisposables.push(this.renderer.listen(el, 'mouseenter', () => {\n        this.delayEnterLeave(true, true, this._mouseEnterDelay);\n      }));\n      this.triggerDisposables.push(this.renderer.listen(el, 'mouseleave', () => {\n        this.delayEnterLeave(true, false, this._mouseLeaveDelay);\n        if (this.component?.overlay.overlayRef && !overlayElement) {\n          overlayElement = this.component.overlay.overlayRef.overlayElement;\n          this.triggerDisposables.push(this.renderer.listen(overlayElement, 'mouseenter', () => {\n            this.delayEnterLeave(false, true, this._mouseEnterDelay);\n          }));\n          this.triggerDisposables.push(this.renderer.listen(overlayElement, 'mouseleave', () => {\n            this.delayEnterLeave(false, false, this._mouseLeaveDelay);\n          }));\n        }\n      }));\n    } else if (trigger === 'focus') {\n      this.triggerDisposables.push(this.renderer.listen(el, 'focus', () => this.show()));\n      this.triggerDisposables.push(this.renderer.listen(el, 'blur', () => this.hide()));\n    } else if (trigger === 'click') {\n      this.triggerDisposables.push(this.renderer.listen(el, 'click', e => {\n        e.preventDefault();\n        this.show();\n      }));\n    }\n    // Else do nothing because user wants to control the visibility programmatically.\n  }\n  updatePropertiesByChanges(changes) {\n    this.updatePropertiesByKeys(Object.keys(changes));\n  }\n  updatePropertiesByKeys(keys) {\n    const mappingProperties = {\n      // common mappings\n      title: ['title', () => this._title],\n      directiveTitle: ['title', () => this._title],\n      content: ['content', () => this._content],\n      directiveContent: ['content', () => this._content],\n      footer: ['footer', () => this._footer],\n      directiveFooter: ['footer', () => this._footer],\n      trigger: ['trigger', () => this._trigger],\n      placement: ['placement', () => this._placement],\n      visible: ['visible', () => this._visible],\n      mouseEnterDelay: ['mouseEnterDelay', () => this._mouseEnterDelay],\n      mouseLeaveDelay: ['mouseLeaveDelay', () => this._mouseLeaveDelay],\n      overlayClassName: ['overlayClassName', () => this._overlayClassName],\n      overlayStyle: ['overlayStyle', () => this._overlayStyle],\n      autoClose: ['autoClose', () => this._autoClose],\n      ...this.getProxyPropertyMap()\n    };\n    (keys || Object.keys(mappingProperties).filter(key => !key.startsWith('directive'))).forEach(property => {\n      if (mappingProperties[property]) {\n        const [name, valueFn] = mappingProperties[property];\n        this.updateComponentValue(name, valueFn());\n      }\n    });\n    this.component?.updateByDirective();\n  }\n  initProperties() {\n    this.updatePropertiesByKeys();\n  }\n  updateComponentValue(key, value) {\n    if (typeof value !== 'undefined') {\n      // @ts-ignore\n      this.component[key] = value;\n    }\n  }\n  delayEnterLeave(isOrigin, isEnter, delay = -1) {\n    if (this.delayTimer) {\n      this.clearTogglingTimer();\n    } else if (delay > 0) {\n      this.delayTimer = setTimeout(() => {\n        this.delayTimer = undefined;\n        isEnter ? this.show() : this.hide();\n      }, delay * 1000);\n    } else {\n      // `isOrigin` is used due to the tooltip will not hide immediately\n      // (may caused by the fade-out animation).\n      isEnter && isOrigin ? this.show() : this.hide();\n    }\n  }\n  removeTriggerListeners() {\n    this.triggerDisposables.forEach(dispose => dispose());\n    this.triggerDisposables.length = 0;\n  }\n  clearTogglingTimer() {\n    if (this.delayTimer) {\n      clearTimeout(this.delayTimer);\n      this.delayTimer = undefined;\n    }\n  }\n  static {\n    this.ɵfac = function TDSTooltipBaseDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSTooltipBaseDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i0.Renderer2));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TDSTooltipBaseDirective,\n      features: [i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSTooltipBaseDirective, [{\n    type: Directive\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.ViewContainerRef\n  }, {\n    type: i0.Renderer2\n  }], null);\n})();\n// tslint:disable-next-line:directive-class-suffix\nclass TDSTooltipBaseComponent {\n  set visible(value) {\n    const visible = toBoolean(value);\n    if (this._visible !== visible) {\n      this._visible = visible;\n      this.visibleChange.next(visible);\n    }\n  }\n  get visible() {\n    return this._visible;\n  }\n  set trigger(value) {\n    this._trigger = value;\n  }\n  get trigger() {\n    return this._trigger;\n  }\n  set placement(value) {\n    const preferredPosition = value.map(placement => POSITION_MAP[placement]);\n    this._positions = [...preferredPosition, ...DEFAULT_TOOLTIP_POSITIONS];\n  }\n  constructor(cdr) {\n    this.cdr = cdr;\n    this.title = null;\n    this.content = null;\n    this.footer = null;\n    this.overlayStyle = {};\n    this.backdrop = false;\n    this.autoClose = true;\n    this.visibleChange = new Subject();\n    this._visible = false;\n    this._trigger = 'hover';\n    this.preferredPlacement = 'top';\n    this.dir = 'ltr';\n    this._classMap = {};\n    this._prefix = 'tds-tooltip';\n    this._positions = [...DEFAULT_TOOLTIP_POSITIONS];\n    this.destroy$ = new Subject();\n  }\n  ngOnInit() {}\n  ngOnDestroy() {\n    this.visibleChange.complete();\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  show() {\n    if (this.visible) {\n      return;\n    }\n    if (!this.isEmpty()) {\n      this.visible = true;\n      this.visibleChange.next(true);\n      this.cdr.detectChanges();\n    }\n    // for ltr for overlay to display tooltip in correct placement in rtl direction.\n    // if (this.origin && this.overlay && this.overlay.overlayRef && this.overlay.overlayRef.getDirection() === 'rtl') {\n    //   this.overlay.overlayRef.setDirection('ltr');\n    // }\n  }\n  hide() {\n    if (!this.visible) {\n      return;\n    }\n    this.visible = false;\n    this.visibleChange.next(false);\n    this.cdr.detectChanges();\n  }\n  updateByDirective() {\n    this.updateStyles();\n    this.cdr.detectChanges();\n    Promise.resolve().then(() => {\n      this.updatePosition();\n      this.updateVisibilityByTitle();\n    });\n  }\n  /**\n   * Force the component to update its position.\n   */\n  updatePosition() {\n    if (this.origin && this.overlay && this.overlay.overlayRef) {\n      this.overlay.overlayRef.updatePosition();\n    }\n  }\n  onPositionChange(position) {\n    this.preferredPlacement = getPlacementName(position);\n    this.updateStyles();\n    // We have to trigger immediate change detection or the element would blink.\n    this.cdr.detectChanges();\n  }\n  updateStyles() {\n    this._classMap = {\n      [this.overlayClassName]: true,\n      [`${this._prefix}-placement-${this.preferredPlacement}`]: true\n    };\n  }\n  setOverlayOrigin(origin) {\n    this.origin = origin;\n    this.cdr.markForCheck();\n  }\n  onClickOutside(event) {\n    const target = _getEventTarget(event);\n    if (!this.origin.nativeElement.contains(target) && this.trigger !== null) {\n      this.hide();\n    }\n  }\n  /**\n   * Hide the component while the content is empty.\n   */\n  updateVisibilityByTitle() {\n    if (this.isEmpty()) {\n      this.hide();\n    }\n  }\n  static {\n    this.ɵfac = function TDSTooltipBaseComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSTooltipBaseComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TDSTooltipBaseComponent,\n      viewQuery: function TDSTooltipBaseComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.overlay = _t.first);\n        }\n      }\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSTooltipBaseComponent, [{\n    type: Directive\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }], {\n    overlay: [{\n      type: ViewChild,\n      args: ['overlay', {\n        static: false\n      }]\n    }]\n  });\n})();\nfunction isTooltipEmpty(value) {\n  return value instanceof TemplateRef ? false : value === '' || !TDSHelperObject.hasValue(value);\n}\nclass TDSTooltipDirective extends TDSTooltipBaseDirective {\n  constructor(elementRef, hostView, renderer) {\n    super(elementRef, hostView, renderer);\n    this.trigger = 'hover';\n    this.placement = 'top';\n    // tslint:disable-next-line:no-output-rename\n    this.visibleChange = new EventEmitter();\n    this.componentRef = this.hostView.createComponent(TDSToolTipComponent);\n  }\n  getProxyPropertyMap() {\n    return {\n      tooltipCss: ['tooltipContentCss', () => this.tooltipContentCss],\n      tooltipArrowCss: ['tooltipArrowCss', () => this.tooltipArrowCss],\n      tooltipColor: ['color', () => this.tooltipColor]\n    };\n  }\n  static {\n    this.ɵfac = function TDSTooltipDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSTooltipDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i0.Renderer2));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TDSTooltipDirective,\n      selectors: [[\"\", \"tds-tooltip\", \"\"]],\n      hostVars: 2,\n      hostBindings: function TDSTooltipDirective_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"tds-tooltip-open\", ctx.visible);\n        }\n      },\n      inputs: {\n        title: [0, \"tooltipTitle\", \"title\"],\n        directiveTitle: [0, \"tds-tooltip\", \"directiveTitle\"],\n        trigger: [0, \"tooltipTrigger\", \"trigger\"],\n        placement: [0, \"tooltipPlacement\", \"placement\"],\n        origin: [0, \"tooltipOrigin\", \"origin\"],\n        visible: [0, \"tooltipVisible\", \"visible\"],\n        mouseEnterDelay: [0, \"tooltipMouseEnterDelay\", \"mouseEnterDelay\"],\n        mouseLeaveDelay: [0, \"tooltipMouseLeaveDelay\", \"mouseLeaveDelay\"],\n        overlayClassName: [0, \"tooltipOverlayClassName\", \"overlayClassName\"],\n        overlayStyle: [0, \"tooltipOverlayStyle\", \"overlayStyle\"],\n        tooltipColor: \"tooltipColor\",\n        tooltipContentCss: \"tooltipContentCss\",\n        tooltipArrowCss: \"tooltipArrowCss\"\n      },\n      outputs: {\n        visibleChange: \"tooltipVisibleChange\"\n      },\n      exportAs: [\"tdsTooltip\"],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSTooltipDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[tds-tooltip]',\n      exportAs: 'tdsTooltip',\n      host: {\n        '[class.tds-tooltip-open]': 'visible'\n      },\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.ViewContainerRef\n  }, {\n    type: i0.Renderer2\n  }], {\n    title: [{\n      type: Input,\n      args: ['tooltipTitle']\n    }],\n    directiveTitle: [{\n      type: Input,\n      args: ['tds-tooltip']\n    }],\n    trigger: [{\n      type: Input,\n      args: ['tooltipTrigger']\n    }],\n    placement: [{\n      type: Input,\n      args: ['tooltipPlacement']\n    }],\n    origin: [{\n      type: Input,\n      args: ['tooltipOrigin']\n    }],\n    visible: [{\n      type: Input,\n      args: ['tooltipVisible']\n    }],\n    mouseEnterDelay: [{\n      type: Input,\n      args: ['tooltipMouseEnterDelay']\n    }],\n    mouseLeaveDelay: [{\n      type: Input,\n      args: ['tooltipMouseLeaveDelay']\n    }],\n    overlayClassName: [{\n      type: Input,\n      args: ['tooltipOverlayClassName']\n    }],\n    overlayStyle: [{\n      type: Input,\n      args: ['tooltipOverlayStyle']\n    }],\n    tooltipColor: [{\n      type: Input\n    }],\n    tooltipContentCss: [{\n      type: Input\n    }],\n    tooltipArrowCss: [{\n      type: Input\n    }],\n    visibleChange: [{\n      type: Output,\n      args: ['tooltipVisibleChange']\n    }]\n  });\n})();\nclass TDSToolTipComponent extends TDSTooltipBaseComponent {\n  constructor(cdr, directionality) {\n    super(cdr);\n    this.title = null;\n    this.tooltipContentCss = null;\n    this.tooltipArrowCss = null;\n    this._contentStyleMap = {};\n    this._contentStyleArrowContent = {};\n    this._contentStyleArrow = {};\n  }\n  isEmpty() {\n    return isTooltipEmpty(this.title);\n  }\n  updateStyles() {\n    const isColorPreset = this.color;\n    this._classMap = {\n      [this.overlayClassName]: true,\n      [`${this._prefix}-placement-${this.preferredPlacement}`]: true\n    };\n    //lấy mặc định or custom\n    // this._tooltipCss = this.tooltipContentCss!;\n    // this._tooltipArrowCss = this.tooltipArrowCss!;\n    this.p_StyleArrow();\n  }\n  p_StyleArrow() {\n    let borderTransparent = '4px solid transparent';\n    // let borderColor = '4px solid';\n    if (this.preferredPlacement.indexOf(\"top\") > -1) {\n      this._contentStyleArrowContent = {\n        width: '0',\n        height: '0',\n        borderLeft: borderTransparent,\n        borderRight: borderTransparent,\n        borderBottom: 0\n        // borderTop: borderColor,\n      };\n    }\n    if (this.preferredPlacement.indexOf(\"bottom\") > -1) {\n      this._contentStyleArrowContent = {\n        width: '0',\n        height: '0',\n        borderLeft: borderTransparent,\n        borderRight: borderTransparent,\n        borderTop: 0\n        // borderBottom: borderColor,        \n      };\n    }\n    if (this.preferredPlacement.indexOf(\"left\") > -1) {\n      this._contentStyleArrowContent = {\n        width: '0',\n        height: '0',\n        borderTop: borderTransparent,\n        // borderLeft : borderColor,\n        borderBottom: borderTransparent,\n        borderRight: 0\n      };\n    }\n    if (this.preferredPlacement.indexOf(\"right\") > -1) {\n      this._contentStyleArrowContent = {\n        width: '0',\n        height: '0',\n        borderTop: borderTransparent,\n        // borderRight : borderColor,\n        borderBottom: borderTransparent,\n        borderLeft: 0\n      };\n    }\n    switch (this.preferredPlacement) {\n      //#region top\n      case \"top\":\n        this._contentStyleArrow = {\n          bottom: '0',\n          left: '50%',\n          transform: 'translateX(-50%)'\n        };\n        break;\n      case \"topLeft\":\n        this._contentStyleArrow = {\n          bottom: '0',\n          left: '10px'\n        };\n        break;\n      case \"topRight\":\n        this._contentStyleArrow = {\n          bottom: '0',\n          right: '10px'\n        };\n        break;\n      //#endregion\n      //#region bottom\n      case \"bottom\":\n        this._contentStyleArrow = {\n          top: '0',\n          left: '50%',\n          transform: 'translateX(-50%)'\n        };\n        break;\n      case \"bottomLeft\":\n        this._contentStyleArrow = {\n          top: '0',\n          left: '10px'\n        };\n        break;\n      case \"bottomRight\":\n        this._contentStyleArrow = {\n          top: '0',\n          right: '10px'\n        };\n        break;\n      //#endregion\n      //#region left\n      case \"left\":\n        this._contentStyleArrow = {\n          right: '0px',\n          top: '50%',\n          transform: 'translateY(-50%)'\n        };\n        break;\n      case \"leftTop\":\n        this._contentStyleArrow = {\n          right: '0px',\n          top: '10px'\n        };\n        break;\n      case \"leftBottom\":\n        this._contentStyleArrow = {\n          right: '0px',\n          bottom: '10px'\n        };\n        break;\n      //#endregion\n      //#region right\n      case \"right\":\n        this._contentStyleArrow = {\n          left: '0px',\n          top: '50%',\n          transform: 'translateY(-50%)'\n        };\n        break;\n      case \"rightTop\":\n        this._contentStyleArrow = {\n          left: '0px',\n          top: '10px'\n        };\n        break;\n      case \"rightBottom\":\n        this._contentStyleArrow = {\n          left: '0px',\n          bottom: '10px'\n        };\n        break;\n      ////#endregion\n      default:\n        this._contentStyleArrow = {\n          bottom: '0',\n          left: '50%',\n          transform: 'translateX(-50%)'\n        };\n        break;\n    }\n  }\n  static {\n    this.ɵfac = function TDSToolTipComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSToolTipComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.Directionality));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TDSToolTipComponent,\n      selectors: [[\"tds-tooltip\"]],\n      exportAs: [\"TDSTooltipComponent\"],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 4,\n      consts: [[\"overlay\", \"cdkConnectedOverlay\"], [\"cdkConnectedOverlay\", \"\", 3, \"overlayOutsideClick\", \"detach\", \"positionChange\", \"cdkConnectedOverlayOrigin\", \"cdkConnectedOverlayOpen\", \"cdkConnectedOverlayPositions\", \"cdkConnectedOverlayPush\"], [1, \"tds-tooltip\", 3, \"ngClass\", \"ngStyle\"], [1, \"tds-tooltip-content\"], [1, \"tds-tooltip-arrow\", 3, \"ngStyle\"], [1, \"tds-tooltip-arrow-content\", 3, \"ngClass\", \"ngStyle\"], [1, \"tds-tooltip-inner\", 3, \"ngClass\"], [4, \"tdsStringTemplateOutlet\"]],\n      template: function TDSToolTipComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵtemplate(0, TDSToolTipComponent_ng_template_0_Template, 6, 20, \"ng-template\", 1, 0, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵlistener(\"overlayOutsideClick\", function TDSToolTipComponent_Template_ng_template_overlayOutsideClick_0_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onClickOutside($event));\n          })(\"detach\", function TDSToolTipComponent_Template_ng_template_detach_0_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.hide());\n          })(\"positionChange\", function TDSToolTipComponent_Template_ng_template_positionChange_0_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onPositionChange($event));\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"cdkConnectedOverlayOrigin\", ctx.origin)(\"cdkConnectedOverlayOpen\", ctx._visible)(\"cdkConnectedOverlayPositions\", ctx._positions)(\"cdkConnectedOverlayPush\", true);\n        }\n      },\n      dependencies: [NgClass, NgStyle, OverlayModule, i2.CdkConnectedOverlay, TDSOutletModule, i3.TDSStringTemplateOutletDirective, TDSOverlayModule],\n      encapsulation: 2,\n      data: {\n        animation: [zoomBigMotion]\n      },\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSToolTipComponent, [{\n    type: Component,\n    args: [{\n      selector: 'tds-tooltip',\n      exportAs: 'TDSTooltipComponent',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      animations: [zoomBigMotion],\n      template: `\n    <ng-template\n      #overlay=\"cdkConnectedOverlay\"\n      cdkConnectedOverlay\n      [cdkConnectedOverlayOrigin]=\"origin\"\n      [cdkConnectedOverlayOpen]=\"_visible\"\n      [cdkConnectedOverlayPositions]=\"_positions\"\n      [cdkConnectedOverlayPush]=\"true\"\n      (overlayOutsideClick)=\"onClickOutside($event)\"\n      (detach)=\"hide()\"\n      (positionChange)=\"onPositionChange($event)\"\n    >\n      <div\n        class=\"tds-tooltip\"\n        [class.tds-tooltip-custom]='tooltipArrowCss || tooltipContentCss'\n        [class.tds-tooltip-default]='!color'\n        [class.tds-tooltip-info]='color == \"info\"'\n        [class.tds-tooltip-success]='color == \"success\"' \n        [class.tds-tooltip-error]='color == \"error\"'         \n        [class.tds-tooltip-warning]='color == \"warning\"' \n        [ngClass]=\"_classMap\"\n        [ngStyle]=\"overlayStyle\"     \n        [@zoomBigMotion]=\"'active'\"\n      >\n        <div class=\"tds-tooltip-content \">\n          <div class=\"tds-tooltip-arrow \"\n          [ngStyle]=\"_contentStyleArrow\" >\n            <span class=\"tds-tooltip-arrow-content\" [ngClass]='tooltipArrowCss!' [ngStyle]=\"_contentStyleArrowContent\"></span>\n          </div>\n          <div class=\"tds-tooltip-inner\" [ngClass]=\"tooltipContentCss!\">\n            <ng-container *tdsStringTemplateOutlet=\"title\">{{ title }}</ng-container>\n          </div>\n        </div>\n      </div>\n    </ng-template>\n  `,\n      preserveWhitespaces: false,\n      standalone: true,\n      imports: [NgClass, NgStyle, OverlayModule, TDSNoAnimationDirective, TDSOutletModule, TDSOverlayModule]\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i1.Directionality\n  }], null);\n})();\nclass TDSToolTipModule {\n  static {\n    this.ɵfac = function TDSToolTipModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSToolTipModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: TDSToolTipModule,\n      imports: [TDSToolTipComponent, TDSTooltipDirective],\n      exports: [TDSToolTipComponent, TDSTooltipDirective]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [TDSToolTipComponent]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSToolTipModule, [{\n    type: NgModule,\n    args: [{\n      imports: [TDSToolTipComponent, TDSTooltipDirective],\n      exports: [TDSToolTipComponent, TDSTooltipDirective]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TDSToolTipComponent, TDSToolTipModule, TDSTooltipBaseComponent, TDSTooltipBaseDirective, TDSTooltipDirective, isTooltipEmpty };\n", "import { __decorate } from 'tslib';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Injectable, SkipSelf, Optional, Inject, Directive, Input, ContentChildren, EventEmitter, Component, ViewEncapsulation, ChangeDetectionStrategy, Output, ElementRef, Host, ViewChild, forwardRef, NgModule } from '@angular/core';\nimport { Subject, BehaviorSubject, merge, combineLatest } from 'rxjs';\nimport { map, mergeMap, filter, mapTo, auditTime, distinctUntilChanged, takeUntil, startWith, switchMap } from 'rxjs/operators';\nimport { InputBoolean } from 'tds-ui/shared/utility';\nimport * as i4 from '@angular/router';\nimport { NavigationEnd, RouterLink, RouterLinkWithHref } from '@angular/router';\nimport * as i1 from '@angular/cdk/bidi';\nimport * as i6 from '@angular/cdk/overlay';\nimport { CdkOverlayOrigin, OverlayModule } from '@angular/cdk/overlay';\nimport { POSITION_MAP, getPlacementName } from 'tds-ui/core/overlay';\nimport * as i2 from 'tds-ui/core/outlet';\nimport { TDSOutletModule } from 'tds-ui/core/outlet';\nimport { collapseMotion, zoomBigMotion, slideMotion } from 'tds-ui/core/animation';\nimport { NgTemplateOutlet, NgClass } from '@angular/common';\nimport * as i3 from '@angular/cdk/platform';\nimport * as i5 from 'tds-ui/core/no-animation';\nconst _c0 = [\"tds-subnavbar-title\", \"\"];\nconst _c1 = [\"*\"];\nfunction TDSSubNavBarTitleComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 5);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r0.tdsIcon);\n  }\n}\nfunction TDSSubNavBarTitleComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 1);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"innerHtml\", ctx_r0.tdsHtmlIcon, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction TDSSubNavBarTitleComponent_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 6);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.tdsTitle);\n  }\n}\nfunction TDSSubNavBarTitleComponent_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 3);\n  }\n}\nfunction TDSSubNavBarTitleComponent_Conditional_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 4);\n  }\n}\nconst _c2 = [\"tds-subnavbar-inline-child\", \"\"];\nfunction TDSSubNavBarInlineChildComponent_ng_template_0_Template(rf, ctx) {}\nconst _c3 = [\"tds-subnavbar-none-inline-child\", \"\"];\nfunction TDSSubNavBarNoneInlineChildComponent_ng_template_1_Template(rf, ctx) {}\nconst _c4 = [\"tds-subnavbar\", \"\"];\nconst _c5 = [[[\"\", \"title\", \"\"]], \"*\"];\nconst _c6 = [\"[title]\", \"*\"];\nfunction TDSSubNavBarComponent_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n  }\n}\nfunction TDSSubNavBarComponent_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 3);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    const subMenuTemplate_r3 = i0.ɵɵreference(6);\n    i0.ɵɵproperty(\"mode\", ctx_r1.mode)(\"tdsOpen\", ctx_r1.tdsOpen)(\"navBarClass\", ctx_r1.tdsNavBarClassName)(\"templateOutlet\", subMenuTemplate_r3);\n  }\n}\nfunction TDSSubNavBarComponent_Conditional_4_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵlistener(\"subMenuMouseState\", function TDSSubNavBarComponent_Conditional_4_ng_template_0_Template_div_subMenuMouseState_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.setMouseEnterState($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    const subMenuTemplate_r3 = i0.ɵɵreference(6);\n    i0.ɵɵproperty(\"theme\", ctx_r1.theme)(\"mode\", ctx_r1.mode)(\"tdsOpen\", ctx_r1.tdsOpen)(\"position\", ctx_r1.position)(\"tdsDisabled\", ctx_r1.tdsDisabled)(\"isNavBarInsideDropDown\", ctx_r1.isNavBarInsideDropDown)(\"templateOutlet\", subMenuTemplate_r3)(\"navBarClass\", ctx_r1.tdsNavBarClassName);\n  }\n}\nfunction TDSSubNavBarComponent_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵtemplate(0, TDSSubNavBarComponent_Conditional_4_ng_template_0_Template, 1, 8, \"ng-template\", 5);\n    i0.ɵɵlistener(\"positionChange\", function TDSSubNavBarComponent_Conditional_4_Template_ng_template_positionChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onPositionChange($event));\n    });\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    const origin_r6 = i0.ɵɵreference(1);\n    i0.ɵɵproperty(\"cdkConnectedOverlayPositions\", ctx_r1.overlayPositions)(\"cdkConnectedOverlayOrigin\", origin_r6)(\"cdkConnectedOverlayWidth\", ctx_r1.triggerWidth)(\"cdkConnectedOverlayOpen\", ctx_r1.tdsOpen)(\"cdkConnectedOverlayTransformOriginOn\", \".tds-navbar-subnavbar\");\n  }\n}\nfunction TDSSubNavBarComponent_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0, 1);\n  }\n}\nconst _c7 = [\"titleElement\"];\nconst _c8 = [\"tds-navbar-group\", \"\"];\nconst _c9 = [\"*\", [[\"\", \"title\", \"\"]]];\nconst _c10 = [\"*\", \"[title]\"];\nfunction TDSNavBarGroupComponent_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.tdsTitle);\n  }\n}\nfunction TDSNavBarGroupComponent_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0, 1);\n  }\n}\nconst TDSIsNavBarInsideDropDownToken = new InjectionToken('TDSIsInDropDownNavBarToken');\nconst TDSNavBarServiceLocalToken = new InjectionToken('TDSNavBarServiceLocalToken');\nclass NavBarService {\n  constructor() {\n    /** all descendant navbar click **/\n    this.descendantMenuItemClick$ = new Subject();\n    /** child navbar item click **/\n    this.childMenuItemClick$ = new Subject();\n    this.theme$ = new BehaviorSubject('light');\n    this.mode$ = new BehaviorSubject('vertical');\n    this.inlineIndent$ = new BehaviorSubject(24);\n    this.isChildSubMenuOpen$ = new BehaviorSubject(false);\n  }\n  onDescendantMenuItemClick(navbar) {\n    this.descendantMenuItemClick$.next(navbar);\n  }\n  onChildMenuItemClick(navbar) {\n    this.childMenuItemClick$.next(navbar);\n  }\n  setMode(mode) {\n    this.mode$.next(mode);\n  }\n  setTheme(theme) {\n    this.theme$.next(theme);\n  }\n  setInlineIndent(indent) {\n    this.inlineIndent$.next(indent);\n  }\n  static {\n    this.ɵfac = function NavBarService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NavBarService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: NavBarService,\n      factory: NavBarService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NavBarService, [{\n    type: Injectable\n  }], null, null);\n})();\nclass TDSSubNavBarService {\n  /**\n   * navbar item inside submenu clicked\n   *\n   * @param navbar\n   */\n  onChildMenuItemClick(navbar) {\n    this.childMenuItemClick$.next(navbar);\n  }\n  setOpenStateWithoutDebounce(value) {\n    this.isCurrentSubMenuOpen$.next(value);\n  }\n  setMouseEnterTitleOrOverlayState(value) {\n    this.isMouseEnterTitleOrOverlay$.next(value);\n  }\n  constructor(nzHostSubmenuService, tdsNavBarService, isNavBarInsideDropDown) {\n    this.nzHostSubmenuService = nzHostSubmenuService;\n    this.tdsNavBarService = tdsNavBarService;\n    this.isNavBarInsideDropDown = isNavBarInsideDropDown;\n    this.mode$ = this.tdsNavBarService.mode$.pipe(map(mode => {\n      if (mode === 'inline') {\n        return 'inline';\n        /** if inside another submenu, set the mode to vertical **/\n      } else if (mode === 'vertical' || this.nzHostSubmenuService) {\n        return 'vertical';\n      } else {\n        return 'horizontal';\n      }\n    }));\n    this.level = 1;\n    this.isCurrentSubMenuOpen$ = new BehaviorSubject(false);\n    this.isChildSubMenuOpen$ = new BehaviorSubject(false);\n    /** submenu title & overlay mouse enter status **/\n    this.isMouseEnterTitleOrOverlay$ = new Subject();\n    this.childMenuItemClick$ = new Subject();\n    this.destroy$ = new Subject();\n    if (this.nzHostSubmenuService) {\n      this.level = this.nzHostSubmenuService.level + 1;\n    }\n    /** close if navbar item clicked **/\n    const isClosedByMenuItemClick = this.childMenuItemClick$.pipe(mergeMap(() => this.mode$), filter(mode => mode !== 'inline' || this.isNavBarInsideDropDown), mapTo(false));\n    const isCurrentSubmenuOpen$ = merge(this.isMouseEnterTitleOrOverlay$, isClosedByMenuItemClick);\n    /** combine the child submenu status with current submenu status to calculate host submenu open **/\n    const isSubMenuOpenWithDebounce$ = combineLatest([this.isChildSubMenuOpen$, isCurrentSubmenuOpen$]).pipe(map(([isChildSubMenuOpen, isCurrentSubmenuOpen]) => isChildSubMenuOpen || isCurrentSubmenuOpen), auditTime(150), distinctUntilChanged(), takeUntil(this.destroy$));\n    isSubMenuOpenWithDebounce$.pipe(distinctUntilChanged()).subscribe(data => {\n      this.setOpenStateWithoutDebounce(data);\n      if (this.nzHostSubmenuService) {\n        /** set parent submenu's child submenu open status **/\n        this.nzHostSubmenuService.isChildSubMenuOpen$.next(data);\n      } else {\n        this.tdsNavBarService.isChildSubMenuOpen$.next(data);\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function TDSSubNavBarService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSSubNavBarService)(i0.ɵɵinject(TDSSubNavBarService, 12), i0.ɵɵinject(NavBarService), i0.ɵɵinject(TDSIsNavBarInsideDropDownToken));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: TDSSubNavBarService,\n      factory: TDSSubNavBarService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSSubNavBarService, [{\n    type: Injectable\n  }], () => [{\n    type: TDSSubNavBarService,\n    decorators: [{\n      type: SkipSelf\n    }, {\n      type: Optional\n    }]\n  }, {\n    type: NavBarService\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [TDSIsNavBarInsideDropDownToken]\n    }]\n  }], null);\n})();\nclass TDSNavBarItemDirective {\n  /** clear all item selected status except this */\n  clickMenuItem(e) {\n    if (this.tdsDisabled) {\n      e.preventDefault();\n      e.stopPropagation();\n    } else {\n      this.tdsNavBarService.onDescendantMenuItemClick(this);\n      if (this.tdsSubNavBarService) {\n        /** navbar item inside the submenu **/\n        this.tdsSubNavBarService.onChildMenuItemClick(this);\n      } else {\n        /** navbar item inside the root navbar **/\n        this.tdsNavBarService.onChildMenuItemClick(this);\n      }\n    }\n  }\n  setSelectedState(value) {\n    this.tdsSelected = value;\n    this.selected$.next(value);\n  }\n  updateRouterActive() {\n    if (!this.listOfRouterLink || !this.listOfRouterLinkWithHref || !this.router || !this.router.navigated || !this.tdsMatchRouter) {\n      return;\n    }\n    Promise.resolve().then(() => {\n      const hasActiveLinks = this.hasActiveLinks();\n      if (this.tdsSelected !== hasActiveLinks) {\n        this.tdsSelected = hasActiveLinks;\n        this.setSelectedState(this.tdsSelected);\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  hasActiveLinks() {\n    const isActiveCheckFn = this.isLinkActive(this.router);\n    return this.routerLink && isActiveCheckFn(this.routerLink) || this.routerLinkWithHref && isActiveCheckFn(this.routerLinkWithHref) || this.listOfRouterLink.some(isActiveCheckFn) || this.listOfRouterLinkWithHref.some(isActiveCheckFn);\n  }\n  isLinkActive(router) {\n    return link => router.isActive(link.urlTree || '', this.tdsMatchOptions || {\n      paths: this.tdsMatchRouterExact ? 'exact' : 'subset',\n      queryParams: this.tdsMatchRouterExact ? 'exact' : 'subset',\n      fragment: 'ignored',\n      matrixParams: 'ignored'\n    });\n  }\n  constructor(tdsNavBarService, cdr, tdsSubNavBarService, isNavBarInsideDropDown, directionality, routerLink, routerLinkWithHref, router) {\n    this.tdsNavBarService = tdsNavBarService;\n    this.cdr = cdr;\n    this.tdsSubNavBarService = tdsSubNavBarService;\n    this.isNavBarInsideDropDown = isNavBarInsideDropDown;\n    this.directionality = directionality;\n    this.routerLink = routerLink;\n    this.routerLinkWithHref = routerLinkWithHref;\n    this.router = router;\n    this.destroy$ = new Subject();\n    this.level = this.tdsSubNavBarService ? this.tdsSubNavBarService.level + 1 : 1;\n    this.selected$ = new Subject();\n    this.inlinePaddingLeft = null;\n    this.dir = 'ltr';\n    this.tdsDisabled = false;\n    this.tdsSelected = false;\n    this.tdsDanger = false;\n    this.tdsMatchRouterExact = false;\n    this.tdsMatchRouter = false;\n    if (router) {\n      this.router.events.pipe(takeUntil(this.destroy$), filter(e => e instanceof NavigationEnd)).subscribe(() => {\n        this.updateRouterActive();\n      });\n    }\n  }\n  ngOnInit() {\n    /** store origin padding in padding */\n    combineLatest([this.tdsNavBarService.mode$, this.tdsNavBarService.inlineIndent$]).pipe(takeUntil(this.destroy$)).subscribe(([mode, inlineIndent]) => {\n      this.inlinePaddingLeft = mode === 'inline' ? this.level * inlineIndent : null;\n    });\n    this.dir = this.directionality.value;\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n    });\n  }\n  ngAfterContentInit() {\n    this.listOfRouterLink.changes.pipe(takeUntil(this.destroy$)).subscribe(() => this.updateRouterActive());\n    this.listOfRouterLinkWithHref.changes.pipe(takeUntil(this.destroy$)).subscribe(() => this.updateRouterActive());\n    this.updateRouterActive();\n  }\n  ngOnChanges(changes) {\n    if (changes.tdsSelected) {\n      this.setSelectedState(this.tdsSelected);\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function TDSNavBarItemDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSNavBarItemDirective)(i0.ɵɵdirectiveInject(NavBarService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(TDSSubNavBarService, 8), i0.ɵɵdirectiveInject(TDSIsNavBarInsideDropDownToken), i0.ɵɵdirectiveInject(i1.Directionality, 8), i0.ɵɵdirectiveInject(i4.RouterLink, 8), i0.ɵɵdirectiveInject(i4.RouterLinkWithHref, 8), i0.ɵɵdirectiveInject(i4.Router, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TDSNavBarItemDirective,\n      selectors: [[\"\", \"tds-navbar-item\", \"\"]],\n      contentQueries: function TDSNavBarItemDirective_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, RouterLink, 5);\n          i0.ɵɵcontentQuery(dirIndex, RouterLinkWithHref, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listOfRouterLink = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listOfRouterLinkWithHref = _t);\n        }\n      },\n      hostVars: 20,\n      hostBindings: function TDSNavBarItemDirective_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function TDSNavBarItemDirective_click_HostBindingHandler($event) {\n            return ctx.clickMenuItem($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"padding-left\", ctx.dir === \"rtl\" ? null : ctx.tdsPaddingLeft || ctx.inlinePaddingLeft, \"px\")(\"padding-right\", ctx.dir === \"rtl\" ? ctx.tdsPaddingLeft || ctx.inlinePaddingLeft : null, \"px\");\n          i0.ɵɵclassProp(\"tds-dropdown-navbar-item\", ctx.isNavBarInsideDropDown)(\"tds-dropdown-navbar-item-selected\", ctx.isNavBarInsideDropDown && ctx.tdsSelected)(\"tds-dropdown-navbar-item-danger\", ctx.isNavBarInsideDropDown && ctx.tdsDanger)(\"tds-dropdown-navbar-item-disabled\", ctx.isNavBarInsideDropDown && ctx.tdsDisabled)(\"tds-navbar-item\", !ctx.isNavBarInsideDropDown)(\"tds-navbar-item-selected\", !ctx.isNavBarInsideDropDown && ctx.tdsSelected)(\"tds-navbar-item-danger\", !ctx.isNavBarInsideDropDown && ctx.tdsDanger)(\"tds-navbar-item-disabled\", !ctx.isNavBarInsideDropDown && ctx.tdsDisabled);\n        }\n      },\n      inputs: {\n        tdsPaddingLeft: \"tdsPaddingLeft\",\n        tdsDisabled: \"tdsDisabled\",\n        tdsSelected: \"tdsSelected\",\n        tdsDanger: \"tdsDanger\",\n        tdsMatchRouterExact: \"tdsMatchRouterExact\",\n        tdsMatchRouter: \"tdsMatchRouter\",\n        tdsMatchOptions: \"tdsMatchOptions\"\n      },\n      exportAs: [\"tdsNavBarItem\"],\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n__decorate([InputBoolean()], TDSNavBarItemDirective.prototype, \"tdsDisabled\", void 0);\n__decorate([InputBoolean()], TDSNavBarItemDirective.prototype, \"tdsSelected\", void 0);\n__decorate([InputBoolean()], TDSNavBarItemDirective.prototype, \"tdsDanger\", void 0);\n__decorate([InputBoolean()], TDSNavBarItemDirective.prototype, \"tdsMatchRouterExact\", void 0);\n__decorate([InputBoolean()], TDSNavBarItemDirective.prototype, \"tdsMatchRouter\", void 0);\n__decorate([InputBoolean()], TDSNavBarItemDirective.prototype, \"tdsMatchOptions\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSNavBarItemDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[tds-navbar-item]',\n      exportAs: 'tdsNavBarItem',\n      host: {\n        '[class.tds-dropdown-navbar-item]': `isNavBarInsideDropDown`,\n        '[class.tds-dropdown-navbar-item-selected]': `isNavBarInsideDropDown && tdsSelected`,\n        '[class.tds-dropdown-navbar-item-danger]': `isNavBarInsideDropDown && tdsDanger`,\n        '[class.tds-dropdown-navbar-item-disabled]': `isNavBarInsideDropDown && tdsDisabled`,\n        '[class.tds-navbar-item]': `!isNavBarInsideDropDown`,\n        '[class.tds-navbar-item-selected]': `!isNavBarInsideDropDown && tdsSelected`,\n        '[class.tds-navbar-item-danger]': `!isNavBarInsideDropDown && tdsDanger`,\n        '[class.tds-navbar-item-disabled]': `!isNavBarInsideDropDown && tdsDisabled`,\n        '[style.paddingLeft.px]': `dir === 'rtl' ? null : tdsPaddingLeft || inlinePaddingLeft`,\n        '[style.paddingRight.px]': `dir === 'rtl' ? tdsPaddingLeft || inlinePaddingLeft : null`,\n        '(click)': 'clickMenuItem($event)'\n      },\n      standalone: true\n    }]\n  }], () => [{\n    type: NavBarService\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: TDSSubNavBarService,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [TDSIsNavBarInsideDropDownToken]\n    }]\n  }, {\n    type: i1.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i4.RouterLink,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i4.RouterLinkWithHref,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i4.Router,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    tdsPaddingLeft: [{\n      type: Input\n    }],\n    tdsDisabled: [{\n      type: Input\n    }],\n    tdsSelected: [{\n      type: Input\n    }],\n    tdsDanger: [{\n      type: Input\n    }],\n    tdsMatchRouterExact: [{\n      type: Input\n    }],\n    tdsMatchRouter: [{\n      type: Input\n    }],\n    tdsMatchOptions: [{\n      type: Input\n    }],\n    listOfRouterLink: [{\n      type: ContentChildren,\n      args: [RouterLink, {\n        descendants: true\n      }]\n    }],\n    listOfRouterLinkWithHref: [{\n      type: ContentChildren,\n      args: [RouterLinkWithHref, {\n        descendants: true\n      }]\n    }]\n  });\n})();\nclass TDSSubNavBarTitleComponent {\n  constructor(cdr, directionality) {\n    this.cdr = cdr;\n    this.directionality = directionality;\n    this.tdsIcon = null;\n    this.tdsHtmlIcon = null;\n    this.tdsTitle = null;\n    this.isNavBarInsideDropDown = false;\n    this.tdsDisabled = false;\n    this.paddingLeft = null;\n    this.mode = 'vertical';\n    this.toggleSubMenu = new EventEmitter();\n    this.subMenuMouseState = new EventEmitter();\n    this.dir = 'ltr';\n    this.destroy$ = new Subject();\n  }\n  ngOnInit() {\n    this.dir = this.directionality.value;\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.cdr.detectChanges();\n    });\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  setMouseState(state) {\n    if (!this.tdsDisabled) {\n      this.subMenuMouseState.next(state);\n    }\n  }\n  clickTitle() {\n    if (this.mode === 'inline' && !this.tdsDisabled) {\n      this.toggleSubMenu.emit();\n    }\n  }\n  static {\n    this.ɵfac = function TDSSubNavBarTitleComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSSubNavBarTitleComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.Directionality, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TDSSubNavBarTitleComponent,\n      selectors: [[\"\", \"tds-subnavbar-title\", \"\"]],\n      hostVars: 8,\n      hostBindings: function TDSSubNavBarTitleComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function TDSSubNavBarTitleComponent_click_HostBindingHandler() {\n            return ctx.clickTitle();\n          })(\"mouseenter\", function TDSSubNavBarTitleComponent_mouseenter_HostBindingHandler() {\n            return ctx.setMouseState(true);\n          })(\"mouseleave\", function TDSSubNavBarTitleComponent_mouseleave_HostBindingHandler() {\n            return ctx.setMouseState(false);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"padding-left\", ctx.dir === \"rtl\" ? null : ctx.paddingLeft, \"px\")(\"padding-right\", ctx.dir === \"rtl\" ? ctx.paddingLeft : null, \"px\");\n          i0.ɵɵclassProp(\"tds-dropdown-navbar-subnavbar-title\", ctx.isNavBarInsideDropDown)(\"tds-navbar-subnavbar-title\", !ctx.isNavBarInsideDropDown);\n        }\n      },\n      inputs: {\n        tdsIcon: \"tdsIcon\",\n        tdsHtmlIcon: \"tdsHtmlIcon\",\n        tdsTitle: \"tdsTitle\",\n        isNavBarInsideDropDown: \"isNavBarInsideDropDown\",\n        tdsDisabled: \"tdsDisabled\",\n        paddingLeft: \"paddingLeft\",\n        mode: \"mode\"\n      },\n      outputs: {\n        toggleSubMenu: \"toggleSubMenu\",\n        subMenuMouseState: \"subMenuMouseState\"\n      },\n      exportAs: [\"tdsSubNavBarTitle\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      attrs: _c0,\n      ngContentSelectors: _c1,\n      decls: 6,\n      vars: 4,\n      consts: [[1, \"tds-subnavbar-title-icon\", 3, \"class\"], [1, \"tds-subnavbar-title-html-icon\", 3, \"innerHtml\"], [4, \"tdsStringTemplateOutlet\"], [1, \"tds-dropdown-navbar-subnavbar-expand-icon\"], [1, \"tds-navbar-subnavbar-arrow\", \"tdsi-chevron-right-fill\"], [1, \"tds-subnavbar-title-icon\"], [1, \"tds-subnavbar-title-name\"]],\n      template: function TDSSubNavBarTitleComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, TDSSubNavBarTitleComponent_Conditional_0_Template, 1, 2, \"span\", 0)(1, TDSSubNavBarTitleComponent_Conditional_1_Template, 1, 1, \"span\", 1)(2, TDSSubNavBarTitleComponent_ng_container_2_Template, 3, 1, \"ng-container\", 2);\n          i0.ɵɵprojection(3);\n          i0.ɵɵtemplate(4, TDSSubNavBarTitleComponent_Conditional_4_Template, 1, 0, \"span\", 3)(5, TDSSubNavBarTitleComponent_Conditional_5_Template, 1, 0, \"span\", 4);\n        }\n        if (rf & 2) {\n          i0.ɵɵconditional(ctx.tdsIcon ? 0 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(!ctx.tdsIcon && ctx.tdsHtmlIcon ? 1 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"tdsStringTemplateOutlet\", ctx.tdsTitle);\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(ctx.isNavBarInsideDropDown ? 4 : 5);\n        }\n      },\n      dependencies: [TDSOutletModule, i2.TDSStringTemplateOutletDirective],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSSubNavBarTitleComponent, [{\n    type: Component,\n    args: [{\n      selector: '[tds-subnavbar-title]',\n      exportAs: 'tdsSubNavBarTitle',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    @if (tdsIcon) {\n      <span class=\"tds-subnavbar-title-icon\"  [class]=\"tdsIcon\"></span>\n    }\n    @if (!tdsIcon && tdsHtmlIcon) {\n      <span class=\"tds-subnavbar-title-html-icon\" [innerHtml]=\"tdsHtmlIcon\"></span>\n    }\n    <ng-container *tdsStringTemplateOutlet=\"tdsTitle\">\n      <span class=\"tds-subnavbar-title-name\">{{ tdsTitle }}</span>\n    </ng-container>\n    <ng-content></ng-content>\n    @if (isNavBarInsideDropDown) {\n      <span\n        class=\"tds-dropdown-navbar-subnavbar-expand-icon\"\n        >\n        @switch (dir) {\n          <!-- <span *ngSwitchCase=\"'rtl'\"  class=\"tds-dropdown-navbar-subnavbar-arrow-icon\"></span>\n          <span *ngSwitchDefault  class=\"tds-dropdown-navbar-subnavbar-arrow-icon\"></span> -->\n        }\n      </span>\n    } @else {\n      <span class=\"tds-navbar-subnavbar-arrow tdsi-chevron-right-fill\"></span>\n    }\n    `,\n      host: {\n        '[class.tds-dropdown-navbar-subnavbar-title]': 'isNavBarInsideDropDown',\n        '[class.tds-navbar-subnavbar-title]': '!isNavBarInsideDropDown',\n        '[style.paddingLeft.px]': `dir === 'rtl' ? null : paddingLeft `,\n        '[style.paddingRight.px]': `dir === 'rtl' ? paddingLeft : null`,\n        '(click)': 'clickTitle()',\n        '(mouseenter)': 'setMouseState(true)',\n        '(mouseleave)': 'setMouseState(false)'\n      },\n      standalone: true,\n      imports: [TDSOutletModule]\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i1.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    tdsIcon: [{\n      type: Input\n    }],\n    tdsHtmlIcon: [{\n      type: Input\n    }],\n    tdsTitle: [{\n      type: Input\n    }],\n    isNavBarInsideDropDown: [{\n      type: Input\n    }],\n    tdsDisabled: [{\n      type: Input\n    }],\n    paddingLeft: [{\n      type: Input\n    }],\n    mode: [{\n      type: Input\n    }],\n    toggleSubMenu: [{\n      type: Output\n    }],\n    subMenuMouseState: [{\n      type: Output\n    }]\n  });\n})();\nclass TDSSubNavBarInlineChildComponent {\n  constructor(elementRef, renderer, directionality) {\n    this.elementRef = elementRef;\n    this.renderer = renderer;\n    this.directionality = directionality;\n    this.templateOutlet = null;\n    this.navBarClass = '';\n    this.mode = 'vertical';\n    this.tdsOpen = false;\n    this.listOfCacheClassName = [];\n    this.expandState = 'collapsed';\n    this.dir = 'ltr';\n    this.destroy$ = new Subject();\n  }\n  calcMotionState() {\n    if (this.tdsOpen) {\n      this.expandState = 'expanded';\n    } else {\n      this.expandState = 'collapsed';\n    }\n  }\n  ngOnInit() {\n    this.calcMotionState();\n    this.dir = this.directionality.value;\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n    });\n  }\n  ngOnChanges(changes) {\n    const {\n      mode,\n      tdsOpen,\n      navBarClass\n    } = changes;\n    if (mode || tdsOpen) {\n      this.calcMotionState();\n    }\n    if (navBarClass) {\n      if (this.listOfCacheClassName.length) {\n        this.listOfCacheClassName.filter(item => !!item).forEach(className => {\n          this.renderer.removeClass(this.elementRef.nativeElement, className);\n        });\n      }\n      if (this.navBarClass) {\n        this.listOfCacheClassName = this.navBarClass.split(' ');\n        this.listOfCacheClassName.filter(item => !!item).forEach(className => {\n          this.renderer.addClass(this.elementRef.nativeElement, className);\n        });\n      }\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function TDSSubNavBarInlineChildComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSSubNavBarInlineChildComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i1.Directionality, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TDSSubNavBarInlineChildComponent,\n      selectors: [[\"\", \"tds-subnavbar-inline-child\", \"\"]],\n      hostAttrs: [1, \"tds-navbar\", \"tds-navbar-inline\", \"tds-navbar-sub\"],\n      hostVars: 3,\n      hostBindings: function TDSSubNavBarInlineChildComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵsyntheticHostProperty(\"@collapseMotion\", ctx.expandState);\n          i0.ɵɵclassProp(\"tds-navbar-rtl\", ctx.dir === \"rtl\");\n        }\n      },\n      inputs: {\n        templateOutlet: \"templateOutlet\",\n        navBarClass: \"navBarClass\",\n        mode: \"mode\",\n        tdsOpen: \"tdsOpen\"\n      },\n      exportAs: [\"tdsSubNavBarInlineChild\"],\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      attrs: _c2,\n      decls: 1,\n      vars: 1,\n      consts: [[3, \"ngTemplateOutlet\"]],\n      template: function TDSSubNavBarInlineChildComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, TDSSubNavBarInlineChildComponent_ng_template_0_Template, 0, 0, \"ng-template\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.templateOutlet);\n        }\n      },\n      dependencies: [NgTemplateOutlet],\n      encapsulation: 2,\n      data: {\n        animation: [collapseMotion]\n      },\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSSubNavBarInlineChildComponent, [{\n    type: Component,\n    args: [{\n      selector: '[tds-subnavbar-inline-child]',\n      animations: [collapseMotion],\n      exportAs: 'tdsSubNavBarInlineChild',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: ` <ng-template [ngTemplateOutlet]=\"templateOutlet\"></ng-template> `,\n      host: {\n        class: 'tds-navbar tds-navbar-inline tds-navbar-sub',\n        '[class.tds-navbar-rtl]': `dir === 'rtl'`,\n        '[@collapseMotion]': 'expandState'\n      },\n      standalone: true,\n      imports: [NgTemplateOutlet]\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i1.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    templateOutlet: [{\n      type: Input\n    }],\n    navBarClass: [{\n      type: Input\n    }],\n    mode: [{\n      type: Input\n    }],\n    tdsOpen: [{\n      type: Input\n    }]\n  });\n})();\nclass TDSSubNavBarNoneInlineChildComponent {\n  constructor(directionality) {\n    this.directionality = directionality;\n    this.navBarClass = '';\n    this.theme = 'light';\n    this.templateOutlet = null;\n    this.isNavBarInsideDropDown = false;\n    this.mode = 'vertical';\n    this.position = 'right';\n    this.tdsDisabled = false;\n    this.tdsOpen = false;\n    this.subMenuMouseState = new EventEmitter();\n    this.expandState = 'collapsed';\n    this.dir = 'ltr';\n    this.destroy$ = new Subject();\n  }\n  setMouseState(state) {\n    if (!this.tdsDisabled) {\n      this.subMenuMouseState.next(state);\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  calcMotionState() {\n    if (this.tdsOpen) {\n      if (this.mode === 'horizontal') {\n        this.expandState = 'bottom';\n      } else if (this.mode === 'vertical') {\n        this.expandState = 'active';\n      }\n    } else {\n      this.expandState = 'collapsed';\n    }\n  }\n  ngOnInit() {\n    this.calcMotionState();\n    this.dir = this.directionality.value;\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n    });\n  }\n  ngOnChanges(changes) {\n    const {\n      mode,\n      tdsOpen\n    } = changes;\n    if (mode || tdsOpen) {\n      this.calcMotionState();\n    }\n  }\n  static {\n    this.ɵfac = function TDSSubNavBarNoneInlineChildComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSSubNavBarNoneInlineChildComponent)(i0.ɵɵdirectiveInject(i1.Directionality, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TDSSubNavBarNoneInlineChildComponent,\n      selectors: [[\"\", \"tds-subnavbar-none-inline-child\", \"\"]],\n      hostAttrs: [1, \"tds-navbar-subnavbar\", \"tds-navbar-subnavbar-popup\"],\n      hostVars: 14,\n      hostBindings: function TDSSubNavBarNoneInlineChildComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"mouseenter\", function TDSSubNavBarNoneInlineChildComponent_mouseenter_HostBindingHandler() {\n            return ctx.setMouseState(true);\n          })(\"mouseleave\", function TDSSubNavBarNoneInlineChildComponent_mouseleave_HostBindingHandler() {\n            return ctx.setMouseState(false);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵsyntheticHostProperty(\"@slideMotion\", ctx.expandState)(\"@zoomBigMotion\", ctx.expandState);\n          i0.ɵɵclassProp(\"tds-navbar-light\", ctx.theme === \"light\")(\"tds-navbar-dark\", ctx.theme === \"dark\")(\"tds-navbar-subnavbar-placement-bottom\", ctx.mode === \"horizontal\")(\"tds-navbar-subnavbar-placement-right\", ctx.mode === \"vertical\" && ctx.position === \"right\")(\"tds-navbar-subnavbar-placement-left\", ctx.mode === \"vertical\" && ctx.position === \"left\")(\"tds-navbar-subnavbar-rtl\", ctx.dir === \"rtl\");\n        }\n      },\n      inputs: {\n        navBarClass: \"navBarClass\",\n        theme: \"theme\",\n        templateOutlet: \"templateOutlet\",\n        isNavBarInsideDropDown: \"isNavBarInsideDropDown\",\n        mode: \"mode\",\n        position: \"position\",\n        tdsDisabled: \"tdsDisabled\",\n        tdsOpen: \"tdsOpen\"\n      },\n      outputs: {\n        subMenuMouseState: \"subMenuMouseState\"\n      },\n      exportAs: [\"tdsSubNavBarNoneInlineChild\"],\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      attrs: _c3,\n      decls: 2,\n      vars: 16,\n      consts: [[3, \"ngClass\"], [3, \"ngTemplateOutlet\"]],\n      template: function TDSSubNavBarNoneInlineChildComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, TDSSubNavBarNoneInlineChildComponent_ng_template_1_Template, 0, 0, \"ng-template\", 1);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"tds-dropdown-navbar\", ctx.isNavBarInsideDropDown)(\"tds-navbar\", !ctx.isNavBarInsideDropDown)(\"tds-dropdown-navbar-vertical\", ctx.isNavBarInsideDropDown)(\"tds-navbar-vertical\", !ctx.isNavBarInsideDropDown)(\"tds-dropdown-navbar-sub\", ctx.isNavBarInsideDropDown)(\"tds-navbar-sub\", !ctx.isNavBarInsideDropDown)(\"tds-navbar-rtl\", ctx.dir === \"rtl\");\n          i0.ɵɵproperty(\"ngClass\", ctx.navBarClass);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.templateOutlet);\n        }\n      },\n      dependencies: [NgClass, NgTemplateOutlet],\n      encapsulation: 2,\n      data: {\n        animation: [zoomBigMotion, slideMotion]\n      },\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSSubNavBarNoneInlineChildComponent, [{\n    type: Component,\n    args: [{\n      selector: '[tds-subnavbar-none-inline-child]',\n      exportAs: 'tdsSubNavBarNoneInlineChild',\n      encapsulation: ViewEncapsulation.None,\n      animations: [zoomBigMotion, slideMotion],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    <div\n      [class.tds-dropdown-navbar]=\"isNavBarInsideDropDown\"\n      [class.tds-navbar]=\"!isNavBarInsideDropDown\"\n      [class.tds-dropdown-navbar-vertical]=\"isNavBarInsideDropDown\"\n      [class.tds-navbar-vertical]=\"!isNavBarInsideDropDown\"\n      [class.tds-dropdown-navbar-sub]=\"isNavBarInsideDropDown\"\n      [class.tds-navbar-sub]=\"!isNavBarInsideDropDown\"\n      [class.tds-navbar-rtl]=\"dir === 'rtl'\"\n      [ngClass]=\"navBarClass\"\n    >\n      <ng-template [ngTemplateOutlet]=\"templateOutlet\"></ng-template>\n    </div>\n  `,\n      host: {\n        class: 'tds-navbar-subnavbar tds-navbar-subnavbar-popup',\n        '[class.tds-navbar-light]': \"theme === 'light'\",\n        '[class.tds-navbar-dark]': \"theme === 'dark'\",\n        '[class.tds-navbar-subnavbar-placement-bottom]': \"mode === 'horizontal'\",\n        '[class.tds-navbar-subnavbar-placement-right]': \"mode === 'vertical' && position === 'right'\",\n        '[class.tds-navbar-subnavbar-placement-left]': \"mode === 'vertical' && position === 'left'\",\n        '[class.tds-navbar-subnavbar-rtl]': 'dir ===\"rtl\"',\n        '[@slideMotion]': 'expandState',\n        '[@zoomBigMotion]': 'expandState',\n        '(mouseenter)': 'setMouseState(true)',\n        '(mouseleave)': 'setMouseState(false)'\n      },\n      standalone: true,\n      imports: [NgClass, NgTemplateOutlet]\n    }]\n  }], () => [{\n    type: i1.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    navBarClass: [{\n      type: Input\n    }],\n    theme: [{\n      type: Input\n    }],\n    templateOutlet: [{\n      type: Input\n    }],\n    isNavBarInsideDropDown: [{\n      type: Input\n    }],\n    mode: [{\n      type: Input\n    }],\n    position: [{\n      type: Input\n    }],\n    tdsDisabled: [{\n      type: Input\n    }],\n    tdsOpen: [{\n      type: Input\n    }],\n    subMenuMouseState: [{\n      type: Output\n    }]\n  });\n})();\nconst listOfVerticalPositions = [POSITION_MAP.rightTop, POSITION_MAP.right, POSITION_MAP.rightBottom, POSITION_MAP.leftTop, POSITION_MAP.left, POSITION_MAP.leftBottom];\nconst listOfHorizontalPositions = [POSITION_MAP.bottomLeft, POSITION_MAP.bottomRight, POSITION_MAP.topRight, POSITION_MAP.topLeft];\nclass TDSSubNavBarComponent {\n  /** set the subnavbar host open status directly **/\n  setOpenStateWithoutDebounce(open) {\n    this.tdsSubNavBarService.setOpenStateWithoutDebounce(open);\n  }\n  toggleSubMenu() {\n    this.setOpenStateWithoutDebounce(!this.tdsOpen);\n  }\n  setMouseEnterState(value) {\n    this.isActive = value;\n    if (this.mode !== 'inline') {\n      this.tdsSubNavBarService.setMouseEnterTitleOrOverlayState(value);\n    }\n  }\n  setTriggerWidth() {\n    if (this.mode === 'horizontal' && this.platform.isBrowser && this.cdkOverlayOrigin && this.tdsPlacement === 'bottomLeft') {\n      /** TODO: fast dom **/\n      this.triggerWidth = this.cdkOverlayOrigin.nativeElement.getBoundingClientRect().width;\n    }\n  }\n  onPositionChange(position) {\n    const placement = getPlacementName(position);\n    if (placement === 'rightTop' || placement === 'rightBottom' || placement === 'right') {\n      this.position = 'right';\n    } else if (placement === 'leftTop' || placement === 'leftBottom' || placement === 'left') {\n      this.position = 'left';\n    }\n  }\n  constructor(tdsNavBarService, cdr, tdsSubNavBarService, platform, isNavBarInsideDropDown, directionality, noAnimation) {\n    this.tdsNavBarService = tdsNavBarService;\n    this.cdr = cdr;\n    this.tdsSubNavBarService = tdsSubNavBarService;\n    this.platform = platform;\n    this.isNavBarInsideDropDown = isNavBarInsideDropDown;\n    this.directionality = directionality;\n    this.noAnimation = noAnimation;\n    this.tdsNavBarClassName = '';\n    this.tdsPaddingLeft = null;\n    this.tdsTitle = null;\n    this.tdsIcon = null;\n    this.tdsHtmlIcon = null;\n    this.tdsOpen = false;\n    this.tdsDisabled = false;\n    this.tdsPlacement = 'bottomLeft';\n    this.tdsOpenChange = new EventEmitter();\n    this.cdkOverlayOrigin = null;\n    // fix errors about circular dependency\n    // Can't construct a query for the property ... since the query selector wasn't defined\"\n    this.listOfTDSSubNavBarComponent = null;\n    this.level = this.tdsSubNavBarService.level;\n    this.destroy$ = new Subject();\n    this.position = 'right';\n    this.triggerWidth = null;\n    this.theme = 'light';\n    this.mode = 'vertical';\n    this.inlinePaddingLeft = null;\n    this.overlayPositions = listOfVerticalPositions;\n    this.isSelected = false;\n    this.isActive = false;\n    this.dir = 'ltr';\n  }\n  ngOnInit() {\n    /** subnavbar theme update **/\n    this.tdsNavBarService.theme$.pipe(takeUntil(this.destroy$)).subscribe(theme => {\n      this.theme = theme;\n      this.cdr.markForCheck();\n    });\n    /** subnavbar mode update **/\n    this.tdsSubNavBarService.mode$.pipe(takeUntil(this.destroy$)).subscribe(mode => {\n      this.mode = mode;\n      if (mode === 'horizontal') {\n        this.overlayPositions = [POSITION_MAP[this.tdsPlacement], ...listOfHorizontalPositions];\n      } else if (mode === 'vertical') {\n        this.overlayPositions = listOfVerticalPositions;\n      }\n      this.cdr.markForCheck();\n    });\n    /** inlineIndent update **/\n    combineLatest([this.tdsSubNavBarService.mode$, this.tdsNavBarService.inlineIndent$]).pipe(takeUntil(this.destroy$)).subscribe(([mode, inlineIndent]) => {\n      this.inlinePaddingLeft = mode === 'inline' ? this.level * inlineIndent : null;\n      this.cdr.markForCheck();\n    });\n    /** current subnavbar open status **/\n    this.tdsSubNavBarService.isCurrentSubMenuOpen$.pipe(takeUntil(this.destroy$)).subscribe(open => {\n      this.isActive = open;\n      if (open !== this.tdsOpen) {\n        this.setTriggerWidth();\n        this.tdsOpen = open;\n        this.tdsOpenChange.emit(this.tdsOpen);\n        this.cdr.markForCheck();\n      }\n    });\n    this.dir = this.directionality.value;\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.cdr.markForCheck();\n    });\n  }\n  ngAfterContentInit() {\n    this.setTriggerWidth();\n    const listOfTDSNavBarItemDirective = this.listOfTDSNavBarItemDirective;\n    const changes = listOfTDSNavBarItemDirective.changes;\n    const mergedObservable = merge(...[changes, ...listOfTDSNavBarItemDirective.map(navbar => navbar.selected$)]);\n    changes.pipe(startWith(listOfTDSNavBarItemDirective), switchMap(() => mergedObservable), startWith(true), map(() => listOfTDSNavBarItemDirective.some(e => e.tdsSelected)), takeUntil(this.destroy$)).subscribe(selected => {\n      this.isSelected = selected;\n      this.cdr.markForCheck();\n    });\n  }\n  ngOnChanges(changes) {\n    const {\n      tdsOpen\n    } = changes;\n    if (tdsOpen) {\n      this.tdsSubNavBarService.setOpenStateWithoutDebounce(this.tdsOpen);\n      this.setTriggerWidth();\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function TDSSubNavBarComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSSubNavBarComponent)(i0.ɵɵdirectiveInject(NavBarService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(TDSSubNavBarService), i0.ɵɵdirectiveInject(i3.Platform), i0.ɵɵdirectiveInject(TDSIsNavBarInsideDropDownToken), i0.ɵɵdirectiveInject(i1.Directionality, 8), i0.ɵɵdirectiveInject(i5.TDSNoAnimationDirective, 9));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TDSSubNavBarComponent,\n      selectors: [[\"\", \"tds-subnavbar\", \"\"]],\n      contentQueries: function TDSSubNavBarComponent_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, TDSSubNavBarComponent, 5);\n          i0.ɵɵcontentQuery(dirIndex, TDSNavBarItemDirective, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listOfTDSSubNavBarComponent = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listOfTDSNavBarItemDirective = _t);\n        }\n      },\n      viewQuery: function TDSSubNavBarComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(CdkOverlayOrigin, 7, ElementRef);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.cdkOverlayOrigin = _t.first);\n        }\n      },\n      hostVars: 34,\n      hostBindings: function TDSSubNavBarComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"tds-dropdown-navbar-subnavbar\", ctx.isNavBarInsideDropDown)(\"tds-dropdown-navbar-subnavbar-disabled\", ctx.isNavBarInsideDropDown && ctx.tdsDisabled)(\"tds-dropdown-navbar-subnavbar-open\", ctx.isNavBarInsideDropDown && ctx.tdsOpen)(\"tds-dropdown-navbar-subnavbar-selected\", ctx.isNavBarInsideDropDown && ctx.isSelected)(\"tds-dropdown-navbar-subnavbar-vertical\", ctx.isNavBarInsideDropDown && ctx.mode === \"vertical\")(\"tds-dropdown-navbar-subnavbar-horizontal\", ctx.isNavBarInsideDropDown && ctx.mode === \"horizontal\")(\"tds-dropdown-navbar-subnavbar-inline\", ctx.isNavBarInsideDropDown && ctx.mode === \"inline\")(\"tds-dropdown-navbar-subnavbar-active\", ctx.isNavBarInsideDropDown && ctx.isActive)(\"tds-navbar-subnavbar\", !ctx.isNavBarInsideDropDown)(\"tds-navbar-subnavbar-disabled\", !ctx.isNavBarInsideDropDown && ctx.tdsDisabled)(\"tds-navbar-subnavbar-open\", !ctx.isNavBarInsideDropDown && ctx.tdsOpen)(\"tds-navbar-subnavbar-selected\", !ctx.isNavBarInsideDropDown && ctx.isSelected)(\"tds-navbar-subnavbar-vertical\", !ctx.isNavBarInsideDropDown && ctx.mode === \"vertical\")(\"tds-navbar-subnavbar-horizontal\", !ctx.isNavBarInsideDropDown && ctx.mode === \"horizontal\")(\"tds-navbar-subnavbar-inline\", !ctx.isNavBarInsideDropDown && ctx.mode === \"inline\")(\"tds-navbar-subnavbar-active\", !ctx.isNavBarInsideDropDown && ctx.isActive)(\"tds-navbar-subnavbar-rtl\", ctx.dir === \"rtl\");\n        }\n      },\n      inputs: {\n        tdsNavBarClassName: \"tdsNavBarClassName\",\n        tdsPaddingLeft: \"tdsPaddingLeft\",\n        tdsTitle: \"tdsTitle\",\n        tdsIcon: \"tdsIcon\",\n        tdsHtmlIcon: \"tdsHtmlIcon\",\n        tdsOpen: \"tdsOpen\",\n        tdsDisabled: \"tdsDisabled\",\n        tdsPlacement: \"tdsPlacement\"\n      },\n      outputs: {\n        tdsOpenChange: \"tdsOpenChange\"\n      },\n      exportAs: [\"tdsSubNavBar\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([TDSSubNavBarService]), i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      attrs: _c4,\n      ngContentSelectors: _c6,\n      decls: 7,\n      vars: 9,\n      consts: [[\"origin\", \"cdkOverlayOrigin\"], [\"subMenuTemplate\", \"\"], [\"tds-subnavbar-title\", \"\", \"cdkOverlayOrigin\", \"\", 3, \"subMenuMouseState\", \"toggleSubMenu\", \"tdsIcon\", \"tdsHtmlIcon\", \"tdsTitle\", \"mode\", \"tdsDisabled\", \"isNavBarInsideDropDown\", \"paddingLeft\"], [\"tds-subnavbar-inline-child\", \"\", 3, \"mode\", \"tdsOpen\", \"navBarClass\", \"templateOutlet\"], [\"cdkConnectedOverlay\", \"\", 3, \"cdkConnectedOverlayPositions\", \"cdkConnectedOverlayOrigin\", \"cdkConnectedOverlayWidth\", \"cdkConnectedOverlayOpen\", \"cdkConnectedOverlayTransformOriginOn\"], [\"cdkConnectedOverlay\", \"\", 3, \"positionChange\", \"cdkConnectedOverlayPositions\", \"cdkConnectedOverlayOrigin\", \"cdkConnectedOverlayWidth\", \"cdkConnectedOverlayOpen\", \"cdkConnectedOverlayTransformOriginOn\"], [\"tds-subnavbar-none-inline-child\", \"\", 3, \"subMenuMouseState\", \"theme\", \"mode\", \"tdsOpen\", \"position\", \"tdsDisabled\", \"isNavBarInsideDropDown\", \"templateOutlet\", \"navBarClass\"]],\n      template: function TDSSubNavBarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵprojectionDef(_c5);\n          i0.ɵɵelementStart(0, \"div\", 2, 0);\n          i0.ɵɵlistener(\"subMenuMouseState\", function TDSSubNavBarComponent_Template_div_subMenuMouseState_0_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.setMouseEnterState($event));\n          })(\"toggleSubMenu\", function TDSSubNavBarComponent_Template_div_toggleSubMenu_0_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.toggleSubMenu());\n          });\n          i0.ɵɵtemplate(2, TDSSubNavBarComponent_Conditional_2_Template, 1, 0);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(3, TDSSubNavBarComponent_Conditional_3_Template, 1, 4, \"div\", 3)(4, TDSSubNavBarComponent_Conditional_4_Template, 1, 5, null, 4)(5, TDSSubNavBarComponent_ng_template_5_Template, 1, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"tdsIcon\", ctx.tdsIcon)(\"tdsHtmlIcon\", ctx.tdsHtmlIcon)(\"tdsTitle\", ctx.tdsTitle)(\"mode\", ctx.mode)(\"tdsDisabled\", ctx.tdsDisabled)(\"isNavBarInsideDropDown\", ctx.isNavBarInsideDropDown)(\"paddingLeft\", ctx.tdsPaddingLeft || ctx.inlinePaddingLeft);\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(!ctx.tdsTitle ? 2 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx.mode === \"inline\" ? 3 : 4);\n        }\n      },\n      dependencies: [OverlayModule, i6.CdkConnectedOverlay, i6.CdkOverlayOrigin, TDSSubNavBarTitleComponent, TDSSubNavBarInlineChildComponent, TDSSubNavBarNoneInlineChildComponent],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n__decorate([InputBoolean()], TDSSubNavBarComponent.prototype, \"tdsOpen\", void 0);\n__decorate([InputBoolean()], TDSSubNavBarComponent.prototype, \"tdsDisabled\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSSubNavBarComponent, [{\n    type: Component,\n    args: [{\n      selector: '[tds-subnavbar]',\n      exportAs: 'tdsSubNavBar',\n      providers: [TDSSubNavBarService],\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      preserveWhitespaces: false,\n      template: `\n    <div\n      tds-subnavbar-title\n      cdkOverlayOrigin\n      #origin=\"cdkOverlayOrigin\"\n      [tdsIcon]=\"tdsIcon\"\n      [tdsHtmlIcon]=\"tdsHtmlIcon\"\n      [tdsTitle]=\"tdsTitle\"\n      [mode]=\"mode\"\n      [tdsDisabled]=\"tdsDisabled\"\n      [isNavBarInsideDropDown]=\"isNavBarInsideDropDown\"\n      [paddingLeft]=\"tdsPaddingLeft || inlinePaddingLeft\"\n      (subMenuMouseState)=\"setMouseEnterState($event)\"\n      (toggleSubMenu)=\"toggleSubMenu()\"\n      >\n      @if (!tdsTitle) {\n        <ng-content select=\"[title]\"></ng-content>\n      }\n    </div>\n    @if (mode === 'inline') {\n      <div\n        tds-subnavbar-inline-child\n        [mode]=\"mode\"\n        [tdsOpen]=\"tdsOpen\"\n        [navBarClass]=\"tdsNavBarClassName\"\n        [templateOutlet]=\"subMenuTemplate\"\n      ></div>\n    } @else {\n      <ng-template\n        cdkConnectedOverlay\n        (positionChange)=\"onPositionChange($event)\"\n        [cdkConnectedOverlayPositions]=\"overlayPositions\"\n        [cdkConnectedOverlayOrigin]=\"origin\"\n        [cdkConnectedOverlayWidth]=\"triggerWidth!\"\n        [cdkConnectedOverlayOpen]=\"tdsOpen\"\n        [cdkConnectedOverlayTransformOriginOn]=\"'.tds-navbar-subnavbar'\"\n        >\n        <div\n          tds-subnavbar-none-inline-child\n          [theme]=\"theme\"\n          [mode]=\"mode\"\n          [tdsOpen]=\"tdsOpen\"\n          [position]=\"position\"\n          [tdsDisabled]=\"tdsDisabled\"\n          [isNavBarInsideDropDown]=\"isNavBarInsideDropDown\"\n          [templateOutlet]=\"subMenuTemplate\"\n          [navBarClass]=\"tdsNavBarClassName\"\n          (subMenuMouseState)=\"setMouseEnterState($event)\"\n        ></div>\n      </ng-template>\n    }\n    \n    <ng-template #subMenuTemplate>\n      <ng-content></ng-content>\n    </ng-template>\n    `,\n      host: {\n        '[class.tds-dropdown-navbar-subnavbar]': `isNavBarInsideDropDown`,\n        '[class.tds-dropdown-navbar-subnavbar-disabled]': `isNavBarInsideDropDown && tdsDisabled`,\n        '[class.tds-dropdown-navbar-subnavbar-open]': `isNavBarInsideDropDown && tdsOpen`,\n        '[class.tds-dropdown-navbar-subnavbar-selected]': `isNavBarInsideDropDown && isSelected`,\n        '[class.tds-dropdown-navbar-subnavbar-vertical]': `isNavBarInsideDropDown && mode === 'vertical'`,\n        '[class.tds-dropdown-navbar-subnavbar-horizontal]': `isNavBarInsideDropDown && mode === 'horizontal'`,\n        '[class.tds-dropdown-navbar-subnavbar-inline]': `isNavBarInsideDropDown && mode === 'inline'`,\n        '[class.tds-dropdown-navbar-subnavbar-active]': `isNavBarInsideDropDown && isActive`,\n        '[class.tds-navbar-subnavbar]': `!isNavBarInsideDropDown`,\n        '[class.tds-navbar-subnavbar-disabled]': `!isNavBarInsideDropDown && tdsDisabled`,\n        '[class.tds-navbar-subnavbar-open]': `!isNavBarInsideDropDown && tdsOpen`,\n        '[class.tds-navbar-subnavbar-selected]': `!isNavBarInsideDropDown && isSelected`,\n        '[class.tds-navbar-subnavbar-vertical]': `!isNavBarInsideDropDown && mode === 'vertical'`,\n        '[class.tds-navbar-subnavbar-horizontal]': `!isNavBarInsideDropDown && mode === 'horizontal'`,\n        '[class.tds-navbar-subnavbar-inline]': `!isNavBarInsideDropDown && mode === 'inline'`,\n        '[class.tds-navbar-subnavbar-active]': `!isNavBarInsideDropDown && isActive`,\n        '[class.tds-navbar-subnavbar-rtl]': `dir === 'rtl'`\n      },\n      standalone: true,\n      imports: [OverlayModule, TDSSubNavBarTitleComponent, TDSSubNavBarInlineChildComponent, TDSSubNavBarNoneInlineChildComponent]\n    }]\n  }], () => [{\n    type: NavBarService\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: TDSSubNavBarService\n  }, {\n    type: i3.Platform\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [TDSIsNavBarInsideDropDownToken]\n    }]\n  }, {\n    type: i1.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i5.TDSNoAnimationDirective,\n    decorators: [{\n      type: Host\n    }, {\n      type: Optional\n    }]\n  }], {\n    tdsNavBarClassName: [{\n      type: Input\n    }],\n    tdsPaddingLeft: [{\n      type: Input\n    }],\n    tdsTitle: [{\n      type: Input\n    }],\n    tdsIcon: [{\n      type: Input\n    }],\n    tdsHtmlIcon: [{\n      type: Input\n    }],\n    tdsOpen: [{\n      type: Input\n    }],\n    tdsDisabled: [{\n      type: Input\n    }],\n    tdsPlacement: [{\n      type: Input\n    }],\n    tdsOpenChange: [{\n      type: Output\n    }],\n    cdkOverlayOrigin: [{\n      type: ViewChild,\n      args: [CdkOverlayOrigin, {\n        static: true,\n        read: ElementRef\n      }]\n    }],\n    listOfTDSSubNavBarComponent: [{\n      type: ContentChildren,\n      args: [forwardRef(() => TDSSubNavBarComponent), {\n        descendants: true\n      }]\n    }],\n    listOfTDSNavBarItemDirective: [{\n      type: ContentChildren,\n      args: [TDSNavBarItemDirective, {\n        descendants: true\n      }]\n    }]\n  });\n})();\nfunction NavBarServiceFactory(serviceInsideDropDown, serviceOutsideDropDown) {\n  return serviceInsideDropDown ? serviceInsideDropDown : serviceOutsideDropDown;\n}\nfunction NavBarDropDownTokenFactory(isMenuInsideDropDownToken) {\n  return isMenuInsideDropDownToken ? isMenuInsideDropDownToken : false;\n}\nclass TDSNavBarDirective {\n  setInlineCollapsed(inlineCollapsed) {\n    this.tdsInlineCollapsed = inlineCollapsed;\n    this.inlineCollapsed$.next(inlineCollapsed);\n  }\n  updateInlineCollapse() {\n    if (this.listOfTDSNavBarItemDirective) {\n      if (this.tdsInlineCollapsed) {\n        this.listOfOpenedTDSSubNavBarComponent = this.listOfTDSSubNavBarComponent.filter(submenu => submenu.tdsOpen);\n        this.listOfTDSSubNavBarComponent.forEach(submenu => submenu.setOpenStateWithoutDebounce(false));\n      } else {\n        this.listOfOpenedTDSSubNavBarComponent.forEach(submenu => submenu.setOpenStateWithoutDebounce(true));\n        this.listOfOpenedTDSSubNavBarComponent = [];\n      }\n    }\n  }\n  constructor(tdsNavBarService, isNavBarInsideDropDown, cdr, directionality) {\n    this.tdsNavBarService = tdsNavBarService;\n    this.isNavBarInsideDropDown = isNavBarInsideDropDown;\n    this.cdr = cdr;\n    this.directionality = directionality;\n    this.tdsInlineIndent = 24;\n    this.tdsTheme = 'light';\n    this.tdsMode = 'vertical';\n    this.tdsInlineCollapsed = false;\n    this.tdsSelectable = !this.isNavBarInsideDropDown;\n    this.tdsClick = new EventEmitter();\n    this.actualMode = 'vertical';\n    this.dir = 'ltr';\n    this.inlineCollapsed$ = new BehaviorSubject(this.tdsInlineCollapsed);\n    this.mode$ = new BehaviorSubject(this.tdsMode);\n    this.destroy$ = new Subject();\n    this.listOfOpenedTDSSubNavBarComponent = [];\n  }\n  ngOnInit() {\n    combineLatest([this.inlineCollapsed$, this.mode$]).pipe(takeUntil(this.destroy$)).subscribe(([inlineCollapsed, mode]) => {\n      this.actualMode = inlineCollapsed ? 'vertical' : mode;\n      this.tdsNavBarService.setMode(this.actualMode);\n      this.cdr.markForCheck();\n    });\n    this.tdsNavBarService.descendantMenuItemClick$.pipe(takeUntil(this.destroy$)).subscribe(navbar => {\n      this.tdsClick.emit(navbar);\n      if (this.tdsSelectable && !navbar.tdsMatchRouter) {\n        this.listOfTDSNavBarItemDirective.forEach(item => item.setSelectedState(item === navbar));\n      }\n    });\n    this.dir = this.directionality.value;\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.tdsNavBarService.setMode(this.actualMode);\n      this.cdr.markForCheck();\n    });\n  }\n  ngAfterContentInit() {\n    this.inlineCollapsed$.pipe(takeUntil(this.destroy$)).subscribe(() => {\n      this.updateInlineCollapse();\n      this.cdr.markForCheck();\n    });\n  }\n  ngOnChanges(changes) {\n    const {\n      tdsInlineCollapsed,\n      tdsInlineIndent,\n      tdsTheme,\n      tdsMode\n    } = changes;\n    if (tdsInlineCollapsed) {\n      this.inlineCollapsed$.next(this.tdsInlineCollapsed);\n    }\n    if (tdsInlineIndent) {\n      this.tdsNavBarService.setInlineIndent(this.tdsInlineIndent);\n    }\n    if (tdsTheme) {\n      this.tdsNavBarService.setTheme(this.tdsTheme);\n    }\n    if (tdsMode) {\n      this.mode$.next(this.tdsMode);\n      if (!changes.tdsMode.isFirstChange() && this.listOfTDSSubNavBarComponent) {\n        this.listOfTDSSubNavBarComponent.forEach(submenu => submenu.setOpenStateWithoutDebounce(false));\n      }\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function TDSNavBarDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSNavBarDirective)(i0.ɵɵdirectiveInject(NavBarService), i0.ɵɵdirectiveInject(TDSIsNavBarInsideDropDownToken), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.Directionality, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TDSNavBarDirective,\n      selectors: [[\"\", \"tds-navbar\", \"\"]],\n      contentQueries: function TDSNavBarDirective_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, TDSNavBarItemDirective, 5);\n          i0.ɵɵcontentQuery(dirIndex, TDSSubNavBarComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listOfTDSNavBarItemDirective = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listOfTDSSubNavBarComponent = _t);\n        }\n      },\n      hostVars: 34,\n      hostBindings: function TDSNavBarDirective_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"tds-dropdown-navbar\", ctx.isNavBarInsideDropDown)(\"tds-dropdown-navbar-root\", ctx.isNavBarInsideDropDown)(\"tds-dropdown-navbar-light\", ctx.isNavBarInsideDropDown && ctx.tdsTheme === \"light\")(\"tds-dropdown-navbar-dark\", ctx.isNavBarInsideDropDown && ctx.tdsTheme === \"dark\")(\"tds-dropdown-navbar-vertical\", ctx.isNavBarInsideDropDown && ctx.actualMode === \"vertical\")(\"tds-dropdown-navbar-horizontal\", ctx.isNavBarInsideDropDown && ctx.actualMode === \"horizontal\")(\"tds-dropdown-navbar-inline\", ctx.isNavBarInsideDropDown && ctx.actualMode === \"inline\")(\"tds-dropdown-navbar-inline-collapsed\", ctx.isNavBarInsideDropDown && ctx.tdsInlineCollapsed)(\"tds-navbar\", !ctx.isNavBarInsideDropDown)(\"tds-navbar-root\", !ctx.isNavBarInsideDropDown)(\"tds-navbar-light\", !ctx.isNavBarInsideDropDown && ctx.tdsTheme === \"light\")(\"tds-navbar-dark\", !ctx.isNavBarInsideDropDown && ctx.tdsTheme === \"dark\")(\"tds-navbar-vertical\", !ctx.isNavBarInsideDropDown && ctx.actualMode === \"vertical\")(\"tds-navbar-horizontal\", !ctx.isNavBarInsideDropDown && ctx.actualMode === \"horizontal\")(\"tds-navbar-inline\", !ctx.isNavBarInsideDropDown && ctx.actualMode === \"inline\")(\"tds-navbar-inline-collapsed\", !ctx.isNavBarInsideDropDown && ctx.tdsInlineCollapsed)(\"tds-navbar-rtl\", ctx.dir === \"rtl\");\n        }\n      },\n      inputs: {\n        tdsInlineIndent: \"tdsInlineIndent\",\n        tdsTheme: \"tdsTheme\",\n        tdsMode: \"tdsMode\",\n        tdsInlineCollapsed: \"tdsInlineCollapsed\",\n        tdsSelectable: \"tdsSelectable\"\n      },\n      outputs: {\n        tdsClick: \"tdsClick\"\n      },\n      exportAs: [\"tdsNavBar\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: TDSNavBarServiceLocalToken,\n        useClass: NavBarService\n      }, /** use the top level service **/\n      {\n        provide: NavBarService,\n        useFactory: NavBarServiceFactory,\n        deps: [[new SkipSelf(), new Optional(), NavBarService], TDSNavBarServiceLocalToken]\n      }, /** check if navbar inside dropdown-navbar component **/\n      {\n        provide: TDSIsNavBarInsideDropDownToken,\n        useFactory: NavBarDropDownTokenFactory,\n        deps: [[new SkipSelf(), new Optional(), TDSIsNavBarInsideDropDownToken]]\n      }]), i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n__decorate([InputBoolean()], TDSNavBarDirective.prototype, \"tdsInlineCollapsed\", void 0);\n__decorate([InputBoolean()], TDSNavBarDirective.prototype, \"tdsSelectable\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSNavBarDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[tds-navbar]',\n      exportAs: 'tdsNavBar',\n      providers: [{\n        provide: TDSNavBarServiceLocalToken,\n        useClass: NavBarService\n      }, /** use the top level service **/\n      {\n        provide: NavBarService,\n        useFactory: NavBarServiceFactory,\n        deps: [[new SkipSelf(), new Optional(), NavBarService], TDSNavBarServiceLocalToken]\n      }, /** check if navbar inside dropdown-navbar component **/\n      {\n        provide: TDSIsNavBarInsideDropDownToken,\n        useFactory: NavBarDropDownTokenFactory,\n        deps: [[new SkipSelf(), new Optional(), TDSIsNavBarInsideDropDownToken]]\n      }],\n      host: {\n        '[class.tds-dropdown-navbar]': `isNavBarInsideDropDown`,\n        '[class.tds-dropdown-navbar-root]': `isNavBarInsideDropDown`,\n        '[class.tds-dropdown-navbar-light]': `isNavBarInsideDropDown && tdsTheme === 'light'`,\n        '[class.tds-dropdown-navbar-dark]': `isNavBarInsideDropDown && tdsTheme === 'dark'`,\n        '[class.tds-dropdown-navbar-vertical]': `isNavBarInsideDropDown && actualMode === 'vertical'`,\n        '[class.tds-dropdown-navbar-horizontal]': `isNavBarInsideDropDown && actualMode === 'horizontal'`,\n        '[class.tds-dropdown-navbar-inline]': `isNavBarInsideDropDown && actualMode === 'inline'`,\n        '[class.tds-dropdown-navbar-inline-collapsed]': `isNavBarInsideDropDown && tdsInlineCollapsed`,\n        '[class.tds-navbar]': `!isNavBarInsideDropDown`,\n        '[class.tds-navbar-root]': `!isNavBarInsideDropDown`,\n        '[class.tds-navbar-light]': `!isNavBarInsideDropDown && tdsTheme === 'light'`,\n        '[class.tds-navbar-dark]': `!isNavBarInsideDropDown && tdsTheme === 'dark'`,\n        '[class.tds-navbar-vertical]': `!isNavBarInsideDropDown && actualMode === 'vertical'`,\n        '[class.tds-navbar-horizontal]': `!isNavBarInsideDropDown && actualMode === 'horizontal'`,\n        '[class.tds-navbar-inline]': `!isNavBarInsideDropDown && actualMode === 'inline'`,\n        '[class.tds-navbar-inline-collapsed]': `!isNavBarInsideDropDown && tdsInlineCollapsed`,\n        '[class.tds-navbar-rtl]': `dir === 'rtl'`\n      },\n      standalone: true\n    }]\n  }], () => [{\n    type: NavBarService\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [TDSIsNavBarInsideDropDownToken]\n    }]\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i1.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    listOfTDSNavBarItemDirective: [{\n      type: ContentChildren,\n      args: [TDSNavBarItemDirective, {\n        descendants: true\n      }]\n    }],\n    listOfTDSSubNavBarComponent: [{\n      type: ContentChildren,\n      args: [TDSSubNavBarComponent, {\n        descendants: true\n      }]\n    }],\n    tdsInlineIndent: [{\n      type: Input\n    }],\n    tdsTheme: [{\n      type: Input\n    }],\n    tdsMode: [{\n      type: Input\n    }],\n    tdsInlineCollapsed: [{\n      type: Input\n    }],\n    tdsSelectable: [{\n      type: Input\n    }],\n    tdsClick: [{\n      type: Output\n    }]\n  });\n})();\nfunction NavBarGroupFactory(isMenuInsideDropDownToken) {\n  return isMenuInsideDropDownToken ? isMenuInsideDropDownToken : false;\n}\nclass TDSNavBarGroupComponent {\n  constructor(elementRef, renderer, isNavBarInsideDropDown) {\n    this.elementRef = elementRef;\n    this.renderer = renderer;\n    this.isNavBarInsideDropDown = isNavBarInsideDropDown;\n    const className = this.isNavBarInsideDropDown ? 'tds-dropdown-navbar-item-group' : 'tds-navbar-item-group';\n    this.renderer.addClass(elementRef.nativeElement, className);\n  }\n  ngAfterViewInit() {\n    const ulElement = this.titleElement.nativeElement.nextElementSibling;\n    if (ulElement) {\n      /** add classname to ul **/\n      const className = this.isNavBarInsideDropDown ? 'tds-dropdown-navbar-item-group-list' : 'tds-navbar-item-group-list';\n      this.renderer.addClass(ulElement, className);\n    }\n  }\n  static {\n    this.ɵfac = function TDSNavBarGroupComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSNavBarGroupComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(TDSIsNavBarInsideDropDownToken));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TDSNavBarGroupComponent,\n      selectors: [[\"\", \"tds-navbar-group\", \"\"]],\n      viewQuery: function TDSNavBarGroupComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c7, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.titleElement = _t.first);\n        }\n      },\n      inputs: {\n        tdsTitle: \"tdsTitle\"\n      },\n      exportAs: [\"tdsNavBarGroup\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([/** check if navbar inside dropdown-navbar component **/\n      {\n        provide: TDSIsNavBarInsideDropDownToken,\n        useFactory: NavBarGroupFactory,\n        deps: [[new SkipSelf(), new Optional(), TDSIsNavBarInsideDropDownToken]]\n      }]), i0.ɵɵStandaloneFeature],\n      attrs: _c8,\n      ngContentSelectors: _c10,\n      decls: 5,\n      vars: 6,\n      consts: [[\"titleElement\", \"\"], [4, \"tdsStringTemplateOutlet\"]],\n      template: function TDSNavBarGroupComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c9);\n          i0.ɵɵelementStart(0, \"div\", null, 0);\n          i0.ɵɵtemplate(2, TDSNavBarGroupComponent_ng_container_2_Template, 2, 1, \"ng-container\", 1)(3, TDSNavBarGroupComponent_Conditional_3_Template, 1, 0);\n          i0.ɵɵelementEnd();\n          i0.ɵɵprojection(4);\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"tds-navbar-item-group-title\", !ctx.isNavBarInsideDropDown)(\"tds-dropdown-navbar-item-group-title\", ctx.isNavBarInsideDropDown);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"tdsStringTemplateOutlet\", ctx.tdsTitle);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(!ctx.tdsTitle ? 3 : -1);\n        }\n      },\n      dependencies: [TDSOutletModule, i2.TDSStringTemplateOutletDirective],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSNavBarGroupComponent, [{\n    type: Component,\n    args: [{\n      selector: '[tds-navbar-group]',\n      exportAs: 'tdsNavBarGroup',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [/** check if navbar inside dropdown-navbar component **/\n      {\n        provide: TDSIsNavBarInsideDropDownToken,\n        useFactory: NavBarGroupFactory,\n        deps: [[new SkipSelf(), new Optional(), TDSIsNavBarInsideDropDownToken]]\n      }],\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    <div\n      [class.tds-navbar-item-group-title]=\"!isNavBarInsideDropDown\"\n      [class.tds-dropdown-navbar-item-group-title]=\"isNavBarInsideDropDown\"\n      #titleElement\n      >\n      <ng-container *tdsStringTemplateOutlet=\"tdsTitle\">{{ tdsTitle }}</ng-container>\n      @if (!tdsTitle) {\n        <ng-content select=\"[title]\"></ng-content>\n      }\n    </div>\n    <ng-content></ng-content>\n    `,\n      preserveWhitespaces: false,\n      standalone: true,\n      imports: [TDSOutletModule]\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [TDSIsNavBarInsideDropDownToken]\n    }]\n  }], {\n    tdsTitle: [{\n      type: Input\n    }],\n    titleElement: [{\n      type: ViewChild,\n      args: ['titleElement']\n    }]\n  });\n})();\nclass TDSNavBarDividerDirective {\n  constructor(elementRef, renderer) {\n    this.elementRef = elementRef;\n    this.renderer = renderer;\n    this.renderer.addClass(elementRef.nativeElement, 'tds-dropdown-navbar-item-divider');\n  }\n  static {\n    this.ɵfac = function TDSNavBarDividerDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSNavBarDividerDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TDSNavBarDividerDirective,\n      selectors: [[\"\", \"tds-navbar-divider\", \"\"]],\n      exportAs: [\"tdsNavBarDivider\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSNavBarDividerDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[tds-navbar-divider]',\n      exportAs: 'tdsNavBarDivider',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.Renderer2\n  }], null);\n})();\nclass TDSNavBarModule {\n  static {\n    this.ɵfac = function TDSNavBarModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSNavBarModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: TDSNavBarModule,\n      imports: [TDSNavBarDirective, TDSNavBarItemDirective, TDSSubNavBarComponent, TDSNavBarDividerDirective, TDSNavBarGroupComponent, TDSSubNavBarTitleComponent, TDSSubNavBarInlineChildComponent, TDSSubNavBarNoneInlineChildComponent],\n      exports: [TDSNavBarDirective, TDSNavBarItemDirective, TDSSubNavBarComponent, TDSNavBarDividerDirective, TDSNavBarGroupComponent]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [TDSSubNavBarComponent, TDSNavBarGroupComponent, TDSSubNavBarTitleComponent]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSNavBarModule, [{\n    type: NgModule,\n    args: [{\n      // imports: [BidiModule,\n      //   CommonModule,\n      //   PlatformModule,\n      //   OverlayModule,\n      //   TDSNoAnimationModule,\n      //   TDSOutletModule],\n      imports: [TDSNavBarDirective, TDSNavBarItemDirective, TDSSubNavBarComponent, TDSNavBarDividerDirective, TDSNavBarGroupComponent, TDSSubNavBarTitleComponent, TDSSubNavBarInlineChildComponent, TDSSubNavBarNoneInlineChildComponent],\n      exports: [TDSNavBarDirective, TDSNavBarItemDirective, TDSSubNavBarComponent, TDSNavBarDividerDirective, TDSNavBarGroupComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NavBarDropDownTokenFactory, NavBarGroupFactory, NavBarService, NavBarServiceFactory, TDSIsNavBarInsideDropDownToken, TDSNavBarDirective, TDSNavBarDividerDirective, TDSNavBarGroupComponent, TDSNavBarItemDirective, TDSNavBarModule, TDSNavBarServiceLocalToken, TDSSubNavBarComponent, TDSSubNavBarInlineChildComponent, TDSSubNavBarNoneInlineChildComponent, TDSSubNavBarService, TDSSubNavBarTitleComponent };\n", "import * as i0 from '@angular/core';\nimport { Injectable, ElementRef, Component, ChangeDetectionStrategy, ViewEncapsulation, Optional, Input, ViewChildren, ViewChild, ContentChildren, Directive, EventEmitter, TemplateRef, Output, ContentChild, NgModule } from '@angular/core';\nimport { __decorate } from 'tslib';\nimport { BehaviorSubject, Subject, merge } from 'rxjs';\nimport { takeUntil, filter, startWith, switchMap, map, debounceTime, distinctUntilChanged } from 'rxjs/operators';\nimport { TDSHelperArray, TDSHelperObject, TDSHelperString, InputBoolean } from 'tds-ui/shared/utility';\nimport * as i4$1 from '@angular/cdk/overlay';\nimport { CdkOverlayOrigin, OverlayModule } from '@angular/cdk/overlay';\nimport * as i2 from '@angular/router';\nimport { NavigationEnd, RouterLink, RouterLinkWithHref, RouterModule } from '@angular/router';\nimport { menuCollapseMotion, slideMotion } from 'tds-ui/core/animation';\nimport * as i3 from 'tds-ui/tag';\nimport { TDSTagModule } from 'tds-ui/tag';\nimport * as i4 from 'tds-ui/badges';\nimport { TDSBadgeModule } from 'tds-ui/badges';\nimport * as i5 from 'tds-ui/cdk/pipes/mapper';\nimport { TDSMapperPipeModule } from 'tds-ui/cdk/pipes/mapper';\nimport * as i3$1 from 'tds-ui/core/pipes';\nimport { TDSPipesModule } from 'tds-ui/core/pipes';\nimport { NgClass, NgTemplateOutlet } from '@angular/common';\nimport { POSITION_MAP, getPlacementName } from 'tds-ui/core/overlay';\nimport * as i3$2 from 'tds-ui/tooltip';\nimport { TDSToolTipModule } from 'tds-ui/tooltip';\nimport { TDSOutletModule } from 'tds-ui/core/outlet';\nimport { TDSNavBarModule } from 'tds-ui/nav-bar';\nconst _c0 = a0 => [a0];\nconst _c1 = () => [];\nfunction TDSMenuItemComponent_Conditional_4_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tds-badge\", 9);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"standalone\", true)(\"count\", ctx_r0.item.badge.count)(\"tdsStyle\", ctx_r0.item.badge.tdsStyle)(\"tdsTheme\", ctx_r0.tdsTheme);\n  }\n}\nfunction TDSMenuItemComponent_Conditional_4_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tds-tag\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"status\", ctx_r0.item.tag.status)(\"rounded\", ctx_r0.item.tag.rounded)(\"type\", ctx_r0.item.tag.type)(\"tdsTheme\", ctx_r0.tdsTheme);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.item.tag.text);\n  }\n}\nfunction TDSMenuItemComponent_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 4)(1, \"span\", 8);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, TDSMenuItemComponent_Conditional_4_Conditional_3_Template, 1, 4, \"tds-badge\", 9)(4, TDSMenuItemComponent_Conditional_4_Conditional_4_Template, 2, 5, \"tds-tag\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(12, _c0, ctx_r0.item.link))(\"queryParams\", ctx_r0.item.linkProps == null ? null : ctx_r0.item.linkProps.queryParams)(\"fragment\", ctx_r0.item.linkProps == null ? null : ctx_r0.item.linkProps.fragment)(\"queryParamsHandling\", ctx_r0.item.linkProps == null ? null : ctx_r0.item.linkProps.queryParamsHandling)(\"preserveFragment\", ctx_r0.item.linkProps == null ? null : ctx_r0.item.linkProps.preserveFragment)(\"skipLocationChange\", ctx_r0.item.linkProps == null ? null : ctx_r0.item.linkProps.skipLocationChange)(\"replaceUrl\", ctx_r0.item.linkProps == null ? null : ctx_r0.item.linkProps.replaceUrl)(\"state\", ctx_r0.item.linkProps == null ? null : ctx_r0.item.linkProps.state)(\"routerLinkActive\", (ctx_r0.item.linkProps == null ? null : ctx_r0.item.linkProps.routerLinkActive) || i0.ɵɵpureFunction0(14, _c1));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.item.name);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r0.item.badge ? 3 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r0.item.tag ? 4 : -1);\n  }\n}\nfunction TDSMenuItemComponent_Conditional_5_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tds-badge\", 9);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"standalone\", true)(\"count\", ctx_r0.item.badge.count)(\"tdsStyle\", ctx_r0.item.badge.tdsStyle)(\"tdsTheme\", ctx_r0.tdsTheme);\n  }\n}\nfunction TDSMenuItemComponent_Conditional_5_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tds-tag\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"status\", ctx_r0.item.tag.status)(\"rounded\", ctx_r0.item.tag.rounded)(\"type\", ctx_r0.item.tag.type)(\"tdsTheme\", ctx_r0.tdsTheme);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.item.tag.text);\n  }\n}\nfunction TDSMenuItemComponent_Conditional_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 5)(1, \"span\", 8);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, TDSMenuItemComponent_Conditional_5_Conditional_3_Template, 1, 4, \"tds-badge\", 9)(4, TDSMenuItemComponent_Conditional_5_Conditional_4_Template, 2, 5, \"tds-tag\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.item.name);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r0.item.badge ? 3 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r0.item.tag ? 4 : -1);\n  }\n}\nfunction TDSMenuItemComponent_Conditional_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵelement(1, \"span\", 11);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TDSMenuItemComponent_Conditional_7_For_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tds-menu-item\", 13);\n  }\n  if (rf & 2) {\n    const menu_r2 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"options\", ctx_r0.options)(\"item\", menu_r2)(\"isSelected\", menu_r2.isSelected)(\"matchRouterExact\", ctx_r0.matchRouterExact)(\"matchRouter\", ctx_r0.matchRouter)(\"tdsTheme\", ctx_r0.tdsTheme);\n  }\n}\nfunction TDSMenuItemComponent_Conditional_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵpipe(1, \"tdsMapper\");\n    i0.ɵɵelementStart(2, \"div\", 12);\n    i0.ɵɵrepeaterCreate(3, TDSMenuItemComponent_Conditional_7_For_4_Template, 1, 6, \"tds-menu-item\", 13, i0.ɵɵrepeaterTrackByIdentity);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"@menuCollapseMotion\", i0.ɵɵpipeBind3(1, 1, ctx_r0.item.isOpen, ctx_r0.mapperExpandState, ctx_r0.hasAllHidden));\n    i0.ɵɵadvance(3);\n    i0.ɵɵrepeater(ctx_r0.item.listChild);\n  }\n}\nconst _c2 = [\"*\"];\nfunction TDSMenuGroupInlineComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.item.groupTitle, \"\\n\");\n  }\n}\nfunction TDSMenuGroupInlineComponent_Conditional_2_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 9);\n    i0.ɵɵpipe(1, \"tdsSanitizer\");\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"innerHTML\", i0.ɵɵpipeBind2(1, 1, ctx_r0.item.htmlIcon, \"html\"), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction TDSMenuGroupInlineComponent_Conditional_2_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 10);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.item.icon);\n  }\n}\nfunction TDSMenuGroupInlineComponent_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵtemplate(1, TDSMenuGroupInlineComponent_Conditional_2_Conditional_1_Template, 2, 4, \"div\", 9)(2, TDSMenuGroupInlineComponent_Conditional_2_Conditional_2_Template, 1, 1, \"span\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"menu-active\", ctx_r0.isSelected);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r0.item.htmlIcon ? 1 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r0.hasIcon && !ctx_r0.item.htmlIcon ? 2 : -1);\n  }\n}\nfunction TDSMenuGroupInlineComponent_Conditional_4_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tds-badge\", 13);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"standalone\", true)(\"overflowCount\", ctx_r0.item.badge.overflowCount)(\"count\", ctx_r0.item.badge.count)(\"tdsStyle\", ctx_r0.item.badge.tdsStyle)(\"tdsTheme\", ctx_r0.tdsTheme);\n  }\n}\nfunction TDSMenuGroupInlineComponent_Conditional_4_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tds-tag\", 14);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"status\", ctx_r0.item.tag.status)(\"rounded\", ctx_r0.item.tag.rounded)(\"type\", ctx_r0.item.tag.type)(\"tdsTheme\", ctx_r0.tdsTheme);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.item.tag.text);\n  }\n}\nfunction TDSMenuGroupInlineComponent_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 11);\n    i0.ɵɵlistener(\"click\", function TDSMenuGroupInlineComponent_Conditional_4_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onClickItem($event));\n    });\n    i0.ɵɵelementStart(1, \"span\", 12);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, TDSMenuGroupInlineComponent_Conditional_4_Conditional_3_Template, 1, 5, \"tds-badge\", 13)(4, TDSMenuGroupInlineComponent_Conditional_4_Conditional_4_Template, 2, 5, \"tds-tag\", 14);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(12, _c0, ctx_r0.item.link))(\"queryParams\", ctx_r0.item.linkProps == null ? null : ctx_r0.item.linkProps.queryParams)(\"fragment\", ctx_r0.item.linkProps == null ? null : ctx_r0.item.linkProps.fragment)(\"queryParamsHandling\", ctx_r0.item.linkProps == null ? null : ctx_r0.item.linkProps.queryParamsHandling)(\"preserveFragment\", ctx_r0.item.linkProps == null ? null : ctx_r0.item.linkProps.preserveFragment)(\"skipLocationChange\", ctx_r0.item.linkProps == null ? null : ctx_r0.item.linkProps.skipLocationChange)(\"replaceUrl\", ctx_r0.item.linkProps == null ? null : ctx_r0.item.linkProps.replaceUrl)(\"state\", ctx_r0.item.linkProps == null ? null : ctx_r0.item.linkProps.state)(\"routerLinkActive\", (ctx_r0.item.linkProps == null ? null : ctx_r0.item.linkProps.routerLinkActive) || i0.ɵɵpureFunction0(14, _c1));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.item.name);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r0.item.badge ? 3 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r0.item.tag ? 4 : -1);\n  }\n}\nfunction TDSMenuGroupInlineComponent_Conditional_5_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tds-badge\", 13);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"standalone\", true)(\"overflowCount\", ctx_r0.item.badge.overflowCount)(\"count\", ctx_r0.item.badge.count)(\"tdsStyle\", ctx_r0.item.badge.tdsStyle)(\"tdsTheme\", ctx_r0.tdsTheme);\n  }\n}\nfunction TDSMenuGroupInlineComponent_Conditional_5_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tds-tag\", 14);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"status\", ctx_r0.item.tag.status)(\"rounded\", ctx_r0.item.tag.rounded)(\"type\", ctx_r0.item.tag.type)(\"tdsTheme\", ctx_r0.tdsTheme);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.item.tag.text);\n  }\n}\nfunction TDSMenuGroupInlineComponent_Conditional_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 5)(1, \"span\", 12);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, TDSMenuGroupInlineComponent_Conditional_5_Conditional_3_Template, 1, 5, \"tds-badge\", 13)(4, TDSMenuGroupInlineComponent_Conditional_5_Conditional_4_Template, 2, 5, \"tds-tag\", 14);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.item.name);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r0.item.badge ? 3 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r0.item.tag ? 4 : -1);\n  }\n}\nfunction TDSMenuGroupInlineComponent_Conditional_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵelement(1, \"span\", 15);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TDSMenuGroupInlineComponent_Conditional_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵpipe(1, \"tdsMapper\");\n    i0.ɵɵelementStart(2, \"div\", 16);\n    i0.ɵɵprojection(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"@menuCollapseMotion\", i0.ɵɵpipeBind3(1, 1, ctx_r0.item.isOpen, ctx_r0.mapperExpandState, ctx_r0.hasAllHidden));\n  }\n}\nconst _c3 = a0 => ({\n  \"hidden\": a0\n});\nconst _c4 = a0 => ({\n  $implicit: a0\n});\nconst _c5 = (a0, a1, a2) => ({\n  \"tds-menu-light\": a0,\n  \"tds-menu-dark\": a1,\n  \"tds-menu-default\": a2\n});\nfunction TDSMenuGroupPopupComponent_Conditional_2_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 7)(1, \"a\", 8);\n    i0.ɵɵlistener(\"click\", function TDSMenuGroupPopupComponent_Conditional_2_Conditional_1_Template_a_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onClickItem($event));\n    });\n    i0.ɵɵelementContainer(2, 9);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    const iconTmpl_r5 = i0.ɵɵreference(6);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(11, _c0, ctx_r2.item.link))(\"queryParams\", ctx_r2.item.linkProps == null ? null : ctx_r2.item.linkProps.queryParams)(\"fragment\", ctx_r2.item.linkProps == null ? null : ctx_r2.item.linkProps.fragment)(\"queryParamsHandling\", ctx_r2.item.linkProps == null ? null : ctx_r2.item.linkProps.queryParamsHandling)(\"preserveFragment\", ctx_r2.item.linkProps == null ? null : ctx_r2.item.linkProps.preserveFragment)(\"skipLocationChange\", ctx_r2.item.linkProps == null ? null : ctx_r2.item.linkProps.skipLocationChange)(\"replaceUrl\", ctx_r2.item.linkProps == null ? null : ctx_r2.item.linkProps.replaceUrl)(\"state\", ctx_r2.item.linkProps == null ? null : ctx_r2.item.linkProps.state)(\"routerLinkActive\", (ctx_r2.item.linkProps == null ? null : ctx_r2.item.linkProps.routerLinkActive) || i0.ɵɵpureFunction0(13, _c1));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", iconTmpl_r5)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(14, _c4, ctx_r2.item));\n  }\n}\nfunction TDSMenuGroupPopupComponent_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵlistener(\"click\", function TDSMenuGroupPopupComponent_Conditional_2_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClickItem($event));\n    });\n    i0.ɵɵtemplate(1, TDSMenuGroupPopupComponent_Conditional_2_Conditional_1_Template, 3, 16, \"div\", 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"tooltipTitle\", ctx_r2.item.name);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r2.hasIcon ? 1 : -1);\n  }\n}\nfunction TDSMenuGroupPopupComponent_Conditional_3_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵelementContainer(1, 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    const iconTmpl_r5 = i0.ɵɵreference(6);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", iconTmpl_r5)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c4, ctx_r2.item));\n  }\n}\nfunction TDSMenuGroupPopupComponent_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵlistener(\"click\", function TDSMenuGroupPopupComponent_Conditional_3_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClickItem($event));\n    });\n    i0.ɵɵtemplate(1, TDSMenuGroupPopupComponent_Conditional_3_Conditional_1_Template, 2, 4, \"div\", 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r2.hasIcon ? 1 : -1);\n  }\n}\nfunction TDSMenuGroupPopupComponent_ng_template_4_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵlistener(\"mouseenter\", function TDSMenuGroupPopupComponent_ng_template_4_Conditional_0_Template_div_mouseenter_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.setMouseState(true));\n    })(\"mouseleave\", function TDSMenuGroupPopupComponent_ng_template_4_Conditional_0_Template_div_mouseleave_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.setMouseState(false));\n    });\n    i0.ɵɵelementStart(1, \"div\", 13)(2, \"div\", 14)(3, \"div\", 15);\n    i0.ɵɵprojection(4);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"margin-top\", ctx_r2.position == \"rightTop\" ? -5 : 0, \"px\");\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(4, _c5, ctx_r2.mode == \"light\", ctx_r2.mode == \"dark\", ctx_r2.mode == \"default\"))(\"@slideMotion\", undefined);\n  }\n}\nfunction TDSMenuGroupPopupComponent_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TDSMenuGroupPopupComponent_ng_template_4_Conditional_0_Template, 5, 8, \"div\", 11);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵconditional(ctx_r2.hasListChild ? 0 : -1);\n  }\n}\nfunction TDSMenuGroupPopupComponent_ng_template_5_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tds-badge\", 16);\n    i0.ɵɵelement(1, \"span\", 17);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r8 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"status\", item_r8.dot.status)(\"dot\", true)(\"tdsTheme\", ctx_r2.tdsTheme);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", item_r8.icon);\n  }\n}\nfunction TDSMenuGroupPopupComponent_ng_template_5_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 17);\n  }\n  if (rf & 2) {\n    const item_r8 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngClass\", item_r8.icon);\n  }\n}\nfunction TDSMenuGroupPopupComponent_ng_template_5_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 18);\n    i0.ɵɵpipe(1, \"tdsSanitizer\");\n  }\n  if (rf & 2) {\n    const item_r8 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"innerHTML\", i0.ɵɵpipeBind2(1, 1, item_r8.htmlIcon, \"html\"), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction TDSMenuGroupPopupComponent_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TDSMenuGroupPopupComponent_ng_template_5_Conditional_0_Template, 2, 4, \"tds-badge\", 16)(1, TDSMenuGroupPopupComponent_ng_template_5_Conditional_1_Template, 1, 1, \"span\", 17)(2, TDSMenuGroupPopupComponent_ng_template_5_Conditional_2_Template, 2, 4, \"span\", 18);\n  }\n  if (rf & 2) {\n    const item_r8 = ctx.$implicit;\n    i0.ɵɵconditional(item_r8.dot ? 0 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(!item_r8.dot && !item_r8.htmlIcon ? 1 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(!item_r8.dot && item_r8.htmlIcon ? 2 : -1);\n  }\n}\nconst _c6 = [\"logo-text\"];\nconst _c7 = [[[\"\", \"logo-text\", \"\"]], [[\"\", \"logo\", \"\"]]];\nconst _c8 = [\"[logo-text]\", \"[logo]\"];\nconst _c9 = a0 => ({\n  \" overflow-x-hidden\": a0\n});\nconst _c10 = a0 => ({\n  \"flex-col\": a0\n});\nconst _c11 = a0 => ({\n  \"tds-menu-footer-icon-inline-collapsed\": a0\n});\nconst _c12 = (a0, a1) => ({\n  \"tdsi-chevron-right-fill\": a0,\n  \"tdsi-chevron-left-fill\": a1\n});\nfunction TDSMenuComponent_Conditional_1_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n  }\n}\nfunction TDSMenuComponent_Conditional_1_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0, 1);\n  }\n}\nfunction TDSMenuComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵtemplate(1, TDSMenuComponent_Conditional_1_Conditional_1_Template, 1, 0)(2, TDSMenuComponent_Conditional_1_Conditional_2_Template, 1, 0);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(!ctx_r0.inlineCollapsed ? 1 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r0.inlineCollapsed ? 2 : -1);\n  }\n}\nfunction TDSMenuComponent_Conditional_3_For_1_Conditional_0_For_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tds-menu-item\", 6);\n  }\n  if (rf & 2) {\n    const children_r2 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(4);\n    i0.ɵɵclassMap(i0.ɵɵpureFunction3(9, _c5, ctx_r0.mode == \"light\", ctx_r0.mode == \"dark\", ctx_r0.mode == \"default\"));\n    i0.ɵɵproperty(\"options\", ctx_r0.options)(\"item\", children_r2)(\"isSelected\", children_r2.isSelected)(\"parentIsGroup\", true)(\"matchRouterExact\", ctx_r0.matchRouterExact)(\"matchRouter\", ctx_r0.matchRouter)(\"tdsTheme\", ctx_r0.mode);\n  }\n}\nfunction TDSMenuComponent_Conditional_3_For_1_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tds-menu-group-popup\", 4);\n    i0.ɵɵrepeaterCreate(1, TDSMenuComponent_Conditional_3_For_1_Conditional_0_For_2_Template, 1, 13, \"tds-menu-item\", 5, i0.ɵɵrepeaterTrackByIdentity);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = i0.ɵɵnextContext().$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"options\", ctx_r0.options)(\"item\", item_r3)(\"tdsTheme\", ctx_r0.mode);\n    i0.ɵɵadvance();\n    i0.ɵɵrepeater(item_r3.listChild);\n  }\n}\nfunction TDSMenuComponent_Conditional_3_For_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TDSMenuComponent_Conditional_3_For_1_Conditional_0_Template, 3, 3, \"tds-menu-group-popup\", 4);\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.$implicit;\n    i0.ɵɵconditional(!item_r3.hidden ? 0 : -1);\n  }\n}\nfunction TDSMenuComponent_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵrepeaterCreate(0, TDSMenuComponent_Conditional_3_For_1_Template, 1, 1, null, null, i0.ɵɵrepeaterTrackByIdentity);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵrepeater(ctx_r0.listMenu);\n  }\n}\nfunction TDSMenuComponent_Conditional_4_For_1_Conditional_0_For_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tds-menu-item\", 9);\n  }\n  if (rf & 2) {\n    const children_r4 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(4);\n    i0.ɵɵclassMap(i0.ɵɵpureFunction3(10, _c5, ctx_r0.mode == \"light\", ctx_r0.mode == \"dark\", ctx_r0.mode == \"default\"));\n    i0.ɵɵproperty(\"options\", ctx_r0.options)(\"showIcon\", ctx_r0.hasIcon)(\"item\", children_r4)(\"isSelected\", children_r4.isSelected)(\"parentIsGroup\", true)(\"matchRouterExact\", ctx_r0.matchRouterExact)(\"matchRouter\", ctx_r0.matchRouter)(\"tdsTheme\", ctx_r0.mode);\n  }\n}\nfunction TDSMenuComponent_Conditional_4_For_1_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tds-menu-group-inline\", 7);\n    i0.ɵɵrepeaterCreate(1, TDSMenuComponent_Conditional_4_For_1_Conditional_0_For_2_Template, 1, 14, \"tds-menu-item\", 8, i0.ɵɵrepeaterTrackByIdentity);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = i0.ɵɵnextContext().$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"options\", ctx_r0.options)(\"showIcon\", ctx_r0.hasIcon)(\"item\", item_r5)(\"tdsTheme\", ctx_r0.mode);\n    i0.ɵɵadvance();\n    i0.ɵɵrepeater(item_r5.listChild);\n  }\n}\nfunction TDSMenuComponent_Conditional_4_For_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TDSMenuComponent_Conditional_4_For_1_Conditional_0_Template, 3, 4, \"tds-menu-group-inline\", 7);\n  }\n  if (rf & 2) {\n    const item_r5 = ctx.$implicit;\n    i0.ɵɵconditional(!item_r5.hidden ? 0 : -1);\n  }\n}\nfunction TDSMenuComponent_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵrepeaterCreate(0, TDSMenuComponent_Conditional_4_For_1_Template, 1, 1, null, null, i0.ɵɵrepeaterTrackByIdentity);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵrepeater(ctx_r0.listMenu);\n  }\n}\nfunction TDSMenuComponent_Conditional_5_Conditional_1_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction TDSMenuComponent_Conditional_5_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TDSMenuComponent_Conditional_5_Conditional_1_ng_container_0_Template, 1, 0, \"ng-container\", 10);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.menuFooter)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c4, ctx_r0.inlineCollapsed));\n  }\n}\nfunction TDSMenuComponent_Conditional_5_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 12);\n    i0.ɵɵelement(2, \"path\", 13)(3, \"path\", 14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"div\", 15);\n    i0.ɵɵlistener(\"click\", function TDSMenuComponent_Conditional_5_Conditional_2_Template_div_click_4_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.onClickInlineCollapsed());\n    });\n    i0.ɵɵelement(5, \"span\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c11, ctx_r0.inlineCollapsed));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(4, _c12, ctx_r0.inlineCollapsed, !ctx_r0.inlineCollapsed));\n  }\n}\nfunction TDSMenuComponent_Conditional_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵtemplate(1, TDSMenuComponent_Conditional_5_Conditional_1_Template, 1, 4, \"ng-container\")(2, TDSMenuComponent_Conditional_5_Conditional_2_Template, 6, 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c10, ctx_r0.inlineCollapsed));\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r0.menuFooter ? 1 : 2);\n  }\n}\nclass TDSMenuService {\n  constructor() {\n    this.mode$ = new BehaviorSubject('dark');\n    this.isChildSubMenuOpen$ = new BehaviorSubject(false);\n    /** all descendant menu click **/\n    this.descendantMenuItemClick$ = new Subject();\n    this.groupMenuOpen$ = new BehaviorSubject(null);\n  }\n  onDescendantMenuItemClick(menu) {\n    this.descendantMenuItemClick$.next(menu);\n  }\n  onModeChange(mode) {\n    this.mode$.next(mode);\n  }\n  static {\n    this.ɵfac = function TDSMenuService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSMenuService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: TDSMenuService,\n      factory: TDSMenuService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSMenuService, [{\n    type: Injectable\n  }], null, null);\n})();\nclass TDSMenuItemComponent {\n  get uid() {\n    return this.item ? this.item.uid : \"\";\n  }\n  constructor(_cdr, tdsMenuService, routerLink, routerLinkWithHref, router) {\n    this._cdr = _cdr;\n    this.tdsMenuService = tdsMenuService;\n    this.routerLink = routerLink;\n    this.routerLinkWithHref = routerLinkWithHref;\n    this.router = router;\n    this.destroy$ = new Subject();\n    this.showIcon = true;\n    this.isSelected = false;\n    this.parentIsGroup = false;\n    this.inlineCollapsed = false;\n    this.matchRouterExact = false;\n    this.matchRouter = false;\n    this.tdsTheme = 'default';\n    this.options = {\n      background: 'bg-white dark:bg-d-neutral-3-200',\n      backgroundItem: 'bg-white dark:bg-d-neutral-3-200',\n      backgroundItemSelected: 'bg-white dark:bg-d-neutral-3-200',\n      backgroundItemHover: 'dark:hover:bg-d-neutral-3-300  hover:bg-neutral-3-50'\n    };\n    this.selected$ = new Subject();\n    this.isOpenPopup = false;\n    this.cdkOverlayOrigin = null;\n    this.mapperExpandState = (isOpen, hasAllHidden) => {\n      return isOpen && !hasAllHidden ? 'expanded' : 'collapsed';\n    };\n    if (router) {\n      this.router.events.pipe(takeUntil(this.destroy$), filter(e => e instanceof NavigationEnd)).subscribe(() => {\n        this.updateRouterActive();\n      });\n    }\n  }\n  ngOnInit() {}\n  ngAfterViewInit() {\n    this.listenItemChangeSelected();\n  }\n  ngOnChanges(changes) {\n    if (changes['item']) {\n      this._cdr.markForCheck();\n    }\n    if (changes.isSelected) {\n      this.setSelectedState(this.isSelected);\n    }\n  }\n  onClickItem(e) {\n    if (this.item.disabled) {\n      e.stopPropagation();\n      e.preventDefault();\n    }\n    if (this.hasListChild) {\n      this.item.isOpen = !this.item.isOpen;\n    } else {\n      if (this.hasLink) {\n        this.p_NavigateByUrl();\n      }\n      this.tdsMenuService.onDescendantMenuItemClick(this);\n    }\n  }\n  get hasListChild() {\n    return TDSHelperArray.hasListValue(this.item.listChild);\n  }\n  get hasAllHidden() {\n    let childs = this.item.listChild?.find(f => {\n      return !f.hidden;\n    });\n    return childs == undefined;\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  setSelectedState(value) {\n    this.isSelected = value;\n    this.item.isSelected = value;\n    this.selected$.next(value);\n    if (this.isSelected) {\n      if (!this.item.isOpen) {\n        this.item.isOpen = true;\n      }\n    }\n    this._cdr.markForCheck();\n  }\n  updateRouterActive() {\n    if (this.hasListChild || !this.listOfRouterLink || !this.listOfRouterLinkWithHref || !this.router || !this.router.navigated || !this.matchRouter) {\n      return;\n    }\n    Promise.resolve().then(() => {\n      const hasActiveLinks = this.hasActiveLinks();\n      if (!this.hasListChild) if (this.isSelected !== hasActiveLinks) {\n        this.isSelected = hasActiveLinks;\n        this.setSelectedState(this.isSelected);\n      }\n    });\n  }\n  hasActiveLinks() {\n    const isActiveCheckFn = this.isLinkActive(this.router);\n    return this.routerLink && isActiveCheckFn(this.routerLink) || this.routerLinkWithHref && isActiveCheckFn(this.routerLinkWithHref) || this.listOfRouterLink.some(isActiveCheckFn) || this.listOfRouterLinkWithHref.some(isActiveCheckFn);\n  }\n  isLinkActive(router) {\n    const isActiveMatchOptions = this.item.linkProps?.routerLinkActiveOptions || {\n      paths: this.matchRouterExact ? 'exact' : 'subset',\n      queryParams: this.matchRouterExact ? 'exact' : 'subset',\n      fragment: 'ignored',\n      matrixParams: 'ignored'\n    };\n    return link => router.isActive(link.urlTree, isActiveMatchOptions);\n  }\n  setSelectedStateListChildren(uid) {\n    if (TDSHelperArray.hasListValue(this.listItem)) {\n      this.listItem.forEach(f => {\n        f.setSelectedStateListChildren(uid);\n      });\n    } else {\n      this.setSelectedState(uid === this.uid);\n    }\n  }\n  get hasLink() {\n    return !this.item.disabled && !this.hasListChild && TDSHelperObject.hasValue(this.item.link);\n  }\n  get hasIcon() {\n    return this.item && (TDSHelperString.hasValueString(this.item.icon) || TDSHelperString.hasValueString(this.item.htmlIcon));\n  }\n  listenItemChangeSelected() {\n    if (this.listOfRouterLink) this.listOfRouterLink.changes.pipe(takeUntil(this.destroy$)).subscribe(() => this.updateRouterActive());\n    if (this.listOfRouterLinkWithHref) this.listOfRouterLinkWithHref.changes.pipe(takeUntil(this.destroy$)).subscribe(() => this.updateRouterActive());\n    if (TDSHelperArray.hasListValue(this.listItem)) {\n      const listOfTDSMenuItemComponent = this.listItem;\n      const changes = listOfTDSMenuItemComponent.changes;\n      const mergedObservable = merge(...[changes, ...listOfTDSMenuItemComponent.map(menu => menu.selected$)]);\n      changes.pipe(startWith(listOfTDSMenuItemComponent), switchMap(() => mergedObservable), startWith(true), map(() => listOfTDSMenuItemComponent.some(e => e.isSelected)), debounceTime(100), distinctUntilChanged(), takeUntil(this.destroy$)).subscribe(selected => {\n        this.setSelectedState(selected);\n      });\n    }\n    this.updateRouterActive();\n  }\n  setOpen(value) {\n    if (this.item.isOpen != value) {\n      this.item.isOpen = value;\n      this._cdr.markForCheck();\n    }\n  }\n  setOpenChildren(value) {\n    if (this.listItem.length) {\n      this.listItem.forEach(f => {\n        f.setOpenChildren(value);\n      });\n      this.setOpen(value);\n    }\n    this._cdr.markForCheck();\n  }\n  p_NavigateByUrl() {\n    if (!TDSHelperObject.hasValue(this.item.linkProps)) {\n      this.router?.navigateByUrl(this.item.link);\n    } else {\n      this.router?.navigate([this.item.link], {\n        queryParams: this.item.linkProps?.queryParams,\n        fragment: this.item.linkProps?.fragment,\n        queryParamsHandling: this.item.linkProps?.queryParamsHandling,\n        preserveFragment: this.item.linkProps?.preserveFragment,\n        skipLocationChange: this.item.linkProps?.skipLocationChange,\n        replaceUrl: this.item.linkProps?.replaceUrl,\n        state: this.item.linkProps?.state\n      });\n    }\n  }\n  static {\n    this.ɵfac = function TDSMenuItemComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSMenuItemComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(TDSMenuService), i0.ɵɵdirectiveInject(i2.RouterLink, 8), i0.ɵɵdirectiveInject(i2.RouterLinkWithHref, 8), i0.ɵɵdirectiveInject(i2.Router, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TDSMenuItemComponent,\n      selectors: [[\"tds-menu-item\"]],\n      viewQuery: function TDSMenuItemComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(CdkOverlayOrigin, 7, ElementRef);\n          i0.ɵɵviewQuery(TDSMenuItemComponent, 5);\n          i0.ɵɵviewQuery(RouterLink, 5);\n          i0.ɵɵviewQuery(RouterLinkWithHref, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.cdkOverlayOrigin = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listItem = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listOfRouterLink = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listOfRouterLinkWithHref = _t);\n        }\n      },\n      hostVars: 16,\n      hostBindings: function TDSMenuItemComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"tds-menu-item-disabled\", ctx.item.disabled)(\"tds-menu-item-hidden\", ctx.item.hidden)(\"tds-menu-item-active\", ctx.isSelected)(\"tds-menu-item-opened\", !!ctx.item.isOpen && ctx.hasListChild)(\"tds-menu-item-show-icon\", ctx.showIcon)(\"tds-menu-item-parent-is-group\", ctx.parentIsGroup)(\"tds-menu-item-hidden-icon\", !ctx.showIcon)(\"tds-menu-item-parent-is-item\", !ctx.parentIsGroup);\n        }\n      },\n      inputs: {\n        showIcon: \"showIcon\",\n        item: \"item\",\n        isSelected: \"isSelected\",\n        parentIsGroup: \"parentIsGroup\",\n        inlineCollapsed: \"inlineCollapsed\",\n        matchRouterExact: \"matchRouterExact\",\n        matchRouter: \"matchRouter\",\n        tdsTheme: \"tdsTheme\",\n        options: \"options\",\n        isOpenPopup: \"isOpenPopup\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 8,\n      vars: 7,\n      consts: [[1, \"tds-menu-item-wrapper\", 3, \"click\"], [1, \"tds-menu-item-badge\"], [\"status\", \"primary\", 3, \"tdsTheme\"], [1, \"tds-menu-item-title\"], [1, \"tds-menu-item-has-link\", 3, \"routerLink\", \"queryParams\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", \"routerLinkActive\"], [1, \"tds-menu-item-text\"], [1, \"tds-menu-item-arrow\"], [1, \"tds-menu-item-list-child\"], [1, \"truncate\"], [3, \"standalone\", \"count\", \"tdsStyle\", \"tdsTheme\"], [3, \"status\", \"rounded\", \"type\", \"tdsTheme\"], [1, \"tdsi-chevron-right-fill\"], [1, \"w-full\", \"flex\", \"flex-col\"], [3, \"options\", \"item\", \"isSelected\", \"matchRouterExact\", \"matchRouter\", \"tdsTheme\"]],\n      template: function TDSMenuItemComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵlistener(\"click\", function TDSMenuItemComponent_Template_div_click_0_listener($event) {\n            return ctx.onClickItem($event);\n          });\n          i0.ɵɵelementStart(1, \"div\", 1);\n          i0.ɵɵelement(2, \"tds-badge\", 2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"div\", 3);\n          i0.ɵɵtemplate(4, TDSMenuItemComponent_Conditional_4_Template, 5, 15, \"a\", 4)(5, TDSMenuItemComponent_Conditional_5_Template, 5, 3, \"span\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(6, TDSMenuItemComponent_Conditional_6_Template, 2, 0, \"div\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(7, TDSMenuItemComponent_Conditional_7_Template, 5, 5, \"div\", 7);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"invisible\", !(ctx.isSelected && !ctx.hasListChild));\n          i0.ɵɵproperty(\"tdsTheme\", ctx.tdsTheme);\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(ctx.hasLink ? 4 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(!ctx.hasLink ? 5 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx.hasListChild && !ctx.hasAllHidden ? 6 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx.hasListChild ? 7 : -1);\n        }\n      },\n      dependencies: [TDSMenuItemComponent, RouterModule, i2.RouterLink, i2.RouterLinkActive, TDSTagModule, i3.TDSTagComponent, TDSBadgeModule, i4.TDSBadgeComponent, TDSMapperPipeModule, i5.TDSMapperPipe],\n      encapsulation: 2,\n      data: {\n        animation: [menuCollapseMotion]\n      },\n      changeDetection: 0\n    });\n  }\n}\n__decorate([InputBoolean()], TDSMenuItemComponent.prototype, \"parentIsGroup\", void 0);\n__decorate([InputBoolean()], TDSMenuItemComponent.prototype, \"inlineCollapsed\", void 0);\n__decorate([InputBoolean()], TDSMenuItemComponent.prototype, \"matchRouterExact\", void 0);\n__decorate([InputBoolean()], TDSMenuItemComponent.prototype, \"matchRouter\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSMenuItemComponent, [{\n    type: Component,\n    args: [{\n      selector: 'tds-menu-item',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      animations: [menuCollapseMotion],\n      host: {\n        '[class.tds-menu-item-disabled]': 'item.disabled',\n        '[class.tds-menu-item-hidden]': 'item.hidden',\n        '[class.tds-menu-item-active]': 'isSelected',\n        '[class.tds-menu-item-opened]': '!!item.isOpen && hasListChild',\n        '[class.tds-menu-item-show-icon]': 'showIcon',\n        '[class.tds-menu-item-parent-is-group]': 'parentIsGroup',\n        '[class.tds-menu-item-hidden-icon]': '!showIcon',\n        '[class.tds-menu-item-parent-is-item]': '!parentIsGroup'\n      },\n      standalone: true,\n      imports: [RouterModule, TDSTagModule, TDSBadgeModule, TDSMapperPipeModule],\n      template: \"<div class=\\\"tds-menu-item-wrapper\\\"  (click)=\\\"onClickItem($event)\\\">\\r\\n  <div class=\\\"tds-menu-item-badge\\\">\\r\\n    <tds-badge [class.invisible]=\\\"!(isSelected && !hasListChild)\\\" status=\\\"primary\\\" [tdsTheme]=\\\"tdsTheme\\\" >\\r\\n    </tds-badge>\\r\\n  </div>\\r\\n  <div class=\\\"tds-menu-item-title\\\">\\r\\n    @if (hasLink) {\\r\\n      <a class=\\\"tds-menu-item-has-link\\\" [routerLink]=\\\"[item.link]\\\"\\r\\n        [queryParams]=\\\"item.linkProps?.queryParams\\\" [fragment]=\\\"item.linkProps?.fragment\\\"\\r\\n        [queryParamsHandling]=\\\"item.linkProps?.queryParamsHandling\\\"\\r\\n        [preserveFragment]=\\\"item.linkProps?.preserveFragment!\\\"\\r\\n        [skipLocationChange]=\\\"item.linkProps?.skipLocationChange!\\\" [replaceUrl]=\\\"item.linkProps?.replaceUrl!\\\"\\r\\n        [state]=\\\"item.linkProps?.state\\\" [routerLinkActive]=\\\"item.linkProps?.routerLinkActive || []\\\">\\r\\n        <span class=\\\"truncate\\\">{{item.name}}</span>\\r\\n        @if (item.badge) {\\r\\n          <tds-badge [standalone]=\\\"true\\\" [count]=\\\"item.badge.count\\\"\\r\\n          [tdsStyle]=\\\"item.badge.tdsStyle!\\\" [tdsTheme]=\\\"tdsTheme\\\"></tds-badge>\\r\\n        }\\r\\n        @if (item.tag) {\\r\\n          <tds-tag [status]='item.tag.status!' [rounded]=\\\"item.tag.rounded!\\\"\\r\\n          [type]=\\\"item.tag.type!\\\" [tdsTheme]=\\\"tdsTheme\\\">{{item.tag.text}}</tds-tag>\\r\\n        }\\r\\n      </a>\\r\\n    }\\r\\n    @if (!hasLink) {\\r\\n      <span class=\\\"tds-menu-item-text\\\">\\r\\n        <span class=\\\"truncate\\\">{{item.name}}</span>\\r\\n        @if (item.badge) {\\r\\n          <tds-badge [standalone]=\\\"true\\\" [count]=\\\"item.badge.count!\\\"\\r\\n          [tdsStyle]=\\\"item.badge.tdsStyle!\\\" [tdsTheme]=\\\"tdsTheme\\\"></tds-badge>\\r\\n        }\\r\\n        @if (item.tag) {\\r\\n          <tds-tag [status]='item.tag.status!' [rounded]=\\\"item.tag.rounded!\\\"\\r\\n          [type]=\\\"item.tag.type!\\\" [tdsTheme]=\\\"tdsTheme\\\">{{item.tag.text!}}</tds-tag>\\r\\n        }\\r\\n      </span>\\r\\n    }\\r\\n  </div>\\r\\n  @if (hasListChild && !hasAllHidden) {\\r\\n    <div class=\\\"tds-menu-item-arrow\\\">\\r\\n      <span class=\\\"tdsi-chevron-right-fill\\\"></span>\\r\\n    </div>\\r\\n  }\\r\\n</div>\\r\\n@if (hasListChild) {\\r\\n  <div class=\\\"tds-menu-item-list-child\\\"\\r\\n    [@menuCollapseMotion]=\\\"item.isOpen  | tdsMapper:mapperExpandState:hasAllHidden \\\">\\r\\n    <div class=\\\"w-full flex flex-col\\\">\\r\\n      @for (menu of item.listChild; track menu) {\\r\\n        <tds-menu-item [options]=\\\"options\\\" [item]=\\\"menu\\\" [isSelected]=\\\"menu.isSelected!\\\"\\r\\n          [matchRouterExact]=\\\"matchRouterExact\\\" [matchRouter]=\\\"matchRouter\\\" [tdsTheme]=\\\"tdsTheme\\\">\\r\\n        </tds-menu-item>\\r\\n      }\\r\\n    </div>\\r\\n  </div>\\r\\n}\"\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }, {\n    type: TDSMenuService\n  }, {\n    type: i2.RouterLink,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i2.RouterLinkWithHref,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i2.Router,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    showIcon: [{\n      type: Input\n    }],\n    item: [{\n      type: Input\n    }],\n    isSelected: [{\n      type: Input\n    }],\n    parentIsGroup: [{\n      type: Input\n    }],\n    inlineCollapsed: [{\n      type: Input\n    }],\n    matchRouterExact: [{\n      type: Input\n    }],\n    matchRouter: [{\n      type: Input\n    }],\n    tdsTheme: [{\n      type: Input\n    }],\n    options: [{\n      type: Input\n    }],\n    isOpenPopup: [{\n      type: Input\n    }],\n    listItem: [{\n      type: ViewChildren,\n      args: [TDSMenuItemComponent]\n    }],\n    cdkOverlayOrigin: [{\n      type: ViewChild,\n      args: [CdkOverlayOrigin, {\n        static: true,\n        read: ElementRef\n      }]\n    }],\n    listOfRouterLink: [{\n      type: ViewChildren,\n      args: [RouterLink]\n    }],\n    listOfRouterLinkWithHref: [{\n      type: ViewChildren,\n      args: [RouterLinkWithHref]\n    }]\n  });\n})();\nclass TDSMenuGroupInlineComponent {\n  get uid() {\n    return this.item ? this.item.uid : \"\";\n  }\n  constructor(_cdr, tdsMenuService, routerLink, routerLinkWithHref, router) {\n    this._cdr = _cdr;\n    this.tdsMenuService = tdsMenuService;\n    this.routerLink = routerLink;\n    this.routerLinkWithHref = routerLinkWithHref;\n    this.router = router;\n    this.destroy$ = new Subject();\n    this.showIcon = true;\n    this.isSelected = false;\n    this.options = {\n      background: 'bg-white dark:bg-d-neutral-3-200',\n      backgroundItem: 'bg-white dark:bg-d-neutral-3-200',\n      backgroundItemSelected: 'bg-neutral-3-50 dark:bg-d-neutral-3-300',\n      backgroundItemHover: 'dark:hover:bg-d-neutral-3-300  hover:bg-neutral-3-50'\n    };\n    this.tdsTheme = 'default';\n    this.cdkOverlayOrigin = null;\n    this.mode = 'dark';\n    this.IsActiveMatchOptions = {\n      paths: \"subset\",\n      matrixParams: \"ignored\",\n      queryParams: \"ignored\",\n      fragment: \"ignored\"\n    };\n    this.ngStyleItem = {};\n    this.mapperExpandState = (isOpen, hasAllHidden) => {\n      return isOpen && !hasAllHidden ? 'expanded' : 'collapsed';\n    };\n    if (router) {\n      this.router.events.pipe(takeUntil(this.destroy$), filter(e => e instanceof NavigationEnd)).subscribe(() => {\n        this.updateRouterActive();\n      });\n    }\n  }\n  ngOnInit() {\n    this.ngStyleItem = this.getStyleItem();\n  }\n  ngAfterContentInit() {\n    this.listenItemChangeSelected();\n    this.tdsMenuService.mode$.pipe(takeUntil(this.destroy$)).subscribe(res => {\n      this.mode = res;\n      this._cdr.markForCheck();\n    });\n  }\n  ngAfterViewInit() {\n    if (!this.hasListChild || this.hasAllHidden) {\n      this.updateRouterActive();\n    }\n  }\n  ngOnChanges(changes) {\n    if (changes['item']) {\n      this._cdr.markForCheck();\n    }\n    if (changes.isSelected) {\n      this.setSelectedState(this.isSelected);\n    }\n  }\n  onClickItem(e) {\n    if (this.item.disabled) {\n      e.stopPropagation();\n      e.preventDefault();\n    }\n    if (this.hasListChild && !this.hasAllHidden) {\n      this.tdsMenuService.groupMenuOpen$.next(this);\n      // this.item.isOpen = !this.item.isOpen;\n    } else {\n      if (!this.isSelected) this.tdsMenuService.onDescendantMenuItemClick(this);\n      if (this.hasLink) this.p_NavigateByUrl();\n    }\n  }\n  get hasListChild() {\n    return TDSHelperArray.hasListValue(this.item.listChild);\n  }\n  get hasAllHidden() {\n    let childs = this.item.listChild?.find(f => {\n      return !f.hidden;\n    });\n    return childs == undefined;\n  }\n  get hasGroupTitle() {\n    return this.item && TDSHelperString.hasValueString(this.item.groupTitle);\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  setSelectedState(value) {\n    this.isSelected = value;\n    this.item.isSelected = value;\n    if (this.isSelected) {\n      if (!this.item.isOpen) {\n        this.item.isOpen = true;\n      }\n    }\n    this.ngStyleItem = this.getStyleItem();\n    this._cdr.markForCheck();\n  }\n  updateRouterActive() {\n    if (this.hasListChild || !this.listOfRouterLink || !this.listOfRouterLinkWithHref || !this.router || !this.router.navigated) {\n      return;\n    }\n    Promise.resolve().then(() => {\n      const hasActiveLinks = this.hasActiveLinks();\n      if (this.isSelected !== hasActiveLinks) {\n        this.setSelectedState(hasActiveLinks);\n      }\n    });\n  }\n  hasActiveLinks() {\n    const isActiveCheckFn = this.isLinkActive(this.router);\n    return this.routerLink && isActiveCheckFn(this.routerLink) || this.routerLinkWithHref && isActiveCheckFn(this.routerLinkWithHref) || this.listOfRouterLink.some(isActiveCheckFn) || this.listOfRouterLinkWithHref.some(isActiveCheckFn);\n  }\n  isLinkActive(router) {\n    return link => router.isActive(link.urlTree, this.item.linkProps?.routerLinkActiveOptions || this.IsActiveMatchOptions);\n  }\n  setSelectedStateListChildren(uid) {\n    if (TDSHelperArray.hasListValue(this.listItem)) {\n      this.listItem.forEach(f => {\n        f.setSelectedStateListChildren(uid);\n      });\n    } else {\n      this.setSelectedState(uid === this.uid);\n    }\n  }\n  get hasLink() {\n    return !this.item.disabled && (!this.hasListChild || this.hasAllHidden) && TDSHelperObject.hasValue(this.item.link);\n  }\n  get hasIcon() {\n    return this.showIcon && this.item && (TDSHelperString.hasValueString(this.item.icon) || TDSHelperString.hasValueString(this.item.htmlIcon));\n  }\n  listenItemChangeSelected() {\n    if (TDSHelperArray.hasListValue(this.listItem)) {\n      const listOfTDSMenuItemComponent = this.listItem;\n      const changes = listOfTDSMenuItemComponent.changes;\n      const mergedObservable = merge(...[changes, ...listOfTDSMenuItemComponent.map(menu => menu.selected$)]);\n      changes.pipe(startWith(listOfTDSMenuItemComponent), switchMap(() => mergedObservable), startWith(true),\n      // debounceTime(200),\n      map(() => listOfTDSMenuItemComponent.some(e => e.isSelected)), takeUntil(this.destroy$)).subscribe(selected => {\n        if (selected != this.isSelected) {\n          if (!this.hasAllHidden) {\n            this.setSelectedState(selected);\n          } else {\n            //lấy link hiện tại khi tất ẩn tất cả menu con\n            const hasActiveLinks = this.hasActiveLinks();\n            this.setSelectedState(hasActiveLinks || selected);\n          }\n        }\n      });\n    }\n  }\n  setOpenStateListChildren(value) {\n    if (TDSHelperArray.hasListValue(this.listItem)) {\n      this.listItem.forEach(f => {\n        if (!f.hasListChild) f.setOpenChildren(value);\n      });\n      this.item.isOpen = value;\n      this._cdr.markForCheck();\n    }\n  }\n  getStyleItem() {\n    return {\n      [`${this.options.backgroundItemSelected}`]: this.isSelected,\n      [`${this.options.backgroundItem}`]: !this.isSelected,\n      [`${this.options.backgroundItemHover}`]: true,\n      [`cursor-pointer`]: !this.item.disabled,\n      ['py-2.5']: this.showIcon,\n      ['py-2']: !this.showIcon\n    };\n  }\n  p_NavigateByUrl() {\n    if (!TDSHelperObject.hasValue(this.item.linkProps)) {\n      this.router?.navigateByUrl(this.item.link);\n    } else {\n      this.router?.navigate([this.item.link], {\n        queryParams: this.item.linkProps?.queryParams,\n        fragment: this.item.linkProps?.fragment,\n        queryParamsHandling: this.item.linkProps?.queryParamsHandling,\n        preserveFragment: this.item.linkProps?.preserveFragment,\n        skipLocationChange: this.item.linkProps?.skipLocationChange,\n        replaceUrl: this.item.linkProps?.replaceUrl,\n        state: this.item.linkProps?.state\n      });\n    }\n  }\n  static {\n    this.ɵfac = function TDSMenuGroupInlineComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSMenuGroupInlineComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(TDSMenuService), i0.ɵɵdirectiveInject(i2.RouterLink, 8), i0.ɵɵdirectiveInject(i2.RouterLinkWithHref, 8), i0.ɵɵdirectiveInject(i2.Router, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TDSMenuGroupInlineComponent,\n      selectors: [[\"tds-menu-group-inline\"]],\n      contentQueries: function TDSMenuGroupInlineComponent_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, TDSMenuItemComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listItem = _t);\n        }\n      },\n      viewQuery: function TDSMenuGroupInlineComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(CdkOverlayOrigin, 7, ElementRef);\n          i0.ɵɵviewQuery(RouterLink, 5);\n          i0.ɵɵviewQuery(RouterLinkWithHref, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.cdkOverlayOrigin = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listOfRouterLink = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listOfRouterLinkWithHref = _t);\n        }\n      },\n      hostVars: 14,\n      hostBindings: function TDSMenuGroupInlineComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"tds-menu-group-inline-opened\", !!ctx.item.isOpen && ctx.hasListChild)(\"tds-menu-group-inline-hidden\", !!ctx.item.hidden)(\"tds-menu-group-inline-show-icon\", ctx.showIcon)(\"tds-menu-group-active\", ctx.isSelected)(\"tds-menu-light\", ctx.mode == \"light\")(\"tds-menu-dark\", ctx.mode == \"dark\")(\"tds-menu-default\", ctx.mode == \"default\");\n        }\n      },\n      inputs: {\n        showIcon: \"showIcon\",\n        item: \"item\",\n        isSelected: \"isSelected\",\n        options: \"options\",\n        tdsTheme: \"tdsTheme\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c2,\n      decls: 8,\n      vars: 6,\n      consts: [[1, \"tds-menu-group-inline-group-title\"], [1, \"tds-menu-group-inline-item\", 3, \"click\"], [1, \"tds-menu-group-inline-icon\", 3, \"menu-active\"], [1, \"tds-menu-group-inline-content-wrapper\"], [1, \"tds-menu-group-inline-has-link\", 3, \"routerLink\", \"queryParams\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", \"routerLinkActive\"], [1, \"tds-menu-group-inline-text\"], [1, \"tds-menu-group-inline-arrow\"], [1, \"tds-menu-list-child\"], [1, \"tds-menu-group-inline-icon\"], [1, \"tds-menu-html-icon\", 3, \"innerHTML\"], [1, \"tds-menu-group-inline-font-icon\", 3, \"ngClass\"], [1, \"tds-menu-group-inline-has-link\", 3, \"click\", \"routerLink\", \"queryParams\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", \"routerLinkActive\"], [1, \"tds-menu-name\"], [3, \"standalone\", \"overflowCount\", \"count\", \"tdsStyle\", \"tdsTheme\"], [3, \"status\", \"rounded\", \"type\", \"tdsTheme\"], [1, \"tdsi-chevron-right-fill\"], [1, \"tds-menu-list-child-wrapper\"]],\n      template: function TDSMenuGroupInlineComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, TDSMenuGroupInlineComponent_Conditional_0_Template, 2, 1, \"div\", 0);\n          i0.ɵɵelementStart(1, \"div\", 1);\n          i0.ɵɵlistener(\"click\", function TDSMenuGroupInlineComponent_Template_div_click_1_listener($event) {\n            return ctx.onClickItem($event);\n          });\n          i0.ɵɵtemplate(2, TDSMenuGroupInlineComponent_Conditional_2_Template, 3, 4, \"div\", 2);\n          i0.ɵɵelementStart(3, \"div\", 3);\n          i0.ɵɵtemplate(4, TDSMenuGroupInlineComponent_Conditional_4_Template, 5, 15, \"a\", 4)(5, TDSMenuGroupInlineComponent_Conditional_5_Template, 5, 3, \"span\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(6, TDSMenuGroupInlineComponent_Conditional_6_Template, 2, 0, \"div\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(7, TDSMenuGroupInlineComponent_Conditional_7_Template, 4, 5, \"div\", 7);\n        }\n        if (rf & 2) {\n          i0.ɵɵconditional(ctx.hasGroupTitle ? 0 : -1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(ctx.showIcon ? 2 : -1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(ctx.hasLink ? 4 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(!ctx.hasLink ? 5 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx.hasListChild && !ctx.hasAllHidden ? 6 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx.hasListChild ? 7 : -1);\n        }\n      },\n      dependencies: [NgClass, TDSPipesModule, i3$1.TDSSanitizerPipe, RouterModule, i2.RouterLink, i2.RouterLinkActive, TDSBadgeModule, i4.TDSBadgeComponent, TDSTagModule, i3.TDSTagComponent, TDSMapperPipeModule, i5.TDSMapperPipe],\n      encapsulation: 2,\n      data: {\n        animation: [menuCollapseMotion]\n      },\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSMenuGroupInlineComponent, [{\n    type: Component,\n    args: [{\n      selector: 'tds-menu-group-inline',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      animations: [menuCollapseMotion],\n      host: {\n        '[class.tds-menu-group-inline-opened]': '!!item.isOpen && hasListChild',\n        '[class.tds-menu-group-inline-hidden]': '!!item.hidden ',\n        '[class.tds-menu-group-inline-show-icon]': 'showIcon',\n        '[class.tds-menu-group-active]': 'isSelected',\n        '[class.tds-menu-light]': 'mode==\"light\"',\n        '[class.tds-menu-dark]': 'mode==\"dark\"',\n        '[class.tds-menu-default]': 'mode==\"default\"'\n      },\n      standalone: true,\n      imports: [NgClass, TDSPipesModule, RouterModule, TDSBadgeModule, TDSTagModule, TDSMapperPipeModule],\n      template: \"@if (hasGroupTitle) {\\r\\n<div class=\\\"tds-menu-group-inline-group-title\\\">\\r\\n  {{item.groupTitle}}\\r\\n</div>\\r\\n}\\r\\n<div class=\\\"tds-menu-group-inline-item\\\" (click)=\\\"onClickItem($event)\\\">\\r\\n  @if (showIcon) {\\r\\n  <div class=\\\"tds-menu-group-inline-icon\\\" [class.menu-active]=\\\"isSelected\\\">\\r\\n    @if (item.htmlIcon) {\\r\\n    <div class=\\\"tds-menu-html-icon\\\" [innerHTML]=\\\"item.htmlIcon | tdsSanitizer:'html'\\\"></div>\\r\\n    }\\r\\n    @if (hasIcon && !item.htmlIcon) {\\r\\n    <span class=\\\"tds-menu-group-inline-font-icon\\\" [ngClass]=\\\"item.icon!\\\"></span>\\r\\n    }\\r\\n  </div>\\r\\n  }\\r\\n  <div class=\\\"tds-menu-group-inline-content-wrapper\\\">\\r\\n    @if (hasLink) {\\r\\n    <a class=\\\"tds-menu-group-inline-has-link\\\" (click)=\\\"onClickItem($event)\\\" [routerLink]=\\\"[item.link]\\\"\\r\\n      [queryParams]=\\\"item.linkProps?.queryParams\\\" [fragment]=\\\"item.linkProps?.fragment\\\"\\r\\n      [queryParamsHandling]=\\\"item.linkProps?.queryParamsHandling\\\" [preserveFragment]=\\\"item.linkProps?.preserveFragment!\\\"\\r\\n      [skipLocationChange]=\\\"item.linkProps?.skipLocationChange!\\\" [replaceUrl]=\\\"item.linkProps?.replaceUrl!\\\"\\r\\n      [state]=\\\"item.linkProps?.state\\\" [routerLinkActive]=\\\"item.linkProps?.routerLinkActive || []\\\">\\r\\n      <span class=\\\"tds-menu-name\\\">{{item.name}}</span>\\r\\n      @if (item.badge) {\\r\\n      <tds-badge [standalone]=\\\"true\\\" [overflowCount]=\\\"item.badge.overflowCount!\\\" [count]=\\\"item.badge.count\\\"\\r\\n        [tdsStyle]=\\\"item.badge.tdsStyle!\\\" [tdsTheme]=\\\"tdsTheme\\\"></tds-badge>\\r\\n      }\\r\\n      @if (item.tag) {\\r\\n      <tds-tag [status]='item.tag.status!' [rounded]=\\\"item.tag.rounded!\\\" [type]=\\\"item.tag.type!\\\"\\r\\n        [tdsTheme]=\\\"tdsTheme\\\">{{item.tag.text}}</tds-tag>\\r\\n      }\\r\\n    </a>\\r\\n    }\\r\\n    @if (!hasLink) {\\r\\n    <span class=\\\"tds-menu-group-inline-text\\\">\\r\\n      <span class=\\\"tds-menu-name\\\">{{item.name}}</span>\\r\\n      @if (item.badge) {\\r\\n      <tds-badge [standalone]=\\\"true\\\" [overflowCount]=\\\"item.badge.overflowCount\\\" [count]=\\\"item.badge.count\\\"\\r\\n        [tdsStyle]=\\\"item.badge.tdsStyle!\\\" [tdsTheme]=\\\"tdsTheme\\\"></tds-badge>\\r\\n      }\\r\\n      @if (item.tag) {\\r\\n      <tds-tag [status]='item.tag.status!' [rounded]=\\\"item.tag.rounded!\\\" [type]=\\\"item.tag.type!\\\"\\r\\n        [tdsTheme]=\\\"tdsTheme\\\">{{item.tag.text}}</tds-tag>\\r\\n      }\\r\\n    </span>\\r\\n    }\\r\\n  </div>\\r\\n  @if (hasListChild && !hasAllHidden) {\\r\\n  <div class=\\\"tds-menu-group-inline-arrow\\\">\\r\\n    <span class=\\\"tdsi-chevron-right-fill\\\"></span>\\r\\n  </div>\\r\\n  }\\r\\n</div>\\r\\n@if (hasListChild) {\\r\\n<div class=\\\"tds-menu-list-child\\\" [@menuCollapseMotion]=\\\"item.isOpen  | tdsMapper:mapperExpandState:hasAllHidden\\\">\\r\\n  <div class=\\\"tds-menu-list-child-wrapper\\\">\\r\\n    <ng-content></ng-content>\\r\\n  </div>\\r\\n</div>\\r\\n}\"\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }, {\n    type: TDSMenuService\n  }, {\n    type: i2.RouterLink,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i2.RouterLinkWithHref,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i2.Router,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    showIcon: [{\n      type: Input\n    }],\n    item: [{\n      type: Input\n    }],\n    isSelected: [{\n      type: Input\n    }],\n    options: [{\n      type: Input\n    }],\n    tdsTheme: [{\n      type: Input\n    }],\n    listItem: [{\n      type: ContentChildren,\n      args: [TDSMenuItemComponent, {\n        descendants: true\n      }]\n    }],\n    cdkOverlayOrigin: [{\n      type: ViewChild,\n      args: [CdkOverlayOrigin, {\n        static: true,\n        read: ElementRef\n      }]\n    }],\n    listOfRouterLink: [{\n      type: ViewChildren,\n      args: [RouterLink]\n    }],\n    listOfRouterLinkWithHref: [{\n      type: ViewChildren,\n      args: [RouterLinkWithHref]\n    }]\n  });\n})();\nconst listOfVerticalPositions = [POSITION_MAP.rightTop, POSITION_MAP.right, POSITION_MAP.rightBottom,\n// POSITION_MAP.leftTop,\nPOSITION_MAP.left\n// POSITION_MAP.leftBottom\n];\nclass TDSMenuGroupPopupComponent {\n  get uid() {\n    return this.item ? this.item.uid : \"\";\n  }\n  constructor(_cdr, tdsMenuService, routerLink, routerLinkWithHref, router) {\n    this._cdr = _cdr;\n    this.tdsMenuService = tdsMenuService;\n    this.routerLink = routerLink;\n    this.routerLinkWithHref = routerLinkWithHref;\n    this.router = router;\n    this.destroy$ = new Subject();\n    this.isSelected = false;\n    this.inlineCollapsed = false;\n    this.options = {\n      background: 'bg-white dark:bg-d-neutral-3-200',\n      backgroundItem: 'bg-white dark:bg-d-neutral-3-200',\n      backgroundItemSelected: 'bg-neutral-3-50 dark:bg-d-neutral-3-300',\n      backgroundItemHover: 'dark:hover:bg-d-neutral-3-300  hover:bg-neutral-3-50'\n    };\n    this.tdsTheme = 'default';\n    this.open$ = new Subject();\n    this.cdkOverlayOrigin = null;\n    this.IsActiveMatchOptions = {\n      paths: \"subset\",\n      matrixParams: \"ignored\",\n      queryParams: \"ignored\",\n      fragment: \"ignored\"\n    };\n    this.position = 'rightTop';\n    this.triggerWidth = null;\n    this.overlayPositions = listOfVerticalPositions;\n    this.mode = 'dark';\n    if (router) {\n      this.router.events.pipe(takeUntil(this.destroy$), filter(e => e instanceof NavigationEnd)).subscribe(() => {\n        if (!this.hasListChild) {\n          this.updateRouterActive();\n        }\n      });\n    }\n  }\n  ngOnInit() {\n    this.open$.pipe(debounceTime(100), distinctUntilChanged(), takeUntil(this.destroy$)).subscribe(res => {\n      this.item.isOpen = res;\n      this._cdr.markForCheck();\n    });\n    this.tdsMenuService.mode$.subscribe(mode => {\n      if (TDSHelperObject.hasValue(mode) && this.mode !== mode) {\n        this.mode = mode;\n        this._cdr.markForCheck();\n      }\n    });\n  }\n  ngAfterContentInit() {\n    this.listenItemChangeSelected();\n  }\n  ngAfterViewInit() {\n    if (!this.hasListChild) {\n      this.updateRouterActive();\n    }\n  }\n  ngOnChanges(changes) {\n    if (changes['item']) {\n      this._cdr.markForCheck();\n    }\n  }\n  onClickItem(e) {\n    if (this.item.disabled) {\n      e.stopPropagation();\n      e.preventDefault();\n    }\n    if (!this.hasListChild || !this.hasAllHidden) {\n      this.tdsMenuService.onDescendantMenuItemClick(this);\n      if (this.hasLink) this.p_NavigateByUrl();\n    }\n  }\n  get hasListChild() {\n    return TDSHelperArray.hasListValue(this.item.listChild);\n  }\n  get hasAllHidden() {\n    let childs = this.item.listChild?.find(f => {\n      return !f.hidden;\n    });\n    return childs == undefined;\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  setSelectedState(value) {\n    this.isSelected = value;\n    this.item.isSelected = value;\n    this._cdr.markForCheck();\n  }\n  updateRouterActive() {\n    if (this.hasListChild || !this.listOfRouterLink || !this.listOfRouterLinkWithHref || !this.router || !this.router.navigated) {\n      return;\n    }\n    Promise.resolve().then(() => {\n      const hasActiveLinks = this.hasActiveLinks();\n      if (this.isSelected !== hasActiveLinks) {\n        this.setSelectedState(hasActiveLinks);\n      }\n    });\n  }\n  hasActiveLinks() {\n    const isActiveCheckFn = this.isLinkActive(this.router);\n    return this.routerLink && isActiveCheckFn(this.routerLink) || this.routerLinkWithHref && isActiveCheckFn(this.routerLinkWithHref) || this.listOfRouterLink.some(isActiveCheckFn) || this.listOfRouterLinkWithHref.some(isActiveCheckFn);\n  }\n  isLinkActive(router) {\n    return link => router.isActive(link.urlTree, this.item.linkProps?.routerLinkActiveOptions || this.IsActiveMatchOptions);\n  }\n  setSelectedStateListChildren(uid) {\n    if (TDSHelperArray.hasListValue(this.listItem)) {\n      this.listItem.forEach(f => {\n        f.setSelectedStateListChildren(uid);\n      });\n    } else {\n      this.setSelectedState(uid === this.uid);\n    }\n  }\n  get hasLink() {\n    return !this.item.disabled && (!this.hasListChild || this.hasAllHidden) && TDSHelperObject.hasValue(this.item.link);\n  }\n  get hasIcon() {\n    return this.item && (TDSHelperString.hasValueString(this.item.icon) || TDSHelperString.hasValueString(this.item.htmlIcon));\n  }\n  listenItemChangeSelected() {\n    if (TDSHelperArray.hasListValue(this.listItem) && this.listItem.length > 0) {\n      const listOfTDSMenuItemComponent = this.listItem;\n      const changes = listOfTDSMenuItemComponent.changes;\n      const mergedObservable = merge(...[changes, ...listOfTDSMenuItemComponent.map(menu => menu.selected$)]);\n      changes.pipe(startWith(listOfTDSMenuItemComponent), switchMap(() => mergedObservable), startWith(true), map(() => listOfTDSMenuItemComponent.some(e => e.isSelected)), takeUntil(this.destroy$)).subscribe(selected => {\n        if (selected != this.isSelected) if (!this.hasAllHidden) this.setSelectedState(selected);else {\n          //lấy link hiện tại khi tất ẩn tất cả menu con\n          const hasActiveLinks = this.hasActiveLinks();\n          this.setSelectedState(hasActiveLinks || selected);\n        }\n      });\n    }\n  }\n  setTriggerWidth() {\n    if (this.cdkOverlayOrigin) {\n      /** TODO: fast dom **/\n      this.triggerWidth = this.cdkOverlayOrigin.nativeElement.getBoundingClientRect().width;\n    }\n  }\n  onPositionChange(position) {\n    const placement = getPlacementName(position);\n    this.position = placement;\n    // console.log(placement)\n    // if (placement === 'rightTop' || placement === 'rightBottom' ) {\n    //   this.position = 'rightTop';\n    // } \n    // else if (placement === 'leftTop' || placement === 'leftBottom' || placement === 'left') {\n    //   this.position = 'left';\n    // }\n    this._cdr.markForCheck();\n  }\n  setOpenStateListChildren(value) {\n    if (TDSHelperArray.hasListValue(this.listItem)) {\n      this.listItem.forEach(f => {\n        f.setOpenChildren(value);\n      });\n      this.item.isOpen = value;\n      this._cdr.markForCheck();\n    }\n  }\n  setMouseState(value) {\n    this.open$.next(value);\n  }\n  p_NavigateByUrl() {\n    if (!TDSHelperObject.hasValue(this.item.linkProps)) {\n      this.router?.navigateByUrl(this.item.link);\n    } else {\n      this.router?.navigate([this.item.link], {\n        queryParams: this.item.linkProps?.queryParams,\n        fragment: this.item.linkProps?.fragment,\n        queryParamsHandling: this.item.linkProps?.queryParamsHandling,\n        preserveFragment: this.item.linkProps?.preserveFragment,\n        skipLocationChange: this.item.linkProps?.skipLocationChange,\n        replaceUrl: this.item.linkProps?.replaceUrl,\n        state: this.item.linkProps?.state\n      });\n    }\n  }\n  static {\n    this.ɵfac = function TDSMenuGroupPopupComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSMenuGroupPopupComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(TDSMenuService), i0.ɵɵdirectiveInject(i2.RouterLink, 8), i0.ɵɵdirectiveInject(i2.RouterLinkWithHref, 8), i0.ɵɵdirectiveInject(i2.Router, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TDSMenuGroupPopupComponent,\n      selectors: [[\"tds-menu-group-popup\"]],\n      contentQueries: function TDSMenuGroupPopupComponent_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, TDSMenuItemComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listItem = _t);\n        }\n      },\n      viewQuery: function TDSMenuGroupPopupComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(CdkOverlayOrigin, 7, ElementRef);\n          i0.ɵɵviewQuery(RouterLink, 5);\n          i0.ɵɵviewQuery(RouterLinkWithHref, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.cdkOverlayOrigin = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listOfRouterLink = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listOfRouterLinkWithHref = _t);\n        }\n      },\n      hostVars: 8,\n      hostBindings: function TDSMenuGroupPopupComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"mouseenter\", function TDSMenuGroupPopupComponent_mouseenter_HostBindingHandler() {\n            return ctx.setMouseState(true);\n          })(\"mouseleave\", function TDSMenuGroupPopupComponent_mouseleave_HostBindingHandler() {\n            return ctx.setMouseState(false);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"tds-menu-group-active\", ctx.isSelected)(\"tds-menu-light\", ctx.mode == \"light\")(\"tds-menu-dark\", ctx.mode == \"dark\")(\"tds-menu-default\", ctx.mode == \"default\");\n        }\n      },\n      inputs: {\n        item: \"item\",\n        isSelected: \"isSelected\",\n        inlineCollapsed: \"inlineCollapsed\",\n        options: \"options\",\n        tdsTheme: \"tdsTheme\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c2,\n      decls: 7,\n      vars: 10,\n      consts: [[\"origin\", \"cdkOverlayOrigin\"], [\"iconTmpl\", \"\"], [\"cdkOverlayOrigin\", \"\", 1, \"tds-menu-group-popup-wrapper\", 3, \"ngClass\"], [\"tooltipPlacement\", \"right\", \"tds-tooltip\", \"\", 1, \"tds-menu-group-popup-wrapper-link\", 3, \"tooltipTitle\"], [1, \"tds-menu-group-popup-wrapper-link\"], [\"cdkConnectedOverlay\", \"\", \"cdkConnectedOverlayPanelClass\", \"bottom-2.5\", 3, \"positionChange\", \"cdkConnectedOverlayPositions\", \"cdkConnectedOverlayOrigin\", \"cdkConnectedOverlayWidth\", \"cdkConnectedOverlayOpen\", \"cdkConnectedOverlayTransformOriginOn\"], [\"tooltipPlacement\", \"right\", \"tds-tooltip\", \"\", 1, \"tds-menu-group-popup-wrapper-link\", 3, \"click\", \"tooltipTitle\"], [1, \"h-5\", \"w-5\", \"flex\", \"items-center\"], [1, \"flex\", \"items-center\", 3, \"click\", \"routerLink\", \"queryParams\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", \"routerLinkActive\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"tds-menu-group-popup-wrapper-link\", 3, \"click\"], [1, \"h-full\", 2, \"min-width\", \"244px\", 3, \"ngClass\", \"margin-top\"], [1, \"h-full\", 2, \"min-width\", \"244px\", 3, \"mouseenter\", \"mouseleave\", \"ngClass\"], [1, \"w-full\", \"h-full\", \"flex\", \"flex-col\", \"relative\"], [1, \"absolute\", \"inset-0\", \"overflow-y-auto\", \"overflow-x-hidden\", \"no-scrollbar\", \"px-1\", \"py-1.5\"], [1, \"tds-menu-panel\"], [3, \"status\", \"dot\", \"tdsTheme\"], [1, \"tds-menu-font-icon\", 3, \"ngClass\"], [1, \"tds-menu-html-icon\", 3, \"innerHTML\"]],\n      template: function TDSMenuGroupPopupComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 2, 0);\n          i0.ɵɵtemplate(2, TDSMenuGroupPopupComponent_Conditional_2_Template, 2, 2, \"div\", 3)(3, TDSMenuGroupPopupComponent_Conditional_3_Template, 2, 1, \"div\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(4, TDSMenuGroupPopupComponent_ng_template_4_Template, 1, 1, \"ng-template\", 5);\n          i0.ɵɵlistener(\"positionChange\", function TDSMenuGroupPopupComponent_Template_ng_template_positionChange_4_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onPositionChange($event));\n          });\n          i0.ɵɵtemplate(5, TDSMenuGroupPopupComponent_ng_template_5_Template, 3, 3, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          const origin_r9 = i0.ɵɵreference(1);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(8, _c3, ctx.item.hidden));\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(ctx.hasLink ? 2 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(!ctx.hasLink ? 3 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"cdkConnectedOverlayPositions\", ctx.overlayPositions)(\"cdkConnectedOverlayOrigin\", origin_r9)(\"cdkConnectedOverlayWidth\", ctx.triggerWidth)(\"cdkConnectedOverlayOpen\", ctx.item.isOpen && !ctx.hasAllHidden)(\"cdkConnectedOverlayTransformOriginOn\", \".tds-menu-submenu\");\n        }\n      },\n      dependencies: [NgClass, TDSToolTipModule, i3$2.TDSTooltipDirective, RouterModule, i2.RouterLink, i2.RouterLinkActive, NgTemplateOutlet, OverlayModule, i4$1.CdkConnectedOverlay, i4$1.CdkOverlayOrigin, TDSBadgeModule, i4.TDSBadgeComponent, TDSPipesModule, i3$1.TDSSanitizerPipe],\n      encapsulation: 2,\n      data: {\n        animation: [slideMotion]\n      },\n      changeDetection: 0\n    });\n  }\n}\n__decorate([InputBoolean()], TDSMenuGroupPopupComponent.prototype, \"inlineCollapsed\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSMenuGroupPopupComponent, [{\n    type: Component,\n    args: [{\n      selector: 'tds-menu-group-popup',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        '[class.tds-menu-group-active]': 'isSelected',\n        '[class.tds-menu-light]': 'mode==\"light\"',\n        '[class.tds-menu-dark]': 'mode==\"dark\"',\n        '[class.tds-menu-default]': 'mode==\"default\"',\n        '(mouseenter)': 'setMouseState(true)',\n        '(mouseleave)': 'setMouseState(false)'\n      },\n      animations: [slideMotion],\n      standalone: true,\n      imports: [NgClass, TDSToolTipModule, RouterModule, NgTemplateOutlet, OverlayModule, TDSBadgeModule, TDSPipesModule],\n      template: \"<div class=\\\"tds-menu-group-popup-wrapper\\\" [ngClass]=\\\"{'hidden':item.hidden}\\\" cdkOverlayOrigin\\r\\n  #origin=\\\"cdkOverlayOrigin\\\">\\r\\n  @if (hasLink) {\\r\\n    <div class=\\\"tds-menu-group-popup-wrapper-link\\\" (click)=\\\"onClickItem($event)\\\"\\r\\n      [tooltipTitle]=\\\"item.name\\\" tooltipPlacement=\\\"right\\\" tds-tooltip>\\r\\n      @if (hasIcon) {\\r\\n        <div class=\\\"h-5 w-5 flex items-center\\\">\\r\\n          <a class=\\\"flex items-center\\\" (click)=\\\"onClickItem($event)\\\" [routerLink]=\\\"[item.link]\\\"\\r\\n            [queryParams]=\\\"item.linkProps?.queryParams\\\" [fragment]=\\\"item.linkProps?.fragment\\\"\\r\\n            [queryParamsHandling]=\\\"item.linkProps?.queryParamsHandling\\\"\\r\\n            [preserveFragment]=\\\"item.linkProps?.preserveFragment!\\\"\\r\\n            [skipLocationChange]=\\\"item.linkProps?.skipLocationChange!\\\" [replaceUrl]=\\\"item.linkProps?.replaceUrl!\\\"\\r\\n            [state]=\\\"item.linkProps?.state\\\" [routerLinkActive]=\\\"item.linkProps?.routerLinkActive || []\\\">\\r\\n            <ng-container [ngTemplateOutlet]=\\\"iconTmpl\\\" [ngTemplateOutletContext]=\\\"{ $implicit: item}\\\">\\r\\n            </ng-container>\\r\\n          </a>\\r\\n        </div>\\r\\n      }\\r\\n    </div>\\r\\n  }\\r\\n  @if (!hasLink) {\\r\\n    <div class=\\\"tds-menu-group-popup-wrapper-link\\\" (click)=\\\"onClickItem($event)\\\">\\r\\n      @if (hasIcon) {\\r\\n        <div class=\\\"h-5 w-5 flex items-center\\\">\\r\\n          <ng-container [ngTemplateOutlet]=\\\"iconTmpl\\\" [ngTemplateOutletContext]=\\\"{ $implicit: item}\\\">\\r\\n          </ng-container>\\r\\n        </div>\\r\\n      }\\r\\n    </div>\\r\\n  }\\r\\n</div>\\r\\n\\r\\n<ng-template cdkConnectedOverlay (positionChange)=\\\"onPositionChange($event)\\\" cdkConnectedOverlayPanelClass=\\\"bottom-2.5\\\"\\r\\n  [cdkConnectedOverlayPositions]=\\\"overlayPositions\\\" [cdkConnectedOverlayOrigin]=\\\"origin\\\"\\r\\n  [cdkConnectedOverlayWidth]=\\\"triggerWidth!\\\" [cdkConnectedOverlayOpen]=\\\"item.isOpen! && !hasAllHidden\\\"\\r\\n  [cdkConnectedOverlayTransformOriginOn]=\\\"'.tds-menu-submenu'\\\">\\r\\n  @if (hasListChild) {\\r\\n    <div\\r\\n      [ngClass]=\\\"{'tds-menu-light': mode=='light','tds-menu-dark': mode=='dark','tds-menu-default': mode=='default'}\\\"\\r\\n      class=\\\"h-full\\\" @slideMotion style=\\\"min-width: 244px;\\\" [style.margin-top.px]=\\\"position == 'rightTop'? -5 : 0\\\"\\r\\n      (mouseenter)='setMouseState(true)' (mouseleave)='setMouseState(false)'>\\r\\n      <div class=\\\"w-full h-full flex flex-col relative \\\">\\r\\n        <div class=\\\"absolute inset-0 overflow-y-auto overflow-x-hidden no-scrollbar  px-1 py-1.5\\\">\\r\\n          <div class=\\\"tds-menu-panel\\\">\\r\\n            <ng-content></ng-content>\\r\\n          </div>\\r\\n        </div>\\r\\n      </div>\\r\\n    </div>\\r\\n  }\\r\\n</ng-template>\\r\\n\\r\\n<ng-template #iconTmpl let-item>\\r\\n  @if (item.dot) {\\r\\n    <tds-badge [status]=\\\"item.dot.status\\\" [dot]=\\\"true\\\" [tdsTheme]=\\\"tdsTheme\\\">\\r\\n      <span [ngClass]=\\\"item.icon!\\\" class=\\\"tds-menu-font-icon\\\">\\r\\n      </span>\\r\\n    </tds-badge>\\r\\n  }\\r\\n  @if (!item.dot && !item.htmlIcon) {\\r\\n    <span [ngClass]=\\\"item.icon!\\\" class=\\\"tds-menu-font-icon\\\">\\r\\n    </span>\\r\\n  }\\r\\n  @if (!item.dot && item.htmlIcon) {\\r\\n    <span class=\\\"tds-menu-html-icon\\\"\\r\\n    [innerHTML]=\\\"item.htmlIcon | tdsSanitizer:'html'\\\"></span>\\r\\n  }\\r\\n</ng-template>\"\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }, {\n    type: TDSMenuService\n  }, {\n    type: i2.RouterLink,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i2.RouterLinkWithHref,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i2.Router,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    item: [{\n      type: Input\n    }],\n    isSelected: [{\n      type: Input\n    }],\n    inlineCollapsed: [{\n      type: Input\n    }],\n    options: [{\n      type: Input\n    }],\n    tdsTheme: [{\n      type: Input\n    }],\n    listItem: [{\n      type: ContentChildren,\n      args: [TDSMenuItemComponent, {\n        descendants: true\n      }]\n    }],\n    cdkOverlayOrigin: [{\n      type: ViewChild,\n      args: [CdkOverlayOrigin, {\n        static: true,\n        read: ElementRef\n      }]\n    }],\n    listOfRouterLink: [{\n      type: ViewChildren,\n      args: [RouterLink]\n    }],\n    listOfRouterLinkWithHref: [{\n      type: ViewChildren,\n      args: [RouterLinkWithHref]\n    }]\n  });\n})();\nclass TDSMenuFooterDirective {\n  static {\n    this.ɵfac = function TDSMenuFooterDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSMenuFooterDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TDSMenuFooterDirective,\n      selectors: [[\"\", \"tdsMenuFooter\", \"\"]],\n      exportAs: [\"tdsMenuFooter\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSMenuFooterDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[tdsMenuFooter]',\n      exportAs: 'tdsMenuFooter',\n      standalone: true\n    }]\n  }], null, null);\n})();\nfunction TDSMenuServiceFactory(serviceInsideDropDown, serviceOutsideDropDown) {\n  return serviceInsideDropDown ? serviceInsideDropDown : serviceOutsideDropDown;\n}\nclass TDSMenuComponent {\n  get menuFooter() {\n    return this.tdsMenuFooter || this.tdsMenuFooterChild;\n  }\n  constructor(element, renderer, _cdr, tdsMenuService) {\n    this.element = element;\n    this.renderer = renderer;\n    this._cdr = _cdr;\n    this.tdsMenuService = tdsMenuService;\n    this.destroy$ = new Subject();\n    this.listMenu = [];\n    this.showLogo = false;\n    this.data = [];\n    this.mode = 'dark';\n    this.showIcon = true;\n    this.showFooter = true;\n    this.inlineCollapsed = false;\n    this.matchRouterExact = false;\n    /**\n     *\n     */\n    this.matchRouter = true;\n    /**\n     * @deprecated\n     * Không còn dùng từ version 2.7.0\n     */\n    this.options = {\n      background: 'bg-white dark:bg-d-neutral-3-200',\n      backgroundItem: 'bg-white dark:bg-d-neutral-3-200',\n      backgroundItemSelected: 'bg-neutral-3-50 dark:bg-d-neutral-3-300',\n      backgroundItemHover: 'dark:hover:bg-d-neutral-3-300  hover:bg-neutral-3-50'\n    };\n    this.onClickItem = new EventEmitter();\n    this.onOpenChange = new EventEmitter();\n    this.inlineCollapsed$ = new BehaviorSubject(this.inlineCollapsed);\n    this.mapHasIcon = () => {\n      if (!this.showIcon) {\n        return false;\n      }\n      const lstIcon = this.listMenu.filter(f => TDSHelperString.hasValueString(f.icon) || TDSHelperString.hasValueString(f.htmlIcon));\n      return lstIcon.length > 0;\n    };\n    this.mapItemHasIcon = item => {\n      if (!this.showIcon) {\n        return false;\n      }\n      return TDSHelperString.hasValueString(item.icon);\n    };\n    this.mapItemHasIconHtml = item => {\n      if (!this.showIcon) {\n        return false;\n      }\n      return !TDSHelperString.hasValueString(item.icon) && TDSHelperString.hasValueString(item.htmlIcon);\n    };\n  }\n  ngOnInit() {\n    this.tdsMenuService.descendantMenuItemClick$.pipe(takeUntil(this.destroy$)).subscribe(menu => {\n      this.onClickItem.emit(menu.item);\n      if (TDSHelperArray.hasListValue(this.listOfTDSMenuGroupInlineComponent)) {\n        this.listOfTDSMenuGroupInlineComponent.forEach(group => {\n          group.setSelectedStateListChildren(menu.uid);\n        });\n      }\n      if (TDSHelperArray.hasListValue(this.listOfTDSMenuGroupPopupComponent)) {\n        this.listOfTDSMenuGroupPopupComponent.forEach(group => {\n          group.setSelectedStateListChildren(menu.uid);\n        });\n      }\n    });\n    this.tdsMenuService.groupMenuOpen$.pipe(takeUntil(this.destroy$)).subscribe(menu => {\n      if (TDSHelperArray.hasListValue(this.listOfTDSMenuGroupInlineComponent)) {\n        this.listOfTDSMenuGroupInlineComponent.forEach(group => {\n          if (group.hasListChild) group.setOpenStateListChildren(group == menu && !group.item.isOpen);\n        });\n      }\n    });\n  }\n  ngOnChanges(changes) {\n    const {\n      data,\n      inlineCollapsed,\n      mode\n    } = changes;\n    if (data) {\n      this.p_mapData(this.data);\n    }\n    if (inlineCollapsed) {\n      this.inlineCollapsed$.next(this.inlineCollapsed);\n    }\n    if (mode) {\n      this.tdsMenuService.onModeChange(this.mode);\n    }\n    this._cdr.markForCheck();\n  }\n  p_mapData(data) {\n    if (!TDSHelperArray.hasListValue(data)) {\n      this.listMenu = [];\n    } else {\n      this.listMenu = data.map(item => {\n        return this.p_mapItem(item);\n      });\n    }\n  }\n  p_mapItem(data) {\n    let listChild = [];\n    if (TDSHelperArray.hasListValue(data.listChild)) {\n      listChild = data.listChild.map(item => {\n        return this.p_mapItem(item);\n      });\n    }\n    return {\n      name: data.name,\n      icon: data.icon,\n      disabled: data.disabled,\n      isSelected: data.isSelected || false,\n      isOpen: data.isOpen || false,\n      link: data.link,\n      listChild: listChild,\n      uid: TDSHelperString.genid(),\n      hidden: data.hidden,\n      groupTitle: data.groupTitle,\n      htmlIcon: data.htmlIcon,\n      linkProps: data.linkProps,\n      badge: data.badge,\n      tag: data.tag,\n      dot: data.dot\n    };\n  }\n  ngAfterViewInit() {\n    this.inlineCollapsed$.pipe(takeUntil(this.destroy$)).subscribe(() => {\n      this.updateInlineCollapse();\n      this._cdr.markForCheck();\n    });\n  }\n  updateInlineCollapse() {\n    if (this.inlineCollapsed) {\n      if (this.listOfTDSMenuGroupInlineComponent.length) {\n        this.listOfTDSMenuGroupInlineComponent.forEach(group => {\n          group.setOpenStateListChildren(false);\n        });\n      }\n    } else {\n      if (this.listOfTDSMenuGroupPopupComponent.length) {\n        this.listOfTDSMenuGroupPopupComponent.forEach(group => {\n          group.setOpenStateListChildren(false);\n        });\n      }\n    }\n  }\n  setInlineCollapsed(inlineCollapsed) {\n    this.inlineCollapsed = inlineCollapsed;\n    this.inlineCollapsed$.next(inlineCollapsed);\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  onClickInlineCollapsed() {\n    this.inlineCollapsed = !this.inlineCollapsed;\n    this.onOpenChange.emit(this.inlineCollapsed);\n    this.inlineCollapsed$.next(this.inlineCollapsed);\n  }\n  get hasIcon() {\n    if (!this.showIcon) {\n      return false;\n    }\n    const lstIcon = this.listMenu.filter(f => TDSHelperString.hasValueString(f.icon) || TDSHelperString.hasValueString(f.htmlIcon));\n    return lstIcon.length > 0;\n  }\n  // TDSLayoutSiderComponent dùng cập nhật class css cho menu\n  addKlass(kClass) {\n    if (!this.hasKlass(kClass)) this.renderer.addClass(this.element.nativeElement, kClass);\n  }\n  removeKlass(kClass) {\n    if (this.hasKlass(kClass)) this.renderer.removeClass(this.element.nativeElement, kClass);\n  }\n  hasKlass(kClass) {\n    return this.element.nativeElement.classList.contains(kClass);\n  }\n  onSubNavbarOpen(value, child, parent) {\n    if (value) {\n      if (TDSHelperObject.hasValue(parent)) {\n        const childOpen = parent?.listChild?.filter(f => !!f.isOpen && f.uid !== child.uid);\n        childOpen?.forEach(element => {\n          element.isOpen = false;\n        });\n      } else {\n        const childOpen = this.listMenu?.filter(f => !!f.isOpen && f.uid !== child.uid);\n        childOpen?.forEach(element => {\n          element.isOpen = false;\n        });\n      }\n    }\n  }\n  static {\n    this.ɵfac = function TDSMenuComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSMenuComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(TDSMenuService));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TDSMenuComponent,\n      selectors: [[\"tds-menu\"]],\n      contentQueries: function TDSMenuComponent_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, _c6, 5);\n          i0.ɵɵcontentQuery(dirIndex, TDSMenuFooterDirective, 5, TemplateRef);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.logoText = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.tdsMenuFooterChild = _t.first);\n        }\n      },\n      viewQuery: function TDSMenuComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(TDSMenuGroupInlineComponent, 5);\n          i0.ɵɵviewQuery(TDSMenuGroupPopupComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listOfTDSMenuGroupInlineComponent = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listOfTDSMenuGroupPopupComponent = _t);\n        }\n      },\n      hostVars: 10,\n      hostBindings: function TDSMenuComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"w-full\", !ctx.inlineCollapsed)(\"tds-menu-collapsed\", ctx.inlineCollapsed)(\"tds-menu-light\", ctx.mode === \"light\")(\"tds-menu-dark\", ctx.mode === \"dark\")(\"tds-menu-default\", ctx.mode === \"default\");\n        }\n      },\n      inputs: {\n        showLogo: \"showLogo\",\n        data: \"data\",\n        mode: \"mode\",\n        showIcon: \"showIcon\",\n        showFooter: \"showFooter\",\n        inlineCollapsed: \"inlineCollapsed\",\n        matchRouterExact: \"matchRouterExact\",\n        matchRouter: \"matchRouter\",\n        options: \"options\",\n        tdsMenuFooter: \"tdsMenuFooter\"\n      },\n      outputs: {\n        onClickItem: \"onClickItem\",\n        onOpenChange: \"onOpenChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([/** use the top level service **/\n      TDSMenuService]), i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c8,\n      decls: 6,\n      vars: 8,\n      consts: [[1, \"tds-menu-wrapper\", 3, \"ngClass\"], [1, \"w-full\", \"tds-menu-header\"], [\"cdkScrollable\", \"\", 1, \"tds-menu-body\", 3, \"ngClass\"], [1, \"tds-menu-footer\", 3, \"ngClass\"], [3, \"options\", \"item\", \"tdsTheme\"], [3, \"class\", \"options\", \"item\", \"isSelected\", \"parentIsGroup\", \"matchRouterExact\", \"matchRouter\", \"tdsTheme\"], [3, \"options\", \"item\", \"isSelected\", \"parentIsGroup\", \"matchRouterExact\", \"matchRouter\", \"tdsTheme\"], [3, \"options\", \"showIcon\", \"item\", \"tdsTheme\"], [3, \"class\", \"options\", \"showIcon\", \"item\", \"isSelected\", \"parentIsGroup\", \"matchRouterExact\", \"matchRouter\", \"tdsTheme\"], [3, \"options\", \"showIcon\", \"item\", \"isSelected\", \"parentIsGroup\", \"matchRouterExact\", \"matchRouter\", \"tdsTheme\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"tds-menu-footer-svg\"], [\"width\", \"38\", \"height\", \"38\", \"viewBox\", \"0 0 38 38\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\"], [\"d\", \"M19 38C29.4934 38 38 29.4934 38 19C38 8.50658 29.4934 0 19 0C8.50662 0 3.04774e-05 8.50658 3.04774e-05 19L0 33.7778C0 36.3111 1.68889 38 4.22221 38H19Z\", 1, \"fill-current\"], [\"fill-rule\", \"evenodd\", \"clip-rule\", \"evenodd\", \"d\", \"M31.2241 13.2812C31.0776 12.3206 30.6631 11.4174 30.0256 10.6695C29.388 9.92167 28.5518 9.35776 27.6077 9.03903C26.6636 8.72029 25.6477 8.65889 24.6702 8.86151C23.6927 9.06413 22.791 9.52301 22.063 10.1884C21.5477 10.6357 21.1308 11.1797 20.8375 11.7876C20.5441 12.3956 20.3803 13.0548 20.356 13.7258C20.2959 15.5879 20.2823 17.4511 20.2687 19.3141C20.265 19.829 20.2612 20.3439 20.2565 20.8588C20.2547 20.9262 20.2679 20.9932 20.2954 21.0551C20.3229 21.117 20.3639 21.1724 20.4156 21.2173C20.4821 21.2335 20.5513 21.2353 20.6185 21.2226C20.6857 21.2099 20.7493 21.1831 20.8046 21.1439C21.7749 20.4924 22.7398 19.8342 23.6995 19.1693C23.7546 19.1262 23.8207 19.0984 23.8907 19.0889C23.9608 19.0794 24.0322 19.0884 24.0974 19.1152C24.9539 19.4042 25.8733 19.4708 26.7644 19.3085C27.7408 19.1524 28.6531 18.7352 29.399 18.1035C30.1449 17.4718 30.6949 16.6507 30.9873 15.7323C31.1136 15.3068 31.2132 14.8743 31.2858 14.4372V13.6736L31.2826 13.6506C31.2657 13.5284 31.2484 13.4031 31.2241 13.2812ZM26.0507 15.4521H25.1335C25.1335 15.0142 25.1748 14.7126 25.2575 14.5473C25.3401 14.3821 25.5632 14.1383 25.9268 13.8161C26.0755 13.6921 26.1994 13.5578 26.2986 13.413C26.3977 13.2686 26.4473 13.1138 26.4473 12.9485C26.4473 12.775 26.3812 12.6158 26.249 12.4711C26.1168 12.3266 25.9185 12.2544 25.6541 12.2544C25.3566 12.2544 25.1357 12.3412 24.9912 12.5147C24.8465 12.6882 24.7534 12.8493 24.7121 12.9981L23.8941 12.6758C24.0098 12.3288 24.2144 12.0313 24.5079 11.7835C24.801 11.5356 25.1831 11.4116 25.6541 11.4116C26.1168 11.4116 26.5175 11.5356 26.8563 11.7835C27.1951 12.0313 27.3645 12.3866 27.3645 12.8493C27.3645 13.122 27.3025 13.3554 27.1786 13.5494C27.0546 13.7437 26.8852 13.9483 26.6704 14.1631C26.3812 14.4358 26.2057 14.6506 26.1439 14.8076C26.0818 14.9646 26.0507 15.1794 26.0507 15.4521ZM26.02 17.1808C25.9 17.3008 25.7532 17.3608 25.5797 17.3608C25.4062 17.3608 25.2595 17.3008 25.1395 17.1808C25.0198 17.0612 24.96 16.9146 24.96 16.7411C24.96 16.5676 25.0198 16.421 25.1395 16.3013C25.2595 16.1814 25.4062 16.1214 25.5797 16.1214C25.7532 16.1214 25.9 16.1814 26.02 16.3013C26.1396 16.421 26.1994 16.5676 26.1994 16.7411C26.1994 16.9146 26.1396 17.0612 26.02 17.1808ZM12.3957 20.7207C12.4807 21.4003 12.8138 22.0278 13.3345 22.4891C13.8551 22.9505 14.5288 23.2151 15.2332 23.235C15.9376 23.2549 16.6259 23.0287 17.1733 22.5975C17.7206 22.1663 18.0907 21.5587 18.2162 20.885C18.2992 20.3939 18.3991 19.9056 18.4989 19.4175C18.5295 19.2678 18.5601 19.1181 18.5903 18.9684C18.7099 18.4734 18.6948 17.9566 18.5464 17.469C18.3979 16.9814 18.1213 16.5398 17.7437 16.1877C17.4238 15.8868 17.0443 15.6524 16.6282 15.4985C16.2121 15.3446 15.7682 15.2746 15.3233 15.2927C15.2183 15.3058 15.1127 15.3165 15.0072 15.3273C14.7787 15.3505 14.5504 15.3736 14.3286 15.4212C13.5736 15.6001 12.9171 16.0513 12.4921 16.6833C12.0672 17.3154 11.9057 18.0809 12.0405 18.8244L12.0722 18.9823C12.1883 19.56 12.3045 20.1387 12.3957 20.7207ZM22.1044 26.2182C22.0049 25.7562 21.8786 25.3328 21.3394 25.155C20.4947 24.8805 19.6563 24.5859 18.8179 24.2913L18.5252 24.1885C18.4642 24.161 18.3954 24.1544 18.33 24.1697C18.2647 24.1849 18.2065 24.2212 18.165 24.2726C17.8939 24.54 17.6187 24.8038 17.3435 25.0676C16.6674 25.7156 15.9911 26.3638 15.3746 27.0677L15.2543 27.0629C14.4452 26.2688 13.325 25.155 12.4838 24.2783C12.4433 24.2265 12.3862 24.1893 12.3214 24.1728C12.2566 24.1564 12.188 24.1615 12.1266 24.1875C11.1769 24.5238 10.2246 24.8554 9.26957 25.182C9.10862 25.2297 8.96474 25.3204 8.85442 25.4438C8.7441 25.5672 8.67181 25.7184 8.64584 25.8799C8.49729 26.5722 8.34396 27.2637 8.19064 27.9551C8.10165 28.3565 8.01266 28.7578 7.92461 29.1593C7.85105 29.495 7.77977 29.8312 7.70706 30.1741C7.67259 30.3367 7.6378 30.5008 7.60229 30.6671L23.0555 30.6593L23.0477 30.6175C23.0334 30.5398 23.0212 30.4741 23.0077 30.41C22.7066 29.0124 22.4055 27.6151 22.1044 26.2182Z\", \"fill\", \"white\"], [1, \"tds-menu-footer-icon\", \"tds-menu-footer-icon-inline-collapsed\", 3, \"click\", \"ngClass\"], [3, \"ngClass\"]],\n      template: function TDSMenuComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c7);\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, TDSMenuComponent_Conditional_1_Template, 3, 2, \"div\", 1);\n          i0.ɵɵelementStart(2, \"div\", 2);\n          i0.ɵɵtemplate(3, TDSMenuComponent_Conditional_3_Template, 2, 0)(4, TDSMenuComponent_Conditional_4_Template, 2, 0);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(5, TDSMenuComponent_Conditional_5_Template, 3, 4, \"div\", 3);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngClass\", ctx.mode);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx.showLogo ? 1 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(6, _c9, ctx.inlineCollapsed));\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx.inlineCollapsed ? 3 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(!ctx.inlineCollapsed ? 4 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx.hasIcon && ctx.showFooter ? 5 : -1);\n        }\n      },\n      dependencies: [NgClass, TDSOutletModule, TDSBadgeModule, TDSToolTipModule, TDSPipesModule, TDSNavBarModule, TDSMapperPipeModule, TDSMenuItemComponent, TDSMenuGroupPopupComponent, TDSMenuGroupInlineComponent, NgTemplateOutlet],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n__decorate([InputBoolean()], TDSMenuComponent.prototype, \"showLogo\", void 0);\n__decorate([InputBoolean()], TDSMenuComponent.prototype, \"inlineCollapsed\", void 0);\n__decorate([InputBoolean()], TDSMenuComponent.prototype, \"matchRouterExact\", void 0);\n__decorate([InputBoolean()], TDSMenuComponent.prototype, \"matchRouter\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSMenuComponent, [{\n    type: Component,\n    args: [{\n      selector: 'tds-menu',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        '[class.w-full]': `!inlineCollapsed`,\n        '[class.tds-menu-collapsed]': `inlineCollapsed`,\n        '[class.tds-menu-light]': 'mode === \"light\"',\n        '[class.tds-menu-dark]': 'mode === \"dark\"',\n        '[class.tds-menu-default]': 'mode === \"default\"'\n      },\n      providers: [/** use the top level service **/\n      TDSMenuService],\n      standalone: true,\n      imports: [NgClass, TDSOutletModule, TDSBadgeModule, TDSToolTipModule, TDSPipesModule, TDSNavBarModule, TDSMapperPipeModule, TDSMenuItemComponent, TDSMenuGroupPopupComponent, TDSMenuGroupInlineComponent, NgTemplateOutlet],\n      template: \"<div class=\\\"tds-menu-wrapper\\\" [ngClass]=\\\"mode\\\">\\r\\n  <!-- header -->\\r\\n  @if (showLogo) {\\r\\n    <div class=\\\"w-full tds-menu-header\\\">\\r\\n      @if (!inlineCollapsed) {\\r\\n        <ng-content select=\\\"[logo-text]\\\">\\r\\n        </ng-content>\\r\\n      }\\r\\n      @if (inlineCollapsed) {\\r\\n        <ng-content select=\\\"[logo]\\\">\\r\\n        </ng-content>\\r\\n      }\\r\\n    </div>\\r\\n  }\\r\\n  <!-- body -->\\r\\n  <div class=\\\"tds-menu-body\\\" [ngClass]=\\\"{' overflow-x-hidden':inlineCollapsed}\\\" cdkScrollable>\\r\\n    <!-- menu item  -->\\r\\n    @if (inlineCollapsed) {\\r\\n      @for (item of listMenu; track item) {\\r\\n        @if (!item.hidden) {\\r\\n          <tds-menu-group-popup [options]=\\\"options\\\" [item]=\\\"item\\\" [tdsTheme]=\\\"mode\\\">\\r\\n            @for (children of item.listChild; track children) {\\r\\n              <tds-menu-item\\r\\n                [class]=\\\"{'tds-menu-light':mode=='light','tds-menu-dark':mode=='dark','tds-menu-default':mode=='default'}\\\"\\r\\n                [options]=\\\"options\\\" [item]=\\\"children\\\" [isSelected]=\\\"children.isSelected!\\\"\\r\\n                [parentIsGroup]=\\\"true\\\" [matchRouterExact]=\\\"matchRouterExact\\\" [matchRouter]=\\\"matchRouter\\\" [tdsTheme]=\\\"mode\\\">\\r\\n              </tds-menu-item>\\r\\n            }\\r\\n          </tds-menu-group-popup>\\r\\n        }\\r\\n      }\\r\\n    }\\r\\n    @if (!inlineCollapsed) {\\r\\n      @for (item of listMenu; track item) {\\r\\n        @if (!item.hidden) {\\r\\n          <tds-menu-group-inline [options]=\\\"options\\\" [showIcon]=\\\"hasIcon\\\" [item]=\\\"item\\\" [tdsTheme]=\\\"mode\\\">\\r\\n            @for (children of item.listChild; track children) {\\r\\n              <tds-menu-item\\r\\n                [class]=\\\"{'tds-menu-light':mode=='light','tds-menu-dark':mode=='dark','tds-menu-default':mode=='default'}\\\"\\r\\n                [options]=\\\"options\\\" [showIcon]=\\\"hasIcon\\\" [item]=\\\"children\\\"\\r\\n                [isSelected]=\\\"children.isSelected!\\\" [parentIsGroup]=\\\"true\\\"\\r\\n                [matchRouterExact]=\\\"matchRouterExact\\\" [matchRouter]=\\\"matchRouter\\\" [tdsTheme]=\\\"mode\\\">\\r\\n              </tds-menu-item>\\r\\n            }\\r\\n          </tds-menu-group-inline>\\r\\n        }\\r\\n      }\\r\\n    }\\r\\n\\r\\n  </div>\\r\\n  <!-- footer -->\\r\\n  @if (hasIcon && showFooter) {\\r\\n    <div class=\\\"tds-menu-footer\\\" [ngClass]=\\\"{'flex-col':inlineCollapsed}\\\">\\r\\n      @if (menuFooter) {\\r\\n        <ng-container *ngTemplateOutlet=\\\"$any(menuFooter); context: { $implicit: inlineCollapsed }\\\">\\r\\n        </ng-container>\\r\\n      } @else {\\r\\n        <div class=\\\"tds-menu-footer-svg\\\">\\r\\n          <svg width=\\\"38\\\" height=\\\"38\\\" viewBox=\\\"0 0 38 38\\\" fill=\\\"none\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\">\\r\\n            <path\\r\\n              d=\\\"M19 38C29.4934 38 38 29.4934 38 19C38 8.50658 29.4934 0 19 0C8.50662 0 3.04774e-05 8.50658 3.04774e-05 19L0 33.7778C0 36.3111 1.68889 38 4.22221 38H19Z\\\"\\r\\n              class=\\\"fill-current\\\" />\\r\\n              <path fill-rule=\\\"evenodd\\\" clip-rule=\\\"evenodd\\\"\\r\\n                d=\\\"M31.2241 13.2812C31.0776 12.3206 30.6631 11.4174 30.0256 10.6695C29.388 9.92167 28.5518 9.35776 27.6077 9.03903C26.6636 8.72029 25.6477 8.65889 24.6702 8.86151C23.6927 9.06413 22.791 9.52301 22.063 10.1884C21.5477 10.6357 21.1308 11.1797 20.8375 11.7876C20.5441 12.3956 20.3803 13.0548 20.356 13.7258C20.2959 15.5879 20.2823 17.4511 20.2687 19.3141C20.265 19.829 20.2612 20.3439 20.2565 20.8588C20.2547 20.9262 20.2679 20.9932 20.2954 21.0551C20.3229 21.117 20.3639 21.1724 20.4156 21.2173C20.4821 21.2335 20.5513 21.2353 20.6185 21.2226C20.6857 21.2099 20.7493 21.1831 20.8046 21.1439C21.7749 20.4924 22.7398 19.8342 23.6995 19.1693C23.7546 19.1262 23.8207 19.0984 23.8907 19.0889C23.9608 19.0794 24.0322 19.0884 24.0974 19.1152C24.9539 19.4042 25.8733 19.4708 26.7644 19.3085C27.7408 19.1524 28.6531 18.7352 29.399 18.1035C30.1449 17.4718 30.6949 16.6507 30.9873 15.7323C31.1136 15.3068 31.2132 14.8743 31.2858 14.4372V13.6736L31.2826 13.6506C31.2657 13.5284 31.2484 13.4031 31.2241 13.2812ZM26.0507 15.4521H25.1335C25.1335 15.0142 25.1748 14.7126 25.2575 14.5473C25.3401 14.3821 25.5632 14.1383 25.9268 13.8161C26.0755 13.6921 26.1994 13.5578 26.2986 13.413C26.3977 13.2686 26.4473 13.1138 26.4473 12.9485C26.4473 12.775 26.3812 12.6158 26.249 12.4711C26.1168 12.3266 25.9185 12.2544 25.6541 12.2544C25.3566 12.2544 25.1357 12.3412 24.9912 12.5147C24.8465 12.6882 24.7534 12.8493 24.7121 12.9981L23.8941 12.6758C24.0098 12.3288 24.2144 12.0313 24.5079 11.7835C24.801 11.5356 25.1831 11.4116 25.6541 11.4116C26.1168 11.4116 26.5175 11.5356 26.8563 11.7835C27.1951 12.0313 27.3645 12.3866 27.3645 12.8493C27.3645 13.122 27.3025 13.3554 27.1786 13.5494C27.0546 13.7437 26.8852 13.9483 26.6704 14.1631C26.3812 14.4358 26.2057 14.6506 26.1439 14.8076C26.0818 14.9646 26.0507 15.1794 26.0507 15.4521ZM26.02 17.1808C25.9 17.3008 25.7532 17.3608 25.5797 17.3608C25.4062 17.3608 25.2595 17.3008 25.1395 17.1808C25.0198 17.0612 24.96 16.9146 24.96 16.7411C24.96 16.5676 25.0198 16.421 25.1395 16.3013C25.2595 16.1814 25.4062 16.1214 25.5797 16.1214C25.7532 16.1214 25.9 16.1814 26.02 16.3013C26.1396 16.421 26.1994 16.5676 26.1994 16.7411C26.1994 16.9146 26.1396 17.0612 26.02 17.1808ZM12.3957 20.7207C12.4807 21.4003 12.8138 22.0278 13.3345 22.4891C13.8551 22.9505 14.5288 23.2151 15.2332 23.235C15.9376 23.2549 16.6259 23.0287 17.1733 22.5975C17.7206 22.1663 18.0907 21.5587 18.2162 20.885C18.2992 20.3939 18.3991 19.9056 18.4989 19.4175C18.5295 19.2678 18.5601 19.1181 18.5903 18.9684C18.7099 18.4734 18.6948 17.9566 18.5464 17.469C18.3979 16.9814 18.1213 16.5398 17.7437 16.1877C17.4238 15.8868 17.0443 15.6524 16.6282 15.4985C16.2121 15.3446 15.7682 15.2746 15.3233 15.2927C15.2183 15.3058 15.1127 15.3165 15.0072 15.3273C14.7787 15.3505 14.5504 15.3736 14.3286 15.4212C13.5736 15.6001 12.9171 16.0513 12.4921 16.6833C12.0672 17.3154 11.9057 18.0809 12.0405 18.8244L12.0722 18.9823C12.1883 19.56 12.3045 20.1387 12.3957 20.7207ZM22.1044 26.2182C22.0049 25.7562 21.8786 25.3328 21.3394 25.155C20.4947 24.8805 19.6563 24.5859 18.8179 24.2913L18.5252 24.1885C18.4642 24.161 18.3954 24.1544 18.33 24.1697C18.2647 24.1849 18.2065 24.2212 18.165 24.2726C17.8939 24.54 17.6187 24.8038 17.3435 25.0676C16.6674 25.7156 15.9911 26.3638 15.3746 27.0677L15.2543 27.0629C14.4452 26.2688 13.325 25.155 12.4838 24.2783C12.4433 24.2265 12.3862 24.1893 12.3214 24.1728C12.2566 24.1564 12.188 24.1615 12.1266 24.1875C11.1769 24.5238 10.2246 24.8554 9.26957 25.182C9.10862 25.2297 8.96474 25.3204 8.85442 25.4438C8.7441 25.5672 8.67181 25.7184 8.64584 25.8799C8.49729 26.5722 8.34396 27.2637 8.19064 27.9551C8.10165 28.3565 8.01266 28.7578 7.92461 29.1593C7.85105 29.495 7.77977 29.8312 7.70706 30.1741C7.67259 30.3367 7.6378 30.5008 7.60229 30.6671L23.0555 30.6593L23.0477 30.6175C23.0334 30.5398 23.0212 30.4741 23.0077 30.41C22.7066 29.0124 22.4055 27.6151 22.1044 26.2182Z\\\"\\r\\n                fill=\\\"white\\\" />\\r\\n              </svg>\\r\\n            </div>\\r\\n            <div class=\\\"tds-menu-footer-icon tds-menu-footer-icon-inline-collapsed\\\"\\r\\n              [ngClass]=\\\"{'tds-menu-footer-icon-inline-collapsed':inlineCollapsed}\\\" (click)=\\\"onClickInlineCollapsed()\\\">\\r\\n              <span [ngClass]=\\\"{'tdsi-chevron-right-fill':inlineCollapsed, 'tdsi-chevron-left-fill':!inlineCollapsed }\\\"\\r\\n              ></span>\\r\\n            </div>\\r\\n          }\\r\\n        </div>\\r\\n      }\\r\\n    </div>\\r\\n\\r\\n\"\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: TDSMenuService\n  }], {\n    showLogo: [{\n      type: Input\n    }],\n    data: [{\n      type: Input\n    }],\n    mode: [{\n      type: Input\n    }],\n    showIcon: [{\n      type: Input\n    }],\n    showFooter: [{\n      type: Input\n    }],\n    inlineCollapsed: [{\n      type: Input\n    }],\n    matchRouterExact: [{\n      type: Input\n    }],\n    matchRouter: [{\n      type: Input\n    }],\n    options: [{\n      type: Input\n    }],\n    onClickItem: [{\n      type: Output\n    }],\n    onOpenChange: [{\n      type: Output\n    }],\n    listOfTDSMenuGroupInlineComponent: [{\n      type: ViewChildren,\n      args: [TDSMenuGroupInlineComponent]\n    }],\n    listOfTDSMenuGroupPopupComponent: [{\n      type: ViewChildren,\n      args: [TDSMenuGroupPopupComponent]\n    }],\n    logoText: [{\n      type: ContentChild,\n      args: ['logo-text']\n    }],\n    tdsMenuFooter: [{\n      type: Input\n    }],\n    tdsMenuFooterChild: [{\n      type: ContentChild,\n      args: [TDSMenuFooterDirective, {\n        static: false,\n        read: TemplateRef\n      }]\n    }]\n  });\n})();\nclass TDSMenuModule {\n  static {\n    this.ɵfac = function TDSMenuModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSMenuModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: TDSMenuModule,\n      imports: [TDSMenuComponent, TDSMenuItemComponent, TDSMenuGroupInlineComponent, TDSMenuGroupPopupComponent, TDSMenuFooterDirective],\n      exports: [TDSMenuComponent, TDSMenuItemComponent, TDSMenuFooterDirective, TDSMenuGroupInlineComponent, TDSMenuGroupPopupComponent]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [TDSMenuComponent, TDSMenuItemComponent, TDSMenuGroupInlineComponent, TDSMenuGroupPopupComponent]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSMenuModule, [{\n    type: NgModule,\n    args: [{\n      imports: [TDSMenuComponent, TDSMenuItemComponent, TDSMenuGroupInlineComponent, TDSMenuGroupPopupComponent, TDSMenuFooterDirective],\n      exports: [TDSMenuComponent, TDSMenuItemComponent, TDSMenuFooterDirective, TDSMenuGroupInlineComponent, TDSMenuGroupPopupComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TDSMenuComponent, TDSMenuFooterDirective, TDSMenuGroupInlineComponent, TDSMenuGroupPopupComponent, TDSMenuItemComponent, TDSMenuModule, TDSMenuService, TDSMenuServiceFactory };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,SAAS,QAAQ,GAAG,KAAK;AACvB,MAAI,eAAe,CAAC,GAAG;AACrB,QAAI;AAAA,EACN;AACA,QAAM,YAAY,aAAa,CAAC;AAChC,MAAI,QAAQ,MAAM,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,GAAG,WAAW,CAAC,CAAC,CAAC;AAE9D,MAAI,WAAW;AACb,QAAI,SAAS,OAAO,IAAI,GAAG,GAAG,EAAE,IAAI;AAAA,EACtC;AAEA,MAAI,KAAK,IAAI,IAAI,GAAG,IAAI,MAAU;AAChC,WAAO;AAAA,EACT;AAEA,MAAI,QAAQ,KAAK;AAIf,SAAK,IAAI,IAAI,IAAI,MAAM,MAAM,IAAI,OAAO,WAAW,OAAO,GAAG,CAAC;AAAA,EAChE,OAAO;AAGL,QAAI,IAAI,MAAM,WAAW,OAAO,GAAG,CAAC;AAAA,EACtC;AACA,SAAO;AACT;AAaA,SAAS,eAAe,GAAG;AACzB,SAAO,OAAO,MAAM,YAAY,EAAE,QAAQ,GAAG,MAAM,MAAM,WAAW,CAAC,MAAM;AAC7E;AAKA,SAAS,aAAa,GAAG;AACvB,SAAO,OAAO,MAAM,YAAY,EAAE,QAAQ,GAAG,MAAM;AACrD;AAKA,SAAS,WAAW,GAAG;AACrB,MAAI,WAAW,CAAC;AAChB,MAAI,MAAM,CAAC,KAAK,IAAI,KAAK,IAAI,GAAG;AAC9B,QAAI;AAAA,EACN;AACA,SAAO;AACT;AAKA,SAAS,oBAAoB,GAAG;AAC9B,MAAI,OAAO,KAAK,YAAY,KAAK,GAAG;AAClC,WAAO,GAAG,OAAO,CAAC,IAAI,GAAG;AAAA,EAC3B;AACA,SAAO;AACT;AAKA,SAAS,KAAK,GAAG;AACf,SAAO,EAAE,WAAW,IAAI,MAAM,IAAI,OAAO,CAAC;AAC5C;AAUA,SAAS,SAAS,GAAG,GAAG,GAAG;AACzB,SAAO;AAAA,IACL,GAAG,QAAQ,GAAG,GAAG,IAAI;AAAA,IACrB,GAAG,QAAQ,GAAG,GAAG,IAAI;AAAA,IACrB,GAAG,QAAQ,GAAG,GAAG,IAAI;AAAA,EACvB;AACF;AA0CA,SAAS,QAAQ,GAAG,GAAG,GAAG;AACxB,MAAI,IAAI,GAAG;AACT,SAAK;AAAA,EACP;AACA,MAAI,IAAI,GAAG;AACT,SAAK;AAAA,EACP;AACA,MAAI,IAAI,IAAI,GAAG;AACb,WAAO,KAAK,IAAI,MAAM,IAAI;AAAA,EAC5B;AACA,MAAI,IAAI,IAAI,GAAG;AACb,WAAO;AAAA,EACT;AACA,MAAI,IAAI,IAAI,GAAG;AACb,WAAO,KAAK,IAAI,MAAM,IAAI,IAAI,KAAK;AAAA,EACrC;AACA,SAAO;AACT;AAOA,SAAS,SAAS,GAAG,GAAG,GAAG;AACzB,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,QAAQ,GAAG,GAAG;AAClB,MAAI,QAAQ,GAAG,GAAG;AAClB,MAAI,QAAQ,GAAG,GAAG;AAClB,MAAI,MAAM,GAAG;AAEX,QAAI;AACJ,QAAI;AACJ,QAAI;AAAA,EACN,OAAO;AACL,UAAM,IAAI,IAAI,MAAM,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI;AAC9C,UAAM,IAAI,IAAI,IAAI;AAClB,QAAI,QAAQ,GAAG,GAAG,IAAI,IAAI,CAAC;AAC3B,QAAI,QAAQ,GAAG,GAAG,CAAC;AACnB,QAAI,QAAQ,GAAG,GAAG,IAAI,IAAI,CAAC;AAAA,EAC7B;AACA,SAAO;AAAA,IACL,GAAG,IAAI;AAAA,IACP,GAAG,IAAI;AAAA,IACP,GAAG,IAAI;AAAA,EACT;AACF;AAOA,SAAS,SAAS,GAAG,GAAG,GAAG;AACzB,MAAI,QAAQ,GAAG,GAAG;AAClB,MAAI,QAAQ,GAAG,GAAG;AAClB,MAAI,QAAQ,GAAG,GAAG;AAClB,QAAM,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC;AAC5B,QAAM,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC;AAC5B,MAAI,IAAI;AACR,QAAM,IAAI;AACV,QAAM,IAAI,MAAM;AAChB,QAAM,IAAI,QAAQ,IAAI,IAAI,IAAI;AAC9B,MAAI,QAAQ,KAAK;AACf,QAAI;AAAA,EACN,OAAO;AACL,YAAQ,KAAK;AAAA,MACX,KAAK;AACH,aAAK,IAAI,KAAK,KAAK,IAAI,IAAI,IAAI;AAC/B;AAAA,MACF,KAAK;AACH,aAAK,IAAI,KAAK,IAAI;AAClB;AAAA,MACF,KAAK;AACH,aAAK,IAAI,KAAK,IAAI;AAClB;AAAA,MACF;AACE;AAAA,IACJ;AACA,SAAK;AAAA,EACP;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAOA,SAAS,SAAS,GAAG,GAAG,GAAG;AACzB,MAAI,QAAQ,GAAG,GAAG,IAAI;AACtB,MAAI,QAAQ,GAAG,GAAG;AAClB,MAAI,QAAQ,GAAG,GAAG;AAClB,QAAM,IAAI,KAAK,MAAM,CAAC;AACtB,QAAM,IAAI,IAAI;AACd,QAAM,IAAI,KAAK,IAAI;AACnB,QAAM,IAAI,KAAK,IAAI,IAAI;AACvB,QAAM,IAAI,KAAK,KAAK,IAAI,KAAK;AAC7B,QAAM,MAAM,IAAI;AAChB,QAAM,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,GAAG;AAChC,QAAM,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,GAAG;AAChC,QAAM,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,GAAG;AAChC,SAAO;AAAA,IACL,GAAG,IAAI;AAAA,IACP,GAAG,IAAI;AAAA,IACP,GAAG,IAAI;AAAA,EACT;AACF;AAOA,SAAS,SAAS,GAAG,GAAG,GAAG,YAAY;AACrC,QAAM,MAAM,CAAC,KAAK,KAAK,MAAM,CAAC,EAAE,SAAS,EAAE,CAAC,GAAG,KAAK,KAAK,MAAM,CAAC,EAAE,SAAS,EAAE,CAAC,GAAG,KAAK,KAAK,MAAM,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC;AAEjH,MAAI,cAAc,IAAI,CAAC,EAAE,WAAW,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC,KAAK,IAAI,CAAC,EAAE,WAAW,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC,KAAK,IAAI,CAAC,EAAE,WAAW,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC,GAAG;AACnI,WAAO,IAAI,CAAC,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,EAAE,OAAO,CAAC;AAAA,EAC9D;AACA,SAAO,IAAI,KAAK,EAAE;AACpB;AA6BA,SAAS,oBAAoB,GAAG;AAC9B,SAAO,gBAAgB,CAAC,IAAI;AAC9B;AAEA,SAAS,gBAAgB,KAAK;AAC5B,SAAO,SAAS,KAAK,EAAE;AACzB;AAaA,IAAM,QAAQ;AAAA,EACZ,WAAW;AAAA,EACX,cAAc;AAAA,EACd,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,gBAAgB;AAAA,EAChB,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,WAAW;AAAA,EACX,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,OAAO;AAAA,EACP,gBAAgB;AAAA,EAChB,UAAU;AAAA,EACV,SAAS;AAAA,EACT,MAAM;AAAA,EACN,UAAU;AAAA,EACV,UAAU;AAAA,EACV,eAAe;AAAA,EACf,UAAU;AAAA,EACV,WAAW;AAAA,EACX,UAAU;AAAA,EACV,WAAW;AAAA,EACX,aAAa;AAAA,EACb,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,eAAe;AAAA,EACf,eAAe;AAAA,EACf,eAAe;AAAA,EACf,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,aAAa;AAAA,EACb,SAAS;AAAA,EACT,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,aAAa;AAAA,EACb,aAAa;AAAA,EACb,SAAS;AAAA,EACT,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AAAA,EACP,aAAa;AAAA,EACb,MAAM;AAAA,EACN,UAAU;AAAA,EACV,SAAS;AAAA,EACT,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AAAA,EACP,eAAe;AAAA,EACf,UAAU;AAAA,EACV,WAAW;AAAA,EACX,cAAc;AAAA,EACd,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,sBAAsB;AAAA,EACtB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,WAAW;AAAA,EACX,aAAa;AAAA,EACb,eAAe;AAAA,EACf,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,gBAAgB;AAAA,EAChB,gBAAgB;AAAA,EAChB,aAAa;AAAA,EACb,MAAM;AAAA,EACN,WAAW;AAAA,EACX,OAAO;AAAA,EACP,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,kBAAkB;AAAA,EAClB,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,cAAc;AAAA,EACd,WAAW;AAAA,EACX,WAAW;AAAA,EACX,UAAU;AAAA,EACV,aAAa;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,eAAe;AAAA,EACf,WAAW;AAAA,EACX,eAAe;AAAA,EACf,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,eAAe;AAAA,EACf,KAAK;AAAA,EACL,WAAW;AAAA,EACX,WAAW;AAAA,EACX,aAAa;AAAA,EACb,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,MAAM;AAAA,EACN,aAAa;AAAA,EACb,WAAW;AAAA,EACX,KAAK;AAAA,EACL,MAAM;AAAA,EACN,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AAAA,EACP,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,aAAa;AACf;AAoBA,SAAS,WAAW,OAAO;AACzB,MAAI,MAAM;AAAA,IACR,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACA,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,KAAK;AACT,MAAI,SAAS;AACb,MAAI,OAAO,UAAU,UAAU;AAC7B,YAAQ,oBAAoB,KAAK;AAAA,EACnC;AACA,MAAI,OAAO,UAAU,UAAU;AAC7B,QAAI,eAAe,MAAM,CAAC,KAAK,eAAe,MAAM,CAAC,KAAK,eAAe,MAAM,CAAC,GAAG;AACjF,YAAM,SAAS,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC;AACxC,WAAK;AACL,eAAS,OAAO,MAAM,CAAC,EAAE,OAAO,EAAE,MAAM,MAAM,SAAS;AAAA,IACzD,WAAW,eAAe,MAAM,CAAC,KAAK,eAAe,MAAM,CAAC,KAAK,eAAe,MAAM,CAAC,GAAG;AACxF,UAAI,oBAAoB,MAAM,CAAC;AAC/B,UAAI,oBAAoB,MAAM,CAAC;AAC/B,YAAM,SAAS,MAAM,GAAG,GAAG,CAAC;AAC5B,WAAK;AACL,eAAS;AAAA,IACX,WAAW,eAAe,MAAM,CAAC,KAAK,eAAe,MAAM,CAAC,KAAK,eAAe,MAAM,CAAC,GAAG;AACxF,UAAI,oBAAoB,MAAM,CAAC;AAC/B,UAAI,oBAAoB,MAAM,CAAC;AAC/B,YAAM,SAAS,MAAM,GAAG,GAAG,CAAC;AAC5B,WAAK;AACL,eAAS;AAAA,IACX;AACA,QAAI,OAAO,UAAU,eAAe,KAAK,OAAO,GAAG,GAAG;AACpD,UAAI,MAAM;AAAA,IACZ;AAAA,EACF;AACA,MAAI,WAAW,CAAC;AAChB,SAAO;AAAA,IACL;AAAA,IACA,QAAQ,MAAM,UAAU;AAAA,IACxB,GAAG,KAAK,IAAI,KAAK,KAAK,IAAI,IAAI,GAAG,CAAC,CAAC;AAAA,IACnC,GAAG,KAAK,IAAI,KAAK,KAAK,IAAI,IAAI,GAAG,CAAC,CAAC;AAAA,IACnC,GAAG,KAAK,IAAI,KAAK,KAAK,IAAI,IAAI,GAAG,CAAC,CAAC;AAAA,IACnC;AAAA,EACF;AACF;AAEA,IAAM,cAAc;AAEpB,IAAM,aAAa;AAEnB,IAAM,WAAW,MAAM,UAAU,QAAQ,WAAW;AAIpD,IAAM,oBAAoB,cAAc,QAAQ,aAAa,QAAQ,aAAa,QAAQ;AAC1F,IAAM,oBAAoB,cAAc,QAAQ,aAAa,QAAQ,aAAa,QAAQ,aAAa,QAAQ;AAC/G,IAAM,WAAW;AAAA,EACf,UAAU,IAAI,OAAO,QAAQ;AAAA,EAC7B,KAAK,IAAI,OAAO,QAAQ,iBAAiB;AAAA,EACzC,MAAM,IAAI,OAAO,SAAS,iBAAiB;AAAA,EAC3C,KAAK,IAAI,OAAO,QAAQ,iBAAiB;AAAA,EACzC,MAAM,IAAI,OAAO,SAAS,iBAAiB;AAAA,EAC3C,KAAK,IAAI,OAAO,QAAQ,iBAAiB;AAAA,EACzC,MAAM,IAAI,OAAO,SAAS,iBAAiB;AAAA,EAC3C,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AACR;AAKA,SAAS,oBAAoB,OAAO;AAClC,UAAQ,MAAM,KAAK,EAAE,YAAY;AACjC,MAAI,MAAM,WAAW,GAAG;AACtB,WAAO;AAAA,EACT;AACA,MAAI,QAAQ;AACZ,MAAI,MAAM,KAAK,GAAG;AAChB,YAAQ,MAAM,KAAK;AACnB,YAAQ;AAAA,EACV,WAAW,UAAU,eAAe;AAClC,WAAO;AAAA,MACL,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,QAAQ;AAAA,IACV;AAAA,EACF;AAKA,MAAI,QAAQ,SAAS,IAAI,KAAK,KAAK;AACnC,MAAI,OAAO;AACT,WAAO;AAAA,MACL,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,IACZ;AAAA,EACF;AACA,UAAQ,SAAS,KAAK,KAAK,KAAK;AAChC,MAAI,OAAO;AACT,WAAO;AAAA,MACL,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,IACZ;AAAA,EACF;AACA,UAAQ,SAAS,IAAI,KAAK,KAAK;AAC/B,MAAI,OAAO;AACT,WAAO;AAAA,MACL,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,IACZ;AAAA,EACF;AACA,UAAQ,SAAS,KAAK,KAAK,KAAK;AAChC,MAAI,OAAO;AACT,WAAO;AAAA,MACL,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,IACZ;AAAA,EACF;AACA,UAAQ,SAAS,IAAI,KAAK,KAAK;AAC/B,MAAI,OAAO;AACT,WAAO;AAAA,MACL,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,IACZ;AAAA,EACF;AACA,UAAQ,SAAS,KAAK,KAAK,KAAK;AAChC,MAAI,OAAO;AACT,WAAO;AAAA,MACL,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,IACZ;AAAA,EACF;AACA,UAAQ,SAAS,KAAK,KAAK,KAAK;AAChC,MAAI,OAAO;AACT,WAAO;AAAA,MACL,GAAG,gBAAgB,MAAM,CAAC,CAAC;AAAA,MAC3B,GAAG,gBAAgB,MAAM,CAAC,CAAC;AAAA,MAC3B,GAAG,gBAAgB,MAAM,CAAC,CAAC;AAAA,MAC3B,GAAG,oBAAoB,MAAM,CAAC,CAAC;AAAA,MAC/B,QAAQ,QAAQ,SAAS;AAAA,IAC3B;AAAA,EACF;AACA,UAAQ,SAAS,KAAK,KAAK,KAAK;AAChC,MAAI,OAAO;AACT,WAAO;AAAA,MACL,GAAG,gBAAgB,MAAM,CAAC,CAAC;AAAA,MAC3B,GAAG,gBAAgB,MAAM,CAAC,CAAC;AAAA,MAC3B,GAAG,gBAAgB,MAAM,CAAC,CAAC;AAAA,MAC3B,QAAQ,QAAQ,SAAS;AAAA,IAC3B;AAAA,EACF;AACA,UAAQ,SAAS,KAAK,KAAK,KAAK;AAChC,MAAI,OAAO;AACT,WAAO;AAAA,MACL,GAAG,gBAAgB,MAAM,CAAC,IAAI,MAAM,CAAC,CAAC;AAAA,MACtC,GAAG,gBAAgB,MAAM,CAAC,IAAI,MAAM,CAAC,CAAC;AAAA,MACtC,GAAG,gBAAgB,MAAM,CAAC,IAAI,MAAM,CAAC,CAAC;AAAA,MACtC,GAAG,oBAAoB,MAAM,CAAC,IAAI,MAAM,CAAC,CAAC;AAAA,MAC1C,QAAQ,QAAQ,SAAS;AAAA,IAC3B;AAAA,EACF;AACA,UAAQ,SAAS,KAAK,KAAK,KAAK;AAChC,MAAI,OAAO;AACT,WAAO;AAAA,MACL,GAAG,gBAAgB,MAAM,CAAC,IAAI,MAAM,CAAC,CAAC;AAAA,MACtC,GAAG,gBAAgB,MAAM,CAAC,IAAI,MAAM,CAAC,CAAC;AAAA,MACtC,GAAG,gBAAgB,MAAM,CAAC,IAAI,MAAM,CAAC,CAAC;AAAA,MACtC,QAAQ,QAAQ,SAAS;AAAA,IAC3B;AAAA,EACF;AACA,SAAO;AACT;AAKA,SAAS,eAAe,OAAO;AAC7B,SAAO,QAAQ,SAAS,SAAS,KAAK,OAAO,KAAK,CAAC,CAAC;AACtD;;;ACzpBA,IAAM,MAAM,CAAC,GAAG;AAChB,SAAS,uCAAuC,IAAI,KAAK;AACvD,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,WAAW,SAAS,SAAS,6DAA6D,QAAQ;AACnG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,aAAa,MAAM,CAAC;AAAA,IACnD,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACF;AACA,SAAS,4CAA4C,IAAI,KAAK;AAC5D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,GAAG,CAAC;AACjC,IAAG,UAAU,GAAG,MAAM;AACtB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,OAAO,OAAO;AAC5B,IAAG,YAAY,SAAS,OAAO,YAAY,OAAO,YAAY,OAAO;AAAA,EACvE;AACF;AACA,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,YAAY,MAAM,UAAU,YAAY,QAAQ;AAC9C,SAAK,OAAO;AACZ,SAAK,WAAW;AAChB,SAAK,aAAa;AAClB,SAAK,SAAS;AACd,SAAK,OAAO;AACZ,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,SAAK,UAAU;AACf,SAAK,OAAO;AACZ,SAAK,WAAW;AAChB,SAAK,WAAW;AAIhB,SAAK,UAAU;AACf,SAAK,QAAQ,IAAI,aAAa;AAC9B,SAAK,gBAAgB,IAAI,aAAa;AACtC,SAAK,WAAW;AAChB,SAAK,WAAW,IAAI,QAAQ;AAAA,EAC9B;AAAA,EACA,WAAW;AACT,SAAK,OAAO,kBAAkB,MAAM;AAClC,gBAAU,KAAK,WAAW,eAAe,OAAO,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM;AAC/F,aAAK,OAAO,IAAI,MAAM,KAAK,oBAAoB,CAAC;AAAA,MAClD,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,YAAY,SAAS;AACnB,SAAK,KAAK,aAAa;AAAA,EACzB;AAAA,EACA,aAAa,GAAG;AACd,QAAI,KAAK,SAAS,eAAe,CAAC,KAAK,UAAU;AAC/C,WAAK,MAAM,KAAK,CAAC;AACjB,UAAI,CAAC,EAAE,kBAAkB;AACvB,aAAK,SAAS,YAAY,KAAK,SAAS,WAAW,KAAK,WAAW,aAAa,GAAG,KAAK,WAAW,aAAa;AAAA,MAClH;AAAA,IACF;AAAA,EACF;AAAA,EACA,sBAAsB;AACpB,QAAI,KAAK,SAAS,eAAe,CAAC,KAAK,UAAU;AAC/C,WAAK,UAAU,CAAC,KAAK;AACrB,WAAK,cAAc,KAAK,KAAK,OAAO;AAAA,IACtC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,wBAAwB,mBAAmB;AAC9D,aAAO,KAAK,qBAAqB,kBAAoB,kBAAqB,iBAAiB,GAAM,kBAAqB,SAAS,GAAM,kBAAqB,UAAU,GAAM,kBAAqB,MAAM,CAAC;AAAA,IACxM;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,SAAS,CAAC;AAAA,MACvB,UAAU;AAAA,MACV,cAAc,SAAS,6BAA6B,IAAI,KAAK;AAC3D,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,IAAI,QAAQ;AAC1B,UAAG,WAAW,IAAI,OAAO;AACzB,UAAG,YAAY,wBAAwB,IAAI,SAAS,SAAS,EAAE,0BAA0B,IAAI,SAAS,WAAW,EAAE,0BAA0B,IAAI,SAAS,WAAW,EAAE,kCAAkC,IAAI,SAAS,eAAe,IAAI,OAAO,EAAE,qBAAqB,IAAI,WAAW,WAAW,EAAE,mBAAmB,IAAI,WAAW,SAAS,EAAE,mBAAmB,IAAI,WAAW,SAAS,EAAE,gBAAgB,IAAI,WAAW,MAAM,EAAE,iBAAiB,IAAI,WAAW,OAAO,EAAE,mBAAmB,IAAI,WAAW,SAAS,EAAE,mBAAmB,IAAI,WAAW,SAAS,EAAE,kBAAkB,IAAI,WAAW,QAAQ,EAAE,mBAAmB,IAAI,WAAW,SAAS,EAAE,wBAAwB,IAAI,SAAS,SAAS,EAAE,wBAAwB,IAAI,SAAS,SAAS,EAAE,mBAAmB,IAAI,SAAS,IAAI,EAAE,mBAAmB,IAAI,SAAS,IAAI,EAAE,mBAAmB,IAAI,SAAS,IAAI,EAAE,mBAAmB,IAAI,SAAS,IAAI,EAAE,oBAAoB,IAAI,QAAQ,EAAE,yBAAyB,IAAI,aAAa,SAAS,EAAE,uBAAuB,IAAI,aAAa,OAAO,EAAE,sBAAsB,IAAI,aAAa,MAAM;AAAA,QAC9iC;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,SAAS;AAAA,QACT,MAAM;AAAA,QACN,UAAU;AAAA,QACV,UAAU;AAAA,QACV,SAAS;AAAA,MACX;AAAA,MACA,SAAS;AAAA,QACP,OAAO;AAAA,QACP,eAAe;AAAA,MACjB;AAAA,MACA,UAAU,CAAC,QAAQ;AAAA,MACnB,YAAY;AAAA,MACZ,UAAU,CAAI,sBAAyB,mBAAmB;AAAA,MAC1D,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,YAAY,MAAM,GAAG,sBAAsB,iBAAiB,GAAG,CAAC,YAAY,MAAM,GAAG,sBAAsB,mBAAmB,GAAG,OAAO,CAAC;AAAA,MACnJ,UAAU,SAAS,yBAAyB,IAAI,KAAK;AACnD,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,aAAa,CAAC;AACjB,UAAG,WAAW,GAAG,wCAAwC,GAAG,GAAG,QAAQ,CAAC;AAAA,QAC1E;AACA,YAAI,KAAK,GAAG;AACV,UAAG,UAAU;AACb,UAAG,cAAc,IAAI,SAAS,cAAc,IAAI,EAAE;AAAA,QACpD;AAAA,MACF;AAAA,MACA,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,aAAa,CAAC,GAAG,gBAAgB,WAAW,WAAW,MAAM;AACzE,WAAW,CAAC,aAAa,CAAC,GAAG,gBAAgB,WAAW,YAAY,MAAM;AAAA,CACzE,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQV,qBAAqB;AAAA,MACrB,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,MAAM;AAAA,QACJ,gCAAgC;AAAA,QAChC,kCAAkC;AAAA,QAClC,kCAAkC;AAAA,QAClC,0CAA0C;AAAA,QAC1C,6BAA6B;AAAA,QAC7B,2BAA2B;AAAA,QAC3B,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,yBAAyB;AAAA,QACzB,2BAA2B;AAAA,QAC3B,2BAA2B;AAAA,QAC3B,0BAA0B;AAAA,QAC1B,2BAA2B;AAAA,QAC3B,gCAAgC;AAAA,QAChC,gCAAgC;AAAA,QAChC,2BAA2B;AAAA,QAC3B,2BAA2B;AAAA,QAC3B,2BAA2B;AAAA,QAC3B,2BAA2B;AAAA,QAC3B,4BAA4B;AAAA,QAC5B,iCAAiC;AAAA,QACjC,+BAA+B;AAAA,QAC/B,8BAA8B;AAAA,QAC9B,WAAW;AAAA,QACX,WAAW;AAAA,MACb;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,6BAAN,MAAM,4BAA2B;AAAA,EAC/B,cAAc;AACZ,SAAK,WAAW;AAChB,SAAK,kBAAkB;AACvB,SAAK,mBAAmB;AACxB,SAAK,mBAAmB;AACxB,SAAK,mBAAmB;AACxB,SAAK,mBAAmB;AACxB,SAAK,kBAAkB;AACvB,SAAK,eAAe,CAAC;AAAA,MACnB,OAAO;AAAA,MACP,SAAS;AAAA,IACX,GAAG;AAAA,MACD,OAAO;AAAA,MACP,SAAS;AAAA,IACX,GAAG;AAAA,MACD,OAAO;AAAA,MACP,SAAS;AAAA,IACX,GAAG;AAAA,MACD,OAAO;AAAA,MACP,SAAS;AAAA,IACX,GAAG;AAAA,MACD,OAAO;AAAA,MACP,SAAS;AAAA,IACX,GAAG;AAAA,MACD,OAAO;AAAA,MACP,SAAS;AAAA,IACX,GAAG;AAAA,MACD,OAAO;AAAA,MACP,SAAS;AAAA,IACX,GAAG;AAAA,MACD,OAAO;AAAA,MACP,SAAS;AAAA,IACX,GAAG;AAAA,MACD,OAAO;AAAA,MACP,SAAS;AAAA,IACX,GAAG;AAAA,MACD,OAAO;AAAA,MACP,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AAAA,EACA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,SAAS;AACnB,SAAK,WAAW,SAAS,OAAO;AAAA,EAClC;AAAA,EACA,IAAI,iBAAiB;AACnB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,eAAe,gBAAgB;AACjC,SAAK,kBAAkB,SAAS,cAAc;AAAA,EAChD;AAAA,EACA,IAAI,kBAAkB;AACpB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,gBAAgB,iBAAiB;AACnC,SAAK,mBAAmB,SAAS,eAAe;AAAA,EAClD;AAAA,EACA,IAAI,kBAAkB;AACpB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,gBAAgB,iBAAiB;AACnC,SAAK,mBAAmB,SAAS,eAAe;AAAA,EAClD;AAAA,EACA,IAAI,kBAAkB;AACpB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,gBAAgB,iBAAiB;AACnC,SAAK,mBAAmB,SAAS,eAAe;AAAA,EAClD;AAAA,EACA,IAAI,kBAAkB;AACpB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,gBAAgB,iBAAiB;AACnC,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EACA,IAAI,iBAAiB;AACnB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,eAAe,gBAAgB;AACjC,SAAK,kBAAkB;AAAA,EACzB;AAAA;AAAA;AAAA,EAGA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAG;AACD,UAAM,MAAM,SAAS,GAAG,GAAG,CAAC;AAC5B,WAAO;AAAA,MACL,GAAG,IAAI,IAAI;AAAA,MACX,GAAG,IAAI;AAAA,MACP,GAAG,IAAI;AAAA,IACT;AAAA,EACF;AAAA;AAAA;AAAA,EAGA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAG;AACD,WAAO,IAAI,SAAS,GAAG,GAAG,GAAG,KAAK,CAAC;AAAA,EACrC;AAAA,EACA,IAAI,MAAM,MAAM,QAAQ;AACtB,UAAM,IAAI,SAAS;AACnB,UAAM,MAAM;AAAA,MACV,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK;AAAA,MAChC,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK;AAAA,MAChC,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK;AAAA,IAClC;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO,KAAK,GAAG,OAAO;AACpB,QAAI;AACJ,QAAI,KAAK,MAAM,IAAI,CAAC,KAAK,MAAM,KAAK,MAAM,IAAI,CAAC,KAAK,KAAK;AACvD,YAAM,QAAQ,KAAK,MAAM,IAAI,CAAC,IAAI,KAAK,UAAU,IAAI,KAAK,MAAM,IAAI,CAAC,IAAI,KAAK,UAAU;AAAA,IAC1F,OAAO;AACL,YAAM,QAAQ,KAAK,MAAM,IAAI,CAAC,IAAI,KAAK,UAAU,IAAI,KAAK,MAAM,IAAI,CAAC,IAAI,KAAK,UAAU;AAAA,IAC1F;AACA,QAAI,MAAM,GAAG;AACX,aAAO;AAAA,IACT,WAAW,OAAO,KAAK;AACrB,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,cAAc,KAAK,GAAG,OAAO;AAE3B,QAAI,IAAI,MAAM,KAAK,IAAI,MAAM,GAAG;AAC9B,aAAO,IAAI;AAAA,IACb;AACA,QAAI;AACJ,QAAI,OAAO;AACT,mBAAa,IAAI,IAAI,KAAK,iBAAiB;AAAA,IAC7C,WAAW,MAAM,KAAK,gBAAgB;AACpC,mBAAa,IAAI,IAAI,KAAK;AAAA,IAC5B,OAAO;AACL,mBAAa,IAAI,IAAI,KAAK,kBAAkB;AAAA,IAC9C;AAEA,QAAI,aAAa,GAAG;AAClB,mBAAa;AAAA,IACf;AAEA,QAAI,SAAS,MAAM,KAAK,mBAAmB,aAAa,KAAK;AAC3D,mBAAa;AAAA,IACf;AACA,QAAI,aAAa,MAAM;AACrB,mBAAa;AAAA,IACf;AACA,WAAO,OAAO,WAAW,QAAQ,CAAC,CAAC;AAAA,EACrC;AAAA,EACA,SAAS,KAAK,GAAG,OAAO;AACtB,QAAI;AACJ,QAAI,OAAO;AACT,cAAQ,IAAI,IAAI,KAAK,kBAAkB;AAAA,IACzC,OAAO;AACL,cAAQ,IAAI,IAAI,KAAK,kBAAkB;AAAA,IACzC;AACA,QAAI,QAAQ,GAAG;AACb,cAAQ;AAAA,IACV;AACA,WAAO,OAAO,MAAM,QAAQ,CAAC,CAAC;AAAA,EAChC;AAAA,EACA,SAAS,OAAO,OAAO,CAAC,GAAG;AACzB,UAAM,WAAW,CAAC;AAClB,UAAM,SAAS,WAAW,KAAK;AAC/B,aAAS,IAAI,KAAK,iBAAiB,IAAI,GAAG,KAAK,GAAG;AAChD,YAAM,MAAM,KAAK,MAAM,MAAM;AAC7B,YAAM,cAAc,KAAK,MAAM,WAAW;AAAA,QACxC,GAAG,KAAK,OAAO,KAAK,GAAG,IAAI;AAAA,QAC3B,GAAG,KAAK,cAAc,KAAK,GAAG,IAAI;AAAA,QAClC,GAAG,KAAK,SAAS,KAAK,GAAG,IAAI;AAAA,MAC/B,CAAC,CAAC;AACF,eAAS,KAAK,WAAW;AAAA,IAC3B;AACA,aAAS,KAAK,KAAK,MAAM,MAAM,CAAC;AAChC,aAAS,IAAI,GAAG,KAAK,KAAK,gBAAgB,KAAK,GAAG;AAChD,YAAM,MAAM,KAAK,MAAM,MAAM;AAC7B,YAAM,cAAc,KAAK,MAAM,WAAW;AAAA,QACxC,GAAG,KAAK,OAAO,KAAK,CAAC;AAAA,QACrB,GAAG,KAAK,cAAc,KAAK,CAAC;AAAA,QAC5B,GAAG,KAAK,SAAS,KAAK,CAAC;AAAA,MACzB,CAAC,CAAC;AACF,eAAS,KAAK,WAAW;AAAA,IAC3B;AAEA,QAAI,KAAK,UAAU,QAAQ;AACzB,aAAO,KAAK,aAAa,IAAI,CAAC;AAAA,QAC5B;AAAA,QACA;AAAA,MACF,MAAM;AACJ,cAAM,kBAAkB,KAAK,MAAM,KAAK,IAAI,WAAW,KAAK,eAAe,GAAG,WAAW,SAAS,KAAK,CAAC,GAAG,UAAU,GAAG,CAAC;AACzH,eAAO;AAAA,MACT,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,mCAAmC,mBAAmB;AACzE,aAAO,KAAK,qBAAqB,6BAA4B;AAAA,IAC/D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,4BAA2B;AAAA,IACtC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,4BAA4B,CAAC;AAAA,IACnG,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AACH,IAAM,yBAAyB;AAC/B,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,YAAY,SAAS,qBAAqB,KAAK,UAAU,UAAU,kBAAkB;AACnF,SAAK,UAAU;AACf,SAAK,sBAAsB;AAC3B,SAAK,MAAM;AACX,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,mBAAmB;AACxB,SAAK,iBAAiB;AACtB,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,aAAa;AAClB,SAAK,iBAAiB;AACtB,SAAK,eAAe;AACpB,SAAK,UAAU;AACf,SAAK,YAAY,CAAC;AAClB,SAAK,aAAa,CAAC,MAAM,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EACxF;AAAA,EACA,WAAW;AACT,SAAK,SAAS;AACd,SAAK,WAAW;AAChB,SAAK,iBAAiB,iCAAiC,sBAAsB,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM;AAC5H,WAAK,SAAS;AACd,WAAK,WAAW;AAChB,WAAK,IAAI,aAAa;AAAA,IACxB,CAAC;AAAA,EACH;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,YAAY,UAAU;AACxB,WAAK,SAAS;AAAA,IAChB;AACA,SAAK,WAAW;AAChB,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,WAAW;AACT,SAAK,YAAY,KAAK,oBAAoB,SAAS,KAAK,UAAU;AAAA,MAChE,OAAO,KAAK;AAAA,MACZ,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AAAA,EACA,aAAa;AACX,QAAI,eAAe,aAAa,KAAK,SAAS,GAAG;AAC/C,WAAK,UAAU,KAAK,UAAU,KAAK,WAAW,UAAU,OAAK,KAAK,eAAe,CAAC,KAAK,CAAC;AACxF,WAAK,cAAc,KAAK,UAAU,KAAK,WAAW,UAAU,OAAK,KAAK,mBAAmB,CAAC,KAAK,CAAC;AAChG,WAAK,YAAY,KAAK,UAAU,KAAK,WAAW,UAAU,OAAK,KAAK,iBAAiB,CAAC,KAAK,CAAC;AAC5F,WAAK,SAAS,SAAS,KAAK,QAAQ,eAAe,gBAAgB,KAAK,cAAc,KAAK,cAAc,aAAa;AACtH,WAAK,SAAS,SAAS,KAAK,QAAQ,eAAe,cAAc,KAAK,OAAO;AAC7E,YAAM,aAAa,KAAK,cAAc,WAAW,KAAK,OAAO,CAAC;AAC9D,WAAK,YAAY,aAAa,MAAM,UAAU;AAAA,IAChD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAG;AAED,YAAQ,IAAI,MAAM,IAAI,MAAM,IAAI,OAAO;AAAA,EACzC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,6BAA6B,mBAAmB;AACnE,aAAO,KAAK,qBAAqB,uBAAyB,kBAAqB,UAAU,GAAM,kBAAkB,0BAA0B,GAAM,kBAAqB,iBAAiB,GAAM,kBAAqB,SAAS,GAAM,kBAAqB,iBAAiB,GAAM,kBAAqB,gBAAgB,CAAC;AAAA,IACrT;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,qBAAqB,CAAC;AAAA,MACnC,UAAU;AAAA,MACV,cAAc,SAAS,kCAAkC,IAAI,KAAK;AAChE,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,mBAAmB,IAAI,YAAY,IAAI,EAAE,mBAAmB,IAAI,YAAY,IAAI,EAAE,mBAAmB,IAAI,YAAY,IAAI,EAAE,mBAAmB,IAAI,YAAY,IAAI;AAAA,QACnL;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,UAAU;AAAA,QACV,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,gBAAgB;AAAA,QAChB,cAAc;AAAA,QACd,SAAS;AAAA,MACX;AAAA,MACA,UAAU,CAAC,kBAAkB;AAAA,MAC7B,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC,4BAA4B,iBAAiB,CAAC,GAAM,sBAAyB,mBAAmB;AAAA,MAClI,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,QAAQ,EAAE,GAAG,CAAC,GAAG,0BAA0B,GAAG,CAAC,GAAG,0BAA0B,CAAC;AAAA,MACvF,UAAU,SAAS,8BAA8B,IAAI,KAAK;AACxD,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,WAAW,GAAG,6CAA6C,GAAG,GAAG,QAAQ,CAAC;AAC7E,UAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,UAAG,aAAa,CAAC;AACjB,UAAG,UAAU,GAAG,MAAM;AACtB,UAAG,aAAa;AAAA,QAClB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,cAAc,IAAI,UAAU,IAAI,EAAE;AACrC,UAAG,UAAU;AACb,UAAG,YAAY,SAAS,IAAI,YAAY,IAAI,YAAY,OAAO;AAAA,QACjE;AAAA,MACF;AAAA,MACA,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,WAAW,CAAC,GAAG,qBAAqB,WAAW,YAAY,MAAM;AAC7E,WAAW,CAAC,WAAW,CAAC,GAAG,qBAAqB,WAAW,YAAY,MAAM;AAC7E,WAAW,CAAC,WAAW,CAAC,GAAG,qBAAqB,WAAW,WAAW,MAAM;AAC5E,WAAW,CAAC,WAAW,CAAC,GAAG,qBAAqB,WAAW,cAAc,MAAM;AAC/E,WAAW,CAAC,WAAW,CAAC,GAAG,qBAAqB,WAAW,kBAAkB,MAAM;AACnF,WAAW,CAAC,WAAW,CAAC,GAAG,qBAAqB,WAAW,gBAAgB,MAAM;AACjF,WAAW,CAAC,WAAW,CAAC,GAAG,qBAAqB,WAAW,WAAW,MAAM;AAAA,CAC3E,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAUV,qBAAqB;AAAA,MACrB,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,MAAM;AAAA,QACJ,2BAA2B;AAAA,QAC3B,2BAA2B;AAAA,QAC3B,2BAA2B;AAAA,QAC3B,2BAA2B;AAAA,MAC7B;AAAA,MACA,WAAW,CAAC,4BAA4B,iBAAiB;AAAA,MACzD,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,OAAO;AACL,SAAK,OAAO,SAAS,qBAAqB,mBAAmB;AAC3D,aAAO,KAAK,qBAAqB,eAAc;AAAA,IACjD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,iBAAiB,oBAAoB;AAAA,MAC/C,SAAS,CAAC,iBAAiB,oBAAoB;AAAA,IACjD,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB,CAAC,CAAC;AAAA,EACnD;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,iBAAiB,oBAAoB;AAAA,MAC/C,SAAS,CAAC,iBAAiB,oBAAoB;AAAA,IACjD,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;ACtpBH,IAAM,qBAAqB;AAC3B,IAAM,0BAAN,MAAM,yBAAwB;AAAA,EAC5B,YAAY,SAAS,UAAU,eAAe;AAC5C,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,gBAAgB;AACrB,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,cAAc;AACZ,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,kBAAkB;AAChB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,cAAc;AACZ,UAAM,UAAU,cAAc,KAAK,OAAO;AAC1C,QAAI,CAAC,SAAS;AACZ;AAAA,IACF;AACA,QAAI,KAAK,eAAe,KAAK,kBAAkB,kBAAkB;AAC/D,WAAK,SAAS,SAAS,SAAS,kBAAkB;AAAA,IACpD,OAAO;AACL,WAAK,SAAS,YAAY,SAAS,kBAAkB;AAAA,IACvD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,gCAAgC,mBAAmB;AACtE,aAAO,KAAK,qBAAqB,0BAA4B,kBAAqB,UAAU,GAAM,kBAAqB,SAAS,GAAM,kBAAkB,uBAAuB,CAAC,CAAC;AAAA,IACnL;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,eAAe,EAAE,CAAC;AAAA,MACnC,QAAQ;AAAA,QACN,aAAa;AAAA,MACf;AAAA,MACA,UAAU,CAAC,aAAa;AAAA,MACxB,YAAY;AAAA,MACZ,UAAU,CAAI,oBAAoB;AAAA,IACpC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,OAAO;AACL,SAAK,OAAO,SAAS,6BAA6B,mBAAmB;AACnE,aAAO,KAAK,qBAAqB,uBAAsB;AAAA,IACzD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,uBAAuB;AAAA,MACjC,SAAS,CAAC,uBAAuB;AAAA,IACnC,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB,CAAC,CAAC;AAAA,EACnD;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,uBAAuB;AAAA,MACjC,SAAS,CAAC,uBAAuB;AAAA,IACnC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;ACpFH,SAAS,sEAAsE,IAAI,KAAK;AACtF,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,KAAK,CAAC;AAC3B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,OAAO,IAAI;AACjB,UAAM,eAAkB,cAAc,CAAC,EAAE;AACzC,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,YAAY,WAAW,SAAS,OAAO,WAAW,YAAY,CAAC;AAClE,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,MAAM,GAAG;AAAA,EACtC;AACF;AACA,SAAS,gEAAgE,IAAI,KAAK;AAChF,MAAI,KAAK,GAAG;AACV,IAAG,iBAAiB,GAAG,uEAAuE,GAAG,GAAG,KAAK,GAAM,yBAAyB;AAAA,EAC1I;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,OAAO,gBAAgB;AAAA,EACvC;AACF;AACA,SAAS,kDAAkD,IAAI,KAAK;AAClE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,WAAW,GAAG,iEAAiE,GAAG,CAAC;AACtF,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,eAAe,IAAI;AACzB,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,YAAY,aAAa,gBAAgB,CAAC,OAAO,WAAW,YAAY,IAAI,MAAM,IAAI;AACzF,IAAG,WAAW,eAAe,OAAO,WAAW;AAC/C,IAAG,UAAU;AACb,IAAG,cAAc,CAAC,OAAO,OAAO,OAAO,WAAW,YAAY,MAAM,SAAY,IAAI,EAAE;AAAA,EACxF;AACF;AACA,SAAS,4CAA4C,IAAI,KAAK;AAC5D,MAAI,KAAK,GAAG;AACV,IAAG,iBAAiB,GAAG,mDAAmD,GAAG,GAAG,QAAQ,GAAM,yBAAyB;AAAA,EACzH;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,OAAO,cAAc;AAAA,EACrC;AACF;AACA,SAAS,4CAA4C,IAAI,KAAK;AAC5D,MAAI,KAAK,GAAG;AACV,IAAG,OAAO,CAAC;AAAA,EACb;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,mBAAmB,KAAK,OAAO,eAAe,IAAI;AAAA,EACvD;AACF;AACA,IAAMA,OAAM,CAAC,GAAG;AAChB,SAAS,sEAAsE,IAAI,KAAK;AACtF,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,IAAI;AAAA,EAClC;AACF;AACA,SAAS,uDAAuD,IAAI,KAAK;AACvE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,WAAW,GAAG,uEAAuE,GAAG,GAAG,gBAAgB,CAAC;AAC/G,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,2BAA2B,OAAO,IAAI;AAAA,EACtD;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,CAAC;AACzB,IAAG,WAAW,GAAG,wDAAwD,GAAG,GAAG,QAAQ,CAAC;AAAA,EAC1F;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,YAAY,cAAc,CAAC,OAAO,eAAe,OAAO,KAAK;AAChE,IAAG,WAAW,WAAW,OAAO,QAAQ;AACxC,IAAG,UAAU;AACb,IAAG,cAAc,OAAO,OAAO,IAAI,EAAE;AAAA,EACvC;AACF;AACA,SAAS,wDAAwD,IAAI,KAAK;AACxE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,iBAAiB,CAAC;AAAA,EACpC;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,OAAO,QAAQ;AAC7B,IAAG,WAAW,UAAU,OAAO,MAAM,EAAE,SAAS,OAAO,KAAK,EAAE,YAAY,OAAO,QAAQ,EAAE,OAAO,OAAO,GAAG,EAAE,QAAQ,OAAO,IAAI,EAAE,iBAAiB,OAAO,aAAa,EAAE,oBAAoB,CAAC,EAAE,OAAO,cAAc,OAAO,UAAU,OAAO,UAAU,OAAO,eAAe,OAAO,OAAO,OAAO,YAAY,aAAa,EAAE,SAAS,OAAO,KAAK,EAAE,eAAe,CAAC,EAAE,OAAO,eAAe,OAAO,OAAO,OAAO,YAAY,YAAY,EAAE,cAAc,OAAO,UAAU,EAAE,aAAa,OAAO,SAAS;AAAA,EAC5e;AACF;AACA,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,yDAAyD,GAAG,IAAI,iBAAiB,CAAC;AACnG,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,cAAc,OAAO,UAAU,IAAI,EAAE;AAAA,EAC1C;AACF;AACA,SAAS,2CAA2C,IAAI,KAAK;AAC3D,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,IAAI;AAAA,EAClC;AACF;AACA,IAAM,oBAAoB,CAAC,QAAQ,OAAO,UAAU,UAAU,QAAQ,SAAS,QAAQ,UAAU,YAAY,WAAW,WAAW,QAAQ,MAAM;AACjJ,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,cAAc;AACZ,SAAK,WAAW;AAChB,SAAK,MAAM;AACX,SAAK,gBAAgB;AACrB,SAAK,mBAAmB;AACxB,SAAK,cAAc;AACnB,SAAK,aAAa;AAClB,SAAK,OAAO;AACZ,SAAK,YAAY;AACjB,SAAK,OAAO;AACZ,SAAK,iBAAiB,CAAC;AACvB,SAAK,aAAa,CAAC;AACnB,SAAK,SAAS;AACd,SAAK,mBAAmB,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,EACvD;AAAA,EACA,yBAAyB;AACvB,SAAK,iBAAiB,KAAK,cAAc,SAAS,EAAE,MAAM,EAAE;AAAA,EAC9D;AAAA,EACA,WAAW;AACT,SAAK,uBAAuB;AAAA,EAC9B;AAAA,EACA,IAAI,MAAM;AACR,WAAO,KAAK;AAAA,EACd;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,SAAS,OAAO,MAAM,iBAAiB,UAAU;AACnD,WAAK,SAAS,KAAK,IAAI,GAAG,MAAM,YAAY;AAC5C,WAAK,aAAa,KAAK,OAAO,SAAS,EAAE,MAAM,EAAE,EAAE,IAAI,UAAQ,CAAC,IAAI;AAAA,IACtE;AACA,QAAI,eAAe;AACjB,WAAK,uBAAuB;AAAA,IAC9B;AAAA,EACF;AAAA,EACA,IAAI,WAAW;AACb,QAAI,QAAQ;AACZ,QAAI,CAAC,KAAK,YAAY;AACpB,cAAQ,KAAK,WAAW;AAAA,QACtB,KAAK;AACH,kBAAQ;AAAA,YACN,CAAC,WAAW,GAAG;AAAA,YACf,CAAC,kBAAkB,GAAG;AAAA,UACxB;AACA;AAAA,QACF,KAAK;AACH,kBAAQ;AAAA,YACN,CAAC,WAAW,GAAG;AAAA,YACf,CAAC,kBAAkB,GAAG;AAAA,UACxB;AACA;AAAA,QACF,KAAK;AACH,kBAAQ;AAAA,YACN,CAAC,WAAW,GAAG;AAAA,YACf,CAAC,kBAAkB,GAAG;AAAA,UACxB;AACA;AAAA,QACF,KAAK;AACH,kBAAQ;AAAA,YACN,CAAC,WAAW,GAAG;AAAA,YACf,CAAC,kBAAkB,GAAG;AAAA,UACxB;AACA;AAAA,QACF;AACE,kBAAQ;AAAA,YACN,CAAC,WAAW,GAAG;AAAA,YACf,CAAC,kBAAkB,GAAG;AAAA,UACxB;AACA;AAAA,MACJ;AAAA,IACF;AACA,QAAI,CAAC,CAAC,KAAK,UAAU;AACnB,UAAI,SAAS,KAAM,QAAO,OAAO,OAAO,CAAC,GAAG,OAAO,KAAK,QAAQ;AAChE,aAAO,KAAK;AAAA,IACd;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,6BAA6B,mBAAmB;AACnE,aAAO,KAAK,qBAAqB,uBAAsB;AAAA,IACzD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,eAAe,CAAC;AAAA,MAC7B,WAAW,CAAC,GAAG,mBAAmB;AAAA,MAClC,UAAU;AAAA,MACV,cAAc,SAAS,kCAAkC,IAAI,KAAK;AAChE,YAAI,KAAK,GAAG;AACV,UAAG,wBAAwB,cAAc,IAAI,gBAAgB,EAAE,oBAAoB,MAAS;AAC5F,UAAG,YAAY,SAAS,IAAI,UAAU,OAAO,KAAK,IAAI,SAAS,IAAI,KAAK;AACxE,UAAG,WAAW,IAAI,QAAQ;AAC1B,UAAG,YAAY,SAAS,IAAI,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,IAAI,MAAM,IAAI,EAAE,cAAc,IAAI,UAAU,IAAI,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI,MAAM,IAAI;AACzJ,UAAG,YAAY,mBAAmB,CAAC,IAAI,GAAG,EAAE,yBAAyB,IAAI,SAAS,IAAI,EAAE,yBAAyB,IAAI,SAAS,IAAI,EAAE,yBAAyB,IAAI,SAAS,IAAI,EAAE,yBAAyB,IAAI,SAAS,IAAI,EAAE,iBAAiB,IAAI,GAAG,EAAE,4BAA4B,IAAI,WAAW,UAAU,CAAC,EAAE,4BAA4B,IAAI,UAAU,EAAE,oCAAoC,IAAI,cAAc,SAAS,EAAE,qCAAqC,IAAI,cAAc,UAAU,EAAE,uCAAuC,IAAI,cAAc,YAAY,EAAE,wCAAwC,IAAI,cAAc,aAAa;AAAA,QAC9mB;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,UAAU;AAAA,QACV,KAAK;AAAA,QACL,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,OAAO;AAAA,QACP,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,MAAM;AAAA,QACN,WAAW;AAAA,MACb;AAAA,MACA,UAAU,CAAC,aAAa;AAAA,MACxB,YAAY;AAAA,MACZ,UAAU,CAAI,sBAAyB,mBAAmB;AAAA,MAC1D,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,0BAA0B,GAAG,cAAc,0CAA0C,GAAG,eAAe,WAAW,GAAG,CAAC,GAAG,0BAA0B,GAAG,cAAc,0CAA0C,GAAG,aAAa,GAAG,CAAC,GAAG,+BAA+B,GAAG,SAAS,GAAG,CAAC,GAAG,6BAA6B,CAAC;AAAA,MAClU,UAAU,SAAS,8BAA8B,IAAI,KAAK;AACxD,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,GAAG,6CAA6C,GAAG,CAAC,EAAE,GAAG,6CAA6C,GAAG,CAAC;AAAA,QAC1H;AACA,YAAI,KAAK,GAAG;AACV,UAAG,cAAc,IAAI,UAAU,IAAI,gBAAgB,IAAI,CAAC;AAAA,QAC1D;AAAA,MACF;AAAA,MACA,cAAc,CAAC,uBAAuB;AAAA,MACtC,eAAe;AAAA,MACf,MAAM;AAAA,QACJ,WAAW,CAAC,eAAe;AAAA,MAC7B;AAAA,MACA,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,YAAY,CAAC,eAAe;AAAA,MAC5B,YAAY;AAAA,MACZ,SAAS,CAAC,uBAAuB;AAAA,MACjC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAyBV,MAAM;AAAA,QACJ,gBAAgB;AAAA,QAChB,sBAAsB;AAAA,QACtB,gBAAgB;AAAA,QAChB,WAAW;AAAA,QACX,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,2BAA2B;AAAA,QAC3B,iCAAiC;AAAA,QACjC,iCAAiC;AAAA,QACjC,iCAAiC;AAAA,QACjC,iCAAiC;AAAA,QACjC,yBAAyB;AAAA,QACzB,oCAAoC;AAAA,QACpC,oCAAoC;AAAA,QACpC,4CAA4C;AAAA,QAC5C,6CAA6C;AAAA,QAC7C,+CAA+C;AAAA,QAC/C,gDAAgD;AAAA,QAChD,SAAS;AAAA;AAAA,MAEX;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,KAAK,CAAC;AAAA,MACJ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAMC,0BAAyB;AAC/B,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,YAAY,kBAAkB,UAAU,KAAK,YAAY,gBAAgB;AACvE,SAAK,mBAAmB;AACxB,SAAK,WAAW;AAChB,SAAK,MAAM;AACX,SAAK,aAAa;AAClB,SAAK,iBAAiB;AACtB,SAAK,iBAAiBA;AACtB,SAAK,UAAU;AACf,SAAK,cAAc;AACnB,SAAK,MAAM;AACX,SAAK,WAAW,IAAI,QAAQ;AAC5B,SAAK,WAAW;AAChB,SAAK,UAAU;AACf,SAAK,aAAa;AAClB,SAAK,MAAM;AACX,SAAK,gBAAgB;AACrB,SAAK,QAAQ;AACb,SAAK,WAAW;AAChB,SAAK,OAAO;AACZ,SAAK,WAAW;AAChB,SAAK,WAAW;AAIhB,SAAK,OAAO;AACZ,SAAK,YAAY;AACjB,SAAK,cAAc,OAAO,yBAAyB;AAAA,MACjD,MAAM;AAAA,MACN,UAAU;AAAA,IACZ,CAAC;AAAA,EACH;AAAA,EACA,WAAW;AACT,SAAK,eAAe,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAChF,WAAK,MAAM;AACX,WAAK,mBAAmB;AACxB,WAAK,IAAI,cAAc;AAAA,IACzB,CAAC;AACD,SAAK,MAAM,KAAK,eAAe;AAC/B,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,OAAO;AACT,WAAK,cAAc,KAAK,SAAS,kBAAkB,QAAQ,KAAK,KAAK,MAAM,KAAK,KAAK,QAAQ;AAAA,IAC/F;AACA,QAAI,WAAW,OAAO,SAAS,UAAU;AACvC,WAAK,UAAU,KAAK,WAAW,KAAK,OAAO,OAAO,KAAK,UAAU,YAAY,KAAK,QAAQ,KAAK,OAAO,KAAK,UAAU,YAAY,KAAK,SAAS,KAAK,KAAK;AAAA,IAC3J;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,QAAI,KAAK,aAAa;AACpB,WAAK,SAAS,SAAS,KAAK,WAAW,eAAe,eAAe;AAAA,IACvE,OAAO;AACL,WAAK,SAAS,YAAY,KAAK,WAAW,eAAe,eAAe;AAAA,IAC1E;AAAA,EACF;AAAA,EACA,IAAI,cAAc;AAChB,WAAO,KAAK,QAAQ;AAAA,EACtB;AAAA,EACA,IAAI,cAAc;AAChB,QAAI,CAAC,CAAC,KAAK,QAAQ;AACjB,aAAO,KAAK,aAAa;AAAA,IAC3B;AACA,WAAO;AAAA,EACT;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,eAAe;AACb,QAAI,SAAS;AACb,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK;AACH,iBAAS;AACT;AAAA,MACF,KAAK;AACH,iBAAS;AACT;AAAA,MACF,KAAK;AACH,iBAAS;AACT;AAAA,MACF,KAAK;AACH,iBAAS;AACT;AAAA,MACF,KAAK;AACH,iBAAS;AACT;AAAA,MACF,KAAK;AACH,iBAAS;AACT;AAAA,MACF;AACE,iBAAS,KAAK;AACd;AAAA,IACJ;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,0BAA0B,mBAAmB;AAChE,aAAO,KAAK,qBAAqB,oBAAsB,kBAAqB,gBAAgB,GAAM,kBAAqB,SAAS,GAAM,kBAAqB,iBAAiB,GAAM,kBAAqB,UAAU,GAAM,kBAAqB,cAAc,CAAC;AAAA,IAC7P;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,WAAW,CAAC;AAAA,MACzB,WAAW,CAAC,GAAG,WAAW;AAAA,MAC1B,UAAU;AAAA,MACV,cAAc,SAAS,+BAA+B,IAAI,KAAK;AAC7D,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,oBAAoB,IAAI,MAAM,EAAE,2BAA2B,CAAC,EAAE,IAAI,cAAc,IAAI,UAAU,IAAI,MAAM,EAAE,qBAAqB,IAAI,SAAS,IAAI,EAAE,qBAAqB,IAAI,SAAS,IAAI,EAAE,qBAAqB,IAAI,SAAS,IAAI,EAAE,qBAAqB,IAAI,SAAS,IAAI,EAAE,4BAA4B,IAAI,UAAU,SAAS,EAAE,8BAA8B,IAAI,UAAU,WAAW,EAAE,4BAA4B,IAAI,UAAU,SAAS,EAAE,yBAAyB,IAAI,UAAU,MAAM,EAAE,4BAA4B,IAAI,UAAU,SAAS,EAAE,0BAA0B,IAAI,UAAU,OAAO,EAAE,2BAA2B,IAAI,aAAa,SAAS,EAAE,yBAAyB,IAAI,aAAa,OAAO,EAAE,wBAAwB,IAAI,aAAa,MAAM;AAAA,QAC5uB;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,KAAK;AAAA,QACL,eAAe;AAAA,QACf,OAAO;AAAA,QACP,UAAU;AAAA,QACV,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,UAAU;AAAA,QACV,MAAM;AAAA,QACN,WAAW;AAAA,MACb;AAAA,MACA,UAAU,CAAC,UAAU;AAAA,MACrB,YAAY;AAAA,MACZ,UAAU,CAAI,sBAAyB,mBAAmB;AAAA,MAC1D,oBAAoBD;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,yBAAyB,GAAG,CAAC,GAAG,wBAAwB,GAAG,SAAS,GAAG,CAAC,GAAG,uBAAuB,GAAG,CAAC,GAAG,UAAU,SAAS,YAAY,OAAO,QAAQ,iBAAiB,oBAAoB,SAAS,eAAe,cAAc,SAAS,WAAW,GAAG,CAAC,GAAG,UAAU,SAAS,YAAY,OAAO,QAAQ,iBAAiB,oBAAoB,SAAS,eAAe,cAAc,WAAW,CAAC;AAAA,MAC/Y,UAAU,SAAS,2BAA2B,IAAI,KAAK;AACrD,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,WAAW,GAAG,0CAA0C,GAAG,CAAC;AAC/D,UAAG,aAAa,CAAC;AACjB,UAAG,WAAW,GAAG,2CAA2C,GAAG,GAAG,gBAAgB,CAAC;AAAA,QACrF;AACA,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,IAAI,UAAU,IAAI,SAAS,CAAC,IAAI,eAAe,CAAC,IAAI,MAAM,IAAI,EAAE;AAClF,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,2BAA2B,IAAI,KAAK;AAAA,QACpD;AAAA,MACF;AAAA,MACA,cAAc,CAAC,SAAS,sBAAsB,iBAAoB,gCAAgC;AAAA,MAClG,eAAe;AAAA,MACf,MAAM;AAAA,QACJ,WAAW,CAAC,eAAe;AAAA,MAC7B;AAAA,MACA,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,aAAa,CAAC,GAAG,kBAAkB,WAAW,YAAY,MAAM;AAC5E,WAAW,CAAC,aAAa,CAAC,GAAG,kBAAkB,WAAW,WAAW,MAAM;AAC3E,WAAW,CAAC,aAAa,CAAC,GAAG,kBAAkB,WAAW,cAAc,MAAM;AAC9E,WAAW,CAAC,aAAa,CAAC,GAAG,kBAAkB,WAAW,OAAO,MAAM;AACvE,WAAW,CAAC,WAAW,CAAC,GAAG,kBAAkB,WAAW,iBAAiB,MAAM;AAC/E,WAAW,CAAC,WAAW,CAAC,GAAG,kBAAkB,WAAW,SAAS,MAAM;AAAA,CACtE,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,YAAY,CAAC,eAAe;AAAA,MAC5B,YAAY;AAAA,MACZ,SAAS,CAAC,SAAS,sBAAsB,eAAe;AAAA,MACxD,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAoCV,MAAM;AAAA,QACJ,4BAA4B;AAAA,QAC5B,mCAAmC;AAAA,QACnC,6BAA6B;AAAA,QAC7B,6BAA6B;AAAA,QAC7B,6BAA6B;AAAA,QAC7B,6BAA6B;AAAA,QAC7B,oCAAoC;AAAA,QACpC,sCAAsC;AAAA,QACtC,oCAAoC;AAAA,QACpC,iCAAiC;AAAA,QACjC,oCAAoC;AAAA,QACpC,kCAAkC;AAAA,QAClC,mCAAmC;AAAA,QACnC,iCAAiC;AAAA,QACjC,gCAAgC;AAAA,QAChC,SAAS;AAAA,MACX;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,KAAK,CAAC;AAAA,MACJ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,cAAc;AACZ,SAAK,YAAY;AACjB,SAAK,OAAO;AACZ,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,OAAO;AACT,WAAK,cAAc,KAAK,SAAS,kBAAkB,QAAQ,KAAK,KAAK,MAAM,KAAK,KAAK,QAAQ;AAAA,IAC/F;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,2BAA2B,mBAAmB;AACjE,aAAO,KAAK,qBAAqB,qBAAoB;AAAA,IACvD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,YAAY,CAAC;AAAA,MAC1B,WAAW,CAAC,GAAG,oBAAoB;AAAA,MACnC,QAAQ;AAAA,QACN,OAAO;AAAA,QACP,WAAW;AAAA,QACX,MAAM;AAAA,MACR;AAAA,MACA,UAAU,CAAC,WAAW;AAAA,MACtB,YAAY;AAAA,MACZ,UAAU,CAAI,sBAAyB,mBAAmB;AAAA,MAC1D,oBAAoBA;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,YAAY,GAAG,CAAC,GAAG,yBAAyB,GAAG,CAAC,GAAG,mBAAmB,CAAC;AAAA,MACpF,UAAU,SAAS,4BAA4B,IAAI,KAAK;AACtD,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,aAAa,CAAC;AACjB,UAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,UAAG,WAAW,GAAG,4CAA4C,GAAG,GAAG,gBAAgB,CAAC;AACpF,UAAG,UAAU,GAAG,OAAO,CAAC;AACxB,UAAG,aAAa;AAAA,QAClB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,UAAU;AACb,UAAG,WAAW,IAAI,eAAe,sBAAsB,IAAI,WAAW;AACtE,UAAG,YAAY,oBAAoB,CAAC,IAAI,eAAe,IAAI,KAAK;AAChE,UAAG,YAAY,4BAA4B,IAAI,cAAc,KAAK,EAAE,8BAA8B,IAAI,cAAc,OAAO;AAC3H,UAAG,UAAU;AACb,UAAG,WAAW,2BAA2B,IAAI,IAAI;AACjD,UAAG,UAAU;AACb,UAAG,YAAY,SAAS,CAAC,IAAI,eAAe,IAAI,KAAK;AAAA,QACvD;AAAA,MACF;AAAA,MACA,cAAc,CAAC,iBAAoB,gCAAgC;AAAA,MACnE,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,YAAY;AAAA,MACZ,SAAS,CAAC,eAAe;AAAA,MACzB,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAaV,MAAM;AAAA,QACJ,SAAS;AAAA,MACX;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,OAAO;AACL,SAAK,OAAO,SAAS,uBAAuB,mBAAmB;AAC7D,aAAO,KAAK,qBAAqB,iBAAgB;AAAA,IACnD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,mBAAmB,sBAAsB,kBAAkB;AAAA,MACrE,SAAS,CAAC,mBAAmB,oBAAoB,oBAAoB;AAAA,IACvE,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,mBAAmB,kBAAkB;AAAA,IACjD,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,mBAAmB,sBAAsB,kBAAkB;AAAA,MACrE,SAAS,CAAC,mBAAmB,oBAAoB,oBAAoB;AAAA,IACvE,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;AC1xBH,IAAME,OAAM,CAAC,SAAS;AACtB,SAAS,0DAA0D,IAAI,KAAK;AAC1E,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,KAAK;AAAA,EACnC;AACF;AACA,SAAS,2CAA2C,IAAI,KAAK;AAC3D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC;AACvD,IAAG,UAAU,GAAG,QAAQ,CAAC;AACzB,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,GAAG,2DAA2D,GAAG,GAAG,gBAAgB,CAAC;AACnG,IAAG,aAAa,EAAE,EAAE;AAAA,EACtB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,YAAY,sBAAsB,OAAO,mBAAmB,OAAO,iBAAiB,EAAE,uBAAuB,CAAC,OAAO,KAAK,EAAE,oBAAoB,OAAO,SAAS,MAAM,EAAE,uBAAuB,OAAO,SAAS,SAAS,EAAE,qBAAqB,OAAO,SAAS,OAAO,EAAE,uBAAuB,OAAO,SAAS,SAAS;AAC3T,IAAG,WAAW,WAAW,OAAO,SAAS,EAAE,WAAW,OAAO,YAAY,EAAE,kBAAkB,QAAQ;AACrG,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,WAAW,OAAO,kBAAkB;AAClD,IAAG,UAAU;AACb,IAAG,WAAW,WAAW,OAAO,eAAe,EAAE,WAAW,OAAO,yBAAyB;AAC5F,IAAG,UAAU;AACb,IAAG,WAAW,WAAW,OAAO,iBAAiB;AACjD,IAAG,UAAU;AACb,IAAG,WAAW,2BAA2B,OAAO,KAAK;AAAA,EACvD;AACF;AACA,IAAM,0BAAN,MAAM,yBAAwB;AAAA;AAAA;AAAA;AAAA,EAI5B,IAAI,SAAS;AACX,WAAO,KAAK,SAAS,KAAK,kBAAkB;AAAA,EAC9C;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK,WAAW,KAAK,oBAAoB;AAAA,EAClD;AAAA,EACA,IAAI,UAAU;AACZ,WAAO,KAAK,UAAU,KAAK,mBAAmB;AAAA,EAChD;AAAA,EACA,IAAI,WAAW;AACb,WAAO,OAAO,KAAK,YAAY,cAAc,KAAK,UAAU;AAAA,EAC9D;AAAA,EACA,IAAI,aAAa;AACf,UAAM,IAAI,KAAK;AACf,WAAO,MAAM,QAAQ,CAAC,KAAK,EAAE,SAAS,IAAI,IAAI,OAAO,MAAM,YAAY,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK;AAAA,EACzF;AAAA,EACA,IAAI,WAAW;AACb,YAAQ,OAAO,KAAK,YAAY,cAAc,KAAK,UAAU,KAAK,oBAAoB;AAAA,EACxF;AAAA,EACA,IAAI,mBAAmB;AACrB,WAAO,KAAK,mBAAmB;AAAA,EACjC;AAAA,EACA,IAAI,mBAAmB;AACrB,WAAO,KAAK,mBAAmB;AAAA,EACjC;AAAA,EACA,IAAI,oBAAoB;AACtB,WAAO,KAAK,oBAAoB;AAAA,EAClC;AAAA,EACA,IAAI,gBAAgB;AAClB,WAAO,KAAK,gBAAgB;AAAA,EAC9B;AAAA,EACA,IAAI,aAAa;AACf,WAAO,KAAK,aAAa,OAAO,KAAK,YAAY;AAAA,EACnD;AAAA,EACA,sBAAsB;AACpB,WAAO;AAAA,MACL,aAAa,CAAC,eAAe,MAAM,IAAI;AAAA,IACzC;AAAA,EACF;AAAA,EACA,YAAY,YAAY,UAAU,UAAU;AAC1C,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,gBAAgB,IAAI,aAAa;AACtC,SAAK,kBAAkB;AACvB,SAAK,WAAW,IAAI,QAAQ;AAC5B,SAAK,qBAAqB,CAAC;AAAA,EAC7B;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,WAAW,CAAC,QAAQ,cAAc,GAAG;AACvC,WAAK,iBAAiB;AAAA,IACxB;AACA,QAAI,KAAK,WAAW;AAClB,WAAK,0BAA0B,OAAO;AAAA,IACxC;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,SAAK,gBAAgB;AACrB,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAEvB,SAAK,mBAAmB;AACxB,SAAK,uBAAuB;AAAA,EAC9B;AAAA,EACA,OAAO;AACL,SAAK,WAAW,KAAK;AAAA,EACvB;AAAA,EACA,OAAO;AACL,SAAK,WAAW,KAAK;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB;AACf,QAAI,KAAK,WAAW;AAClB,WAAK,UAAU,eAAe;AAAA,IAChC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,kBAAkB;AAChB,UAAM,eAAe,KAAK;AAC1B,SAAK,YAAY,aAAa;AAE9B,SAAK,SAAS,YAAY,KAAK,SAAS,WAAW,KAAK,WAAW,aAAa,GAAG,aAAa,SAAS,aAAa;AACtH,SAAK,UAAU,iBAAiB,KAAK,UAAU,KAAK,UAAU;AAC9D,SAAK,eAAe;AACpB,UAAM,mBAAmB,KAAK,UAAU,cAAc,KAAK,qBAAqB,CAAC;AACjF,qBAAiB,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,aAAW;AACnE,WAAK,kBAAkB;AACvB,WAAK,cAAc,KAAK,OAAO;AAAA,IACjC,CAAC;AAID,qBAAiB,KAAK,OAAO,aAAW,OAAO,GAAG,MAAM,GAAG,aAAa,GAAG,OAAO,MAAM,QAAQ,KAAK,WAAW,SAAS,UAAU,CAAC,GAAG,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM;AAC/K,WAAK,WAAW,eAAe;AAAA,IACjC,CAAC;AAAA,EACH;AAAA,EACA,mBAAmB;AAGjB,UAAM,KAAK,KAAK,WAAW;AAC3B,UAAM,UAAU,KAAK;AACrB,SAAK,uBAAuB;AAC5B,QAAI,YAAY,SAAS;AACvB,UAAI;AACJ,WAAK,mBAAmB,KAAK,KAAK,SAAS,OAAO,IAAI,cAAc,MAAM;AACxE,aAAK,gBAAgB,MAAM,MAAM,KAAK,gBAAgB;AAAA,MACxD,CAAC,CAAC;AACF,WAAK,mBAAmB,KAAK,KAAK,SAAS,OAAO,IAAI,cAAc,MAAM;AACxE,aAAK,gBAAgB,MAAM,OAAO,KAAK,gBAAgB;AACvD,YAAI,KAAK,WAAW,QAAQ,cAAc,CAAC,gBAAgB;AACzD,2BAAiB,KAAK,UAAU,QAAQ,WAAW;AACnD,eAAK,mBAAmB,KAAK,KAAK,SAAS,OAAO,gBAAgB,cAAc,MAAM;AACpF,iBAAK,gBAAgB,OAAO,MAAM,KAAK,gBAAgB;AAAA,UACzD,CAAC,CAAC;AACF,eAAK,mBAAmB,KAAK,KAAK,SAAS,OAAO,gBAAgB,cAAc,MAAM;AACpF,iBAAK,gBAAgB,OAAO,OAAO,KAAK,gBAAgB;AAAA,UAC1D,CAAC,CAAC;AAAA,QACJ;AAAA,MACF,CAAC,CAAC;AAAA,IACJ,WAAW,YAAY,SAAS;AAC9B,WAAK,mBAAmB,KAAK,KAAK,SAAS,OAAO,IAAI,SAAS,MAAM,KAAK,KAAK,CAAC,CAAC;AACjF,WAAK,mBAAmB,KAAK,KAAK,SAAS,OAAO,IAAI,QAAQ,MAAM,KAAK,KAAK,CAAC,CAAC;AAAA,IAClF,WAAW,YAAY,SAAS;AAC9B,WAAK,mBAAmB,KAAK,KAAK,SAAS,OAAO,IAAI,SAAS,OAAK;AAClE,UAAE,eAAe;AACjB,aAAK,KAAK;AAAA,MACZ,CAAC,CAAC;AAAA,IACJ;AAAA,EAEF;AAAA,EACA,0BAA0B,SAAS;AACjC,SAAK,uBAAuB,OAAO,KAAK,OAAO,CAAC;AAAA,EAClD;AAAA,EACA,uBAAuB,MAAM;AAC3B,UAAM,oBAAoB;AAAA;AAAA,MAExB,OAAO,CAAC,SAAS,MAAM,KAAK,MAAM;AAAA,MAClC,gBAAgB,CAAC,SAAS,MAAM,KAAK,MAAM;AAAA,MAC3C,SAAS,CAAC,WAAW,MAAM,KAAK,QAAQ;AAAA,MACxC,kBAAkB,CAAC,WAAW,MAAM,KAAK,QAAQ;AAAA,MACjD,QAAQ,CAAC,UAAU,MAAM,KAAK,OAAO;AAAA,MACrC,iBAAiB,CAAC,UAAU,MAAM,KAAK,OAAO;AAAA,MAC9C,SAAS,CAAC,WAAW,MAAM,KAAK,QAAQ;AAAA,MACxC,WAAW,CAAC,aAAa,MAAM,KAAK,UAAU;AAAA,MAC9C,SAAS,CAAC,WAAW,MAAM,KAAK,QAAQ;AAAA,MACxC,iBAAiB,CAAC,mBAAmB,MAAM,KAAK,gBAAgB;AAAA,MAChE,iBAAiB,CAAC,mBAAmB,MAAM,KAAK,gBAAgB;AAAA,MAChE,kBAAkB,CAAC,oBAAoB,MAAM,KAAK,iBAAiB;AAAA,MACnE,cAAc,CAAC,gBAAgB,MAAM,KAAK,aAAa;AAAA,MACvD,WAAW,CAAC,aAAa,MAAM,KAAK,UAAU;AAAA,OAC3C,KAAK,oBAAoB;AAE9B,KAAC,QAAQ,OAAO,KAAK,iBAAiB,EAAE,OAAO,SAAO,CAAC,IAAI,WAAW,WAAW,CAAC,GAAG,QAAQ,cAAY;AACvG,UAAI,kBAAkB,QAAQ,GAAG;AAC/B,cAAM,CAAC,MAAM,OAAO,IAAI,kBAAkB,QAAQ;AAClD,aAAK,qBAAqB,MAAM,QAAQ,CAAC;AAAA,MAC3C;AAAA,IACF,CAAC;AACD,SAAK,WAAW,kBAAkB;AAAA,EACpC;AAAA,EACA,iBAAiB;AACf,SAAK,uBAAuB;AAAA,EAC9B;AAAA,EACA,qBAAqB,KAAK,OAAO;AAC/B,QAAI,OAAO,UAAU,aAAa;AAEhC,WAAK,UAAU,GAAG,IAAI;AAAA,IACxB;AAAA,EACF;AAAA,EACA,gBAAgB,UAAU,SAASC,SAAQ,IAAI;AAC7C,QAAI,KAAK,YAAY;AACnB,WAAK,mBAAmB;AAAA,IAC1B,WAAWA,SAAQ,GAAG;AACpB,WAAK,aAAa,WAAW,MAAM;AACjC,aAAK,aAAa;AAClB,kBAAU,KAAK,KAAK,IAAI,KAAK,KAAK;AAAA,MACpC,GAAGA,SAAQ,GAAI;AAAA,IACjB,OAAO;AAGL,iBAAW,WAAW,KAAK,KAAK,IAAI,KAAK,KAAK;AAAA,IAChD;AAAA,EACF;AAAA,EACA,yBAAyB;AACvB,SAAK,mBAAmB,QAAQ,aAAW,QAAQ,CAAC;AACpD,SAAK,mBAAmB,SAAS;AAAA,EACnC;AAAA,EACA,qBAAqB;AACnB,QAAI,KAAK,YAAY;AACnB,mBAAa,KAAK,UAAU;AAC5B,WAAK,aAAa;AAAA,IACpB;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,gCAAgC,mBAAmB;AACtE,aAAO,KAAK,qBAAqB,0BAA4B,kBAAqB,UAAU,GAAM,kBAAqB,gBAAgB,GAAM,kBAAqB,SAAS,CAAC;AAAA,IAC9K;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,UAAU,CAAI,oBAAoB;AAAA,IACpC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AAEH,IAAM,0BAAN,MAAM,yBAAwB;AAAA,EAC5B,IAAI,QAAQ,OAAO;AACjB,UAAM,UAAU,UAAU,KAAK;AAC/B,QAAI,KAAK,aAAa,SAAS;AAC7B,WAAK,WAAW;AAChB,WAAK,cAAc,KAAK,OAAO;AAAA,IACjC;AAAA,EACF;AAAA,EACA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,OAAO;AACjB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,UAAU,OAAO;AACnB,UAAM,oBAAoB,MAAM,IAAI,eAAa,aAAa,SAAS,CAAC;AACxE,SAAK,aAAa,CAAC,GAAG,mBAAmB,GAAG,yBAAyB;AAAA,EACvE;AAAA,EACA,YAAY,KAAK;AACf,SAAK,MAAM;AACX,SAAK,QAAQ;AACb,SAAK,UAAU;AACf,SAAK,SAAS;AACd,SAAK,eAAe,CAAC;AACrB,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,gBAAgB,IAAI,QAAQ;AACjC,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,qBAAqB;AAC1B,SAAK,MAAM;AACX,SAAK,YAAY,CAAC;AAClB,SAAK,UAAU;AACf,SAAK,aAAa,CAAC,GAAG,yBAAyB;AAC/C,SAAK,WAAW,IAAI,QAAQ;AAAA,EAC9B;AAAA,EACA,WAAW;AAAA,EAAC;AAAA,EACZ,cAAc;AACZ,SAAK,cAAc,SAAS;AAC5B,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,OAAO;AACL,QAAI,KAAK,SAAS;AAChB;AAAA,IACF;AACA,QAAI,CAAC,KAAK,QAAQ,GAAG;AACnB,WAAK,UAAU;AACf,WAAK,cAAc,KAAK,IAAI;AAC5B,WAAK,IAAI,cAAc;AAAA,IACzB;AAAA,EAKF;AAAA,EACA,OAAO;AACL,QAAI,CAAC,KAAK,SAAS;AACjB;AAAA,IACF;AACA,SAAK,UAAU;AACf,SAAK,cAAc,KAAK,KAAK;AAC7B,SAAK,IAAI,cAAc;AAAA,EACzB;AAAA,EACA,oBAAoB;AAClB,SAAK,aAAa;AAClB,SAAK,IAAI,cAAc;AACvB,YAAQ,QAAQ,EAAE,KAAK,MAAM;AAC3B,WAAK,eAAe;AACpB,WAAK,wBAAwB;AAAA,IAC/B,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB;AACf,QAAI,KAAK,UAAU,KAAK,WAAW,KAAK,QAAQ,YAAY;AAC1D,WAAK,QAAQ,WAAW,eAAe;AAAA,IACzC;AAAA,EACF;AAAA,EACA,iBAAiB,UAAU;AACzB,SAAK,qBAAqB,iBAAiB,QAAQ;AACnD,SAAK,aAAa;AAElB,SAAK,IAAI,cAAc;AAAA,EACzB;AAAA,EACA,eAAe;AACb,SAAK,YAAY;AAAA,MACf,CAAC,KAAK,gBAAgB,GAAG;AAAA,MACzB,CAAC,GAAG,KAAK,OAAO,cAAc,KAAK,kBAAkB,EAAE,GAAG;AAAA,IAC5D;AAAA,EACF;AAAA,EACA,iBAAiB,QAAQ;AACvB,SAAK,SAAS;AACd,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,eAAe,OAAO;AACpB,UAAM,SAAS,gBAAgB,KAAK;AACpC,QAAI,CAAC,KAAK,OAAO,cAAc,SAAS,MAAM,KAAK,KAAK,YAAY,MAAM;AACxE,WAAK,KAAK;AAAA,IACZ;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,0BAA0B;AACxB,QAAI,KAAK,QAAQ,GAAG;AAClB,WAAK,KAAK;AAAA,IACZ;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,gCAAgC,mBAAmB;AACtE,aAAO,KAAK,qBAAqB,0BAA4B,kBAAqB,iBAAiB,CAAC;AAAA,IACtG;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,SAAS,8BAA8B,IAAI,KAAK;AACzD,YAAI,KAAK,GAAG;AACV,UAAG,YAAYD,MAAK,CAAC;AAAA,QACvB;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,UAAU,GAAG;AAAA,QAChE;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,QAChB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,SAAS,eAAe,OAAO;AAC7B,SAAO,iBAAiB,cAAc,QAAQ,UAAU,MAAM,CAAC,gBAAgB,SAAS,KAAK;AAC/F;AACA,IAAM,sBAAN,MAAM,6BAA4B,wBAAwB;AAAA,EACxD,YAAY,YAAY,UAAU,UAAU;AAC1C,UAAM,YAAY,UAAU,QAAQ;AACpC,SAAK,UAAU;AACf,SAAK,YAAY;AAEjB,SAAK,gBAAgB,IAAI,aAAa;AACtC,SAAK,eAAe,KAAK,SAAS,gBAAgB,mBAAmB;AAAA,EACvE;AAAA,EACA,sBAAsB;AACpB,WAAO;AAAA,MACL,YAAY,CAAC,qBAAqB,MAAM,KAAK,iBAAiB;AAAA,MAC9D,iBAAiB,CAAC,mBAAmB,MAAM,KAAK,eAAe;AAAA,MAC/D,cAAc,CAAC,SAAS,MAAM,KAAK,YAAY;AAAA,IACjD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,4BAA4B,mBAAmB;AAClE,aAAO,KAAK,qBAAqB,sBAAwB,kBAAqB,UAAU,GAAM,kBAAqB,gBAAgB,GAAM,kBAAqB,SAAS,CAAC;AAAA,IAC1K;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,eAAe,EAAE,CAAC;AAAA,MACnC,UAAU;AAAA,MACV,cAAc,SAAS,iCAAiC,IAAI,KAAK;AAC/D,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,oBAAoB,IAAI,OAAO;AAAA,QAChD;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,OAAO,CAAC,GAAG,gBAAgB,OAAO;AAAA,QAClC,gBAAgB,CAAC,GAAG,eAAe,gBAAgB;AAAA,QACnD,SAAS,CAAC,GAAG,kBAAkB,SAAS;AAAA,QACxC,WAAW,CAAC,GAAG,oBAAoB,WAAW;AAAA,QAC9C,QAAQ,CAAC,GAAG,iBAAiB,QAAQ;AAAA,QACrC,SAAS,CAAC,GAAG,kBAAkB,SAAS;AAAA,QACxC,iBAAiB,CAAC,GAAG,0BAA0B,iBAAiB;AAAA,QAChE,iBAAiB,CAAC,GAAG,0BAA0B,iBAAiB;AAAA,QAChE,kBAAkB,CAAC,GAAG,2BAA2B,kBAAkB;AAAA,QACnE,cAAc,CAAC,GAAG,uBAAuB,cAAc;AAAA,QACvD,cAAc;AAAA,QACd,mBAAmB;AAAA,QACnB,iBAAiB;AAAA,MACnB;AAAA,MACA,SAAS;AAAA,QACP,eAAe;AAAA,MACjB;AAAA,MACA,UAAU,CAAC,YAAY;AAAA,MACvB,YAAY;AAAA,MACZ,UAAU,CAAI,0BAA0B;AAAA,IAC1C,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,4BAA4B;AAAA,MAC9B;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,IACvB,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,IAC3B,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC,wBAAwB;AAAA,IACjC,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC,wBAAwB;AAAA,IACjC,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,yBAAyB;AAAA,IAClC,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,sBAAsB;AAAA,IAC/B,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,sBAAN,MAAM,6BAA4B,wBAAwB;AAAA,EACxD,YAAY,KAAK,gBAAgB;AAC/B,UAAM,GAAG;AACT,SAAK,QAAQ;AACb,SAAK,oBAAoB;AACzB,SAAK,kBAAkB;AACvB,SAAK,mBAAmB,CAAC;AACzB,SAAK,4BAA4B,CAAC;AAClC,SAAK,qBAAqB,CAAC;AAAA,EAC7B;AAAA,EACA,UAAU;AACR,WAAO,eAAe,KAAK,KAAK;AAAA,EAClC;AAAA,EACA,eAAe;AACb,UAAM,gBAAgB,KAAK;AAC3B,SAAK,YAAY;AAAA,MACf,CAAC,KAAK,gBAAgB,GAAG;AAAA,MACzB,CAAC,GAAG,KAAK,OAAO,cAAc,KAAK,kBAAkB,EAAE,GAAG;AAAA,IAC5D;AAIA,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,eAAe;AACb,QAAI,oBAAoB;AAExB,QAAI,KAAK,mBAAmB,QAAQ,KAAK,IAAI,IAAI;AAC/C,WAAK,4BAA4B;AAAA,QAC/B,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,cAAc;AAAA;AAAA,MAEhB;AAAA,IACF;AACA,QAAI,KAAK,mBAAmB,QAAQ,QAAQ,IAAI,IAAI;AAClD,WAAK,4BAA4B;AAAA,QAC/B,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,WAAW;AAAA;AAAA,MAEb;AAAA,IACF;AACA,QAAI,KAAK,mBAAmB,QAAQ,MAAM,IAAI,IAAI;AAChD,WAAK,4BAA4B;AAAA,QAC/B,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,WAAW;AAAA;AAAA,QAEX,cAAc;AAAA,QACd,aAAa;AAAA,MACf;AAAA,IACF;AACA,QAAI,KAAK,mBAAmB,QAAQ,OAAO,IAAI,IAAI;AACjD,WAAK,4BAA4B;AAAA,QAC/B,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,WAAW;AAAA;AAAA,QAEX,cAAc;AAAA,QACd,YAAY;AAAA,MACd;AAAA,IACF;AACA,YAAQ,KAAK,oBAAoB;AAAA,MAE/B,KAAK;AACH,aAAK,qBAAqB;AAAA,UACxB,QAAQ;AAAA,UACR,MAAM;AAAA,UACN,WAAW;AAAA,QACb;AACA;AAAA,MACF,KAAK;AACH,aAAK,qBAAqB;AAAA,UACxB,QAAQ;AAAA,UACR,MAAM;AAAA,QACR;AACA;AAAA,MACF,KAAK;AACH,aAAK,qBAAqB;AAAA,UACxB,QAAQ;AAAA,UACR,OAAO;AAAA,QACT;AACA;AAAA,MAGF,KAAK;AACH,aAAK,qBAAqB;AAAA,UACxB,KAAK;AAAA,UACL,MAAM;AAAA,UACN,WAAW;AAAA,QACb;AACA;AAAA,MACF,KAAK;AACH,aAAK,qBAAqB;AAAA,UACxB,KAAK;AAAA,UACL,MAAM;AAAA,QACR;AACA;AAAA,MACF,KAAK;AACH,aAAK,qBAAqB;AAAA,UACxB,KAAK;AAAA,UACL,OAAO;AAAA,QACT;AACA;AAAA,MAGF,KAAK;AACH,aAAK,qBAAqB;AAAA,UACxB,OAAO;AAAA,UACP,KAAK;AAAA,UACL,WAAW;AAAA,QACb;AACA;AAAA,MACF,KAAK;AACH,aAAK,qBAAqB;AAAA,UACxB,OAAO;AAAA,UACP,KAAK;AAAA,QACP;AACA;AAAA,MACF,KAAK;AACH,aAAK,qBAAqB;AAAA,UACxB,OAAO;AAAA,UACP,QAAQ;AAAA,QACV;AACA;AAAA,MAGF,KAAK;AACH,aAAK,qBAAqB;AAAA,UACxB,MAAM;AAAA,UACN,KAAK;AAAA,UACL,WAAW;AAAA,QACb;AACA;AAAA,MACF,KAAK;AACH,aAAK,qBAAqB;AAAA,UACxB,MAAM;AAAA,UACN,KAAK;AAAA,QACP;AACA;AAAA,MACF,KAAK;AACH,aAAK,qBAAqB;AAAA,UACxB,MAAM;AAAA,UACN,QAAQ;AAAA,QACV;AACA;AAAA,MAEF;AACE,aAAK,qBAAqB;AAAA,UACxB,QAAQ;AAAA,UACR,MAAM;AAAA,UACN,WAAW;AAAA,QACb;AACA;AAAA,IACJ;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,4BAA4B,mBAAmB;AAClE,aAAO,KAAK,qBAAqB,sBAAwB,kBAAqB,iBAAiB,GAAM,kBAAqB,cAAc,CAAC;AAAA,IAC3I;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,aAAa,CAAC;AAAA,MAC3B,UAAU,CAAC,qBAAqB;AAAA,MAChC,YAAY;AAAA,MACZ,UAAU,CAAI,4BAA+B,mBAAmB;AAAA,MAChE,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,WAAW,qBAAqB,GAAG,CAAC,uBAAuB,IAAI,GAAG,uBAAuB,UAAU,kBAAkB,6BAA6B,2BAA2B,gCAAgC,yBAAyB,GAAG,CAAC,GAAG,eAAe,GAAG,WAAW,SAAS,GAAG,CAAC,GAAG,qBAAqB,GAAG,CAAC,GAAG,qBAAqB,GAAG,SAAS,GAAG,CAAC,GAAG,6BAA6B,GAAG,WAAW,SAAS,GAAG,CAAC,GAAG,qBAAqB,GAAG,SAAS,GAAG,CAAC,GAAG,yBAAyB,CAAC;AAAA,MACre,UAAU,SAAS,6BAA6B,IAAI,KAAK;AACvD,YAAI,KAAK,GAAG;AACV,gBAAM,MAAS,iBAAiB;AAChC,UAAG,WAAW,GAAG,4CAA4C,GAAG,IAAI,eAAe,GAAG,GAAM,sBAAsB;AAClH,UAAG,WAAW,uBAAuB,SAAS,wEAAwE,QAAQ;AAC5H,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,eAAe,MAAM,CAAC;AAAA,UAClD,CAAC,EAAE,UAAU,SAAS,6DAA6D;AACjF,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,KAAK,CAAC;AAAA,UAClC,CAAC,EAAE,kBAAkB,SAAS,mEAAmE,QAAQ;AACvG,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,iBAAiB,MAAM,CAAC;AAAA,UACpD,CAAC;AAAA,QACH;AACA,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,6BAA6B,IAAI,MAAM,EAAE,2BAA2B,IAAI,QAAQ,EAAE,gCAAgC,IAAI,UAAU,EAAE,2BAA2B,IAAI;AAAA,QACjL;AAAA,MACF;AAAA,MACA,cAAc,CAAC,SAAS,SAAS,eAAkB,qBAAqB,iBAAoB,kCAAkC,gBAAgB;AAAA,MAC9I,eAAe;AAAA,MACf,MAAM;AAAA,QACJ,WAAW,CAAC,aAAa;AAAA,MAC3B;AAAA,MACA,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,YAAY,CAAC,aAAa;AAAA,MAC1B,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAoCV,qBAAqB;AAAA,MACrB,YAAY;AAAA,MACZ,SAAS,CAAC,SAAS,SAAS,eAAe,yBAAyB,iBAAiB,gBAAgB;AAAA,IACvG,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,mBAAmB;AAC/D,aAAO,KAAK,qBAAqB,mBAAkB;AAAA,IACrD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,qBAAqB,mBAAmB;AAAA,MAClD,SAAS,CAAC,qBAAqB,mBAAmB;AAAA,IACpD,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,mBAAmB;AAAA,IAC/B,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,qBAAqB,mBAAmB;AAAA,MAClD,SAAS,CAAC,qBAAqB,mBAAmB;AAAA,IACpD,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;AC7zBH,IAAME,OAAM,CAAC,uBAAuB,EAAE;AACtC,IAAM,MAAM,CAAC,GAAG;AAChB,SAAS,kDAAkD,IAAI,KAAK;AAClE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,CAAC;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,OAAO,OAAO;AAAA,EAC9B;AACF;AACA,SAAS,kDAAkD,IAAI,KAAK;AAClE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,CAAC;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,aAAa,OAAO,aAAgB,cAAc;AAAA,EAClE;AACF;AACA,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAChB,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU,CAAC;AACd,IAAG,kBAAkB,OAAO,QAAQ;AAAA,EACtC;AACF;AACA,SAAS,kDAAkD,IAAI,KAAK;AAClE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,CAAC;AAAA,EAC3B;AACF;AACA,SAAS,kDAAkD,IAAI,KAAK;AAClE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,CAAC;AAAA,EAC3B;AACF;AACA,IAAM,MAAM,CAAC,8BAA8B,EAAE;AAC7C,SAAS,wDAAwD,IAAI,KAAK;AAAC;AAC3E,IAAM,MAAM,CAAC,mCAAmC,EAAE;AAClD,SAAS,4DAA4D,IAAI,KAAK;AAAC;AAC/E,IAAM,MAAM,CAAC,iBAAiB,EAAE;AAChC,IAAM,MAAM,CAAC,CAAC,CAAC,IAAI,SAAS,EAAE,CAAC,GAAG,GAAG;AACrC,IAAM,MAAM,CAAC,WAAW,GAAG;AAC3B,SAAS,6CAA6C,IAAI,KAAK;AAC7D,MAAI,KAAK,GAAG;AACV,IAAG,aAAa,CAAC;AAAA,EACnB;AACF;AACA,SAAS,6CAA6C,IAAI,KAAK;AAC7D,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,OAAO,CAAC;AAAA,EAC1B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,UAAM,qBAAwB,YAAY,CAAC;AAC3C,IAAG,WAAW,QAAQ,OAAO,IAAI,EAAE,WAAW,OAAO,OAAO,EAAE,eAAe,OAAO,kBAAkB,EAAE,kBAAkB,kBAAkB;AAAA,EAC9I;AACF;AACA,SAAS,2DAA2D,IAAI,KAAK;AAC3E,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,qBAAqB,SAAS,4FAA4F,QAAQ;AAC9I,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,mBAAmB,MAAM,CAAC;AAAA,IACzD,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,UAAM,qBAAwB,YAAY,CAAC;AAC3C,IAAG,WAAW,SAAS,OAAO,KAAK,EAAE,QAAQ,OAAO,IAAI,EAAE,WAAW,OAAO,OAAO,EAAE,YAAY,OAAO,QAAQ,EAAE,eAAe,OAAO,WAAW,EAAE,0BAA0B,OAAO,sBAAsB,EAAE,kBAAkB,kBAAkB,EAAE,eAAe,OAAO,kBAAkB;AAAA,EAC9R;AACF;AACA,SAAS,6CAA6C,IAAI,KAAK;AAC7D,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,WAAW,GAAG,4DAA4D,GAAG,GAAG,eAAe,CAAC;AACnG,IAAG,WAAW,kBAAkB,SAAS,mFAAmF,QAAQ;AAClI,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,iBAAiB,MAAM,CAAC;AAAA,IACvD,CAAC;AAAA,EACH;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,UAAM,YAAe,YAAY,CAAC;AAClC,IAAG,WAAW,gCAAgC,OAAO,gBAAgB,EAAE,6BAA6B,SAAS,EAAE,4BAA4B,OAAO,YAAY,EAAE,2BAA2B,OAAO,OAAO,EAAE,wCAAwC,uBAAuB;AAAA,EAC5Q;AACF;AACA,SAAS,6CAA6C,IAAI,KAAK;AAC7D,MAAI,KAAK,GAAG;AACV,IAAG,aAAa,GAAG,CAAC;AAAA,EACtB;AACF;AACA,IAAM,MAAM,CAAC,cAAc;AAC3B,IAAM,MAAM,CAAC,oBAAoB,EAAE;AACnC,IAAM,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,SAAS,EAAE,CAAC,CAAC;AACrC,IAAM,OAAO,CAAC,KAAK,SAAS;AAC5B,SAAS,gDAAgD,IAAI,KAAK;AAChE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,QAAQ;AAAA,EACtC;AACF;AACA,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,IAAG,aAAa,GAAG,CAAC;AAAA,EACtB;AACF;AACA,IAAM,iCAAiC,IAAI,eAAe,4BAA4B;AACtF,IAAM,6BAA6B,IAAI,eAAe,4BAA4B;AAClF,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,cAAc;AAEZ,SAAK,2BAA2B,IAAI,QAAQ;AAE5C,SAAK,sBAAsB,IAAI,QAAQ;AACvC,SAAK,SAAS,IAAI,gBAAgB,OAAO;AACzC,SAAK,QAAQ,IAAI,gBAAgB,UAAU;AAC3C,SAAK,gBAAgB,IAAI,gBAAgB,EAAE;AAC3C,SAAK,sBAAsB,IAAI,gBAAgB,KAAK;AAAA,EACtD;AAAA,EACA,0BAA0B,QAAQ;AAChC,SAAK,yBAAyB,KAAK,MAAM;AAAA,EAC3C;AAAA,EACA,qBAAqB,QAAQ;AAC3B,SAAK,oBAAoB,KAAK,MAAM;AAAA,EACtC;AAAA,EACA,QAAQ,MAAM;AACZ,SAAK,MAAM,KAAK,IAAI;AAAA,EACtB;AAAA,EACA,SAAS,OAAO;AACd,SAAK,OAAO,KAAK,KAAK;AAAA,EACxB;AAAA,EACA,gBAAgB,QAAQ;AACtB,SAAK,cAAc,KAAK,MAAM;AAAA,EAChC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,sBAAsB,mBAAmB;AAC5D,aAAO,KAAK,qBAAqB,gBAAe;AAAA,IAClD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,eAAc;AAAA,IACzB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,sBAAN,MAAM,qBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMxB,qBAAqB,QAAQ;AAC3B,SAAK,oBAAoB,KAAK,MAAM;AAAA,EACtC;AAAA,EACA,4BAA4B,OAAO;AACjC,SAAK,sBAAsB,KAAK,KAAK;AAAA,EACvC;AAAA,EACA,iCAAiC,OAAO;AACtC,SAAK,4BAA4B,KAAK,KAAK;AAAA,EAC7C;AAAA,EACA,YAAY,sBAAsB,kBAAkB,wBAAwB;AAC1E,SAAK,uBAAuB;AAC5B,SAAK,mBAAmB;AACxB,SAAK,yBAAyB;AAC9B,SAAK,QAAQ,KAAK,iBAAiB,MAAM,KAAK,IAAI,UAAQ;AACxD,UAAI,SAAS,UAAU;AACrB,eAAO;AAAA,MAET,WAAW,SAAS,cAAc,KAAK,sBAAsB;AAC3D,eAAO;AAAA,MACT,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF,CAAC,CAAC;AACF,SAAK,QAAQ;AACb,SAAK,wBAAwB,IAAI,gBAAgB,KAAK;AACtD,SAAK,sBAAsB,IAAI,gBAAgB,KAAK;AAEpD,SAAK,8BAA8B,IAAI,QAAQ;AAC/C,SAAK,sBAAsB,IAAI,QAAQ;AACvC,SAAK,WAAW,IAAI,QAAQ;AAC5B,QAAI,KAAK,sBAAsB;AAC7B,WAAK,QAAQ,KAAK,qBAAqB,QAAQ;AAAA,IACjD;AAEA,UAAM,0BAA0B,KAAK,oBAAoB,KAAK,SAAS,MAAM,KAAK,KAAK,GAAG,OAAO,UAAQ,SAAS,YAAY,KAAK,sBAAsB,GAAG,MAAM,KAAK,CAAC;AACxK,UAAM,wBAAwB,MAAM,KAAK,6BAA6B,uBAAuB;AAE7F,UAAM,6BAA6B,cAAc,CAAC,KAAK,qBAAqB,qBAAqB,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,oBAAoB,oBAAoB,MAAM,sBAAsB,oBAAoB,GAAG,UAAU,GAAG,GAAG,qBAAqB,GAAG,UAAU,KAAK,QAAQ,CAAC;AAC1Q,+BAA2B,KAAK,qBAAqB,CAAC,EAAE,UAAU,UAAQ;AACxE,WAAK,4BAA4B,IAAI;AACrC,UAAI,KAAK,sBAAsB;AAE7B,aAAK,qBAAqB,oBAAoB,KAAK,IAAI;AAAA,MACzD,OAAO;AACL,aAAK,iBAAiB,oBAAoB,KAAK,IAAI;AAAA,MACrD;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,4BAA4B,mBAAmB;AAClE,aAAO,KAAK,qBAAqB,sBAAwB,SAAS,sBAAqB,EAAE,GAAM,SAAS,aAAa,GAAM,SAAS,8BAA8B,CAAC;AAAA,IACrK;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,qBAAoB;AAAA,IAC/B,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,8BAA8B;AAAA,IACvC,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,yBAAN,MAAM,wBAAuB;AAAA;AAAA,EAE3B,cAAc,GAAG;AACf,QAAI,KAAK,aAAa;AACpB,QAAE,eAAe;AACjB,QAAE,gBAAgB;AAAA,IACpB,OAAO;AACL,WAAK,iBAAiB,0BAA0B,IAAI;AACpD,UAAI,KAAK,qBAAqB;AAE5B,aAAK,oBAAoB,qBAAqB,IAAI;AAAA,MACpD,OAAO;AAEL,aAAK,iBAAiB,qBAAqB,IAAI;AAAA,MACjD;AAAA,IACF;AAAA,EACF;AAAA,EACA,iBAAiB,OAAO;AACtB,SAAK,cAAc;AACnB,SAAK,UAAU,KAAK,KAAK;AAAA,EAC3B;AAAA,EACA,qBAAqB;AACnB,QAAI,CAAC,KAAK,oBAAoB,CAAC,KAAK,4BAA4B,CAAC,KAAK,UAAU,CAAC,KAAK,OAAO,aAAa,CAAC,KAAK,gBAAgB;AAC9H;AAAA,IACF;AACA,YAAQ,QAAQ,EAAE,KAAK,MAAM;AAC3B,YAAM,iBAAiB,KAAK,eAAe;AAC3C,UAAI,KAAK,gBAAgB,gBAAgB;AACvC,aAAK,cAAc;AACnB,aAAK,iBAAiB,KAAK,WAAW;AACtC,aAAK,IAAI,aAAa;AAAA,MACxB;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,iBAAiB;AACf,UAAM,kBAAkB,KAAK,aAAa,KAAK,MAAM;AACrD,WAAO,KAAK,cAAc,gBAAgB,KAAK,UAAU,KAAK,KAAK,sBAAsB,gBAAgB,KAAK,kBAAkB,KAAK,KAAK,iBAAiB,KAAK,eAAe,KAAK,KAAK,yBAAyB,KAAK,eAAe;AAAA,EACxO;AAAA,EACA,aAAa,QAAQ;AACnB,WAAO,UAAQ,OAAO,SAAS,KAAK,WAAW,IAAI,KAAK,mBAAmB;AAAA,MACzE,OAAO,KAAK,sBAAsB,UAAU;AAAA,MAC5C,aAAa,KAAK,sBAAsB,UAAU;AAAA,MAClD,UAAU;AAAA,MACV,cAAc;AAAA,IAChB,CAAC;AAAA,EACH;AAAA,EACA,YAAY,kBAAkB,KAAK,qBAAqB,wBAAwB,gBAAgB,YAAY,oBAAoB,QAAQ;AACtI,SAAK,mBAAmB;AACxB,SAAK,MAAM;AACX,SAAK,sBAAsB;AAC3B,SAAK,yBAAyB;AAC9B,SAAK,iBAAiB;AACtB,SAAK,aAAa;AAClB,SAAK,qBAAqB;AAC1B,SAAK,SAAS;AACd,SAAK,WAAW,IAAI,QAAQ;AAC5B,SAAK,QAAQ,KAAK,sBAAsB,KAAK,oBAAoB,QAAQ,IAAI;AAC7E,SAAK,YAAY,IAAI,QAAQ;AAC7B,SAAK,oBAAoB;AACzB,SAAK,MAAM;AACX,SAAK,cAAc;AACnB,SAAK,cAAc;AACnB,SAAK,YAAY;AACjB,SAAK,sBAAsB;AAC3B,SAAK,iBAAiB;AACtB,QAAI,QAAQ;AACV,WAAK,OAAO,OAAO,KAAK,UAAU,KAAK,QAAQ,GAAG,OAAO,OAAK,aAAa,aAAa,CAAC,EAAE,UAAU,MAAM;AACzG,aAAK,mBAAmB;AAAA,MAC1B,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,WAAW;AAET,kBAAc,CAAC,KAAK,iBAAiB,OAAO,KAAK,iBAAiB,aAAa,CAAC,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,CAAC,CAAC,MAAM,YAAY,MAAM;AACnJ,WAAK,oBAAoB,SAAS,WAAW,KAAK,QAAQ,eAAe;AAAA,IAC3E,CAAC;AACD,SAAK,MAAM,KAAK,eAAe;AAC/B,SAAK,eAAe,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAChF,WAAK,MAAM;AAAA,IACb,CAAC;AAAA,EACH;AAAA,EACA,qBAAqB;AACnB,SAAK,iBAAiB,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM,KAAK,mBAAmB,CAAC;AACtG,SAAK,yBAAyB,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM,KAAK,mBAAmB,CAAC;AAC9G,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,QAAQ,aAAa;AACvB,WAAK,iBAAiB,KAAK,WAAW;AAAA,IACxC;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,+BAA+B,mBAAmB;AACrE,aAAO,KAAK,qBAAqB,yBAA2B,kBAAkB,aAAa,GAAM,kBAAqB,iBAAiB,GAAM,kBAAkB,qBAAqB,CAAC,GAAM,kBAAkB,8BAA8B,GAAM,kBAAqB,gBAAgB,CAAC,GAAM,kBAAqB,YAAY,CAAC,GAAM,kBAAqB,YAAoB,CAAC,GAAM,kBAAqB,QAAQ,CAAC,CAAC;AAAA,IACtZ;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,mBAAmB,EAAE,CAAC;AAAA,MACvC,gBAAgB,SAAS,sCAAsC,IAAI,KAAK,UAAU;AAChF,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,UAAU,YAAY,CAAC;AACzC,UAAG,eAAe,UAAU,YAAoB,CAAC;AAAA,QACnD;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,mBAAmB;AACpE,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,2BAA2B;AAAA,QAC9E;AAAA,MACF;AAAA,MACA,UAAU;AAAA,MACV,cAAc,SAAS,oCAAoC,IAAI,KAAK;AAClE,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,SAAS,SAAS,gDAAgD,QAAQ;AACtF,mBAAO,IAAI,cAAc,MAAM;AAAA,UACjC,CAAC;AAAA,QACH;AACA,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,gBAAgB,IAAI,QAAQ,QAAQ,OAAO,IAAI,kBAAkB,IAAI,mBAAmB,IAAI,EAAE,iBAAiB,IAAI,QAAQ,QAAQ,IAAI,kBAAkB,IAAI,oBAAoB,MAAM,IAAI;AAC1M,UAAG,YAAY,4BAA4B,IAAI,sBAAsB,EAAE,qCAAqC,IAAI,0BAA0B,IAAI,WAAW,EAAE,mCAAmC,IAAI,0BAA0B,IAAI,SAAS,EAAE,qCAAqC,IAAI,0BAA0B,IAAI,WAAW,EAAE,mBAAmB,CAAC,IAAI,sBAAsB,EAAE,4BAA4B,CAAC,IAAI,0BAA0B,IAAI,WAAW,EAAE,0BAA0B,CAAC,IAAI,0BAA0B,IAAI,SAAS,EAAE,4BAA4B,CAAC,IAAI,0BAA0B,IAAI,WAAW;AAAA,QAC/kB;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,gBAAgB;AAAA,QAChB,aAAa;AAAA,QACb,aAAa;AAAA,QACb,WAAW;AAAA,QACX,qBAAqB;AAAA,QACrB,gBAAgB;AAAA,QAChB,iBAAiB;AAAA,MACnB;AAAA,MACA,UAAU,CAAC,eAAe;AAAA,MAC1B,YAAY;AAAA,MACZ,UAAU,CAAI,oBAAoB;AAAA,IACpC,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,aAAa,CAAC,GAAG,uBAAuB,WAAW,eAAe,MAAM;AACpF,WAAW,CAAC,aAAa,CAAC,GAAG,uBAAuB,WAAW,eAAe,MAAM;AACpF,WAAW,CAAC,aAAa,CAAC,GAAG,uBAAuB,WAAW,aAAa,MAAM;AAClF,WAAW,CAAC,aAAa,CAAC,GAAG,uBAAuB,WAAW,uBAAuB,MAAM;AAC5F,WAAW,CAAC,aAAa,CAAC,GAAG,uBAAuB,WAAW,kBAAkB,MAAM;AACvF,WAAW,CAAC,aAAa,CAAC,GAAG,uBAAuB,WAAW,mBAAmB,MAAM;AAAA,CACvF,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,oCAAoC;AAAA,QACpC,6CAA6C;AAAA,QAC7C,2CAA2C;AAAA,QAC3C,6CAA6C;AAAA,QAC7C,2BAA2B;AAAA,QAC3B,oCAAoC;AAAA,QACpC,kCAAkC;AAAA,QAClC,oCAAoC;AAAA,QACpC,0BAA0B;AAAA,QAC1B,2BAA2B;AAAA,QAC3B,WAAW;AAAA,MACb;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,8BAA8B;AAAA,IACvC,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,QACjB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,0BAA0B,CAAC;AAAA,MACzB,MAAM;AAAA,MACN,MAAM,CAAC,YAAoB;AAAA,QACzB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,6BAAN,MAAM,4BAA2B;AAAA,EAC/B,YAAY,KAAK,gBAAgB;AAC/B,SAAK,MAAM;AACX,SAAK,iBAAiB;AACtB,SAAK,UAAU;AACf,SAAK,cAAc;AACnB,SAAK,WAAW;AAChB,SAAK,yBAAyB;AAC9B,SAAK,cAAc;AACnB,SAAK,cAAc;AACnB,SAAK,OAAO;AACZ,SAAK,gBAAgB,IAAI,aAAa;AACtC,SAAK,oBAAoB,IAAI,aAAa;AAC1C,SAAK,MAAM;AACX,SAAK,WAAW,IAAI,QAAQ;AAAA,EAC9B;AAAA,EACA,WAAW;AACT,SAAK,MAAM,KAAK,eAAe;AAC/B,SAAK,eAAe,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAChF,WAAK,MAAM;AACX,WAAK,IAAI,cAAc;AAAA,IACzB,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,cAAc,OAAO;AACnB,QAAI,CAAC,KAAK,aAAa;AACrB,WAAK,kBAAkB,KAAK,KAAK;AAAA,IACnC;AAAA,EACF;AAAA,EACA,aAAa;AACX,QAAI,KAAK,SAAS,YAAY,CAAC,KAAK,aAAa;AAC/C,WAAK,cAAc,KAAK;AAAA,IAC1B;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,mCAAmC,mBAAmB;AACzE,aAAO,KAAK,qBAAqB,6BAA+B,kBAAqB,iBAAiB,GAAM,kBAAqB,gBAAgB,CAAC,CAAC;AAAA,IACrJ;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,uBAAuB,EAAE,CAAC;AAAA,MAC3C,UAAU;AAAA,MACV,cAAc,SAAS,wCAAwC,IAAI,KAAK;AACtE,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,SAAS,SAAS,sDAAsD;AACpF,mBAAO,IAAI,WAAW;AAAA,UACxB,CAAC,EAAE,cAAc,SAAS,2DAA2D;AACnF,mBAAO,IAAI,cAAc,IAAI;AAAA,UAC/B,CAAC,EAAE,cAAc,SAAS,2DAA2D;AACnF,mBAAO,IAAI,cAAc,KAAK;AAAA,UAChC,CAAC;AAAA,QACH;AACA,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,gBAAgB,IAAI,QAAQ,QAAQ,OAAO,IAAI,aAAa,IAAI,EAAE,iBAAiB,IAAI,QAAQ,QAAQ,IAAI,cAAc,MAAM,IAAI;AAClJ,UAAG,YAAY,uCAAuC,IAAI,sBAAsB,EAAE,8BAA8B,CAAC,IAAI,sBAAsB;AAAA,QAC7I;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,UAAU;AAAA,QACV,wBAAwB;AAAA,QACxB,aAAa;AAAA,QACb,aAAa;AAAA,QACb,MAAM;AAAA,MACR;AAAA,MACA,SAAS;AAAA,QACP,eAAe;AAAA,QACf,mBAAmB;AAAA,MACrB;AAAA,MACA,UAAU,CAAC,mBAAmB;AAAA,MAC9B,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,MACjC,OAAOA;AAAA,MACP,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,4BAA4B,GAAG,OAAO,GAAG,CAAC,GAAG,iCAAiC,GAAG,WAAW,GAAG,CAAC,GAAG,yBAAyB,GAAG,CAAC,GAAG,2CAA2C,GAAG,CAAC,GAAG,8BAA8B,yBAAyB,GAAG,CAAC,GAAG,0BAA0B,GAAG,CAAC,GAAG,0BAA0B,CAAC;AAAA,MAC5T,UAAU,SAAS,oCAAoC,IAAI,KAAK;AAC9D,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,WAAW,GAAG,mDAAmD,GAAG,GAAG,QAAQ,CAAC,EAAE,GAAG,mDAAmD,GAAG,GAAG,QAAQ,CAAC,EAAE,GAAG,oDAAoD,GAAG,GAAG,gBAAgB,CAAC;AAC1O,UAAG,aAAa,CAAC;AACjB,UAAG,WAAW,GAAG,mDAAmD,GAAG,GAAG,QAAQ,CAAC,EAAE,GAAG,mDAAmD,GAAG,GAAG,QAAQ,CAAC;AAAA,QAC5J;AACA,YAAI,KAAK,GAAG;AACV,UAAG,cAAc,IAAI,UAAU,IAAI,EAAE;AACrC,UAAG,UAAU;AACb,UAAG,cAAc,CAAC,IAAI,WAAW,IAAI,cAAc,IAAI,EAAE;AACzD,UAAG,UAAU;AACb,UAAG,WAAW,2BAA2B,IAAI,QAAQ;AACrD,UAAG,UAAU,CAAC;AACd,UAAG,cAAc,IAAI,yBAAyB,IAAI,CAAC;AAAA,QACrD;AAAA,MACF;AAAA,MACA,cAAc,CAAC,iBAAoB,gCAAgC;AAAA,MACnE,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,4BAA4B,CAAC;AAAA,IACnG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAwBV,MAAM;AAAA,QACJ,+CAA+C;AAAA,QAC/C,sCAAsC;AAAA,QACtC,0BAA0B;AAAA,QAC1B,2BAA2B;AAAA,QAC3B,WAAW;AAAA,QACX,gBAAgB;AAAA,QAChB,gBAAgB;AAAA,MAClB;AAAA,MACA,YAAY;AAAA,MACZ,SAAS,CAAC,eAAe;AAAA,IAC3B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,wBAAwB,CAAC;AAAA,MACvB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,mCAAN,MAAM,kCAAiC;AAAA,EACrC,YAAY,YAAY,UAAU,gBAAgB;AAChD,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,iBAAiB;AACtB,SAAK,iBAAiB;AACtB,SAAK,cAAc;AACnB,SAAK,OAAO;AACZ,SAAK,UAAU;AACf,SAAK,uBAAuB,CAAC;AAC7B,SAAK,cAAc;AACnB,SAAK,MAAM;AACX,SAAK,WAAW,IAAI,QAAQ;AAAA,EAC9B;AAAA,EACA,kBAAkB;AAChB,QAAI,KAAK,SAAS;AAChB,WAAK,cAAc;AAAA,IACrB,OAAO;AACL,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA,EACA,WAAW;AACT,SAAK,gBAAgB;AACrB,SAAK,MAAM,KAAK,eAAe;AAC/B,SAAK,eAAe,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAChF,WAAK,MAAM;AAAA,IACb,CAAC;AAAA,EACH;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,QAAQ,SAAS;AACnB,WAAK,gBAAgB;AAAA,IACvB;AACA,QAAI,aAAa;AACf,UAAI,KAAK,qBAAqB,QAAQ;AACpC,aAAK,qBAAqB,OAAO,UAAQ,CAAC,CAAC,IAAI,EAAE,QAAQ,eAAa;AACpE,eAAK,SAAS,YAAY,KAAK,WAAW,eAAe,SAAS;AAAA,QACpE,CAAC;AAAA,MACH;AACA,UAAI,KAAK,aAAa;AACpB,aAAK,uBAAuB,KAAK,YAAY,MAAM,GAAG;AACtD,aAAK,qBAAqB,OAAO,UAAQ,CAAC,CAAC,IAAI,EAAE,QAAQ,eAAa;AACpE,eAAK,SAAS,SAAS,KAAK,WAAW,eAAe,SAAS;AAAA,QACjE,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yCAAyC,mBAAmB;AAC/E,aAAO,KAAK,qBAAqB,mCAAqC,kBAAqB,UAAU,GAAM,kBAAqB,SAAS,GAAM,kBAAqB,gBAAgB,CAAC,CAAC;AAAA,IACxL;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,8BAA8B,EAAE,CAAC;AAAA,MAClD,WAAW,CAAC,GAAG,cAAc,qBAAqB,gBAAgB;AAAA,MAClE,UAAU;AAAA,MACV,cAAc,SAAS,8CAA8C,IAAI,KAAK;AAC5E,YAAI,KAAK,GAAG;AACV,UAAG,wBAAwB,mBAAmB,IAAI,WAAW;AAC7D,UAAG,YAAY,kBAAkB,IAAI,QAAQ,KAAK;AAAA,QACpD;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,gBAAgB;AAAA,QAChB,aAAa;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,MACX;AAAA,MACA,UAAU,CAAC,yBAAyB;AAAA,MACpC,YAAY;AAAA,MACZ,UAAU,CAAI,sBAAyB,mBAAmB;AAAA,MAC1D,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,kBAAkB,CAAC;AAAA,MAChC,UAAU,SAAS,0CAA0C,IAAI,KAAK;AACpE,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,GAAG,yDAAyD,GAAG,GAAG,eAAe,CAAC;AAAA,QAClG;AACA,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,oBAAoB,IAAI,cAAc;AAAA,QACtD;AAAA,MACF;AAAA,MACA,cAAc,CAAC,gBAAgB;AAAA,MAC/B,eAAe;AAAA,MACf,MAAM;AAAA,QACJ,WAAW,CAAC,cAAc;AAAA,MAC5B;AAAA,MACA,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kCAAkC,CAAC;AAAA,IACzG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY,CAAC,cAAc;AAAA,MAC3B,UAAU;AAAA,MACV,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,qBAAqB;AAAA,MACvB;AAAA,MACA,YAAY;AAAA,MACZ,SAAS,CAAC,gBAAgB;AAAA,IAC5B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,uCAAN,MAAM,sCAAqC;AAAA,EACzC,YAAY,gBAAgB;AAC1B,SAAK,iBAAiB;AACtB,SAAK,cAAc;AACnB,SAAK,QAAQ;AACb,SAAK,iBAAiB;AACtB,SAAK,yBAAyB;AAC9B,SAAK,OAAO;AACZ,SAAK,WAAW;AAChB,SAAK,cAAc;AACnB,SAAK,UAAU;AACf,SAAK,oBAAoB,IAAI,aAAa;AAC1C,SAAK,cAAc;AACnB,SAAK,MAAM;AACX,SAAK,WAAW,IAAI,QAAQ;AAAA,EAC9B;AAAA,EACA,cAAc,OAAO;AACnB,QAAI,CAAC,KAAK,aAAa;AACrB,WAAK,kBAAkB,KAAK,KAAK;AAAA,IACnC;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,kBAAkB;AAChB,QAAI,KAAK,SAAS;AAChB,UAAI,KAAK,SAAS,cAAc;AAC9B,aAAK,cAAc;AAAA,MACrB,WAAW,KAAK,SAAS,YAAY;AACnC,aAAK,cAAc;AAAA,MACrB;AAAA,IACF,OAAO;AACL,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA,EACA,WAAW;AACT,SAAK,gBAAgB;AACrB,SAAK,MAAM,KAAK,eAAe;AAC/B,SAAK,eAAe,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAChF,WAAK,MAAM;AAAA,IACb,CAAC;AAAA,EACH;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,QAAQ,SAAS;AACnB,WAAK,gBAAgB;AAAA,IACvB;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,6CAA6C,mBAAmB;AACnF,aAAO,KAAK,qBAAqB,uCAAyC,kBAAqB,gBAAgB,CAAC,CAAC;AAAA,IACnH;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,mCAAmC,EAAE,CAAC;AAAA,MACvD,WAAW,CAAC,GAAG,wBAAwB,4BAA4B;AAAA,MACnE,UAAU;AAAA,MACV,cAAc,SAAS,kDAAkD,IAAI,KAAK;AAChF,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,cAAc,SAAS,qEAAqE;AACxG,mBAAO,IAAI,cAAc,IAAI;AAAA,UAC/B,CAAC,EAAE,cAAc,SAAS,qEAAqE;AAC7F,mBAAO,IAAI,cAAc,KAAK;AAAA,UAChC,CAAC;AAAA,QACH;AACA,YAAI,KAAK,GAAG;AACV,UAAG,wBAAwB,gBAAgB,IAAI,WAAW,EAAE,kBAAkB,IAAI,WAAW;AAC7F,UAAG,YAAY,oBAAoB,IAAI,UAAU,OAAO,EAAE,mBAAmB,IAAI,UAAU,MAAM,EAAE,yCAAyC,IAAI,SAAS,YAAY,EAAE,wCAAwC,IAAI,SAAS,cAAc,IAAI,aAAa,OAAO,EAAE,uCAAuC,IAAI,SAAS,cAAc,IAAI,aAAa,MAAM,EAAE,4BAA4B,IAAI,QAAQ,KAAK;AAAA,QAC9Y;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,aAAa;AAAA,QACb,OAAO;AAAA,QACP,gBAAgB;AAAA,QAChB,wBAAwB;AAAA,QACxB,MAAM;AAAA,QACN,UAAU;AAAA,QACV,aAAa;AAAA,QACb,SAAS;AAAA,MACX;AAAA,MACA,SAAS;AAAA,QACP,mBAAmB;AAAA,MACrB;AAAA,MACA,UAAU,CAAC,6BAA6B;AAAA,MACxC,YAAY;AAAA,MACZ,UAAU,CAAI,sBAAyB,mBAAmB;AAAA,MAC1D,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,SAAS,GAAG,CAAC,GAAG,kBAAkB,CAAC;AAAA,MAChD,UAAU,SAAS,8CAA8C,IAAI,KAAK;AACxE,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,UAAG,WAAW,GAAG,6DAA6D,GAAG,GAAG,eAAe,CAAC;AACpG,UAAG,aAAa;AAAA,QAClB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,uBAAuB,IAAI,sBAAsB,EAAE,cAAc,CAAC,IAAI,sBAAsB,EAAE,gCAAgC,IAAI,sBAAsB,EAAE,uBAAuB,CAAC,IAAI,sBAAsB,EAAE,2BAA2B,IAAI,sBAAsB,EAAE,kBAAkB,CAAC,IAAI,sBAAsB,EAAE,kBAAkB,IAAI,QAAQ,KAAK;AACtW,UAAG,WAAW,WAAW,IAAI,WAAW;AACxC,UAAG,UAAU;AACb,UAAG,WAAW,oBAAoB,IAAI,cAAc;AAAA,QACtD;AAAA,MACF;AAAA,MACA,cAAc,CAAC,SAAS,gBAAgB;AAAA,MACxC,eAAe;AAAA,MACf,MAAM;AAAA,QACJ,WAAW,CAAC,eAAe,WAAW;AAAA,MACxC;AAAA,MACA,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sCAAsC,CAAC;AAAA,IAC7G,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,eAAe,oBAAkB;AAAA,MACjC,YAAY,CAAC,eAAe,WAAW;AAAA,MACvC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAcV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,4BAA4B;AAAA,QAC5B,2BAA2B;AAAA,QAC3B,iDAAiD;AAAA,QACjD,gDAAgD;AAAA,QAChD,+CAA+C;AAAA,QAC/C,oCAAoC;AAAA,QACpC,kBAAkB;AAAA,QAClB,oBAAoB;AAAA,QACpB,gBAAgB;AAAA,QAChB,gBAAgB;AAAA,MAClB;AAAA,MACA,YAAY;AAAA,MACZ,SAAS,CAAC,SAAS,gBAAgB;AAAA,IACrC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,wBAAwB,CAAC;AAAA,MACvB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,0BAA0B,CAAC,aAAa,UAAU,aAAa,OAAO,aAAa,aAAa,aAAa,SAAS,aAAa,MAAM,aAAa,UAAU;AACtK,IAAM,4BAA4B,CAAC,aAAa,YAAY,aAAa,aAAa,aAAa,UAAU,aAAa,OAAO;AACjI,IAAM,wBAAN,MAAM,uBAAsB;AAAA;AAAA,EAE1B,4BAA4B,MAAM;AAChC,SAAK,oBAAoB,4BAA4B,IAAI;AAAA,EAC3D;AAAA,EACA,gBAAgB;AACd,SAAK,4BAA4B,CAAC,KAAK,OAAO;AAAA,EAChD;AAAA,EACA,mBAAmB,OAAO;AACxB,SAAK,WAAW;AAChB,QAAI,KAAK,SAAS,UAAU;AAC1B,WAAK,oBAAoB,iCAAiC,KAAK;AAAA,IACjE;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,QAAI,KAAK,SAAS,gBAAgB,KAAK,SAAS,aAAa,KAAK,oBAAoB,KAAK,iBAAiB,cAAc;AAExH,WAAK,eAAe,KAAK,iBAAiB,cAAc,sBAAsB,EAAE;AAAA,IAClF;AAAA,EACF;AAAA,EACA,iBAAiB,UAAU;AACzB,UAAM,YAAY,iBAAiB,QAAQ;AAC3C,QAAI,cAAc,cAAc,cAAc,iBAAiB,cAAc,SAAS;AACpF,WAAK,WAAW;AAAA,IAClB,WAAW,cAAc,aAAa,cAAc,gBAAgB,cAAc,QAAQ;AACxF,WAAK,WAAW;AAAA,IAClB;AAAA,EACF;AAAA,EACA,YAAY,kBAAkB,KAAK,qBAAqB,UAAU,wBAAwB,gBAAgB,aAAa;AACrH,SAAK,mBAAmB;AACxB,SAAK,MAAM;AACX,SAAK,sBAAsB;AAC3B,SAAK,WAAW;AAChB,SAAK,yBAAyB;AAC9B,SAAK,iBAAiB;AACtB,SAAK,cAAc;AACnB,SAAK,qBAAqB;AAC1B,SAAK,iBAAiB;AACtB,SAAK,WAAW;AAChB,SAAK,UAAU;AACf,SAAK,cAAc;AACnB,SAAK,UAAU;AACf,SAAK,cAAc;AACnB,SAAK,eAAe;AACpB,SAAK,gBAAgB,IAAI,aAAa;AACtC,SAAK,mBAAmB;AAGxB,SAAK,8BAA8B;AACnC,SAAK,QAAQ,KAAK,oBAAoB;AACtC,SAAK,WAAW,IAAI,QAAQ;AAC5B,SAAK,WAAW;AAChB,SAAK,eAAe;AACpB,SAAK,QAAQ;AACb,SAAK,OAAO;AACZ,SAAK,oBAAoB;AACzB,SAAK,mBAAmB;AACxB,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,MAAM;AAAA,EACb;AAAA,EACA,WAAW;AAET,SAAK,iBAAiB,OAAO,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,WAAS;AAC7E,WAAK,QAAQ;AACb,WAAK,IAAI,aAAa;AAAA,IACxB,CAAC;AAED,SAAK,oBAAoB,MAAM,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,UAAQ;AAC9E,WAAK,OAAO;AACZ,UAAI,SAAS,cAAc;AACzB,aAAK,mBAAmB,CAAC,aAAa,KAAK,YAAY,GAAG,GAAG,yBAAyB;AAAA,MACxF,WAAW,SAAS,YAAY;AAC9B,aAAK,mBAAmB;AAAA,MAC1B;AACA,WAAK,IAAI,aAAa;AAAA,IACxB,CAAC;AAED,kBAAc,CAAC,KAAK,oBAAoB,OAAO,KAAK,iBAAiB,aAAa,CAAC,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,CAAC,CAAC,MAAM,YAAY,MAAM;AACtJ,WAAK,oBAAoB,SAAS,WAAW,KAAK,QAAQ,eAAe;AACzE,WAAK,IAAI,aAAa;AAAA,IACxB,CAAC;AAED,SAAK,oBAAoB,sBAAsB,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,UAAQ;AAC9F,WAAK,WAAW;AAChB,UAAI,SAAS,KAAK,SAAS;AACzB,aAAK,gBAAgB;AACrB,aAAK,UAAU;AACf,aAAK,cAAc,KAAK,KAAK,OAAO;AACpC,aAAK,IAAI,aAAa;AAAA,MACxB;AAAA,IACF,CAAC;AACD,SAAK,MAAM,KAAK,eAAe;AAC/B,SAAK,eAAe,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAChF,WAAK,MAAM;AACX,WAAK,IAAI,aAAa;AAAA,IACxB,CAAC;AAAA,EACH;AAAA,EACA,qBAAqB;AACnB,SAAK,gBAAgB;AACrB,UAAM,+BAA+B,KAAK;AAC1C,UAAM,UAAU,6BAA6B;AAC7C,UAAM,mBAAmB,MAAM,GAAG,CAAC,SAAS,GAAG,6BAA6B,IAAI,YAAU,OAAO,SAAS,CAAC,CAAC;AAC5G,YAAQ,KAAK,UAAU,4BAA4B,GAAG,UAAU,MAAM,gBAAgB,GAAG,UAAU,IAAI,GAAG,IAAI,MAAM,6BAA6B,KAAK,OAAK,EAAE,WAAW,CAAC,GAAG,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,cAAY;AAC1N,WAAK,aAAa;AAClB,WAAK,IAAI,aAAa;AAAA,IACxB,CAAC;AAAA,EACH;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,SAAS;AACX,WAAK,oBAAoB,4BAA4B,KAAK,OAAO;AACjE,WAAK,gBAAgB;AAAA,IACvB;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,8BAA8B,mBAAmB;AACpE,aAAO,KAAK,qBAAqB,wBAA0B,kBAAkB,aAAa,GAAM,kBAAqB,iBAAiB,GAAM,kBAAkB,mBAAmB,GAAM,kBAAqB,QAAQ,GAAM,kBAAkB,8BAA8B,GAAM,kBAAqB,gBAAgB,CAAC,GAAM,kBAAqB,yBAAyB,CAAC,CAAC;AAAA,IAC9W;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,iBAAiB,EAAE,CAAC;AAAA,MACrC,gBAAgB,SAAS,qCAAqC,IAAI,KAAK,UAAU;AAC/E,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,UAAU,wBAAuB,CAAC;AACpD,UAAG,eAAe,UAAU,wBAAwB,CAAC;AAAA,QACvD;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,8BAA8B;AAC/E,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,+BAA+B;AAAA,QAClF;AAAA,MACF;AAAA,MACA,WAAW,SAAS,4BAA4B,IAAI,KAAK;AACvD,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,kBAAkB,GAAG,UAAU;AAAA,QAChD;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,mBAAmB,GAAG;AAAA,QACzE;AAAA,MACF;AAAA,MACA,UAAU;AAAA,MACV,cAAc,SAAS,mCAAmC,IAAI,KAAK;AACjE,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,iCAAiC,IAAI,sBAAsB,EAAE,0CAA0C,IAAI,0BAA0B,IAAI,WAAW,EAAE,sCAAsC,IAAI,0BAA0B,IAAI,OAAO,EAAE,0CAA0C,IAAI,0BAA0B,IAAI,UAAU,EAAE,0CAA0C,IAAI,0BAA0B,IAAI,SAAS,UAAU,EAAE,4CAA4C,IAAI,0BAA0B,IAAI,SAAS,YAAY,EAAE,wCAAwC,IAAI,0BAA0B,IAAI,SAAS,QAAQ,EAAE,wCAAwC,IAAI,0BAA0B,IAAI,QAAQ,EAAE,wBAAwB,CAAC,IAAI,sBAAsB,EAAE,iCAAiC,CAAC,IAAI,0BAA0B,IAAI,WAAW,EAAE,6BAA6B,CAAC,IAAI,0BAA0B,IAAI,OAAO,EAAE,iCAAiC,CAAC,IAAI,0BAA0B,IAAI,UAAU,EAAE,iCAAiC,CAAC,IAAI,0BAA0B,IAAI,SAAS,UAAU,EAAE,mCAAmC,CAAC,IAAI,0BAA0B,IAAI,SAAS,YAAY,EAAE,+BAA+B,CAAC,IAAI,0BAA0B,IAAI,SAAS,QAAQ,EAAE,+BAA+B,CAAC,IAAI,0BAA0B,IAAI,QAAQ,EAAE,4BAA4B,IAAI,QAAQ,KAAK;AAAA,QAC12C;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,oBAAoB;AAAA,QACpB,gBAAgB;AAAA,QAChB,UAAU;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,QACb,SAAS;AAAA,QACT,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,MACA,SAAS;AAAA,QACP,eAAe;AAAA,MACjB;AAAA,MACA,UAAU,CAAC,cAAc;AAAA,MACzB,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC,mBAAmB,CAAC,GAAM,sBAAyB,mBAAmB;AAAA,MACxG,OAAO;AAAA,MACP,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,UAAU,kBAAkB,GAAG,CAAC,mBAAmB,EAAE,GAAG,CAAC,uBAAuB,IAAI,oBAAoB,IAAI,GAAG,qBAAqB,iBAAiB,WAAW,eAAe,YAAY,QAAQ,eAAe,0BAA0B,aAAa,GAAG,CAAC,8BAA8B,IAAI,GAAG,QAAQ,WAAW,eAAe,gBAAgB,GAAG,CAAC,uBAAuB,IAAI,GAAG,gCAAgC,6BAA6B,4BAA4B,2BAA2B,sCAAsC,GAAG,CAAC,uBAAuB,IAAI,GAAG,kBAAkB,gCAAgC,6BAA6B,4BAA4B,2BAA2B,sCAAsC,GAAG,CAAC,mCAAmC,IAAI,GAAG,qBAAqB,SAAS,QAAQ,WAAW,YAAY,eAAe,0BAA0B,kBAAkB,aAAa,CAAC;AAAA,MAC55B,UAAU,SAAS,+BAA+B,IAAI,KAAK;AACzD,YAAI,KAAK,GAAG;AACV,gBAAM,MAAS,iBAAiB;AAChC,UAAG,gBAAgB,GAAG;AACtB,UAAG,eAAe,GAAG,OAAO,GAAG,CAAC;AAChC,UAAG,WAAW,qBAAqB,SAAS,gEAAgE,QAAQ;AAClH,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,mBAAmB,MAAM,CAAC;AAAA,UACtD,CAAC,EAAE,iBAAiB,SAAS,8DAA8D;AACzF,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,cAAc,CAAC;AAAA,UAC3C,CAAC;AACD,UAAG,WAAW,GAAG,8CAA8C,GAAG,CAAC;AACnE,UAAG,aAAa;AAChB,UAAG,WAAW,GAAG,8CAA8C,GAAG,GAAG,OAAO,CAAC,EAAE,GAAG,8CAA8C,GAAG,GAAG,MAAM,CAAC,EAAE,GAAG,8CAA8C,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AAAA,QACzP;AACA,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,WAAW,IAAI,OAAO,EAAE,eAAe,IAAI,WAAW,EAAE,YAAY,IAAI,QAAQ,EAAE,QAAQ,IAAI,IAAI,EAAE,eAAe,IAAI,WAAW,EAAE,0BAA0B,IAAI,sBAAsB,EAAE,eAAe,IAAI,kBAAkB,IAAI,iBAAiB;AAClQ,UAAG,UAAU,CAAC;AACd,UAAG,cAAc,CAAC,IAAI,WAAW,IAAI,EAAE;AACvC,UAAG,UAAU;AACb,UAAG,cAAc,IAAI,SAAS,WAAW,IAAI,CAAC;AAAA,QAChD;AAAA,MACF;AAAA,MACA,cAAc,CAAC,eAAkB,qBAAwB,kBAAkB,4BAA4B,kCAAkC,oCAAoC;AAAA,MAC7K,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,aAAa,CAAC,GAAG,sBAAsB,WAAW,WAAW,MAAM;AAC/E,WAAW,CAAC,aAAa,CAAC,GAAG,sBAAsB,WAAW,eAAe,MAAM;AAAA,CAClF,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,WAAW,CAAC,mBAAmB;AAAA,MAC/B,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,qBAAqB;AAAA,MACrB,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAwDV,MAAM;AAAA,QACJ,yCAAyC;AAAA,QACzC,kDAAkD;AAAA,QAClD,8CAA8C;AAAA,QAC9C,kDAAkD;AAAA,QAClD,kDAAkD;AAAA,QAClD,oDAAoD;AAAA,QACpD,gDAAgD;AAAA,QAChD,gDAAgD;AAAA,QAChD,gCAAgC;AAAA,QAChC,yCAAyC;AAAA,QACzC,qCAAqC;AAAA,QACrC,yCAAyC;AAAA,QACzC,yCAAyC;AAAA,QACzC,2CAA2C;AAAA,QAC3C,uCAAuC;AAAA,QACvC,uCAAuC;AAAA,QACvC,oCAAoC;AAAA,MACtC;AAAA,MACA,YAAY;AAAA,MACZ,SAAS,CAAC,eAAe,4BAA4B,kCAAkC,oCAAoC;AAAA,IAC7H,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,8BAA8B;AAAA,IACvC,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,IACD,6BAA6B,CAAC;AAAA,MAC5B,MAAM;AAAA,MACN,MAAM,CAAC,WAAW,MAAM,qBAAqB,GAAG;AAAA,QAC9C,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,8BAA8B,CAAC;AAAA,MAC7B,MAAM;AAAA,MACN,MAAM,CAAC,wBAAwB;AAAA,QAC7B,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,SAAS,qBAAqB,uBAAuB,wBAAwB;AAC3E,SAAO,wBAAwB,wBAAwB;AACzD;AACA,SAAS,2BAA2B,2BAA2B;AAC7D,SAAO,4BAA4B,4BAA4B;AACjE;AACA,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,mBAAmB,iBAAiB;AAClC,SAAK,qBAAqB;AAC1B,SAAK,iBAAiB,KAAK,eAAe;AAAA,EAC5C;AAAA,EACA,uBAAuB;AACrB,QAAI,KAAK,8BAA8B;AACrC,UAAI,KAAK,oBAAoB;AAC3B,aAAK,oCAAoC,KAAK,4BAA4B,OAAO,aAAW,QAAQ,OAAO;AAC3G,aAAK,4BAA4B,QAAQ,aAAW,QAAQ,4BAA4B,KAAK,CAAC;AAAA,MAChG,OAAO;AACL,aAAK,kCAAkC,QAAQ,aAAW,QAAQ,4BAA4B,IAAI,CAAC;AACnG,aAAK,oCAAoC,CAAC;AAAA,MAC5C;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY,kBAAkB,wBAAwB,KAAK,gBAAgB;AACzE,SAAK,mBAAmB;AACxB,SAAK,yBAAyB;AAC9B,SAAK,MAAM;AACX,SAAK,iBAAiB;AACtB,SAAK,kBAAkB;AACvB,SAAK,WAAW;AAChB,SAAK,UAAU;AACf,SAAK,qBAAqB;AAC1B,SAAK,gBAAgB,CAAC,KAAK;AAC3B,SAAK,WAAW,IAAI,aAAa;AACjC,SAAK,aAAa;AAClB,SAAK,MAAM;AACX,SAAK,mBAAmB,IAAI,gBAAgB,KAAK,kBAAkB;AACnE,SAAK,QAAQ,IAAI,gBAAgB,KAAK,OAAO;AAC7C,SAAK,WAAW,IAAI,QAAQ;AAC5B,SAAK,oCAAoC,CAAC;AAAA,EAC5C;AAAA,EACA,WAAW;AACT,kBAAc,CAAC,KAAK,kBAAkB,KAAK,KAAK,CAAC,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,CAAC,CAAC,iBAAiB,IAAI,MAAM;AACvH,WAAK,aAAa,kBAAkB,aAAa;AACjD,WAAK,iBAAiB,QAAQ,KAAK,UAAU;AAC7C,WAAK,IAAI,aAAa;AAAA,IACxB,CAAC;AACD,SAAK,iBAAiB,yBAAyB,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,YAAU;AAChG,WAAK,SAAS,KAAK,MAAM;AACzB,UAAI,KAAK,iBAAiB,CAAC,OAAO,gBAAgB;AAChD,aAAK,6BAA6B,QAAQ,UAAQ,KAAK,iBAAiB,SAAS,MAAM,CAAC;AAAA,MAC1F;AAAA,IACF,CAAC;AACD,SAAK,MAAM,KAAK,eAAe;AAC/B,SAAK,eAAe,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAChF,WAAK,MAAM;AACX,WAAK,iBAAiB,QAAQ,KAAK,UAAU;AAC7C,WAAK,IAAI,aAAa;AAAA,IACxB,CAAC;AAAA,EACH;AAAA,EACA,qBAAqB;AACnB,SAAK,iBAAiB,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM;AACnE,WAAK,qBAAqB;AAC1B,WAAK,IAAI,aAAa;AAAA,IACxB,CAAC;AAAA,EACH;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,oBAAoB;AACtB,WAAK,iBAAiB,KAAK,KAAK,kBAAkB;AAAA,IACpD;AACA,QAAI,iBAAiB;AACnB,WAAK,iBAAiB,gBAAgB,KAAK,eAAe;AAAA,IAC5D;AACA,QAAI,UAAU;AACZ,WAAK,iBAAiB,SAAS,KAAK,QAAQ;AAAA,IAC9C;AACA,QAAI,SAAS;AACX,WAAK,MAAM,KAAK,KAAK,OAAO;AAC5B,UAAI,CAAC,QAAQ,QAAQ,cAAc,KAAK,KAAK,6BAA6B;AACxE,aAAK,4BAA4B,QAAQ,aAAW,QAAQ,4BAA4B,KAAK,CAAC;AAAA,MAChG;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,2BAA2B,mBAAmB;AACjE,aAAO,KAAK,qBAAqB,qBAAuB,kBAAkB,aAAa,GAAM,kBAAkB,8BAA8B,GAAM,kBAAqB,iBAAiB,GAAM,kBAAqB,gBAAgB,CAAC,CAAC;AAAA,IACxO;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,cAAc,EAAE,CAAC;AAAA,MAClC,gBAAgB,SAAS,kCAAkC,IAAI,KAAK,UAAU;AAC5E,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,UAAU,wBAAwB,CAAC;AACrD,UAAG,eAAe,UAAU,uBAAuB,CAAC;AAAA,QACtD;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,+BAA+B;AAChF,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,8BAA8B;AAAA,QACjF;AAAA,MACF;AAAA,MACA,UAAU;AAAA,MACV,cAAc,SAAS,gCAAgC,IAAI,KAAK;AAC9D,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,uBAAuB,IAAI,sBAAsB,EAAE,4BAA4B,IAAI,sBAAsB,EAAE,6BAA6B,IAAI,0BAA0B,IAAI,aAAa,OAAO,EAAE,4BAA4B,IAAI,0BAA0B,IAAI,aAAa,MAAM,EAAE,gCAAgC,IAAI,0BAA0B,IAAI,eAAe,UAAU,EAAE,kCAAkC,IAAI,0BAA0B,IAAI,eAAe,YAAY,EAAE,8BAA8B,IAAI,0BAA0B,IAAI,eAAe,QAAQ,EAAE,wCAAwC,IAAI,0BAA0B,IAAI,kBAAkB,EAAE,cAAc,CAAC,IAAI,sBAAsB,EAAE,mBAAmB,CAAC,IAAI,sBAAsB,EAAE,oBAAoB,CAAC,IAAI,0BAA0B,IAAI,aAAa,OAAO,EAAE,mBAAmB,CAAC,IAAI,0BAA0B,IAAI,aAAa,MAAM,EAAE,uBAAuB,CAAC,IAAI,0BAA0B,IAAI,eAAe,UAAU,EAAE,yBAAyB,CAAC,IAAI,0BAA0B,IAAI,eAAe,YAAY,EAAE,qBAAqB,CAAC,IAAI,0BAA0B,IAAI,eAAe,QAAQ,EAAE,+BAA+B,CAAC,IAAI,0BAA0B,IAAI,kBAAkB,EAAE,kBAAkB,IAAI,QAAQ,KAAK;AAAA,QACpwC;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,iBAAiB;AAAA,QACjB,UAAU;AAAA,QACV,SAAS;AAAA,QACT,oBAAoB;AAAA,QACpB,eAAe;AAAA,MACjB;AAAA,MACA,SAAS;AAAA,QACP,UAAU;AAAA,MACZ;AAAA,MACA,UAAU,CAAC,WAAW;AAAA,MACtB,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,QAAC;AAAA,UAChC,SAAS;AAAA,UACT,UAAU;AAAA,QACZ;AAAA;AAAA,QACA;AAAA,UACE,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,MAAM,CAAC,CAAC,IAAI,SAAS,GAAG,IAAI,SAAS,GAAG,aAAa,GAAG,0BAA0B;AAAA,QACpF;AAAA;AAAA,QACA;AAAA,UACE,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,MAAM,CAAC,CAAC,IAAI,SAAS,GAAG,IAAI,SAAS,GAAG,8BAA8B,CAAC;AAAA,QACzE;AAAA,MAAC,CAAC,GAAM,oBAAoB;AAAA,IAC9B,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,aAAa,CAAC,GAAG,mBAAmB,WAAW,sBAAsB,MAAM;AACvF,WAAW,CAAC,aAAa,CAAC,GAAG,mBAAmB,WAAW,iBAAiB,MAAM;AAAA,CACjF,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,WAAW;AAAA,QAAC;AAAA,UACV,SAAS;AAAA,UACT,UAAU;AAAA,QACZ;AAAA;AAAA,QACA;AAAA,UACE,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,MAAM,CAAC,CAAC,IAAI,SAAS,GAAG,IAAI,SAAS,GAAG,aAAa,GAAG,0BAA0B;AAAA,QACpF;AAAA;AAAA,QACA;AAAA,UACE,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,MAAM,CAAC,CAAC,IAAI,SAAS,GAAG,IAAI,SAAS,GAAG,8BAA8B,CAAC;AAAA,QACzE;AAAA,MAAC;AAAA,MACD,MAAM;AAAA,QACJ,+BAA+B;AAAA,QAC/B,oCAAoC;AAAA,QACpC,qCAAqC;AAAA,QACrC,oCAAoC;AAAA,QACpC,wCAAwC;AAAA,QACxC,0CAA0C;AAAA,QAC1C,sCAAsC;AAAA,QACtC,gDAAgD;AAAA,QAChD,sBAAsB;AAAA,QACtB,2BAA2B;AAAA,QAC3B,4BAA4B;AAAA,QAC5B,2BAA2B;AAAA,QAC3B,+BAA+B;AAAA,QAC/B,iCAAiC;AAAA,QACjC,6BAA6B;AAAA,QAC7B,uCAAuC;AAAA,QACvC,0BAA0B;AAAA,MAC5B;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,8BAA8B;AAAA,IACvC,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,8BAA8B,CAAC;AAAA,MAC7B,MAAM;AAAA,MACN,MAAM,CAAC,wBAAwB;AAAA,QAC7B,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,6BAA6B,CAAC;AAAA,MAC5B,MAAM;AAAA,MACN,MAAM,CAAC,uBAAuB;AAAA,QAC5B,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,SAAS,mBAAmB,2BAA2B;AACrD,SAAO,4BAA4B,4BAA4B;AACjE;AACA,IAAM,0BAAN,MAAM,yBAAwB;AAAA,EAC5B,YAAY,YAAY,UAAU,wBAAwB;AACxD,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,yBAAyB;AAC9B,UAAM,YAAY,KAAK,yBAAyB,mCAAmC;AACnF,SAAK,SAAS,SAAS,WAAW,eAAe,SAAS;AAAA,EAC5D;AAAA,EACA,kBAAkB;AAChB,UAAM,YAAY,KAAK,aAAa,cAAc;AAClD,QAAI,WAAW;AAEb,YAAM,YAAY,KAAK,yBAAyB,wCAAwC;AACxF,WAAK,SAAS,SAAS,WAAW,SAAS;AAAA,IAC7C;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,gCAAgC,mBAAmB;AACtE,aAAO,KAAK,qBAAqB,0BAA4B,kBAAqB,UAAU,GAAM,kBAAqB,SAAS,GAAM,kBAAkB,8BAA8B,CAAC;AAAA,IACzL;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,oBAAoB,EAAE,CAAC;AAAA,MACxC,WAAW,SAAS,8BAA8B,IAAI,KAAK;AACzD,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,KAAK,CAAC;AAAA,QACvB;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,eAAe,GAAG;AAAA,QACrE;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,MACZ;AAAA,MACA,UAAU,CAAC,gBAAgB;AAAA,MAC3B,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA;AAAA,QACjC;AAAA,UACE,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,MAAM,CAAC,CAAC,IAAI,SAAS,GAAG,IAAI,SAAS,GAAG,8BAA8B,CAAC;AAAA,QACzE;AAAA,MAAC,CAAC,GAAM,mBAAmB;AAAA,MAC3B,OAAO;AAAA,MACP,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,gBAAgB,EAAE,GAAG,CAAC,GAAG,yBAAyB,CAAC;AAAA,MAC7D,UAAU,SAAS,iCAAiC,IAAI,KAAK;AAC3D,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB,GAAG;AACtB,UAAG,eAAe,GAAG,OAAO,MAAM,CAAC;AACnC,UAAG,WAAW,GAAG,iDAAiD,GAAG,GAAG,gBAAgB,CAAC,EAAE,GAAG,gDAAgD,GAAG,CAAC;AAClJ,UAAG,aAAa;AAChB,UAAG,aAAa,CAAC;AAAA,QACnB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,+BAA+B,CAAC,IAAI,sBAAsB,EAAE,wCAAwC,IAAI,sBAAsB;AAC7I,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,2BAA2B,IAAI,QAAQ;AACrD,UAAG,UAAU;AACb,UAAG,cAAc,CAAC,IAAI,WAAW,IAAI,EAAE;AAAA,QACzC;AAAA,MACF;AAAA,MACA,cAAc,CAAC,iBAAoB,gCAAgC;AAAA,MACnE,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,WAAW;AAAA;AAAA,QACX;AAAA,UACE,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,MAAM,CAAC,CAAC,IAAI,SAAS,GAAG,IAAI,SAAS,GAAG,8BAA8B,CAAC;AAAA,QACzE;AAAA,MAAC;AAAA,MACD,eAAe,oBAAkB;AAAA,MACjC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAaV,qBAAqB;AAAA,MACrB,YAAY;AAAA,MACZ,SAAS,CAAC,eAAe;AAAA,IAC3B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,8BAA8B;AAAA,IACvC,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,IACvB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,4BAAN,MAAM,2BAA0B;AAAA,EAC9B,YAAY,YAAY,UAAU;AAChC,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,SAAS,SAAS,WAAW,eAAe,kCAAkC;AAAA,EACrF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,kCAAkC,mBAAmB;AACxE,aAAO,KAAK,qBAAqB,4BAA8B,kBAAqB,UAAU,GAAM,kBAAqB,SAAS,CAAC;AAAA,IACrI;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,sBAAsB,EAAE,CAAC;AAAA,MAC1C,UAAU,CAAC,kBAAkB;AAAA,MAC7B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,2BAA2B,CAAC;AAAA,IAClG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,OAAO;AACL,SAAK,OAAO,SAAS,wBAAwB,mBAAmB;AAC9D,aAAO,KAAK,qBAAqB,kBAAiB;AAAA,IACpD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,oBAAoB,wBAAwB,uBAAuB,2BAA2B,yBAAyB,4BAA4B,kCAAkC,oCAAoC;AAAA,MACnO,SAAS,CAAC,oBAAoB,wBAAwB,uBAAuB,2BAA2B,uBAAuB;AAAA,IACjI,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,uBAAuB,yBAAyB,0BAA0B;AAAA,IACtF,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOL,SAAS,CAAC,oBAAoB,wBAAwB,uBAAuB,2BAA2B,yBAAyB,4BAA4B,kCAAkC,oCAAoC;AAAA,MACnO,SAAS,CAAC,oBAAoB,wBAAwB,uBAAuB,2BAA2B,uBAAuB;AAAA,IACjI,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;AC7xDH,IAAMC,OAAM,QAAM,CAAC,EAAE;AACrB,IAAMC,OAAM,MAAM,CAAC;AACnB,SAAS,0DAA0D,IAAI,KAAK;AAC1E,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,aAAa,CAAC;AAAA,EAChC;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,cAAc,IAAI,EAAE,SAAS,OAAO,KAAK,MAAM,KAAK,EAAE,YAAY,OAAO,KAAK,MAAM,QAAQ,EAAE,YAAY,OAAO,QAAQ;AAAA,EACzI;AACF;AACA,SAAS,0DAA0D,IAAI,KAAK;AAC1E,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,WAAW,EAAE;AAClC,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,UAAU,OAAO,KAAK,IAAI,MAAM,EAAE,WAAW,OAAO,KAAK,IAAI,OAAO,EAAE,QAAQ,OAAO,KAAK,IAAI,IAAI,EAAE,YAAY,OAAO,QAAQ;AAC7I,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,KAAK,IAAI,IAAI;AAAA,EAC3C;AACF;AACA,SAAS,4CAA4C,IAAI,KAAK;AAC5D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,KAAK,CAAC,EAAE,GAAG,QAAQ,CAAC;AACzC,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAChB,IAAG,WAAW,GAAG,2DAA2D,GAAG,GAAG,aAAa,CAAC,EAAE,GAAG,2DAA2D,GAAG,GAAG,WAAW,EAAE;AACnL,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,cAAiB,gBAAgB,IAAID,MAAK,OAAO,KAAK,IAAI,CAAC,EAAE,eAAe,OAAO,KAAK,aAAa,OAAO,OAAO,OAAO,KAAK,UAAU,WAAW,EAAE,YAAY,OAAO,KAAK,aAAa,OAAO,OAAO,OAAO,KAAK,UAAU,QAAQ,EAAE,uBAAuB,OAAO,KAAK,aAAa,OAAO,OAAO,OAAO,KAAK,UAAU,mBAAmB,EAAE,oBAAoB,OAAO,KAAK,aAAa,OAAO,OAAO,OAAO,KAAK,UAAU,gBAAgB,EAAE,sBAAsB,OAAO,KAAK,aAAa,OAAO,OAAO,OAAO,KAAK,UAAU,kBAAkB,EAAE,cAAc,OAAO,KAAK,aAAa,OAAO,OAAO,OAAO,KAAK,UAAU,UAAU,EAAE,SAAS,OAAO,KAAK,aAAa,OAAO,OAAO,OAAO,KAAK,UAAU,KAAK,EAAE,qBAAqB,OAAO,KAAK,aAAa,OAAO,OAAO,OAAO,KAAK,UAAU,qBAAwB,gBAAgB,IAAIC,IAAG,CAAC;AACh1B,IAAG,UAAU,CAAC;AACd,IAAG,kBAAkB,OAAO,KAAK,IAAI;AACrC,IAAG,UAAU;AACb,IAAG,cAAc,OAAO,KAAK,QAAQ,IAAI,EAAE;AAC3C,IAAG,UAAU;AACb,IAAG,cAAc,OAAO,KAAK,MAAM,IAAI,EAAE;AAAA,EAC3C;AACF;AACA,SAAS,0DAA0D,IAAI,KAAK;AAC1E,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,aAAa,CAAC;AAAA,EAChC;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,cAAc,IAAI,EAAE,SAAS,OAAO,KAAK,MAAM,KAAK,EAAE,YAAY,OAAO,KAAK,MAAM,QAAQ,EAAE,YAAY,OAAO,QAAQ;AAAA,EACzI;AACF;AACA,SAAS,0DAA0D,IAAI,KAAK;AAC1E,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,WAAW,EAAE;AAClC,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,UAAU,OAAO,KAAK,IAAI,MAAM,EAAE,WAAW,OAAO,KAAK,IAAI,OAAO,EAAE,QAAQ,OAAO,KAAK,IAAI,IAAI,EAAE,YAAY,OAAO,QAAQ;AAC7I,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,KAAK,IAAI,IAAI;AAAA,EAC3C;AACF;AACA,SAAS,4CAA4C,IAAI,KAAK;AAC5D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC;AAC5C,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAChB,IAAG,WAAW,GAAG,2DAA2D,GAAG,GAAG,aAAa,CAAC,EAAE,GAAG,2DAA2D,GAAG,GAAG,WAAW,EAAE;AACnL,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU,CAAC;AACd,IAAG,kBAAkB,OAAO,KAAK,IAAI;AACrC,IAAG,UAAU;AACb,IAAG,cAAc,OAAO,KAAK,QAAQ,IAAI,EAAE;AAC3C,IAAG,UAAU;AACb,IAAG,cAAc,OAAO,KAAK,MAAM,IAAI,EAAE;AAAA,EAC3C;AACF;AACA,SAAS,4CAA4C,IAAI,KAAK;AAC5D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,UAAU,GAAG,QAAQ,EAAE;AAC1B,IAAG,aAAa;AAAA,EAClB;AACF;AACA,SAAS,kDAAkD,IAAI,KAAK;AAClE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,iBAAiB,EAAE;AAAA,EACrC;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAU,IAAI;AACpB,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,WAAW,OAAO,OAAO,EAAE,QAAQ,OAAO,EAAE,cAAc,QAAQ,UAAU,EAAE,oBAAoB,OAAO,gBAAgB,EAAE,eAAe,OAAO,WAAW,EAAE,YAAY,OAAO,QAAQ;AAAA,EACzM;AACF;AACA,SAAS,4CAA4C,IAAI,KAAK;AAC5D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,iBAAiB,GAAG,mDAAmD,GAAG,GAAG,iBAAiB,IAAO,yBAAyB;AACjI,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,uBAA0B,YAAY,GAAG,GAAG,OAAO,KAAK,QAAQ,OAAO,mBAAmB,OAAO,YAAY,CAAC;AAC5H,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,OAAO,KAAK,SAAS;AAAA,EACrC;AACF;AACA,IAAMC,OAAM,CAAC,GAAG;AAChB,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,OAAO,KAAK,YAAY,IAAI;AAAA,EACzD;AACF;AACA,SAAS,iEAAiE,IAAI,KAAK;AACjF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,OAAO,CAAC;AACxB,IAAG,OAAO,GAAG,cAAc;AAAA,EAC7B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,aAAgB,YAAY,GAAG,GAAG,OAAO,KAAK,UAAU,MAAM,GAAM,cAAc;AAAA,EAClG;AACF;AACA,SAAS,iEAAiE,IAAI,KAAK;AACjF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,EAAE;AAAA,EAC5B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,WAAW,OAAO,KAAK,IAAI;AAAA,EAC3C;AACF;AACA,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,GAAG,kEAAkE,GAAG,GAAG,OAAO,CAAC,EAAE,GAAG,kEAAkE,GAAG,GAAG,QAAQ,EAAE;AACxL,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,YAAY,eAAe,OAAO,UAAU;AAC/C,IAAG,UAAU;AACb,IAAG,cAAc,OAAO,KAAK,WAAW,IAAI,EAAE;AAC9C,IAAG,UAAU;AACb,IAAG,cAAc,OAAO,WAAW,CAAC,OAAO,KAAK,WAAW,IAAI,EAAE;AAAA,EACnE;AACF;AACA,SAAS,iEAAiE,IAAI,KAAK;AACjF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,aAAa,EAAE;AAAA,EACjC;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,cAAc,IAAI,EAAE,iBAAiB,OAAO,KAAK,MAAM,aAAa,EAAE,SAAS,OAAO,KAAK,MAAM,KAAK,EAAE,YAAY,OAAO,KAAK,MAAM,QAAQ,EAAE,YAAY,OAAO,QAAQ;AAAA,EAC3L;AACF;AACA,SAAS,iEAAiE,IAAI,KAAK;AACjF,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,WAAW,EAAE;AAClC,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,UAAU,OAAO,KAAK,IAAI,MAAM,EAAE,WAAW,OAAO,KAAK,IAAI,OAAO,EAAE,QAAQ,OAAO,KAAK,IAAI,IAAI,EAAE,YAAY,OAAO,QAAQ;AAC7I,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,KAAK,IAAI,IAAI;AAAA,EAC3C;AACF;AACA,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,KAAK,EAAE;AAC5B,IAAG,WAAW,SAAS,SAAS,sEAAsE,QAAQ;AAC5G,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,YAAY,MAAM,CAAC;AAAA,IAClD,CAAC;AACD,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAChB,IAAG,WAAW,GAAG,kEAAkE,GAAG,GAAG,aAAa,EAAE,EAAE,GAAG,kEAAkE,GAAG,GAAG,WAAW,EAAE;AAClM,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,cAAiB,gBAAgB,IAAIF,MAAK,OAAO,KAAK,IAAI,CAAC,EAAE,eAAe,OAAO,KAAK,aAAa,OAAO,OAAO,OAAO,KAAK,UAAU,WAAW,EAAE,YAAY,OAAO,KAAK,aAAa,OAAO,OAAO,OAAO,KAAK,UAAU,QAAQ,EAAE,uBAAuB,OAAO,KAAK,aAAa,OAAO,OAAO,OAAO,KAAK,UAAU,mBAAmB,EAAE,oBAAoB,OAAO,KAAK,aAAa,OAAO,OAAO,OAAO,KAAK,UAAU,gBAAgB,EAAE,sBAAsB,OAAO,KAAK,aAAa,OAAO,OAAO,OAAO,KAAK,UAAU,kBAAkB,EAAE,cAAc,OAAO,KAAK,aAAa,OAAO,OAAO,OAAO,KAAK,UAAU,UAAU,EAAE,SAAS,OAAO,KAAK,aAAa,OAAO,OAAO,OAAO,KAAK,UAAU,KAAK,EAAE,qBAAqB,OAAO,KAAK,aAAa,OAAO,OAAO,OAAO,KAAK,UAAU,qBAAwB,gBAAgB,IAAIC,IAAG,CAAC;AACh1B,IAAG,UAAU,CAAC;AACd,IAAG,kBAAkB,OAAO,KAAK,IAAI;AACrC,IAAG,UAAU;AACb,IAAG,cAAc,OAAO,KAAK,QAAQ,IAAI,EAAE;AAC3C,IAAG,UAAU;AACb,IAAG,cAAc,OAAO,KAAK,MAAM,IAAI,EAAE;AAAA,EAC3C;AACF;AACA,SAAS,iEAAiE,IAAI,KAAK;AACjF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,aAAa,EAAE;AAAA,EACjC;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,cAAc,IAAI,EAAE,iBAAiB,OAAO,KAAK,MAAM,aAAa,EAAE,SAAS,OAAO,KAAK,MAAM,KAAK,EAAE,YAAY,OAAO,KAAK,MAAM,QAAQ,EAAE,YAAY,OAAO,QAAQ;AAAA,EAC3L;AACF;AACA,SAAS,iEAAiE,IAAI,KAAK;AACjF,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,WAAW,EAAE;AAClC,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,UAAU,OAAO,KAAK,IAAI,MAAM,EAAE,WAAW,OAAO,KAAK,IAAI,OAAO,EAAE,QAAQ,OAAO,KAAK,IAAI,IAAI,EAAE,YAAY,OAAO,QAAQ;AAC7I,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,KAAK,IAAI,IAAI;AAAA,EAC3C;AACF;AACA,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,CAAC,EAAE,GAAG,QAAQ,EAAE;AAC7C,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAChB,IAAG,WAAW,GAAG,kEAAkE,GAAG,GAAG,aAAa,EAAE,EAAE,GAAG,kEAAkE,GAAG,GAAG,WAAW,EAAE;AAClM,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU,CAAC;AACd,IAAG,kBAAkB,OAAO,KAAK,IAAI;AACrC,IAAG,UAAU;AACb,IAAG,cAAc,OAAO,KAAK,QAAQ,IAAI,EAAE;AAC3C,IAAG,UAAU;AACb,IAAG,cAAc,OAAO,KAAK,MAAM,IAAI,EAAE;AAAA,EAC3C;AACF;AACA,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,UAAU,GAAG,QAAQ,EAAE;AAC1B,IAAG,aAAa;AAAA,EAClB;AACF;AACA,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,aAAa,CAAC;AACjB,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,uBAA0B,YAAY,GAAG,GAAG,OAAO,KAAK,QAAQ,OAAO,mBAAmB,OAAO,YAAY,CAAC;AAAA,EAC9H;AACF;AACA,IAAME,OAAM,SAAO;AAAA,EACjB,UAAU;AACZ;AACA,IAAMC,OAAM,SAAO;AAAA,EACjB,WAAW;AACb;AACA,IAAMC,OAAM,CAAC,IAAI,IAAI,QAAQ;AAAA,EAC3B,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,oBAAoB;AACtB;AACA,SAAS,gEAAgE,IAAI,KAAK;AAChF,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,KAAK,CAAC;AACxC,IAAG,WAAW,SAAS,SAAS,mFAAmF,QAAQ;AACzH,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,YAAY,MAAM,CAAC;AAAA,IAClD,CAAC;AACD,IAAG,mBAAmB,GAAG,CAAC;AAC1B,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,UAAM,cAAiB,YAAY,CAAC;AACpC,IAAG,UAAU;AACb,IAAG,WAAW,cAAiB,gBAAgB,IAAIL,MAAK,OAAO,KAAK,IAAI,CAAC,EAAE,eAAe,OAAO,KAAK,aAAa,OAAO,OAAO,OAAO,KAAK,UAAU,WAAW,EAAE,YAAY,OAAO,KAAK,aAAa,OAAO,OAAO,OAAO,KAAK,UAAU,QAAQ,EAAE,uBAAuB,OAAO,KAAK,aAAa,OAAO,OAAO,OAAO,KAAK,UAAU,mBAAmB,EAAE,oBAAoB,OAAO,KAAK,aAAa,OAAO,OAAO,OAAO,KAAK,UAAU,gBAAgB,EAAE,sBAAsB,OAAO,KAAK,aAAa,OAAO,OAAO,OAAO,KAAK,UAAU,kBAAkB,EAAE,cAAc,OAAO,KAAK,aAAa,OAAO,OAAO,OAAO,KAAK,UAAU,UAAU,EAAE,SAAS,OAAO,KAAK,aAAa,OAAO,OAAO,OAAO,KAAK,UAAU,KAAK,EAAE,qBAAqB,OAAO,KAAK,aAAa,OAAO,OAAO,OAAO,KAAK,UAAU,qBAAwB,gBAAgB,IAAIC,IAAG,CAAC;AACh1B,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,WAAW,EAAE,2BAA8B,gBAAgB,IAAIG,MAAK,OAAO,IAAI,CAAC;AAAA,EACpH;AACF;AACA,SAAS,kDAAkD,IAAI,KAAK;AAClE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,SAAS,SAAS,uEAAuE,QAAQ;AAC7G,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,YAAY,MAAM,CAAC;AAAA,IAClD,CAAC;AACD,IAAG,WAAW,GAAG,iEAAiE,GAAG,IAAI,OAAO,CAAC;AACjG,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,gBAAgB,OAAO,KAAK,IAAI;AAC9C,IAAG,UAAU;AACb,IAAG,cAAc,OAAO,UAAU,IAAI,EAAE;AAAA,EAC1C;AACF;AACA,SAAS,gEAAgE,IAAI,KAAK;AAChF,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,mBAAmB,GAAG,CAAC;AAC1B,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,UAAM,cAAiB,YAAY,CAAC;AACpC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,WAAW,EAAE,2BAA8B,gBAAgB,GAAGA,MAAK,OAAO,IAAI,CAAC;AAAA,EACnH;AACF;AACA,SAAS,kDAAkD,IAAI,KAAK;AAClE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,WAAW,SAAS,SAAS,uEAAuE,QAAQ;AAC7G,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,YAAY,MAAM,CAAC;AAAA,IAClD,CAAC;AACD,IAAG,WAAW,GAAG,iEAAiE,GAAG,GAAG,OAAO,CAAC;AAChG,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,cAAc,OAAO,UAAU,IAAI,EAAE;AAAA,EAC1C;AACF;AACA,SAAS,gEAAgE,IAAI,KAAK;AAChF,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,WAAW,cAAc,SAAS,4FAA4F;AAC/H,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,cAAc,IAAI,CAAC;AAAA,IAClD,CAAC,EAAE,cAAc,SAAS,4FAA4F;AACpH,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,cAAc,KAAK,CAAC;AAAA,IACnD,CAAC;AACD,IAAG,eAAe,GAAG,OAAO,EAAE,EAAE,GAAG,OAAO,EAAE,EAAE,GAAG,OAAO,EAAE;AAC1D,IAAG,aAAa,CAAC;AACjB,IAAG,aAAa,EAAE,EAAE,EAAE;AAAA,EACxB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,YAAY,cAAc,OAAO,YAAY,aAAa,KAAK,GAAG,IAAI;AACzE,IAAG,WAAW,WAAc,gBAAgB,GAAGC,MAAK,OAAO,QAAQ,SAAS,OAAO,QAAQ,QAAQ,OAAO,QAAQ,SAAS,CAAC,EAAE,gBAAgB,MAAS;AAAA,EACzJ;AACF;AACA,SAAS,kDAAkD,IAAI,KAAK;AAClE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,iEAAiE,GAAG,GAAG,OAAO,EAAE;AAAA,EACnG;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,cAAc,OAAO,eAAe,IAAI,EAAE;AAAA,EAC/C;AACF;AACA,SAAS,gEAAgE,IAAI,KAAK;AAChF,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,aAAa,EAAE;AACpC,IAAG,UAAU,GAAG,QAAQ,EAAE;AAC1B,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,EAAE;AACnC,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,UAAU,QAAQ,IAAI,MAAM,EAAE,OAAO,IAAI,EAAE,YAAY,OAAO,QAAQ;AACpF,IAAG,UAAU;AACb,IAAG,WAAW,WAAW,QAAQ,IAAI;AAAA,EACvC;AACF;AACA,SAAS,gEAAgE,IAAI,KAAK;AAChF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,EAAE;AAAA,EAC5B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,EAAE;AACnC,IAAG,WAAW,WAAW,QAAQ,IAAI;AAAA,EACvC;AACF;AACA,SAAS,gEAAgE,IAAI,KAAK;AAChF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,EAAE;AAC1B,IAAG,OAAO,GAAG,cAAc;AAAA,EAC7B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,EAAE;AACnC,IAAG,WAAW,aAAgB,YAAY,GAAG,GAAG,QAAQ,UAAU,MAAM,GAAM,cAAc;AAAA,EAC9F;AACF;AACA,SAAS,kDAAkD,IAAI,KAAK;AAClE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,iEAAiE,GAAG,GAAG,aAAa,EAAE,EAAE,GAAG,iEAAiE,GAAG,GAAG,QAAQ,EAAE,EAAE,GAAG,iEAAiE,GAAG,GAAG,QAAQ,EAAE;AAAA,EACrR;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAU,IAAI;AACpB,IAAG,cAAc,QAAQ,MAAM,IAAI,EAAE;AACrC,IAAG,UAAU;AACb,IAAG,cAAc,CAAC,QAAQ,OAAO,CAAC,QAAQ,WAAW,IAAI,EAAE;AAC3D,IAAG,UAAU;AACb,IAAG,cAAc,CAAC,QAAQ,OAAO,QAAQ,WAAW,IAAI,EAAE;AAAA,EAC5D;AACF;AACA,IAAMC,OAAM,CAAC,WAAW;AACxB,IAAMC,OAAM,CAAC,CAAC,CAAC,IAAI,aAAa,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,QAAQ,EAAE,CAAC,CAAC;AACxD,IAAMC,OAAM,CAAC,eAAe,QAAQ;AACpC,IAAMC,OAAM,SAAO;AAAA,EACjB,sBAAsB;AACxB;AACA,IAAMC,QAAO,SAAO;AAAA,EAClB,YAAY;AACd;AACA,IAAM,OAAO,SAAO;AAAA,EAClB,yCAAyC;AAC3C;AACA,IAAMC,QAAO,CAAC,IAAI,QAAQ;AAAA,EACxB,2BAA2B;AAAA,EAC3B,0BAA0B;AAC5B;AACA,SAAS,sDAAsD,IAAI,KAAK;AACtE,MAAI,KAAK,GAAG;AACV,IAAG,aAAa,CAAC;AAAA,EACnB;AACF;AACA,SAAS,sDAAsD,IAAI,KAAK;AACtE,MAAI,KAAK,GAAG;AACV,IAAG,aAAa,GAAG,CAAC;AAAA,EACtB;AACF;AACA,SAAS,wCAAwC,IAAI,KAAK;AACxD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,GAAG,uDAAuD,GAAG,CAAC,EAAE,GAAG,uDAAuD,GAAG,CAAC;AAC5I,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,cAAc,CAAC,OAAO,kBAAkB,IAAI,EAAE;AACjD,IAAG,UAAU;AACb,IAAG,cAAc,OAAO,kBAAkB,IAAI,EAAE;AAAA,EAClD;AACF;AACA,SAAS,kEAAkE,IAAI,KAAK;AAClF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,iBAAiB,CAAC;AAAA,EACpC;AACA,MAAI,KAAK,GAAG;AACV,UAAM,cAAc,IAAI;AACxB,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAc,gBAAgB,GAAGN,MAAK,OAAO,QAAQ,SAAS,OAAO,QAAQ,QAAQ,OAAO,QAAQ,SAAS,CAAC;AACjH,IAAG,WAAW,WAAW,OAAO,OAAO,EAAE,QAAQ,WAAW,EAAE,cAAc,YAAY,UAAU,EAAE,iBAAiB,IAAI,EAAE,oBAAoB,OAAO,gBAAgB,EAAE,eAAe,OAAO,WAAW,EAAE,YAAY,OAAO,IAAI;AAAA,EACpO;AACF;AACA,SAAS,4DAA4D,IAAI,KAAK;AAC5E,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,wBAAwB,CAAC;AAC9C,IAAG,iBAAiB,GAAG,mEAAmE,GAAG,IAAI,iBAAiB,GAAM,yBAAyB;AACjJ,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,EAAE;AACnC,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,WAAW,OAAO,OAAO,EAAE,QAAQ,OAAO,EAAE,YAAY,OAAO,IAAI;AACjF,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,SAAS;AAAA,EACjC;AACF;AACA,SAAS,8CAA8C,IAAI,KAAK;AAC9D,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,6DAA6D,GAAG,GAAG,wBAAwB,CAAC;AAAA,EAC/G;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAU,IAAI;AACpB,IAAG,cAAc,CAAC,QAAQ,SAAS,IAAI,EAAE;AAAA,EAC3C;AACF;AACA,SAAS,wCAAwC,IAAI,KAAK;AACxD,MAAI,KAAK,GAAG;AACV,IAAG,iBAAiB,GAAG,+CAA+C,GAAG,GAAG,MAAM,MAAS,yBAAyB;AAAA,EACtH;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,OAAO,QAAQ;AAAA,EAC/B;AACF;AACA,SAAS,kEAAkE,IAAI,KAAK;AAClF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,iBAAiB,CAAC;AAAA,EACpC;AACA,MAAI,KAAK,GAAG;AACV,UAAM,cAAc,IAAI;AACxB,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAc,gBAAgB,IAAIA,MAAK,OAAO,QAAQ,SAAS,OAAO,QAAQ,QAAQ,OAAO,QAAQ,SAAS,CAAC;AAClH,IAAG,WAAW,WAAW,OAAO,OAAO,EAAE,YAAY,OAAO,OAAO,EAAE,QAAQ,WAAW,EAAE,cAAc,YAAY,UAAU,EAAE,iBAAiB,IAAI,EAAE,oBAAoB,OAAO,gBAAgB,EAAE,eAAe,OAAO,WAAW,EAAE,YAAY,OAAO,IAAI;AAAA,EAChQ;AACF;AACA,SAAS,4DAA4D,IAAI,KAAK;AAC5E,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,yBAAyB,CAAC;AAC/C,IAAG,iBAAiB,GAAG,mEAAmE,GAAG,IAAI,iBAAiB,GAAM,yBAAyB;AACjJ,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,EAAE;AACnC,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,WAAW,OAAO,OAAO,EAAE,YAAY,OAAO,OAAO,EAAE,QAAQ,OAAO,EAAE,YAAY,OAAO,IAAI;AAC7G,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,SAAS;AAAA,EACjC;AACF;AACA,SAAS,8CAA8C,IAAI,KAAK;AAC9D,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,6DAA6D,GAAG,GAAG,yBAAyB,CAAC;AAAA,EAChH;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAU,IAAI;AACpB,IAAG,cAAc,CAAC,QAAQ,SAAS,IAAI,EAAE;AAAA,EAC3C;AACF;AACA,SAAS,wCAAwC,IAAI,KAAK;AACxD,MAAI,KAAK,GAAG;AACV,IAAG,iBAAiB,GAAG,+CAA+C,GAAG,GAAG,MAAM,MAAS,yBAAyB;AAAA,EACtH;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,OAAO,QAAQ;AAAA,EAC/B;AACF;AACA,SAAS,qEAAqE,IAAI,KAAK;AACrF,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,sDAAsD,IAAI,KAAK;AACtE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,sEAAsE,GAAG,GAAG,gBAAgB,EAAE;AAAA,EACjH;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,oBAAoB,OAAO,UAAU,EAAE,2BAA8B,gBAAgB,GAAGD,MAAK,OAAO,eAAe,CAAC;AAAA,EACpI;AACF;AACA,SAAS,sDAAsD,IAAI,KAAK;AACtE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,eAAe;AAClB,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,UAAU,GAAG,QAAQ,EAAE,EAAE,GAAG,QAAQ,EAAE;AACzC,IAAG,aAAa,EAAE;AAClB,IAAG,gBAAgB;AACnB,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,WAAW,SAAS,SAAS,6EAA6E;AAC3G,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,uBAAuB,CAAC;AAAA,IACvD,CAAC;AACD,IAAG,UAAU,GAAG,QAAQ,EAAE;AAC1B,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,WAAc,gBAAgB,GAAG,MAAM,OAAO,eAAe,CAAC;AAC5E,IAAG,UAAU;AACb,IAAG,WAAW,WAAc,gBAAgB,GAAGO,OAAM,OAAO,iBAAiB,CAAC,OAAO,eAAe,CAAC;AAAA,EACvG;AACF;AACA,SAAS,wCAAwC,IAAI,KAAK;AACxD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,GAAG,uDAAuD,GAAG,GAAG,cAAc,EAAE,GAAG,uDAAuD,GAAG,CAAC;AAC5J,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,WAAc,gBAAgB,GAAGD,OAAM,OAAO,eAAe,CAAC;AAC5E,IAAG,UAAU;AACb,IAAG,cAAc,OAAO,aAAa,IAAI,CAAC;AAAA,EAC5C;AACF;AACA,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,cAAc;AACZ,SAAK,QAAQ,IAAI,gBAAgB,MAAM;AACvC,SAAK,sBAAsB,IAAI,gBAAgB,KAAK;AAEpD,SAAK,2BAA2B,IAAI,QAAQ;AAC5C,SAAK,iBAAiB,IAAI,gBAAgB,IAAI;AAAA,EAChD;AAAA,EACA,0BAA0B,MAAM;AAC9B,SAAK,yBAAyB,KAAK,IAAI;AAAA,EACzC;AAAA,EACA,aAAa,MAAM;AACjB,SAAK,MAAM,KAAK,IAAI;AAAA,EACtB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,uBAAuB,mBAAmB;AAC7D,aAAO,KAAK,qBAAqB,iBAAgB;AAAA,IACnD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,gBAAe;AAAA,IAC1B,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,IAAI,MAAM;AACR,WAAO,KAAK,OAAO,KAAK,KAAK,MAAM;AAAA,EACrC;AAAA,EACA,YAAY,MAAM,gBAAgB,YAAY,oBAAoB,QAAQ;AACxE,SAAK,OAAO;AACZ,SAAK,iBAAiB;AACtB,SAAK,aAAa;AAClB,SAAK,qBAAqB;AAC1B,SAAK,SAAS;AACd,SAAK,WAAW,IAAI,QAAQ;AAC5B,SAAK,WAAW;AAChB,SAAK,aAAa;AAClB,SAAK,gBAAgB;AACrB,SAAK,kBAAkB;AACvB,SAAK,mBAAmB;AACxB,SAAK,cAAc;AACnB,SAAK,WAAW;AAChB,SAAK,UAAU;AAAA,MACb,YAAY;AAAA,MACZ,gBAAgB;AAAA,MAChB,wBAAwB;AAAA,MACxB,qBAAqB;AAAA,IACvB;AACA,SAAK,YAAY,IAAI,QAAQ;AAC7B,SAAK,cAAc;AACnB,SAAK,mBAAmB;AACxB,SAAK,oBAAoB,CAAC,QAAQ,iBAAiB;AACjD,aAAO,UAAU,CAAC,eAAe,aAAa;AAAA,IAChD;AACA,QAAI,QAAQ;AACV,WAAK,OAAO,OAAO,KAAK,UAAU,KAAK,QAAQ,GAAG,OAAO,OAAK,aAAa,aAAa,CAAC,EAAE,UAAU,MAAM;AACzG,aAAK,mBAAmB;AAAA,MAC1B,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,WAAW;AAAA,EAAC;AAAA,EACZ,kBAAkB;AAChB,SAAK,yBAAyB;AAAA,EAChC;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,QAAQ,MAAM,GAAG;AACnB,WAAK,KAAK,aAAa;AAAA,IACzB;AACA,QAAI,QAAQ,YAAY;AACtB,WAAK,iBAAiB,KAAK,UAAU;AAAA,IACvC;AAAA,EACF;AAAA,EACA,YAAY,GAAG;AACb,QAAI,KAAK,KAAK,UAAU;AACtB,QAAE,gBAAgB;AAClB,QAAE,eAAe;AAAA,IACnB;AACA,QAAI,KAAK,cAAc;AACrB,WAAK,KAAK,SAAS,CAAC,KAAK,KAAK;AAAA,IAChC,OAAO;AACL,UAAI,KAAK,SAAS;AAChB,aAAK,gBAAgB;AAAA,MACvB;AACA,WAAK,eAAe,0BAA0B,IAAI;AAAA,IACpD;AAAA,EACF;AAAA,EACA,IAAI,eAAe;AACjB,WAAO,eAAe,aAAa,KAAK,KAAK,SAAS;AAAA,EACxD;AAAA,EACA,IAAI,eAAe;AACjB,QAAI,SAAS,KAAK,KAAK,WAAW,KAAK,OAAK;AAC1C,aAAO,CAAC,EAAE;AAAA,IACZ,CAAC;AACD,WAAO,UAAU;AAAA,EACnB;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,iBAAiB,OAAO;AACtB,SAAK,aAAa;AAClB,SAAK,KAAK,aAAa;AACvB,SAAK,UAAU,KAAK,KAAK;AACzB,QAAI,KAAK,YAAY;AACnB,UAAI,CAAC,KAAK,KAAK,QAAQ;AACrB,aAAK,KAAK,SAAS;AAAA,MACrB;AAAA,IACF;AACA,SAAK,KAAK,aAAa;AAAA,EACzB;AAAA,EACA,qBAAqB;AACnB,QAAI,KAAK,gBAAgB,CAAC,KAAK,oBAAoB,CAAC,KAAK,4BAA4B,CAAC,KAAK,UAAU,CAAC,KAAK,OAAO,aAAa,CAAC,KAAK,aAAa;AAChJ;AAAA,IACF;AACA,YAAQ,QAAQ,EAAE,KAAK,MAAM;AAC3B,YAAM,iBAAiB,KAAK,eAAe;AAC3C,UAAI,CAAC,KAAK;AAAc,YAAI,KAAK,eAAe,gBAAgB;AAC9D,eAAK,aAAa;AAClB,eAAK,iBAAiB,KAAK,UAAU;AAAA,QACvC;AAAA;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,iBAAiB;AACf,UAAM,kBAAkB,KAAK,aAAa,KAAK,MAAM;AACrD,WAAO,KAAK,cAAc,gBAAgB,KAAK,UAAU,KAAK,KAAK,sBAAsB,gBAAgB,KAAK,kBAAkB,KAAK,KAAK,iBAAiB,KAAK,eAAe,KAAK,KAAK,yBAAyB,KAAK,eAAe;AAAA,EACxO;AAAA,EACA,aAAa,QAAQ;AACnB,UAAM,uBAAuB,KAAK,KAAK,WAAW,2BAA2B;AAAA,MAC3E,OAAO,KAAK,mBAAmB,UAAU;AAAA,MACzC,aAAa,KAAK,mBAAmB,UAAU;AAAA,MAC/C,UAAU;AAAA,MACV,cAAc;AAAA,IAChB;AACA,WAAO,UAAQ,OAAO,SAAS,KAAK,SAAS,oBAAoB;AAAA,EACnE;AAAA,EACA,6BAA6B,KAAK;AAChC,QAAI,eAAe,aAAa,KAAK,QAAQ,GAAG;AAC9C,WAAK,SAAS,QAAQ,OAAK;AACzB,UAAE,6BAA6B,GAAG;AAAA,MACpC,CAAC;AAAA,IACH,OAAO;AACL,WAAK,iBAAiB,QAAQ,KAAK,GAAG;AAAA,IACxC;AAAA,EACF;AAAA,EACA,IAAI,UAAU;AACZ,WAAO,CAAC,KAAK,KAAK,YAAY,CAAC,KAAK,gBAAgB,gBAAgB,SAAS,KAAK,KAAK,IAAI;AAAA,EAC7F;AAAA,EACA,IAAI,UAAU;AACZ,WAAO,KAAK,SAAS,gBAAgB,eAAe,KAAK,KAAK,IAAI,KAAK,gBAAgB,eAAe,KAAK,KAAK,QAAQ;AAAA,EAC1H;AAAA,EACA,2BAA2B;AACzB,QAAI,KAAK,iBAAkB,MAAK,iBAAiB,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM,KAAK,mBAAmB,CAAC;AACjI,QAAI,KAAK,yBAA0B,MAAK,yBAAyB,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM,KAAK,mBAAmB,CAAC;AACjJ,QAAI,eAAe,aAAa,KAAK,QAAQ,GAAG;AAC9C,YAAM,6BAA6B,KAAK;AACxC,YAAM,UAAU,2BAA2B;AAC3C,YAAM,mBAAmB,MAAM,GAAG,CAAC,SAAS,GAAG,2BAA2B,IAAI,UAAQ,KAAK,SAAS,CAAC,CAAC;AACtG,cAAQ,KAAK,UAAU,0BAA0B,GAAG,UAAU,MAAM,gBAAgB,GAAG,UAAU,IAAI,GAAG,IAAI,MAAM,2BAA2B,KAAK,OAAK,EAAE,UAAU,CAAC,GAAG,aAAa,GAAG,GAAG,qBAAqB,GAAG,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,cAAY;AAChQ,aAAK,iBAAiB,QAAQ;AAAA,MAChC,CAAC;AAAA,IACH;AACA,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EACA,QAAQ,OAAO;AACb,QAAI,KAAK,KAAK,UAAU,OAAO;AAC7B,WAAK,KAAK,SAAS;AACnB,WAAK,KAAK,aAAa;AAAA,IACzB;AAAA,EACF;AAAA,EACA,gBAAgB,OAAO;AACrB,QAAI,KAAK,SAAS,QAAQ;AACxB,WAAK,SAAS,QAAQ,OAAK;AACzB,UAAE,gBAAgB,KAAK;AAAA,MACzB,CAAC;AACD,WAAK,QAAQ,KAAK;AAAA,IACpB;AACA,SAAK,KAAK,aAAa;AAAA,EACzB;AAAA,EACA,kBAAkB;AAChB,QAAI,CAAC,gBAAgB,SAAS,KAAK,KAAK,SAAS,GAAG;AAClD,WAAK,QAAQ,cAAc,KAAK,KAAK,IAAI;AAAA,IAC3C,OAAO;AACL,WAAK,QAAQ,SAAS,CAAC,KAAK,KAAK,IAAI,GAAG;AAAA,QACtC,aAAa,KAAK,KAAK,WAAW;AAAA,QAClC,UAAU,KAAK,KAAK,WAAW;AAAA,QAC/B,qBAAqB,KAAK,KAAK,WAAW;AAAA,QAC1C,kBAAkB,KAAK,KAAK,WAAW;AAAA,QACvC,oBAAoB,KAAK,KAAK,WAAW;AAAA,QACzC,YAAY,KAAK,KAAK,WAAW;AAAA,QACjC,OAAO,KAAK,KAAK,WAAW;AAAA,MAC9B,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,6BAA6B,mBAAmB;AACnE,aAAO,KAAK,qBAAqB,uBAAyB,kBAAqB,iBAAiB,GAAM,kBAAkB,cAAc,GAAM,kBAAqB,YAAY,CAAC,GAAM,kBAAqB,YAAoB,CAAC,GAAM,kBAAqB,QAAQ,CAAC,CAAC;AAAA,IACrQ;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,eAAe,CAAC;AAAA,MAC7B,WAAW,SAAS,2BAA2B,IAAI,KAAK;AACtD,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,kBAAkB,GAAG,UAAU;AAC9C,UAAG,YAAY,uBAAsB,CAAC;AACtC,UAAG,YAAY,YAAY,CAAC;AAC5B,UAAG,YAAY,YAAoB,CAAC;AAAA,QACtC;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,mBAAmB,GAAG;AACvE,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,WAAW;AAC5D,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,mBAAmB;AACpE,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,2BAA2B;AAAA,QAC9E;AAAA,MACF;AAAA,MACA,UAAU;AAAA,MACV,cAAc,SAAS,kCAAkC,IAAI,KAAK;AAChE,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,0BAA0B,IAAI,KAAK,QAAQ,EAAE,wBAAwB,IAAI,KAAK,MAAM,EAAE,wBAAwB,IAAI,UAAU,EAAE,wBAAwB,CAAC,CAAC,IAAI,KAAK,UAAU,IAAI,YAAY,EAAE,2BAA2B,IAAI,QAAQ,EAAE,iCAAiC,IAAI,aAAa,EAAE,6BAA6B,CAAC,IAAI,QAAQ,EAAE,gCAAgC,CAAC,IAAI,aAAa;AAAA,QACzY;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,MAAM;AAAA,QACN,YAAY;AAAA,QACZ,eAAe;AAAA,QACf,iBAAiB;AAAA,QACjB,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,UAAU;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,sBAAyB,mBAAmB;AAAA,MAC1D,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,yBAAyB,GAAG,OAAO,GAAG,CAAC,GAAG,qBAAqB,GAAG,CAAC,UAAU,WAAW,GAAG,UAAU,GAAG,CAAC,GAAG,qBAAqB,GAAG,CAAC,GAAG,0BAA0B,GAAG,cAAc,eAAe,YAAY,uBAAuB,oBAAoB,sBAAsB,cAAc,SAAS,kBAAkB,GAAG,CAAC,GAAG,oBAAoB,GAAG,CAAC,GAAG,qBAAqB,GAAG,CAAC,GAAG,0BAA0B,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,GAAG,cAAc,SAAS,YAAY,UAAU,GAAG,CAAC,GAAG,UAAU,WAAW,QAAQ,UAAU,GAAG,CAAC,GAAG,yBAAyB,GAAG,CAAC,GAAG,UAAU,QAAQ,UAAU,GAAG,CAAC,GAAG,WAAW,QAAQ,cAAc,oBAAoB,eAAe,UAAU,CAAC;AAAA,MACzqB,UAAU,SAAS,8BAA8B,IAAI,KAAK;AACxD,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,UAAG,WAAW,SAAS,SAAS,mDAAmD,QAAQ;AACzF,mBAAO,IAAI,YAAY,MAAM;AAAA,UAC/B,CAAC;AACD,UAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,UAAG,UAAU,GAAG,aAAa,CAAC;AAC9B,UAAG,aAAa;AAChB,UAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,UAAG,WAAW,GAAG,6CAA6C,GAAG,IAAI,KAAK,CAAC,EAAE,GAAG,6CAA6C,GAAG,GAAG,QAAQ,CAAC;AAC5I,UAAG,aAAa;AAChB,UAAG,WAAW,GAAG,6CAA6C,GAAG,GAAG,OAAO,CAAC;AAC5E,UAAG,aAAa;AAChB,UAAG,WAAW,GAAG,6CAA6C,GAAG,GAAG,OAAO,CAAC;AAAA,QAC9E;AACA,YAAI,KAAK,GAAG;AACV,UAAG,UAAU,CAAC;AACd,UAAG,YAAY,aAAa,EAAE,IAAI,cAAc,CAAC,IAAI,aAAa;AAClE,UAAG,WAAW,YAAY,IAAI,QAAQ;AACtC,UAAG,UAAU,CAAC;AACd,UAAG,cAAc,IAAI,UAAU,IAAI,EAAE;AACrC,UAAG,UAAU;AACb,UAAG,cAAc,CAAC,IAAI,UAAU,IAAI,EAAE;AACtC,UAAG,UAAU;AACb,UAAG,cAAc,IAAI,gBAAgB,CAAC,IAAI,eAAe,IAAI,EAAE;AAC/D,UAAG,UAAU;AACb,UAAG,cAAc,IAAI,eAAe,IAAI,EAAE;AAAA,QAC5C;AAAA,MACF;AAAA,MACA,cAAc,CAAC,uBAAsB,cAAiB,YAAe,kBAAkB,cAAiB,iBAAiB,gBAAmB,mBAAmB,qBAAwB,aAAa;AAAA,MACpM,eAAe;AAAA,MACf,MAAM;AAAA,QACJ,WAAW,CAAC,kBAAkB;AAAA,MAChC;AAAA,MACA,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,aAAa,CAAC,GAAG,qBAAqB,WAAW,iBAAiB,MAAM;AACpF,WAAW,CAAC,aAAa,CAAC,GAAG,qBAAqB,WAAW,mBAAmB,MAAM;AACtF,WAAW,CAAC,aAAa,CAAC,GAAG,qBAAqB,WAAW,oBAAoB,MAAM;AACvF,WAAW,CAAC,aAAa,CAAC,GAAG,qBAAqB,WAAW,eAAe,MAAM;AAAA,CACjF,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,YAAY,CAAC,kBAAkB;AAAA,MAC/B,MAAM;AAAA,QACJ,kCAAkC;AAAA,QAClC,gCAAgC;AAAA,QAChC,gCAAgC;AAAA,QAChC,gCAAgC;AAAA,QAChC,mCAAmC;AAAA,QACnC,yCAAyC;AAAA,QACzC,qCAAqC;AAAA,QACrC,wCAAwC;AAAA,MAC1C;AAAA,MACA,YAAY;AAAA,MACZ,SAAS,CAAC,cAAc,cAAc,gBAAgB,mBAAmB;AAAA,MACzE,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,UAAU;AAAA,IACnB,CAAC;AAAA,IACD,0BAA0B,CAAC;AAAA,MACzB,MAAM;AAAA,MACN,MAAM,CAAC,UAAkB;AAAA,IAC3B,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,8BAAN,MAAM,6BAA4B;AAAA,EAChC,IAAI,MAAM;AACR,WAAO,KAAK,OAAO,KAAK,KAAK,MAAM;AAAA,EACrC;AAAA,EACA,YAAY,MAAM,gBAAgB,YAAY,oBAAoB,QAAQ;AACxE,SAAK,OAAO;AACZ,SAAK,iBAAiB;AACtB,SAAK,aAAa;AAClB,SAAK,qBAAqB;AAC1B,SAAK,SAAS;AACd,SAAK,WAAW,IAAI,QAAQ;AAC5B,SAAK,WAAW;AAChB,SAAK,aAAa;AAClB,SAAK,UAAU;AAAA,MACb,YAAY;AAAA,MACZ,gBAAgB;AAAA,MAChB,wBAAwB;AAAA,MACxB,qBAAqB;AAAA,IACvB;AACA,SAAK,WAAW;AAChB,SAAK,mBAAmB;AACxB,SAAK,OAAO;AACZ,SAAK,uBAAuB;AAAA,MAC1B,OAAO;AAAA,MACP,cAAc;AAAA,MACd,aAAa;AAAA,MACb,UAAU;AAAA,IACZ;AACA,SAAK,cAAc,CAAC;AACpB,SAAK,oBAAoB,CAAC,QAAQ,iBAAiB;AACjD,aAAO,UAAU,CAAC,eAAe,aAAa;AAAA,IAChD;AACA,QAAI,QAAQ;AACV,WAAK,OAAO,OAAO,KAAK,UAAU,KAAK,QAAQ,GAAG,OAAO,OAAK,aAAa,aAAa,CAAC,EAAE,UAAU,MAAM;AACzG,aAAK,mBAAmB;AAAA,MAC1B,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,WAAW;AACT,SAAK,cAAc,KAAK,aAAa;AAAA,EACvC;AAAA,EACA,qBAAqB;AACnB,SAAK,yBAAyB;AAC9B,SAAK,eAAe,MAAM,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,SAAO;AACxE,WAAK,OAAO;AACZ,WAAK,KAAK,aAAa;AAAA,IACzB,CAAC;AAAA,EACH;AAAA,EACA,kBAAkB;AAChB,QAAI,CAAC,KAAK,gBAAgB,KAAK,cAAc;AAC3C,WAAK,mBAAmB;AAAA,IAC1B;AAAA,EACF;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,QAAQ,MAAM,GAAG;AACnB,WAAK,KAAK,aAAa;AAAA,IACzB;AACA,QAAI,QAAQ,YAAY;AACtB,WAAK,iBAAiB,KAAK,UAAU;AAAA,IACvC;AAAA,EACF;AAAA,EACA,YAAY,GAAG;AACb,QAAI,KAAK,KAAK,UAAU;AACtB,QAAE,gBAAgB;AAClB,QAAE,eAAe;AAAA,IACnB;AACA,QAAI,KAAK,gBAAgB,CAAC,KAAK,cAAc;AAC3C,WAAK,eAAe,eAAe,KAAK,IAAI;AAAA,IAE9C,OAAO;AACL,UAAI,CAAC,KAAK,WAAY,MAAK,eAAe,0BAA0B,IAAI;AACxE,UAAI,KAAK,QAAS,MAAK,gBAAgB;AAAA,IACzC;AAAA,EACF;AAAA,EACA,IAAI,eAAe;AACjB,WAAO,eAAe,aAAa,KAAK,KAAK,SAAS;AAAA,EACxD;AAAA,EACA,IAAI,eAAe;AACjB,QAAI,SAAS,KAAK,KAAK,WAAW,KAAK,OAAK;AAC1C,aAAO,CAAC,EAAE;AAAA,IACZ,CAAC;AACD,WAAO,UAAU;AAAA,EACnB;AAAA,EACA,IAAI,gBAAgB;AAClB,WAAO,KAAK,QAAQ,gBAAgB,eAAe,KAAK,KAAK,UAAU;AAAA,EACzE;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,iBAAiB,OAAO;AACtB,SAAK,aAAa;AAClB,SAAK,KAAK,aAAa;AACvB,QAAI,KAAK,YAAY;AACnB,UAAI,CAAC,KAAK,KAAK,QAAQ;AACrB,aAAK,KAAK,SAAS;AAAA,MACrB;AAAA,IACF;AACA,SAAK,cAAc,KAAK,aAAa;AACrC,SAAK,KAAK,aAAa;AAAA,EACzB;AAAA,EACA,qBAAqB;AACnB,QAAI,KAAK,gBAAgB,CAAC,KAAK,oBAAoB,CAAC,KAAK,4BAA4B,CAAC,KAAK,UAAU,CAAC,KAAK,OAAO,WAAW;AAC3H;AAAA,IACF;AACA,YAAQ,QAAQ,EAAE,KAAK,MAAM;AAC3B,YAAM,iBAAiB,KAAK,eAAe;AAC3C,UAAI,KAAK,eAAe,gBAAgB;AACtC,aAAK,iBAAiB,cAAc;AAAA,MACtC;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,iBAAiB;AACf,UAAM,kBAAkB,KAAK,aAAa,KAAK,MAAM;AACrD,WAAO,KAAK,cAAc,gBAAgB,KAAK,UAAU,KAAK,KAAK,sBAAsB,gBAAgB,KAAK,kBAAkB,KAAK,KAAK,iBAAiB,KAAK,eAAe,KAAK,KAAK,yBAAyB,KAAK,eAAe;AAAA,EACxO;AAAA,EACA,aAAa,QAAQ;AACnB,WAAO,UAAQ,OAAO,SAAS,KAAK,SAAS,KAAK,KAAK,WAAW,2BAA2B,KAAK,oBAAoB;AAAA,EACxH;AAAA,EACA,6BAA6B,KAAK;AAChC,QAAI,eAAe,aAAa,KAAK,QAAQ,GAAG;AAC9C,WAAK,SAAS,QAAQ,OAAK;AACzB,UAAE,6BAA6B,GAAG;AAAA,MACpC,CAAC;AAAA,IACH,OAAO;AACL,WAAK,iBAAiB,QAAQ,KAAK,GAAG;AAAA,IACxC;AAAA,EACF;AAAA,EACA,IAAI,UAAU;AACZ,WAAO,CAAC,KAAK,KAAK,aAAa,CAAC,KAAK,gBAAgB,KAAK,iBAAiB,gBAAgB,SAAS,KAAK,KAAK,IAAI;AAAA,EACpH;AAAA,EACA,IAAI,UAAU;AACZ,WAAO,KAAK,YAAY,KAAK,SAAS,gBAAgB,eAAe,KAAK,KAAK,IAAI,KAAK,gBAAgB,eAAe,KAAK,KAAK,QAAQ;AAAA,EAC3I;AAAA,EACA,2BAA2B;AACzB,QAAI,eAAe,aAAa,KAAK,QAAQ,GAAG;AAC9C,YAAM,6BAA6B,KAAK;AACxC,YAAM,UAAU,2BAA2B;AAC3C,YAAM,mBAAmB,MAAM,GAAG,CAAC,SAAS,GAAG,2BAA2B,IAAI,UAAQ,KAAK,SAAS,CAAC,CAAC;AACtG,cAAQ;AAAA,QAAK,UAAU,0BAA0B;AAAA,QAAG,UAAU,MAAM,gBAAgB;AAAA,QAAG,UAAU,IAAI;AAAA;AAAA,QAErG,IAAI,MAAM,2BAA2B,KAAK,OAAK,EAAE,UAAU,CAAC;AAAA,QAAG,UAAU,KAAK,QAAQ;AAAA,MAAC,EAAE,UAAU,cAAY;AAC7G,YAAI,YAAY,KAAK,YAAY;AAC/B,cAAI,CAAC,KAAK,cAAc;AACtB,iBAAK,iBAAiB,QAAQ;AAAA,UAChC,OAAO;AAEL,kBAAM,iBAAiB,KAAK,eAAe;AAC3C,iBAAK,iBAAiB,kBAAkB,QAAQ;AAAA,UAClD;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,yBAAyB,OAAO;AAC9B,QAAI,eAAe,aAAa,KAAK,QAAQ,GAAG;AAC9C,WAAK,SAAS,QAAQ,OAAK;AACzB,YAAI,CAAC,EAAE,aAAc,GAAE,gBAAgB,KAAK;AAAA,MAC9C,CAAC;AACD,WAAK,KAAK,SAAS;AACnB,WAAK,KAAK,aAAa;AAAA,IACzB;AAAA,EACF;AAAA,EACA,eAAe;AACb,WAAO;AAAA,MACL,CAAC,GAAG,KAAK,QAAQ,sBAAsB,EAAE,GAAG,KAAK;AAAA,MACjD,CAAC,GAAG,KAAK,QAAQ,cAAc,EAAE,GAAG,CAAC,KAAK;AAAA,MAC1C,CAAC,GAAG,KAAK,QAAQ,mBAAmB,EAAE,GAAG;AAAA,MACzC,CAAC,gBAAgB,GAAG,CAAC,KAAK,KAAK;AAAA,MAC/B,CAAC,QAAQ,GAAG,KAAK;AAAA,MACjB,CAAC,MAAM,GAAG,CAAC,KAAK;AAAA,IAClB;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,QAAI,CAAC,gBAAgB,SAAS,KAAK,KAAK,SAAS,GAAG;AAClD,WAAK,QAAQ,cAAc,KAAK,KAAK,IAAI;AAAA,IAC3C,OAAO;AACL,WAAK,QAAQ,SAAS,CAAC,KAAK,KAAK,IAAI,GAAG;AAAA,QACtC,aAAa,KAAK,KAAK,WAAW;AAAA,QAClC,UAAU,KAAK,KAAK,WAAW;AAAA,QAC/B,qBAAqB,KAAK,KAAK,WAAW;AAAA,QAC1C,kBAAkB,KAAK,KAAK,WAAW;AAAA,QACvC,oBAAoB,KAAK,KAAK,WAAW;AAAA,QACzC,YAAY,KAAK,KAAK,WAAW;AAAA,QACjC,OAAO,KAAK,KAAK,WAAW;AAAA,MAC9B,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,oCAAoC,mBAAmB;AAC1E,aAAO,KAAK,qBAAqB,8BAAgC,kBAAqB,iBAAiB,GAAM,kBAAkB,cAAc,GAAM,kBAAqB,YAAY,CAAC,GAAM,kBAAqB,YAAoB,CAAC,GAAM,kBAAqB,QAAQ,CAAC,CAAC;AAAA,IAC5Q;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,uBAAuB,CAAC;AAAA,MACrC,gBAAgB,SAAS,2CAA2C,IAAI,KAAK,UAAU;AACrF,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,UAAU,sBAAsB,CAAC;AAAA,QACrD;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,WAAW;AAAA,QAC9D;AAAA,MACF;AAAA,MACA,WAAW,SAAS,kCAAkC,IAAI,KAAK;AAC7D,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,kBAAkB,GAAG,UAAU;AAC9C,UAAG,YAAY,YAAY,CAAC;AAC5B,UAAG,YAAY,YAAoB,CAAC;AAAA,QACtC;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,mBAAmB,GAAG;AACvE,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,mBAAmB;AACpE,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,2BAA2B;AAAA,QAC9E;AAAA,MACF;AAAA,MACA,UAAU;AAAA,MACV,cAAc,SAAS,yCAAyC,IAAI,KAAK;AACvE,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,gCAAgC,CAAC,CAAC,IAAI,KAAK,UAAU,IAAI,YAAY,EAAE,gCAAgC,CAAC,CAAC,IAAI,KAAK,MAAM,EAAE,mCAAmC,IAAI,QAAQ,EAAE,yBAAyB,IAAI,UAAU,EAAE,kBAAkB,IAAI,QAAQ,OAAO,EAAE,iBAAiB,IAAI,QAAQ,MAAM,EAAE,oBAAoB,IAAI,QAAQ,SAAS;AAAA,QAC1V;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,MAAM;AAAA,QACN,YAAY;AAAA,QACZ,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,sBAAyB,mBAAmB;AAAA,MAC1D,oBAAoBR;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,mCAAmC,GAAG,CAAC,GAAG,8BAA8B,GAAG,OAAO,GAAG,CAAC,GAAG,8BAA8B,GAAG,aAAa,GAAG,CAAC,GAAG,uCAAuC,GAAG,CAAC,GAAG,kCAAkC,GAAG,cAAc,eAAe,YAAY,uBAAuB,oBAAoB,sBAAsB,cAAc,SAAS,kBAAkB,GAAG,CAAC,GAAG,4BAA4B,GAAG,CAAC,GAAG,6BAA6B,GAAG,CAAC,GAAG,qBAAqB,GAAG,CAAC,GAAG,4BAA4B,GAAG,CAAC,GAAG,sBAAsB,GAAG,WAAW,GAAG,CAAC,GAAG,mCAAmC,GAAG,SAAS,GAAG,CAAC,GAAG,kCAAkC,GAAG,SAAS,cAAc,eAAe,YAAY,uBAAuB,oBAAoB,sBAAsB,cAAc,SAAS,kBAAkB,GAAG,CAAC,GAAG,eAAe,GAAG,CAAC,GAAG,cAAc,iBAAiB,SAAS,YAAY,UAAU,GAAG,CAAC,GAAG,UAAU,WAAW,QAAQ,UAAU,GAAG,CAAC,GAAG,yBAAyB,GAAG,CAAC,GAAG,6BAA6B,CAAC;AAAA,MAC9/B,UAAU,SAAS,qCAAqC,IAAI,KAAK;AAC/D,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,WAAW,GAAG,oDAAoD,GAAG,GAAG,OAAO,CAAC;AACnF,UAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,UAAG,WAAW,SAAS,SAAS,0DAA0D,QAAQ;AAChG,mBAAO,IAAI,YAAY,MAAM;AAAA,UAC/B,CAAC;AACD,UAAG,WAAW,GAAG,oDAAoD,GAAG,GAAG,OAAO,CAAC;AACnF,UAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,UAAG,WAAW,GAAG,oDAAoD,GAAG,IAAI,KAAK,CAAC,EAAE,GAAG,oDAAoD,GAAG,GAAG,QAAQ,CAAC;AAC1J,UAAG,aAAa;AAChB,UAAG,WAAW,GAAG,oDAAoD,GAAG,GAAG,OAAO,CAAC;AACnF,UAAG,aAAa;AAChB,UAAG,WAAW,GAAG,oDAAoD,GAAG,GAAG,OAAO,CAAC;AAAA,QACrF;AACA,YAAI,KAAK,GAAG;AACV,UAAG,cAAc,IAAI,gBAAgB,IAAI,EAAE;AAC3C,UAAG,UAAU,CAAC;AACd,UAAG,cAAc,IAAI,WAAW,IAAI,EAAE;AACtC,UAAG,UAAU,CAAC;AACd,UAAG,cAAc,IAAI,UAAU,IAAI,EAAE;AACrC,UAAG,UAAU;AACb,UAAG,cAAc,CAAC,IAAI,UAAU,IAAI,EAAE;AACtC,UAAG,UAAU;AACb,UAAG,cAAc,IAAI,gBAAgB,CAAC,IAAI,eAAe,IAAI,EAAE;AAC/D,UAAG,UAAU;AACb,UAAG,cAAc,IAAI,eAAe,IAAI,EAAE;AAAA,QAC5C;AAAA,MACF;AAAA,MACA,cAAc,CAAC,SAAS,gBAAqB,kBAAkB,cAAiB,YAAe,kBAAkB,gBAAmB,mBAAmB,cAAiB,iBAAiB,qBAAwB,aAAa;AAAA,MAC9N,eAAe;AAAA,MACf,MAAM;AAAA,QACJ,WAAW,CAAC,kBAAkB;AAAA,MAChC;AAAA,MACA,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,6BAA6B,CAAC;AAAA,IACpG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,YAAY,CAAC,kBAAkB;AAAA,MAC/B,MAAM;AAAA,QACJ,wCAAwC;AAAA,QACxC,wCAAwC;AAAA,QACxC,2CAA2C;AAAA,QAC3C,iCAAiC;AAAA,QACjC,0BAA0B;AAAA,QAC1B,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,YAAY;AAAA,MACZ,SAAS,CAAC,SAAS,gBAAgB,cAAc,gBAAgB,cAAc,mBAAmB;AAAA,MAClG,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,sBAAsB;AAAA,QAC3B,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,UAAU;AAAA,IACnB,CAAC;AAAA,IACD,0BAA0B,CAAC;AAAA,MACzB,MAAM;AAAA,MACN,MAAM,CAAC,UAAkB;AAAA,IAC3B,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAMU,2BAA0B;AAAA,EAAC,aAAa;AAAA,EAAU,aAAa;AAAA,EAAO,aAAa;AAAA;AAAA,EAEzF,aAAa;AAAA;AAEb;AACA,IAAM,6BAAN,MAAM,4BAA2B;AAAA,EAC/B,IAAI,MAAM;AACR,WAAO,KAAK,OAAO,KAAK,KAAK,MAAM;AAAA,EACrC;AAAA,EACA,YAAY,MAAM,gBAAgB,YAAY,oBAAoB,QAAQ;AACxE,SAAK,OAAO;AACZ,SAAK,iBAAiB;AACtB,SAAK,aAAa;AAClB,SAAK,qBAAqB;AAC1B,SAAK,SAAS;AACd,SAAK,WAAW,IAAI,QAAQ;AAC5B,SAAK,aAAa;AAClB,SAAK,kBAAkB;AACvB,SAAK,UAAU;AAAA,MACb,YAAY;AAAA,MACZ,gBAAgB;AAAA,MAChB,wBAAwB;AAAA,MACxB,qBAAqB;AAAA,IACvB;AACA,SAAK,WAAW;AAChB,SAAK,QAAQ,IAAI,QAAQ;AACzB,SAAK,mBAAmB;AACxB,SAAK,uBAAuB;AAAA,MAC1B,OAAO;AAAA,MACP,cAAc;AAAA,MACd,aAAa;AAAA,MACb,UAAU;AAAA,IACZ;AACA,SAAK,WAAW;AAChB,SAAK,eAAe;AACpB,SAAK,mBAAmBA;AACxB,SAAK,OAAO;AACZ,QAAI,QAAQ;AACV,WAAK,OAAO,OAAO,KAAK,UAAU,KAAK,QAAQ,GAAG,OAAO,OAAK,aAAa,aAAa,CAAC,EAAE,UAAU,MAAM;AACzG,YAAI,CAAC,KAAK,cAAc;AACtB,eAAK,mBAAmB;AAAA,QAC1B;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,WAAW;AACT,SAAK,MAAM,KAAK,aAAa,GAAG,GAAG,qBAAqB,GAAG,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,SAAO;AACpG,WAAK,KAAK,SAAS;AACnB,WAAK,KAAK,aAAa;AAAA,IACzB,CAAC;AACD,SAAK,eAAe,MAAM,UAAU,UAAQ;AAC1C,UAAI,gBAAgB,SAAS,IAAI,KAAK,KAAK,SAAS,MAAM;AACxD,aAAK,OAAO;AACZ,aAAK,KAAK,aAAa;AAAA,MACzB;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,qBAAqB;AACnB,SAAK,yBAAyB;AAAA,EAChC;AAAA,EACA,kBAAkB;AAChB,QAAI,CAAC,KAAK,cAAc;AACtB,WAAK,mBAAmB;AAAA,IAC1B;AAAA,EACF;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,QAAQ,MAAM,GAAG;AACnB,WAAK,KAAK,aAAa;AAAA,IACzB;AAAA,EACF;AAAA,EACA,YAAY,GAAG;AACb,QAAI,KAAK,KAAK,UAAU;AACtB,QAAE,gBAAgB;AAClB,QAAE,eAAe;AAAA,IACnB;AACA,QAAI,CAAC,KAAK,gBAAgB,CAAC,KAAK,cAAc;AAC5C,WAAK,eAAe,0BAA0B,IAAI;AAClD,UAAI,KAAK,QAAS,MAAK,gBAAgB;AAAA,IACzC;AAAA,EACF;AAAA,EACA,IAAI,eAAe;AACjB,WAAO,eAAe,aAAa,KAAK,KAAK,SAAS;AAAA,EACxD;AAAA,EACA,IAAI,eAAe;AACjB,QAAI,SAAS,KAAK,KAAK,WAAW,KAAK,OAAK;AAC1C,aAAO,CAAC,EAAE;AAAA,IACZ,CAAC;AACD,WAAO,UAAU;AAAA,EACnB;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,iBAAiB,OAAO;AACtB,SAAK,aAAa;AAClB,SAAK,KAAK,aAAa;AACvB,SAAK,KAAK,aAAa;AAAA,EACzB;AAAA,EACA,qBAAqB;AACnB,QAAI,KAAK,gBAAgB,CAAC,KAAK,oBAAoB,CAAC,KAAK,4BAA4B,CAAC,KAAK,UAAU,CAAC,KAAK,OAAO,WAAW;AAC3H;AAAA,IACF;AACA,YAAQ,QAAQ,EAAE,KAAK,MAAM;AAC3B,YAAM,iBAAiB,KAAK,eAAe;AAC3C,UAAI,KAAK,eAAe,gBAAgB;AACtC,aAAK,iBAAiB,cAAc;AAAA,MACtC;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,iBAAiB;AACf,UAAM,kBAAkB,KAAK,aAAa,KAAK,MAAM;AACrD,WAAO,KAAK,cAAc,gBAAgB,KAAK,UAAU,KAAK,KAAK,sBAAsB,gBAAgB,KAAK,kBAAkB,KAAK,KAAK,iBAAiB,KAAK,eAAe,KAAK,KAAK,yBAAyB,KAAK,eAAe;AAAA,EACxO;AAAA,EACA,aAAa,QAAQ;AACnB,WAAO,UAAQ,OAAO,SAAS,KAAK,SAAS,KAAK,KAAK,WAAW,2BAA2B,KAAK,oBAAoB;AAAA,EACxH;AAAA,EACA,6BAA6B,KAAK;AAChC,QAAI,eAAe,aAAa,KAAK,QAAQ,GAAG;AAC9C,WAAK,SAAS,QAAQ,OAAK;AACzB,UAAE,6BAA6B,GAAG;AAAA,MACpC,CAAC;AAAA,IACH,OAAO;AACL,WAAK,iBAAiB,QAAQ,KAAK,GAAG;AAAA,IACxC;AAAA,EACF;AAAA,EACA,IAAI,UAAU;AACZ,WAAO,CAAC,KAAK,KAAK,aAAa,CAAC,KAAK,gBAAgB,KAAK,iBAAiB,gBAAgB,SAAS,KAAK,KAAK,IAAI;AAAA,EACpH;AAAA,EACA,IAAI,UAAU;AACZ,WAAO,KAAK,SAAS,gBAAgB,eAAe,KAAK,KAAK,IAAI,KAAK,gBAAgB,eAAe,KAAK,KAAK,QAAQ;AAAA,EAC1H;AAAA,EACA,2BAA2B;AACzB,QAAI,eAAe,aAAa,KAAK,QAAQ,KAAK,KAAK,SAAS,SAAS,GAAG;AAC1E,YAAM,6BAA6B,KAAK;AACxC,YAAM,UAAU,2BAA2B;AAC3C,YAAM,mBAAmB,MAAM,GAAG,CAAC,SAAS,GAAG,2BAA2B,IAAI,UAAQ,KAAK,SAAS,CAAC,CAAC;AACtG,cAAQ,KAAK,UAAU,0BAA0B,GAAG,UAAU,MAAM,gBAAgB,GAAG,UAAU,IAAI,GAAG,IAAI,MAAM,2BAA2B,KAAK,OAAK,EAAE,UAAU,CAAC,GAAG,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,cAAY;AACrN,YAAI,YAAY,KAAK,WAAY,KAAI,CAAC,KAAK,aAAc,MAAK,iBAAiB,QAAQ;AAAA,aAAO;AAE5F,gBAAM,iBAAiB,KAAK,eAAe;AAC3C,eAAK,iBAAiB,kBAAkB,QAAQ;AAAA,QAClD;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,QAAI,KAAK,kBAAkB;AAEzB,WAAK,eAAe,KAAK,iBAAiB,cAAc,sBAAsB,EAAE;AAAA,IAClF;AAAA,EACF;AAAA,EACA,iBAAiB,UAAU;AACzB,UAAM,YAAY,iBAAiB,QAAQ;AAC3C,SAAK,WAAW;AAQhB,SAAK,KAAK,aAAa;AAAA,EACzB;AAAA,EACA,yBAAyB,OAAO;AAC9B,QAAI,eAAe,aAAa,KAAK,QAAQ,GAAG;AAC9C,WAAK,SAAS,QAAQ,OAAK;AACzB,UAAE,gBAAgB,KAAK;AAAA,MACzB,CAAC;AACD,WAAK,KAAK,SAAS;AACnB,WAAK,KAAK,aAAa;AAAA,IACzB;AAAA,EACF;AAAA,EACA,cAAc,OAAO;AACnB,SAAK,MAAM,KAAK,KAAK;AAAA,EACvB;AAAA,EACA,kBAAkB;AAChB,QAAI,CAAC,gBAAgB,SAAS,KAAK,KAAK,SAAS,GAAG;AAClD,WAAK,QAAQ,cAAc,KAAK,KAAK,IAAI;AAAA,IAC3C,OAAO;AACL,WAAK,QAAQ,SAAS,CAAC,KAAK,KAAK,IAAI,GAAG;AAAA,QACtC,aAAa,KAAK,KAAK,WAAW;AAAA,QAClC,UAAU,KAAK,KAAK,WAAW;AAAA,QAC/B,qBAAqB,KAAK,KAAK,WAAW;AAAA,QAC1C,kBAAkB,KAAK,KAAK,WAAW;AAAA,QACvC,oBAAoB,KAAK,KAAK,WAAW;AAAA,QACzC,YAAY,KAAK,KAAK,WAAW;AAAA,QACjC,OAAO,KAAK,KAAK,WAAW;AAAA,MAC9B,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,mCAAmC,mBAAmB;AACzE,aAAO,KAAK,qBAAqB,6BAA+B,kBAAqB,iBAAiB,GAAM,kBAAkB,cAAc,GAAM,kBAAqB,YAAY,CAAC,GAAM,kBAAqB,YAAoB,CAAC,GAAM,kBAAqB,QAAQ,CAAC,CAAC;AAAA,IAC3Q;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,sBAAsB,CAAC;AAAA,MACpC,gBAAgB,SAAS,0CAA0C,IAAI,KAAK,UAAU;AACpF,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,UAAU,sBAAsB,CAAC;AAAA,QACrD;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,WAAW;AAAA,QAC9D;AAAA,MACF;AAAA,MACA,WAAW,SAAS,iCAAiC,IAAI,KAAK;AAC5D,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,kBAAkB,GAAG,UAAU;AAC9C,UAAG,YAAY,YAAY,CAAC;AAC5B,UAAG,YAAY,YAAoB,CAAC;AAAA,QACtC;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,mBAAmB,GAAG;AACvE,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,mBAAmB;AACpE,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,2BAA2B;AAAA,QAC9E;AAAA,MACF;AAAA,MACA,UAAU;AAAA,MACV,cAAc,SAAS,wCAAwC,IAAI,KAAK;AACtE,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,cAAc,SAAS,2DAA2D;AAC9F,mBAAO,IAAI,cAAc,IAAI;AAAA,UAC/B,CAAC,EAAE,cAAc,SAAS,2DAA2D;AACnF,mBAAO,IAAI,cAAc,KAAK;AAAA,UAChC,CAAC;AAAA,QACH;AACA,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,yBAAyB,IAAI,UAAU,EAAE,kBAAkB,IAAI,QAAQ,OAAO,EAAE,iBAAiB,IAAI,QAAQ,MAAM,EAAE,oBAAoB,IAAI,QAAQ,SAAS;AAAA,QAC/K;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,YAAY;AAAA,QACZ,iBAAiB;AAAA,QACjB,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,sBAAyB,mBAAmB;AAAA,MAC1D,oBAAoBV;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,UAAU,kBAAkB,GAAG,CAAC,YAAY,EAAE,GAAG,CAAC,oBAAoB,IAAI,GAAG,gCAAgC,GAAG,SAAS,GAAG,CAAC,oBAAoB,SAAS,eAAe,IAAI,GAAG,qCAAqC,GAAG,cAAc,GAAG,CAAC,GAAG,mCAAmC,GAAG,CAAC,uBAAuB,IAAI,iCAAiC,cAAc,GAAG,kBAAkB,gCAAgC,6BAA6B,4BAA4B,2BAA2B,sCAAsC,GAAG,CAAC,oBAAoB,SAAS,eAAe,IAAI,GAAG,qCAAqC,GAAG,SAAS,cAAc,GAAG,CAAC,GAAG,OAAO,OAAO,QAAQ,cAAc,GAAG,CAAC,GAAG,QAAQ,gBAAgB,GAAG,SAAS,cAAc,eAAe,YAAY,uBAAuB,oBAAoB,sBAAsB,cAAc,SAAS,kBAAkB,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,GAAG,CAAC,GAAG,qCAAqC,GAAG,OAAO,GAAG,CAAC,GAAG,UAAU,GAAG,aAAa,SAAS,GAAG,WAAW,YAAY,GAAG,CAAC,GAAG,UAAU,GAAG,aAAa,SAAS,GAAG,cAAc,cAAc,SAAS,GAAG,CAAC,GAAG,UAAU,UAAU,QAAQ,YAAY,UAAU,GAAG,CAAC,GAAG,YAAY,WAAW,mBAAmB,qBAAqB,gBAAgB,QAAQ,QAAQ,GAAG,CAAC,GAAG,gBAAgB,GAAG,CAAC,GAAG,UAAU,OAAO,UAAU,GAAG,CAAC,GAAG,sBAAsB,GAAG,SAAS,GAAG,CAAC,GAAG,sBAAsB,GAAG,WAAW,CAAC;AAAA,MACn6C,UAAU,SAAS,oCAAoC,IAAI,KAAK;AAC9D,YAAI,KAAK,GAAG;AACV,gBAAM,MAAS,iBAAiB;AAChC,UAAG,gBAAgB;AACnB,UAAG,eAAe,GAAG,OAAO,GAAG,CAAC;AAChC,UAAG,WAAW,GAAG,mDAAmD,GAAG,GAAG,OAAO,CAAC,EAAE,GAAG,mDAAmD,GAAG,GAAG,OAAO,CAAC;AACxJ,UAAG,aAAa;AAChB,UAAG,WAAW,GAAG,mDAAmD,GAAG,GAAG,eAAe,CAAC;AAC1F,UAAG,WAAW,kBAAkB,SAAS,0EAA0E,QAAQ;AACzH,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,iBAAiB,MAAM,CAAC;AAAA,UACpD,CAAC;AACD,UAAG,WAAW,GAAG,mDAAmD,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AAAA,QAC7H;AACA,YAAI,KAAK,GAAG;AACV,gBAAM,YAAe,YAAY,CAAC;AAClC,UAAG,WAAW,WAAc,gBAAgB,GAAGC,MAAK,IAAI,KAAK,MAAM,CAAC;AACpE,UAAG,UAAU,CAAC;AACd,UAAG,cAAc,IAAI,UAAU,IAAI,EAAE;AACrC,UAAG,UAAU;AACb,UAAG,cAAc,CAAC,IAAI,UAAU,IAAI,EAAE;AACtC,UAAG,UAAU;AACb,UAAG,WAAW,gCAAgC,IAAI,gBAAgB,EAAE,6BAA6B,SAAS,EAAE,4BAA4B,IAAI,YAAY,EAAE,2BAA2B,IAAI,KAAK,UAAU,CAAC,IAAI,YAAY,EAAE,wCAAwC,mBAAmB;AAAA,QACxR;AAAA,MACF;AAAA,MACA,cAAc,CAAC,SAAS,kBAAuB,qBAAqB,cAAiB,YAAe,kBAAkB,kBAAkB,eAAoB,qBAA0B,kBAAkB,gBAAmB,mBAAmB,gBAAqB,gBAAgB;AAAA,MACnR,eAAe;AAAA,MACf,MAAM;AAAA,QACJ,WAAW,CAAC,WAAW;AAAA,MACzB;AAAA,MACA,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,aAAa,CAAC,GAAG,2BAA2B,WAAW,mBAAmB,MAAM;AAAA,CAC3F,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,4BAA4B,CAAC;AAAA,IACnG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,MAAM;AAAA,QACJ,iCAAiC;AAAA,QACjC,0BAA0B;AAAA,QAC1B,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,QAC5B,gBAAgB;AAAA,QAChB,gBAAgB;AAAA,MAClB;AAAA,MACA,YAAY,CAAC,WAAW;AAAA,MACxB,YAAY;AAAA,MACZ,SAAS,CAAC,SAAS,kBAAkB,cAAc,kBAAkB,eAAe,gBAAgB,cAAc;AAAA,MAClH,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,sBAAsB;AAAA,QAC3B,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,UAAU;AAAA,IACnB,CAAC;AAAA,IACD,0BAA0B,CAAC;AAAA,MACzB,MAAM;AAAA,MACN,MAAM,CAAC,UAAkB;AAAA,IAC3B,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B,OAAO;AACL,SAAK,OAAO,SAAS,+BAA+B,mBAAmB;AACrE,aAAO,KAAK,qBAAqB,yBAAwB;AAAA,IAC3D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,iBAAiB,EAAE,CAAC;AAAA,MACrC,UAAU,CAAC,eAAe;AAAA,MAC1B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,SAAS,sBAAsB,uBAAuB,wBAAwB;AAC5E,SAAO,wBAAwB,wBAAwB;AACzD;AACA,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,IAAI,aAAa;AACf,WAAO,KAAK,iBAAiB,KAAK;AAAA,EACpC;AAAA,EACA,YAAY,SAAS,UAAU,MAAM,gBAAgB;AACnD,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,OAAO;AACZ,SAAK,iBAAiB;AACtB,SAAK,WAAW,IAAI,QAAQ;AAC5B,SAAK,WAAW,CAAC;AACjB,SAAK,WAAW;AAChB,SAAK,OAAO,CAAC;AACb,SAAK,OAAO;AACZ,SAAK,WAAW;AAChB,SAAK,aAAa;AAClB,SAAK,kBAAkB;AACvB,SAAK,mBAAmB;AAIxB,SAAK,cAAc;AAKnB,SAAK,UAAU;AAAA,MACb,YAAY;AAAA,MACZ,gBAAgB;AAAA,MAChB,wBAAwB;AAAA,MACxB,qBAAqB;AAAA,IACvB;AACA,SAAK,cAAc,IAAI,aAAa;AACpC,SAAK,eAAe,IAAI,aAAa;AACrC,SAAK,mBAAmB,IAAI,gBAAgB,KAAK,eAAe;AAChE,SAAK,aAAa,MAAM;AACtB,UAAI,CAAC,KAAK,UAAU;AAClB,eAAO;AAAA,MACT;AACA,YAAM,UAAU,KAAK,SAAS,OAAO,OAAK,gBAAgB,eAAe,EAAE,IAAI,KAAK,gBAAgB,eAAe,EAAE,QAAQ,CAAC;AAC9H,aAAO,QAAQ,SAAS;AAAA,IAC1B;AACA,SAAK,iBAAiB,UAAQ;AAC5B,UAAI,CAAC,KAAK,UAAU;AAClB,eAAO;AAAA,MACT;AACA,aAAO,gBAAgB,eAAe,KAAK,IAAI;AAAA,IACjD;AACA,SAAK,qBAAqB,UAAQ;AAChC,UAAI,CAAC,KAAK,UAAU;AAClB,eAAO;AAAA,MACT;AACA,aAAO,CAAC,gBAAgB,eAAe,KAAK,IAAI,KAAK,gBAAgB,eAAe,KAAK,QAAQ;AAAA,IACnG;AAAA,EACF;AAAA,EACA,WAAW;AACT,SAAK,eAAe,yBAAyB,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,UAAQ;AAC5F,WAAK,YAAY,KAAK,KAAK,IAAI;AAC/B,UAAI,eAAe,aAAa,KAAK,iCAAiC,GAAG;AACvE,aAAK,kCAAkC,QAAQ,WAAS;AACtD,gBAAM,6BAA6B,KAAK,GAAG;AAAA,QAC7C,CAAC;AAAA,MACH;AACA,UAAI,eAAe,aAAa,KAAK,gCAAgC,GAAG;AACtE,aAAK,iCAAiC,QAAQ,WAAS;AACrD,gBAAM,6BAA6B,KAAK,GAAG;AAAA,QAC7C,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AACD,SAAK,eAAe,eAAe,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,UAAQ;AAClF,UAAI,eAAe,aAAa,KAAK,iCAAiC,GAAG;AACvE,aAAK,kCAAkC,QAAQ,WAAS;AACtD,cAAI,MAAM,aAAc,OAAM,yBAAyB,SAAS,QAAQ,CAAC,MAAM,KAAK,MAAM;AAAA,QAC5F,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,MAAM;AACR,WAAK,UAAU,KAAK,IAAI;AAAA,IAC1B;AACA,QAAI,iBAAiB;AACnB,WAAK,iBAAiB,KAAK,KAAK,eAAe;AAAA,IACjD;AACA,QAAI,MAAM;AACR,WAAK,eAAe,aAAa,KAAK,IAAI;AAAA,IAC5C;AACA,SAAK,KAAK,aAAa;AAAA,EACzB;AAAA,EACA,UAAU,MAAM;AACd,QAAI,CAAC,eAAe,aAAa,IAAI,GAAG;AACtC,WAAK,WAAW,CAAC;AAAA,IACnB,OAAO;AACL,WAAK,WAAW,KAAK,IAAI,UAAQ;AAC/B,eAAO,KAAK,UAAU,IAAI;AAAA,MAC5B,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,UAAU,MAAM;AACd,QAAI,YAAY,CAAC;AACjB,QAAI,eAAe,aAAa,KAAK,SAAS,GAAG;AAC/C,kBAAY,KAAK,UAAU,IAAI,UAAQ;AACrC,eAAO,KAAK,UAAU,IAAI;AAAA,MAC5B,CAAC;AAAA,IACH;AACA,WAAO;AAAA,MACL,MAAM,KAAK;AAAA,MACX,MAAM,KAAK;AAAA,MACX,UAAU,KAAK;AAAA,MACf,YAAY,KAAK,cAAc;AAAA,MAC/B,QAAQ,KAAK,UAAU;AAAA,MACvB,MAAM,KAAK;AAAA,MACX;AAAA,MACA,KAAK,gBAAgB,MAAM;AAAA,MAC3B,QAAQ,KAAK;AAAA,MACb,YAAY,KAAK;AAAA,MACjB,UAAU,KAAK;AAAA,MACf,WAAW,KAAK;AAAA,MAChB,OAAO,KAAK;AAAA,MACZ,KAAK,KAAK;AAAA,MACV,KAAK,KAAK;AAAA,IACZ;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,SAAK,iBAAiB,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM;AACnE,WAAK,qBAAqB;AAC1B,WAAK,KAAK,aAAa;AAAA,IACzB,CAAC;AAAA,EACH;AAAA,EACA,uBAAuB;AACrB,QAAI,KAAK,iBAAiB;AACxB,UAAI,KAAK,kCAAkC,QAAQ;AACjD,aAAK,kCAAkC,QAAQ,WAAS;AACtD,gBAAM,yBAAyB,KAAK;AAAA,QACtC,CAAC;AAAA,MACH;AAAA,IACF,OAAO;AACL,UAAI,KAAK,iCAAiC,QAAQ;AAChD,aAAK,iCAAiC,QAAQ,WAAS;AACrD,gBAAM,yBAAyB,KAAK;AAAA,QACtC,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAAA,EACA,mBAAmB,iBAAiB;AAClC,SAAK,kBAAkB;AACvB,SAAK,iBAAiB,KAAK,eAAe;AAAA,EAC5C;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,yBAAyB;AACvB,SAAK,kBAAkB,CAAC,KAAK;AAC7B,SAAK,aAAa,KAAK,KAAK,eAAe;AAC3C,SAAK,iBAAiB,KAAK,KAAK,eAAe;AAAA,EACjD;AAAA,EACA,IAAI,UAAU;AACZ,QAAI,CAAC,KAAK,UAAU;AAClB,aAAO;AAAA,IACT;AACA,UAAM,UAAU,KAAK,SAAS,OAAO,OAAK,gBAAgB,eAAe,EAAE,IAAI,KAAK,gBAAgB,eAAe,EAAE,QAAQ,CAAC;AAC9H,WAAO,QAAQ,SAAS;AAAA,EAC1B;AAAA;AAAA,EAEA,SAAS,QAAQ;AACf,QAAI,CAAC,KAAK,SAAS,MAAM,EAAG,MAAK,SAAS,SAAS,KAAK,QAAQ,eAAe,MAAM;AAAA,EACvF;AAAA,EACA,YAAY,QAAQ;AAClB,QAAI,KAAK,SAAS,MAAM,EAAG,MAAK,SAAS,YAAY,KAAK,QAAQ,eAAe,MAAM;AAAA,EACzF;AAAA,EACA,SAAS,QAAQ;AACf,WAAO,KAAK,QAAQ,cAAc,UAAU,SAAS,MAAM;AAAA,EAC7D;AAAA,EACA,gBAAgB,OAAO,OAAO,QAAQ;AACpC,QAAI,OAAO;AACT,UAAI,gBAAgB,SAAS,MAAM,GAAG;AACpC,cAAM,YAAY,QAAQ,WAAW,OAAO,OAAK,CAAC,CAAC,EAAE,UAAU,EAAE,QAAQ,MAAM,GAAG;AAClF,mBAAW,QAAQ,aAAW;AAC5B,kBAAQ,SAAS;AAAA,QACnB,CAAC;AAAA,MACH,OAAO;AACL,cAAM,YAAY,KAAK,UAAU,OAAO,OAAK,CAAC,CAAC,EAAE,UAAU,EAAE,QAAQ,MAAM,GAAG;AAC9E,mBAAW,QAAQ,aAAW;AAC5B,kBAAQ,SAAS;AAAA,QACnB,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,mBAAmB;AAC/D,aAAO,KAAK,qBAAqB,mBAAqB,kBAAqB,UAAU,GAAM,kBAAqB,SAAS,GAAM,kBAAqB,iBAAiB,GAAM,kBAAkB,cAAc,CAAC;AAAA,IAC9M;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,UAAU,CAAC;AAAA,MACxB,gBAAgB,SAAS,gCAAgC,IAAI,KAAK,UAAU;AAC1E,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,UAAUG,MAAK,CAAC;AAClC,UAAG,eAAe,UAAU,wBAAwB,GAAG,WAAW;AAAA,QACpE;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,WAAW,GAAG;AAC/D,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,qBAAqB,GAAG;AAAA,QAC3E;AAAA,MACF;AAAA,MACA,WAAW,SAAS,uBAAuB,IAAI,KAAK;AAClD,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,6BAA6B,CAAC;AAC7C,UAAG,YAAY,4BAA4B,CAAC;AAAA,QAC9C;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,oCAAoC;AACrF,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,mCAAmC;AAAA,QACtF;AAAA,MACF;AAAA,MACA,UAAU;AAAA,MACV,cAAc,SAAS,8BAA8B,IAAI,KAAK;AAC5D,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,UAAU,CAAC,IAAI,eAAe,EAAE,sBAAsB,IAAI,eAAe,EAAE,kBAAkB,IAAI,SAAS,OAAO,EAAE,iBAAiB,IAAI,SAAS,MAAM,EAAE,oBAAoB,IAAI,SAAS,SAAS;AAAA,QACpN;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,MAAM;AAAA,QACN,MAAM;AAAA,QACN,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,iBAAiB;AAAA,QACjB,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,SAAS;AAAA,QACT,eAAe;AAAA,MACjB;AAAA,MACA,SAAS;AAAA,QACP,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA;AAAA,QACjC;AAAA,MAAc,CAAC,GAAM,sBAAyB,mBAAmB;AAAA,MACjE,oBAAoBE;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,oBAAoB,GAAG,SAAS,GAAG,CAAC,GAAG,UAAU,iBAAiB,GAAG,CAAC,iBAAiB,IAAI,GAAG,iBAAiB,GAAG,SAAS,GAAG,CAAC,GAAG,mBAAmB,GAAG,SAAS,GAAG,CAAC,GAAG,WAAW,QAAQ,UAAU,GAAG,CAAC,GAAG,SAAS,WAAW,QAAQ,cAAc,iBAAiB,oBAAoB,eAAe,UAAU,GAAG,CAAC,GAAG,WAAW,QAAQ,cAAc,iBAAiB,oBAAoB,eAAe,UAAU,GAAG,CAAC,GAAG,WAAW,YAAY,QAAQ,UAAU,GAAG,CAAC,GAAG,SAAS,WAAW,YAAY,QAAQ,cAAc,iBAAiB,oBAAoB,eAAe,UAAU,GAAG,CAAC,GAAG,WAAW,YAAY,QAAQ,cAAc,iBAAiB,oBAAoB,eAAe,UAAU,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,GAAG,CAAC,GAAG,qBAAqB,GAAG,CAAC,SAAS,MAAM,UAAU,MAAM,WAAW,aAAa,QAAQ,QAAQ,SAAS,4BAA4B,GAAG,CAAC,KAAK,2JAA2J,GAAG,cAAc,GAAG,CAAC,aAAa,WAAW,aAAa,WAAW,KAAK,mxHAAmxH,QAAQ,OAAO,GAAG,CAAC,GAAG,wBAAwB,yCAAyC,GAAG,SAAS,SAAS,GAAG,CAAC,GAAG,SAAS,CAAC;AAAA,MAClgK,UAAU,SAAS,0BAA0B,IAAI,KAAK;AACpD,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgBD,IAAG;AACtB,UAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,UAAG,WAAW,GAAG,yCAAyC,GAAG,GAAG,OAAO,CAAC;AACxE,UAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,UAAG,WAAW,GAAG,yCAAyC,GAAG,CAAC,EAAE,GAAG,yCAAyC,GAAG,CAAC;AAChH,UAAG,aAAa;AAChB,UAAG,WAAW,GAAG,yCAAyC,GAAG,GAAG,OAAO,CAAC;AACxE,UAAG,aAAa;AAAA,QAClB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,WAAW,IAAI,IAAI;AACjC,UAAG,UAAU;AACb,UAAG,cAAc,IAAI,WAAW,IAAI,EAAE;AACtC,UAAG,UAAU;AACb,UAAG,WAAW,WAAc,gBAAgB,GAAGE,MAAK,IAAI,eAAe,CAAC;AACxE,UAAG,UAAU;AACb,UAAG,cAAc,IAAI,kBAAkB,IAAI,EAAE;AAC7C,UAAG,UAAU;AACb,UAAG,cAAc,CAAC,IAAI,kBAAkB,IAAI,EAAE;AAC9C,UAAG,UAAU;AACb,UAAG,cAAc,IAAI,WAAW,IAAI,aAAa,IAAI,EAAE;AAAA,QACzD;AAAA,MACF;AAAA,MACA,cAAc,CAAC,SAAS,iBAAiB,gBAAgB,kBAAkB,gBAAgB,iBAAiB,qBAAqB,sBAAsB,4BAA4B,6BAA6B,gBAAgB;AAAA,MAChO,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,aAAa,CAAC,GAAG,iBAAiB,WAAW,YAAY,MAAM;AAC3E,WAAW,CAAC,aAAa,CAAC,GAAG,iBAAiB,WAAW,mBAAmB,MAAM;AAClF,WAAW,CAAC,aAAa,CAAC,GAAG,iBAAiB,WAAW,oBAAoB,MAAM;AACnF,WAAW,CAAC,aAAa,CAAC,GAAG,iBAAiB,WAAW,eAAe,MAAM;AAAA,CAC7E,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,MAAM;AAAA,QACJ,kBAAkB;AAAA,QAClB,8BAA8B;AAAA,QAC9B,0BAA0B;AAAA,QAC1B,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,WAAW;AAAA;AAAA,QACX;AAAA,MAAc;AAAA,MACd,YAAY;AAAA,MACZ,SAAS,CAAC,SAAS,iBAAiB,gBAAgB,kBAAkB,gBAAgB,iBAAiB,qBAAqB,sBAAsB,4BAA4B,6BAA6B,gBAAgB;AAAA,MAC3N,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,CAAC,GAAG;AAAA,IACF,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mCAAmC,CAAC;AAAA,MAClC,MAAM;AAAA,MACN,MAAM,CAAC,2BAA2B;AAAA,IACpC,CAAC;AAAA,IACD,kCAAkC,CAAC;AAAA,MACjC,MAAM;AAAA,MACN,MAAM,CAAC,0BAA0B;AAAA,IACnC,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC,wBAAwB;AAAA,QAC7B,QAAQ;AAAA,QACR,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,OAAO;AACL,SAAK,OAAO,SAAS,sBAAsB,mBAAmB;AAC5D,aAAO,KAAK,qBAAqB,gBAAe;AAAA,IAClD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,kBAAkB,sBAAsB,6BAA6B,4BAA4B,sBAAsB;AAAA,MACjI,SAAS,CAAC,kBAAkB,sBAAsB,wBAAwB,6BAA6B,0BAA0B;AAAA,IACnI,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,kBAAkB,sBAAsB,6BAA6B,0BAA0B;AAAA,IAC3G,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,kBAAkB,sBAAsB,6BAA6B,4BAA4B,sBAAsB;AAAA,MACjI,SAAS,CAAC,kBAAkB,sBAAsB,wBAAwB,6BAA6B,0BAA0B;AAAA,IACnI,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["_c0", "TDS_CONFIG_MODULE_NAME", "_c0", "delay", "_c0", "_c0", "_c1", "_c2", "_c3", "_c4", "_c5", "_c6", "_c7", "_c8", "_c9", "_c10", "_c12", "listOfVerticalPositions"]}