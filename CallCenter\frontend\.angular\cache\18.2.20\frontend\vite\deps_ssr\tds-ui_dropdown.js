import { createRequire } from 'module';const require = createRequire(import.meta.url);
import {
  TDSMenuModule,
  TDSMenuService,
  TDSNoAnimationDirective
} from "./chunk-5CVR7W7N.js";
import "./chunk-LICHUDVX.js";
import {
  ConnectionPositionPair,
  Overlay,
  POSITION_MAP,
  TemplatePortal,
  fromEventOutsideAngular,
  slideMotion
} from "./chunk-WKYNVKBH.js";
import "./chunk-NQSZR37A.js";
import {
  ESCAPE,
  hasModifierKey
} from "./chunk-OMWWZ65K.js";
import {
  Directionality,
  Platform
} from "./chunk-6BGFCIZB.js";
import "./chunk-4PCOC6ME.js";
import "./chunk-O2K6NUWL.js";
import {
  NgClass,
  NgStyle
} from "./chunk-D3JV2RY4.js";
import {
  __decorate
} from "./chunk-A2D67SU4.js";
import {
  InputBoolean
} from "./chunk-VVZCKIK2.js";
import "./chunk-PFNSG66E.js";
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Directive,
  ElementRef,
  EventEmitter,
  Injectable,
  Input,
  NgModule,
  NgZone,
  Output,
  Renderer2,
  TemplateRef,
  ViewChild,
  ViewContainerRef,
  ViewEncapsulation$1,
  inject,
  require_cjs,
  require_operators,
  setClassMetadata,
  ɵɵNgOnChangesFeature,
  ɵɵProvidersFeature,
  ɵɵStandaloneFeature,
  ɵɵclassProp,
  ɵɵdefineComponent,
  ɵɵdefineDirective,
  ɵɵdefineInjectable,
  ɵɵdefineInjector,
  ɵɵdefineNgModule,
  ɵɵdirectiveInject,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵinject,
  ɵɵlistener,
  ɵɵloadQuery,
  ɵɵnextContext,
  ɵɵprojection,
  ɵɵprojectionDef,
  ɵɵproperty,
  ɵɵqueryRefresh,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵtemplate,
  ɵɵviewQuery
} from "./chunk-NCYSEW5N.js";
import {
  __toESM
} from "./chunk-NQ4HTGF6.js";

// node_modules/tds-ui/fesm2022/tds-ui-dropdown.mjs
var import_rxjs = __toESM(require_cjs(), 1);
var import_operators = __toESM(require_operators(), 1);
var _c0 = ["*"];
function TDSDropdownMenuComponent_ng_template_0_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = ɵɵgetCurrentView();
    ɵɵelementStart(0, "div", 0);
    ɵɵlistener("@slideMotion.done", function TDSDropdownMenuComponent_ng_template_0_Template_div_animation_slideMotion_done_0_listener($event) {
      ɵɵrestoreView(_r1);
      const ctx_r1 = ɵɵnextContext();
      return ɵɵresetView(ctx_r1.onAnimationEvent($event));
    })("mouseenter", function TDSDropdownMenuComponent_ng_template_0_Template_div_mouseenter_0_listener() {
      ɵɵrestoreView(_r1);
      const ctx_r1 = ɵɵnextContext();
      return ɵɵresetView(ctx_r1.setMouseState(true));
    })("mouseleave", function TDSDropdownMenuComponent_ng_template_0_Template_div_mouseleave_0_listener() {
      ɵɵrestoreView(_r1);
      const ctx_r1 = ɵɵnextContext();
      return ɵɵresetView(ctx_r1.setMouseState(false));
    });
    ɵɵelementStart(1, "div", 1)(2, "div", 2);
    ɵɵprojection(3);
    ɵɵelementEnd()()();
  }
  if (rf & 2) {
    const ctx_r1 = ɵɵnextContext();
    ɵɵclassProp("tds-dropdown-rtl", ctx_r1.dir === "rtl");
    ɵɵproperty("ngClass", ctx_r1.overlayClassName)("ngStyle", ctx_r1.overlayStyle)("@slideMotion", void 0);
  }
}
var listOfPositions$1 = [POSITION_MAP.bottomLeft, POSITION_MAP.bottomRight, POSITION_MAP.topRight, POSITION_MAP.topLeft];
var TDSDropDownDirective = class _TDSDropDownDirective {
  setDropdownMenuValue(key, value) {
    if (this.tdsDropdownMenu) {
      this.tdsDropdownMenu.setValue(key, value);
    }
  }
  constructor(elementRef, overlay, renderer, viewContainerRef, platform) {
    this.elementRef = elementRef;
    this.overlay = overlay;
    this.renderer = renderer;
    this.viewContainerRef = viewContainerRef;
    this.platform = platform;
    this.overlayRef = null;
    this.destroy$ = new import_rxjs.Subject();
    this.positionStrategy = this.overlay.position().flexibleConnectedTo(this.elementRef.nativeElement).withLockedPosition().withTransformOriginOn(".tds-dropdown");
    this.inputVisible$ = new import_rxjs.BehaviorSubject(false);
    this.trigger$ = new import_rxjs.BehaviorSubject("hover");
    this.overlayClose$ = new import_rxjs.Subject();
    this.tdsDropdownMenu = null;
    this.trigger = "hover";
    this.matchWidthElement = null;
    this.backdrop = false;
    this.clickHide = true;
    this.disabled = false;
    this.visible = false;
    this.autoClose = true;
    this.overlayClassName = "";
    this.overlayStyle = {};
    this.placement = "bottomLeft";
    this.visibleChange = new EventEmitter();
    this.tdsDropdownOutsideClick = new EventEmitter();
  }
  ngOnInit() {
  }
  ngAfterViewInit() {
    if (this.tdsDropdownMenu) {
      const nativeElement = this.elementRef.nativeElement;
      const hostMouseState$ = (0, import_rxjs.merge)((0, import_rxjs.fromEvent)(nativeElement, "mouseenter").pipe((0, import_operators.mapTo)(true)), (0, import_rxjs.fromEvent)(nativeElement, "mouseleave").pipe((0, import_operators.mapTo)(false)));
      const menuMouseState$ = this.tdsDropdownMenu.mouseState$;
      const mergedMouseState$ = (0, import_rxjs.merge)(menuMouseState$, hostMouseState$);
      const hostClickState$ = (0, import_rxjs.fromEvent)(nativeElement, "click").pipe((0, import_operators.map)(() => !this.visible));
      const visibleStateByTrigger$ = this.trigger$.pipe((0, import_operators.switchMap)((trigger) => {
        if (trigger === "hover") {
          return mergedMouseState$;
        } else if (trigger === "click") {
          return hostClickState$;
        } else {
          return import_rxjs.EMPTY;
        }
      }));
      const descendantMenuItemClick$ = this.tdsDropdownMenu.descendantMenuItemClick$.pipe((0, import_operators.filter)(() => this.clickHide), (0, import_operators.mapTo)(false));
      const domTriggerVisible$ = (0, import_rxjs.merge)(visibleStateByTrigger$, descendantMenuItemClick$, this.overlayClose$).pipe((0, import_operators.filter)(() => !this.disabled));
      const visible$ = (0, import_rxjs.merge)(this.inputVisible$, domTriggerVisible$);
      (0, import_rxjs.combineLatest)([visible$, this.tdsDropdownMenu.isChildSubMenuOpen$]).pipe((0, import_operators.map)(([visible, sub]) => visible || sub), (0, import_operators.auditTime)(150), (0, import_operators.distinctUntilChanged)(), (0, import_operators.filter)(() => this.platform.isBrowser), (0, import_operators.takeUntil)(this.destroy$)).subscribe((visible) => {
        const element = this.matchWidthElement ? this.matchWidthElement.nativeElement : nativeElement;
        const triggerWidth = element.getBoundingClientRect().width;
        if (this.visible !== visible) {
          this.visibleChange.emit(visible);
        }
        this.visible = visible;
        if (visible) {
          if (!this.overlayRef) {
            this.overlayRef = this.overlay.create({
              positionStrategy: this.positionStrategy,
              minWidth: triggerWidth,
              disposeOnNavigation: true,
              hasBackdrop: this.backdrop && this.trigger === "click",
              scrollStrategy: this.autoClose ? this.overlay.scrollStrategies.close() : this.overlay.scrollStrategies.reposition()
            });
            (0, import_rxjs.merge)(this.overlayRef.backdropClick(), this.overlayRef.detachments(), this.overlayRef.outsidePointerEvents().pipe((0, import_operators.filter)((e) => !this.elementRef.nativeElement.contains(e.target))), this.overlayRef.keydownEvents().pipe((0, import_operators.filter)((e) => e.keyCode === ESCAPE && !hasModifierKey(e)))).pipe((0, import_operators.filter)(() => this.autoClose), (0, import_operators.takeUntil)(this.destroy$)).subscribe(() => {
              this.overlayClose$.next(false);
            });
            (0, import_rxjs.merge)(this.overlayRef.backdropClick(), this.overlayRef.outsidePointerEvents().pipe((0, import_operators.filter)((e) => !this.elementRef.nativeElement.contains(e.target)))).pipe((0, import_operators.takeUntil)(this.destroy$)).subscribe((e) => {
              this.tdsDropdownOutsideClick.next(e);
            });
          } else {
            const overlayConfig = this.overlayRef.getConfig();
            overlayConfig.minWidth = triggerWidth;
          }
          this.positionStrategy.withPositions([POSITION_MAP[this.placement], ...listOfPositions$1]);
          if (!this.portal || this.portal.templateRef !== this.tdsDropdownMenu.templateRef) {
            this.portal = new TemplatePortal(this.tdsDropdownMenu.templateRef, this.viewContainerRef);
          }
          this.overlayRef.attach(this.portal);
        } else {
          if (this.overlayRef) {
            this.overlayRef.detach();
          }
        }
      });
      this.tdsDropdownMenu.animationStateChange$.pipe((0, import_operators.takeUntil)(this.destroy$)).subscribe((event) => {
        if (event.toState === "void") {
          if (this.overlayRef) {
            this.overlayRef.dispose();
          }
          this.overlayRef = null;
        }
      });
    }
  }
  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
    if (this.overlayRef) {
      this.overlayRef.dispose();
      this.overlayRef = null;
    }
  }
  ngOnChanges(changes) {
    const {
      visible,
      disabled,
      overlayClassName,
      overlayStyle,
      trigger,
      backdrop
    } = changes;
    if (trigger) {
      this.trigger$.next(this.trigger);
    }
    if (visible) {
      this.inputVisible$.next(this.visible);
    }
    if (disabled) {
      const nativeElement = this.elementRef.nativeElement;
      if (this.disabled) {
        this.renderer.setAttribute(nativeElement, "disabled", "");
        this.inputVisible$.next(false);
      } else {
        this.renderer.removeAttribute(nativeElement, "disabled");
      }
    }
    if (overlayClassName) {
      this.setDropdownMenuValue("overlayClassName", this.overlayClassName);
    }
    if (overlayStyle) {
      this.setDropdownMenuValue("overlayStyle", this.overlayStyle);
    }
    if (backdrop) {
    }
  }
  static {
    this.ɵfac = function TDSDropDownDirective_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TDSDropDownDirective)(ɵɵdirectiveInject(ElementRef), ɵɵdirectiveInject(Overlay), ɵɵdirectiveInject(Renderer2), ɵɵdirectiveInject(ViewContainerRef), ɵɵdirectiveInject(Platform));
    };
  }
  static {
    this.ɵdir = ɵɵdefineDirective({
      type: _TDSDropDownDirective,
      selectors: [["", "tds-dropdown", ""]],
      hostAttrs: [1, "tds-dropdown-trigger"],
      inputs: {
        tdsDropdownMenu: "tdsDropdownMenu",
        trigger: "trigger",
        matchWidthElement: "matchWidthElement",
        backdrop: "backdrop",
        clickHide: "clickHide",
        disabled: "disabled",
        visible: "visible",
        autoClose: "autoClose",
        overlayClassName: "overlayClassName",
        overlayStyle: "overlayStyle",
        placement: "placement"
      },
      outputs: {
        visibleChange: "visibleChange",
        tdsDropdownOutsideClick: "tdsDropdownOutsideClick"
      },
      exportAs: ["tdsDropdown"],
      standalone: true,
      features: [ɵɵNgOnChangesFeature]
    });
  }
};
__decorate([InputBoolean()], TDSDropDownDirective.prototype, "backdrop", void 0);
__decorate([InputBoolean()], TDSDropDownDirective.prototype, "clickHide", void 0);
__decorate([InputBoolean()], TDSDropDownDirective.prototype, "disabled", void 0);
__decorate([InputBoolean()], TDSDropDownDirective.prototype, "visible", void 0);
__decorate([InputBoolean()], TDSDropDownDirective.prototype, "autoClose", void 0);
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TDSDropDownDirective, [{
    type: Directive,
    args: [{
      selector: "[tds-dropdown]",
      exportAs: "tdsDropdown",
      host: {
        class: "tds-dropdown-trigger"
      },
      standalone: true
    }]
  }], () => [{
    type: ElementRef
  }, {
    type: Overlay
  }, {
    type: Renderer2
  }, {
    type: ViewContainerRef
  }, {
    type: Platform
  }], {
    tdsDropdownMenu: [{
      type: Input
    }],
    trigger: [{
      type: Input
    }],
    matchWidthElement: [{
      type: Input
    }],
    backdrop: [{
      type: Input
    }],
    clickHide: [{
      type: Input
    }],
    disabled: [{
      type: Input
    }],
    visible: [{
      type: Input
    }],
    autoClose: [{
      type: Input
    }],
    overlayClassName: [{
      type: Input
    }],
    overlayStyle: [{
      type: Input
    }],
    placement: [{
      type: Input
    }],
    visibleChange: [{
      type: Output
    }],
    tdsDropdownOutsideClick: [{
      type: Output
    }]
  });
})();
var TDSContextMenuServiceModule = class _TDSContextMenuServiceModule {
  static {
    this.ɵfac = function TDSContextMenuServiceModule_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TDSContextMenuServiceModule)();
    };
  }
  static {
    this.ɵmod = ɵɵdefineNgModule({
      type: _TDSContextMenuServiceModule
    });
  }
  static {
    this.ɵinj = ɵɵdefineInjector({});
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TDSContextMenuServiceModule, [{
    type: NgModule
  }], null, null);
})();
var TDSDropDownADirective = class _TDSDropDownADirective {
  constructor() {
  }
  static {
    this.ɵfac = function TDSDropDownADirective_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TDSDropDownADirective)();
    };
  }
  static {
    this.ɵdir = ɵɵdefineDirective({
      type: _TDSDropDownADirective,
      selectors: [["a", "tds-dropdown", ""]],
      hostAttrs: [1, "tds-dropdown-link"],
      standalone: true
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TDSDropDownADirective, [{
    type: Directive,
    args: [{
      selector: "a[tds-dropdown]",
      host: {
        class: "tds-dropdown-link"
      },
      standalone: true
    }]
  }], () => [], null);
})();
var TDSDropdownButtonDirective = class _TDSDropdownButtonDirective {
  constructor(renderer, elementRef) {
    this.renderer = renderer;
    this.elementRef = elementRef;
  }
  ngAfterViewInit() {
    const parentElement = this.renderer.parentNode(this.elementRef.nativeElement);
  }
  static {
    this.ɵfac = function TDSDropdownButtonDirective_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TDSDropdownButtonDirective)(ɵɵdirectiveInject(Renderer2), ɵɵdirectiveInject(ElementRef));
    };
  }
  static {
    this.ɵdir = ɵɵdefineDirective({
      type: _TDSDropdownButtonDirective,
      selectors: [["", "tds-button", "", "tds-dropdown", ""]],
      standalone: true
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TDSDropdownButtonDirective, [{
    type: Directive,
    args: [{
      selector: "[tds-button][tds-dropdown]",
      standalone: true
    }]
  }], () => [{
    type: Renderer2
  }, {
    type: ElementRef
  }], null);
})();
var TDSDropDownItemDirective = class _TDSDropDownItemDirective {
  /** clear all item selected status except this */
  clickMenuItem(e) {
    if (this.disabled) {
      e.preventDefault();
      e.stopPropagation();
    } else {
      this.TDSMenuService.onDescendantMenuItemClick(this);
    }
  }
  constructor(TDSMenuService2, cdr) {
    this.TDSMenuService = TDSMenuService2;
    this.cdr = cdr;
    this.destroy$ = new import_rxjs.Subject();
    this.selected$ = new import_rxjs.Subject();
    this.disabled = false;
    this.selected = false;
  }
  ngOnInit() {
  }
  ngAfterContentInit() {
  }
  ngOnChanges(changes) {
    if (changes.disabled) {
      this.cdr.markForCheck();
    }
  }
  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
  static {
    this.ɵfac = function TDSDropDownItemDirective_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TDSDropDownItemDirective)(ɵɵdirectiveInject(TDSMenuService), ɵɵdirectiveInject(ChangeDetectorRef));
    };
  }
  static {
    this.ɵdir = ɵɵdefineDirective({
      type: _TDSDropDownItemDirective,
      selectors: [["", "tds-dropdown-item", ""]],
      hostAttrs: [1, "tds-dropdown-item"],
      hostVars: 4,
      hostBindings: function TDSDropDownItemDirective_HostBindings(rf, ctx) {
        if (rf & 1) {
          ɵɵlistener("click", function TDSDropDownItemDirective_click_HostBindingHandler($event) {
            return ctx.clickMenuItem($event);
          });
        }
        if (rf & 2) {
          ɵɵclassProp("tds-dropdown-item-disabled", !!ctx.disabled)("tds-dropdown-item-selected", !!ctx.selected);
        }
      },
      inputs: {
        disabled: "disabled",
        selected: "selected"
      },
      exportAs: ["tdsDropdownItem"],
      standalone: true,
      features: [ɵɵNgOnChangesFeature]
    });
  }
};
__decorate([InputBoolean()], TDSDropDownItemDirective.prototype, "disabled", void 0);
__decorate([InputBoolean()], TDSDropDownItemDirective.prototype, "selected", void 0);
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TDSDropDownItemDirective, [{
    type: Directive,
    args: [{
      selector: "[tds-dropdown-item]",
      exportAs: "tdsDropdownItem",
      host: {
        class: "tds-dropdown-item ",
        "[class.tds-dropdown-item-disabled]": "!!disabled",
        "[class.tds-dropdown-item-selected]": "!!selected",
        "(click)": "clickMenuItem($event)"
      },
      standalone: true
    }]
  }], () => [{
    type: TDSMenuService
  }, {
    type: ChangeDetectorRef
  }], {
    disabled: [{
      type: Input
    }],
    selected: [{
      type: Input
    }]
  });
})();
var TDSDropdownMenuComponent = class _TDSDropdownMenuComponent {
  onAnimationEvent(event) {
    this.animationStateChange$.emit(event);
  }
  setMouseState(visible) {
    this.mouseState$.next(visible);
  }
  setValue(key, value) {
    this[key] = value;
    this.cdr.markForCheck();
  }
  constructor(cdr, elementRef, renderer, viewContainerRef, tdsMenuService, directionality) {
    this.cdr = cdr;
    this.elementRef = elementRef;
    this.renderer = renderer;
    this.viewContainerRef = viewContainerRef;
    this.tdsMenuService = tdsMenuService;
    this.directionality = directionality;
    this.mouseState$ = new import_rxjs.BehaviorSubject(false);
    this.isChildSubMenuOpen$ = this.tdsMenuService.isChildSubMenuOpen$;
    this.descendantMenuItemClick$ = this.tdsMenuService.descendantMenuItemClick$;
    this.animationStateChange$ = new EventEmitter();
    this.overlayClassName = "";
    this.overlayStyle = {};
    this.dir = "ltr";
    this.destroy$ = new import_rxjs.Subject();
    this.noAnimation = inject(TDSNoAnimationDirective, {
      host: true,
      optional: true
    });
  }
  ngOnInit() {
    this.directionality.change?.pipe((0, import_operators.takeUntil)(this.destroy$)).subscribe((direction) => {
      this.dir = direction;
      this.cdr.detectChanges();
    });
    this.dir = this.directionality.value;
  }
  ngAfterContentInit() {
    this.renderer.removeChild(this.renderer.parentNode(this.elementRef.nativeElement), this.elementRef.nativeElement);
  }
  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
  static {
    this.ɵfac = function TDSDropdownMenuComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TDSDropdownMenuComponent)(ɵɵdirectiveInject(ChangeDetectorRef), ɵɵdirectiveInject(ElementRef), ɵɵdirectiveInject(Renderer2), ɵɵdirectiveInject(ViewContainerRef), ɵɵdirectiveInject(TDSMenuService), ɵɵdirectiveInject(Directionality));
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _TDSDropdownMenuComponent,
      selectors: [["tds-dropdown-menu"]],
      viewQuery: function TDSDropdownMenuComponent_Query(rf, ctx) {
        if (rf & 1) {
          ɵɵviewQuery(TemplateRef, 7);
        }
        if (rf & 2) {
          let _t;
          ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.templateRef = _t.first);
        }
      },
      exportAs: ["tdsDropdownMenu"],
      standalone: true,
      features: [ɵɵProvidersFeature([
        TDSMenuService
        // /** menu is inside dropdown-menu component **/
        // {
        //   provide: TDSIsMenuInsideDropDownToken,
        //   useValue: true
        // }
      ]), ɵɵStandaloneFeature],
      ngContentSelectors: _c0,
      decls: 1,
      vars: 0,
      consts: [[1, "tds-dropdown", "min-w-full", "relative", 3, "mouseenter", "mouseleave", "ngClass", "ngStyle"], [1, "tds-dropdown-menu-outer-box"], [1, "tds-dropdown-menu-panel"]],
      template: function TDSDropdownMenuComponent_Template(rf, ctx) {
        if (rf & 1) {
          ɵɵprojectionDef();
          ɵɵtemplate(0, TDSDropdownMenuComponent_ng_template_0_Template, 4, 5, "ng-template");
        }
      },
      dependencies: [NgClass, NgStyle],
      encapsulation: 2,
      data: {
        animation: [slideMotion]
      },
      changeDetection: 0
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TDSDropdownMenuComponent, [{
    type: Component,
    args: [{
      selector: `tds-dropdown-menu`,
      exportAs: `tdsDropdownMenu`,
      animations: [slideMotion],
      providers: [
        TDSMenuService
        // /** menu is inside dropdown-menu component **/
        // {
        //   provide: TDSIsMenuInsideDropDownToken,
        //   useValue: true
        // }
      ],
      template: `
    <ng-template>
      <div 
        class="tds-dropdown  min-w-full relative"
        [class.tds-dropdown-rtl]="dir === 'rtl'"
        [ngClass]="overlayClassName"
        [ngStyle]="overlayStyle"
        @slideMotion
        (@slideMotion.done)="onAnimationEvent($event)"       
        (mouseenter)="setMouseState(true)"
        (mouseleave)="setMouseState(false)"
      >
      <div class="tds-dropdown-menu-outer-box" >
        <div class='tds-dropdown-menu-panel'>
          <ng-content></ng-content>
        </div>        
      </div>
        
      </div>
    </ng-template>
  `,
      preserveWhitespaces: false,
      encapsulation: ViewEncapsulation$1.None,
      changeDetection: ChangeDetectionStrategy.OnPush,
      imports: [NgClass, NgStyle],
      standalone: true
    }]
  }], () => [{
    type: ChangeDetectorRef
  }, {
    type: ElementRef
  }, {
    type: Renderer2
  }, {
    type: ViewContainerRef
  }, {
    type: TDSMenuService
  }, {
    type: Directionality
  }], {
    templateRef: [{
      type: ViewChild,
      args: [TemplateRef, {
        static: true
      }]
    }]
  });
})();
var TDSDropDownModule = class _TDSDropDownModule {
  static {
    this.ɵfac = function TDSDropDownModule_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TDSDropDownModule)();
    };
  }
  static {
    this.ɵmod = ɵɵdefineNgModule({
      type: _TDSDropDownModule,
      imports: [TDSDropDownDirective, TDSDropDownADirective, TDSDropdownMenuComponent, TDSDropdownButtonDirective, TDSDropDownItemDirective, TDSContextMenuServiceModule],
      exports: [TDSMenuModule, TDSDropDownDirective, TDSDropDownADirective, TDSDropdownMenuComponent, TDSDropdownButtonDirective, TDSDropDownItemDirective]
    });
  }
  static {
    this.ɵinj = ɵɵdefineInjector({
      imports: [TDSContextMenuServiceModule, TDSMenuModule]
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TDSDropDownModule, [{
    type: NgModule,
    args: [{
      imports: [TDSDropDownDirective, TDSDropDownADirective, TDSDropdownMenuComponent, TDSDropdownButtonDirective, TDSDropDownItemDirective, TDSContextMenuServiceModule],
      exports: [TDSMenuModule, TDSDropDownDirective, TDSDropDownADirective, TDSDropdownMenuComponent, TDSDropdownButtonDirective, TDSDropDownItemDirective]
    }]
  }], null, null);
})();
var listOfPositions = [new ConnectionPositionPair({
  originX: "start",
  originY: "top"
}, {
  overlayX: "start",
  overlayY: "top"
}), new ConnectionPositionPair({
  originX: "start",
  originY: "top"
}, {
  overlayX: "start",
  overlayY: "bottom"
}), new ConnectionPositionPair({
  originX: "start",
  originY: "top"
}, {
  overlayX: "end",
  overlayY: "bottom"
}), new ConnectionPositionPair({
  originX: "start",
  originY: "top"
}, {
  overlayX: "end",
  overlayY: "top"
})];
var TDSContextMenuService = class _TDSContextMenuService {
  constructor(ngZone, overlay) {
    this.ngZone = ngZone;
    this.overlay = overlay;
    this.overlayRef = null;
    this.closeSubscription = import_rxjs.Subscription.EMPTY;
  }
  create($event, tdsDropdownMenuComponent) {
    this.close(true);
    const {
      x,
      y
    } = $event;
    if ($event instanceof MouseEvent) {
      $event.preventDefault();
    }
    const positionStrategy = this.overlay.position().flexibleConnectedTo({
      x,
      y
    }).withPositions(listOfPositions).withTransformOriginOn(".tds-dropdown");
    this.overlayRef = this.overlay.create({
      positionStrategy,
      disposeOnNavigation: true,
      scrollStrategy: this.overlay.scrollStrategies.close()
    });
    this.closeSubscription = new import_rxjs.Subscription();
    this.closeSubscription.add(tdsDropdownMenuComponent.descendantMenuItemClick$.subscribe(() => this.close()));
    this.closeSubscription.add((0, import_rxjs.merge)(fromEventOutsideAngular(document, "click").pipe(
      (0, import_operators.filter)((event) => !!this.overlayRef && !this.overlayRef.overlayElement.contains(event.target)),
      /** handle firefox contextmenu event **/
      (0, import_operators.filter)((event) => event.button !== 2)
    ), fromEventOutsideAngular(document, "keydown").pipe((0, import_operators.filter)((event) => event.key === "Escape"))).pipe((0, import_operators.first)()).subscribe(() => this.ngZone.run(() => this.close())));
    return this.overlayRef.attach(new TemplatePortal(tdsDropdownMenuComponent.templateRef, tdsDropdownMenuComponent.viewContainerRef));
  }
  close(clear = false) {
    if (this.overlayRef) {
      this.overlayRef.detach();
      if (clear) {
        this.overlayRef.dispose();
      }
      this.overlayRef = null;
      this.closeSubscription.unsubscribe();
    }
  }
  static {
    this.ɵfac = function TDSContextMenuService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TDSContextMenuService)(ɵɵinject(NgZone), ɵɵinject(Overlay));
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _TDSContextMenuService,
      factory: _TDSContextMenuService.ɵfac,
      providedIn: TDSContextMenuServiceModule
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TDSContextMenuService, [{
    type: Injectable,
    args: [{
      providedIn: TDSContextMenuServiceModule
    }]
  }], () => [{
    type: NgZone
  }, {
    type: Overlay
  }], null);
})();
export {
  TDSContextMenuService,
  TDSContextMenuServiceModule,
  TDSDropDownADirective,
  TDSDropDownDirective,
  TDSDropDownItemDirective,
  TDSDropDownModule,
  TDSDropdownButtonDirective,
  TDSDropdownMenuComponent
};
//# sourceMappingURL=tds-ui_dropdown.js.map
