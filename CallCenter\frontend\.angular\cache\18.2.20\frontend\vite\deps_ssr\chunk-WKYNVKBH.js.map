{"version": 3, "sources": ["../../../../../../node_modules/tds-ui/fesm2022/tds-ui-core-animation.mjs", "../../../../../../node_modules/tds-ui/fesm2022/tds-ui-core-util.mjs", "../../../../../../node_modules/tds-ui/fesm2022/tds-ui-core-services.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/portal.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/overlay.mjs", "../../../../../../node_modules/tds-ui/fesm2022/tds-ui-core-overlay.mjs", "../../../../../../node_modules/tds-ui/fesm2022/tds-ui-core-outlet.mjs", "../../../../../../node_modules/tds-ui/fesm2022/tds-ui-core-constants.mjs", "../../../../../../node_modules/@ng-web-apis/common/fesm2022/ng-web-apis-common.mjs", "../../../../../../node_modules/tds-ui/fesm2022/tds-ui-cdk-utils-os.mjs", "../../../../../../node_modules/tds-ui/fesm2022/tds-ui-core-token.mjs", "../../../../../../node_modules/tds-ui/fesm2022/tds-ui-cdk-contants.mjs", "../../../../../../node_modules/tds-ui/fesm2022/tds-ui-core-format.mjs", "../../../../../../node_modules/tds-ui/fesm2022/tds-ui-core-pipes.mjs"], "sourcesContent": ["import { trigger, state, style, transition, animate, query, stagger } from '@angular/animations';\nclass AnimationDuration {\n  static {\n    this.SLOW = '0.3s';\n  } // Modal\n  static {\n    this.BASE = '0.2s';\n  }\n  static {\n    this.FAST = '0.1s';\n  } // Tooltip\n}\nclass AnimationCurves {\n  static {\n    this.EASE_BASE_OUT = 'cubic-bezier(0.7, 0.3, 0.1, 1)';\n  }\n  static {\n    this.EASE_BASE_IN = 'cubic-bezier(0.9, 0, 0.3, 0.7)';\n  }\n  static {\n    this.EASE_OUT = 'cubic-bezier(0.215, 0.61, 0.355, 1)';\n  }\n  static {\n    this.EASE_IN = 'cubic-bezier(0.55, 0.055, 0.675, 0.19)';\n  }\n  static {\n    this.EASE_IN_OUT = 'cubic-bezier(0.645, 0.045, 0.355, 1)';\n  }\n  static {\n    this.EASE_OUT_BACK = 'cubic-bezier(0.12, 0.4, 0.29, 1.46)';\n  }\n  static {\n    this.EASE_IN_BACK = 'cubic-bezier(0.71, -0.46, 0.88, 0.6)';\n  }\n  static {\n    this.EASE_IN_OUT_BACK = 'cubic-bezier(0.71, -0.46, 0.29, 1.46)';\n  }\n  static {\n    this.EASE_OUT_CIRC = 'cubic-bezier(0.08, 0.82, 0.17, 1)';\n  }\n  static {\n    this.EASE_IN_CIRC = 'cubic-bezier(0.6, 0.04, 0.98, 0.34)';\n  }\n  static {\n    this.EASE_IN_OUT_CIRC = 'cubic-bezier(0.78, 0.14, 0.15, 0.86)';\n  }\n  static {\n    this.EASE_OUT_QUINT = 'cubic-bezier(0.23, 1, 0.32, 1)';\n  }\n  static {\n    this.EASE_IN_QUINT = 'cubic-bezier(0.755, 0.05, 0.855, 0.06)';\n  }\n  static {\n    this.EASE_IN_OUT_QUINT = 'cubic-bezier(0.86, 0, 0.07, 1)';\n  }\n}\nconst collapseMotion = trigger('collapseMotion', [state('expanded', style({\n  height: '*',\n  overflow: 'hidden'\n})), state('collapsed', style({\n  height: 0,\n  overflow: 'hidden'\n})), state('hidden', style({\n  height: 0,\n  borderTopWidth: '0',\n  overflow: 'hidden'\n})), transition('expanded => collapsed', animate(`1000ms ${AnimationCurves.EASE_IN_OUT}`)), transition('expanded => hidden', animate(`150ms ${AnimationCurves.EASE_IN_OUT}`)), transition('collapsed => expanded', animate(`150ms ${AnimationCurves.EASE_IN_OUT}`)), transition('hidden => expanded', animate(`150ms ${AnimationCurves.EASE_IN_OUT}`))]);\nconst menuCollapseMotion = trigger('menuCollapseMotion', [state('expanded', style({\n  height: '*',\n  overflow: 'hidden'\n})), state('collapsed', style({\n  height: 0,\n  overflow: 'hidden'\n})), state('hidden', style({\n  height: 0,\n  borderTopWidth: '0',\n  overflow: 'hidden'\n})), transition('expanded => collapsed', animate(`150ms ${AnimationCurves.EASE_IN_OUT}`)), transition('expanded => hidden', animate(`150ms ${AnimationCurves.EASE_IN_OUT}`)), transition('collapsed => expanded', animate(`150ms ${AnimationCurves.EASE_IN_OUT}`)), transition('hidden => expanded', animate(`150ms ${AnimationCurves.EASE_IN_OUT}`))]);\nconst treeCollapseMotion = trigger('treeCollapseMotion', [transition('* => *', [query('tds-tree-node:leave,tds-tree-builtin-node:leave', [style({\n  overflow: 'hidden'\n}), stagger(0, [animate(`150ms ${AnimationCurves.EASE_IN_OUT}`, style({\n  height: 0,\n  opacity: 0,\n  'padding-bottom': 0\n}))])], {\n  optional: true\n}), query('tds-tree-node:enter,tds-tree-builtin-node:enter', [style({\n  overflow: 'hidden',\n  height: 0,\n  opacity: 0,\n  'padding-bottom': 0\n}), stagger(0, [animate(`150ms ${AnimationCurves.EASE_IN_OUT}`, style({\n  overflow: 'hidden',\n  height: '*',\n  opacity: '*',\n  'padding-bottom': '*'\n}))])], {\n  optional: true\n})])]);\nconst fadeMotion = trigger('fadeMotion', [transition(':enter', [style({\n  opacity: 0\n}), animate(`${AnimationDuration.BASE}`, style({\n  opacity: 1\n}))]), transition(':leave', [style({\n  opacity: 1\n}), animate(`${AnimationDuration.BASE}`, style({\n  opacity: 0\n}))])]);\nconst helpMotion = trigger('helpMotion', [transition(':enter', [style({\n  opacity: 0,\n  transform: 'translateY(-5px)'\n}), animate(`${AnimationDuration.SLOW} ${AnimationCurves.EASE_IN_OUT}`, style({\n  opacity: 1,\n  transform: 'translateY(0)'\n}))]), transition(':leave', [style({\n  opacity: 1,\n  transform: 'translateY(0)'\n}), animate(`${AnimationDuration.SLOW} ${AnimationCurves.EASE_IN_OUT}`, style({\n  opacity: 0,\n  transform: 'translateY(-5px)'\n}))])]);\nconst moveUpMotion = trigger('moveUpMotion', [transition('* => enter', [style({\n  transformOrigin: '0 0',\n  transform: 'translateY(-100%)',\n  opacity: 0\n}), animate(`${AnimationDuration.BASE}`, style({\n  transformOrigin: '0 0',\n  transform: 'translateY(0%)',\n  opacity: 1\n}))]), transition('* => leave', [style({\n  transformOrigin: '0 0',\n  transform: 'translateY(0%)',\n  opacity: 1\n}), animate(`${AnimationDuration.BASE}`, style({\n  transformOrigin: '0 0',\n  transform: 'translateY(-100%)',\n  opacity: 0\n}))])]);\nconst notificationMotion = trigger('notificationMotion', [state('enterRight', style({\n  opacity: 1,\n  transform: 'translateX(0)'\n})), transition('* => enterRight', [style({\n  opacity: 0,\n  transform: 'translateX(5%)'\n}), animate('100ms linear')]), state('enterLeft', style({\n  opacity: 1,\n  transform: 'translateX(0)'\n})), transition('* => enterLeft', [style({\n  opacity: 0,\n  transform: 'translateX(-5%)'\n}), animate('100ms linear')]), state('leave', style({\n  opacity: 0,\n  transform: 'scaleY(0.8)',\n  transformOrigin: '0% 0%'\n})), transition('* => leave', [style({\n  opacity: 1,\n  transform: 'scaleY(1)',\n  transformOrigin: '0% 0%'\n}), animate('100ms linear')])]);\nconst ANIMATION_TRANSITION_IN = `${AnimationDuration.BASE} ${AnimationCurves.EASE_OUT_QUINT}`;\nconst ANIMATION_TRANSITION_OUT = `${AnimationDuration.BASE} ${AnimationCurves.EASE_IN_QUINT}`;\nconst slideMotion = trigger('slideMotion', [state('void', style({\n  opacity: 0,\n  transform: 'scaleY(0.8)'\n})), state('enter', style({\n  opacity: 1,\n  transform: 'scaleY(1)'\n})), transition('void => *', [animate(ANIMATION_TRANSITION_IN)]), transition('* => void', [animate(ANIMATION_TRANSITION_OUT)])]);\nconst slideAlertMotion = trigger('slideAlertMotion', [transition(':leave', [style({\n  opacity: 1,\n  transform: 'scaleY(1)',\n  transformOrigin: '0% 0%'\n}), animate(`${AnimationDuration.SLOW} ${AnimationCurves.EASE_IN_OUT_CIRC}`, style({\n  opacity: 0,\n  transform: 'scaleY(0)',\n  transformOrigin: '0% 0%'\n}))])]);\nconst zoomBigMotion = trigger('zoomBigMotion', [transition('void => active', [style({\n  opacity: 0,\n  transform: 'scale(0.8)'\n}), animate(`${AnimationDuration.BASE} ${AnimationCurves.EASE_OUT_CIRC}`, style({\n  opacity: 1,\n  transform: 'scale(1)'\n}))]), transition('active => void', [style({\n  opacity: 1,\n  transform: 'scale(1)'\n}), animate(`${AnimationDuration.BASE} ${AnimationCurves.EASE_IN_OUT_CIRC}`, style({\n  opacity: 0,\n  transform: 'scale(0.8)'\n}))])]);\nconst zoomBadgeMotion = trigger('zoomBadgeMotion', [transition(':enter', [style({\n  opacity: 0,\n  transform: 'scale(0) translate(50%, -50%)'\n}), animate(`${AnimationDuration.SLOW} ${AnimationCurves.EASE_OUT_BACK}`, style({\n  opacity: 1,\n  transform: 'scale(1) translate(50%, -50%)'\n}))]), transition(':leave', [style({\n  opacity: 1,\n  transform: 'scale(1) translate(50%, -50%)'\n}), animate(`${AnimationDuration.SLOW} ${AnimationCurves.EASE_IN_BACK}`, style({\n  opacity: 0,\n  transform: 'scale(0) translate(50%, -50%)'\n}))])]);\n\n// tslint:disable: typedef no-invalid-this\nconst availablePrefixes = ['moz', 'ms', 'webkit'];\nfunction requestAnimationFramePolyfill() {\n  let lastTime = 0;\n  return function (callback) {\n    const currTime = new Date().getTime();\n    const timeToCall = Math.max(0, 16 - (currTime - lastTime));\n    const timeout = window.setTimeout(() => {\n      callback(currTime + timeToCall);\n    }, timeToCall);\n    lastTime = currTime + timeToCall;\n    return timeout;\n  };\n}\nfunction getRequestAnimationFrame() {\n  if (typeof window === 'undefined') {\n    return () => 0;\n  }\n  if (window.requestAnimationFrame) {\n    // https://github.com/vuejs/vue/issues/4465\n    return window.requestAnimationFrame.bind(window);\n  }\n  const prefix = availablePrefixes.filter(key => `${key}RequestAnimationFrame` in window)[0];\n  return prefix ? window[`${prefix}RequestAnimationFrame`] : requestAnimationFramePolyfill();\n}\nfunction cancelRequestAnimationFrame(id) {\n  if (typeof window === 'undefined') {\n    return null;\n  }\n  if (window.cancelAnimationFrame) {\n    return window.cancelAnimationFrame(id);\n  }\n  const prefix = availablePrefixes.filter(key => `${key}CancelAnimationFrame` in window || `${key}CancelRequestAnimationFrame` in window)[0];\n  return prefix ? (window[`${prefix}CancelAnimationFrame`] || window[`${prefix}CancelRequestAnimationFrame`]\n  // @ts-ignore\n  ).call(this, id) : clearTimeout(id);\n}\nconst reqAnimFrame = getRequestAnimationFrame();\nconst thumbMotion = trigger('thumbMotion', [state('from', style({\n  transform: 'translateX({{ transform }}px)',\n  width: '{{ width }}px'\n}), {\n  params: {\n    transform: 0,\n    width: 0\n  }\n}), state('to', style({\n  transform: 'translateX({{ transform }}px)',\n  width: '{{ width }}px'\n}), {\n  params: {\n    transform: 100,\n    width: 0\n  }\n}), transition('from => to', animate(`200ms ${AnimationCurves.EASE_IN_OUT}`))]);\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AnimationCurves, AnimationDuration, cancelRequestAnimationFrame, collapseMotion, fadeMotion, helpMotion, menuCollapseMotion, moveUpMotion, notificationMotion, reqAnimFrame, slideAlertMotion, slideMotion, thumbMotion, treeCollapseMotion, zoomBadgeMotion, zoomBigMotion };\n", "import { TemplateRef } from '@angular/core';\nimport { TDSHelperString } from 'tds-ui/shared/utility';\nimport { isObservable, from, of, EMPTY, Observable, fromEvent } from 'rxjs';\nfunction isNotNil(value) {\n  return typeof value !== 'undefined' && value !== null;\n}\nfunction isNil(value) {\n  return typeof value === 'undefined' || value === null;\n}\n/**\n * Examine if two objects are shallowly equaled.\n */\nfunction shallowEqual(objA, objB) {\n  if (objA === objB) {\n    return true;\n  }\n  if (typeof objA !== 'object' || !objA || typeof objB !== 'object' || !objB) {\n    return false;\n  }\n  const keysA = Object.keys(objA);\n  const keysB = Object.keys(objB);\n  if (keysA.length !== keysB.length) {\n    return false;\n  }\n  const bHasOwnProperty = Object.prototype.hasOwnProperty.bind(objB);\n  // tslint:disable-next-line:prefer-for-of\n  for (let idx = 0; idx < keysA.length; idx++) {\n    const key = keysA[idx];\n    if (!bHasOwnProperty(key)) {\n      return false;\n    }\n    if (objA[key] !== objB[key]) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction isNonEmptyString(value) {\n  return typeof value === 'string' && value !== '';\n}\nfunction isTemplateRef(value) {\n  return value instanceof TemplateRef;\n}\n\n/**\n * This module provides utility functions to query DOM information or\n * set properties.\n */\n/**\n * Silent an event by stopping and preventing it.\n */\nfunction silentEvent(e) {\n  e.stopPropagation();\n  e.preventDefault();\n}\nfunction getElementOffset(elem) {\n  if (!elem.getClientRects().length) {\n    return {\n      top: 0,\n      left: 0\n    };\n  }\n  const rect = elem.getBoundingClientRect();\n  const win = elem.ownerDocument.defaultView;\n  return {\n    top: rect.top + win.pageYOffset,\n    left: rect.left + win.pageXOffset\n  };\n}\n/**\n * Investigate if an event is a `TouchEvent`.\n */\nfunction isTouchEvent(event) {\n  return event.type.startsWith('touch');\n}\nfunction getEventPosition(event) {\n  return isTouchEvent(event) ? event.touches[0] || event.changedTouches[0] : event;\n}\nfunction isPromise(obj) {\n  return !!obj && typeof obj.then === 'function' && typeof obj.catch === 'function';\n}\nfunction isStyleSupport(styleName) {\n  if (typeof window !== 'undefined' && window.document && window.document.documentElement) {\n    const styleNameList = Array.isArray(styleName) ? styleName : [styleName];\n    const {\n      documentElement\n    } = window.document;\n    return styleNameList.some(name => name in documentElement.style);\n  }\n  return false;\n}\nfunction getStyleAsText(styles) {\n  if (!styles) {\n    return '';\n  }\n  return Object.keys(styles).map(key => {\n    const val = styles[key];\n    return `${key}:${typeof val === 'string' ? val : val + 'px'}`;\n  }).join(';');\n}\nfunction scrollIntoView(node) {\n  const nodeAsAny = node;\n  if (nodeAsAny.scrollIntoViewIfNeeded) {\n    /* eslint-disable-next-line @typescript-eslint/dot-notation */\n    nodeAsAny.scrollIntoViewIfNeeded(false);\n    return;\n  }\n  if (node.scrollIntoView) {\n    node.scrollIntoView(false);\n    return;\n  }\n}\n\n// We only handle element & text node.\nconst ELEMENT_NODE = 1;\nconst TEXT_NODE = 3;\nconst COMMENT_NODE = 8;\nlet ellipsisContainer;\nconst wrapperStyle = {\n  padding: '0',\n  margin: '0',\n  display: 'inline',\n  lineHeight: 'inherit'\n};\nfunction pxToNumber(value) {\n  if (!value) {\n    return 0;\n  }\n  const match = value.match(/^\\d*(\\.\\d*)?/);\n  return match ? Number(match[0]) : 0;\n}\nfunction styleToString(style) {\n  // There are some different behavior between Firefox & Chrome.\n  // We have to handle this ourself.\n  const styleNames = Array.prototype.slice.apply(style);\n  return styleNames.map(name => `${name}: ${style.getPropertyValue(name)};`).join('');\n}\nfunction mergeChildren(children) {\n  const childList = [];\n  children.forEach(child => {\n    const prevChild = childList[childList.length - 1];\n    if (prevChild && child.nodeType === TEXT_NODE && prevChild.nodeType === TEXT_NODE) {\n      prevChild.data += child.data;\n    } else {\n      childList.push(child);\n    }\n  });\n  return childList;\n}\nfunction measure(originEle, rows, contentNodes, fixedContent, ellipsisStr, suffixStr = '') {\n  if (!ellipsisContainer) {\n    ellipsisContainer = document.createElement('div');\n    ellipsisContainer.setAttribute('aria-hidden', 'true');\n    document.body.appendChild(ellipsisContainer);\n  }\n  // Get origin style\n  const originStyle = window.getComputedStyle(originEle);\n  const originCSS = styleToString(originStyle);\n  const lineHeight = pxToNumber(originStyle.lineHeight);\n  const maxHeight = Math.round(lineHeight * (rows + 1) + pxToNumber(originStyle.paddingTop) + pxToNumber(originStyle.paddingBottom));\n  // Set shadow\n  ellipsisContainer.setAttribute('style', originCSS);\n  ellipsisContainer.style.position = 'fixed';\n  ellipsisContainer.style.left = '0';\n  ellipsisContainer.style.height = 'auto';\n  ellipsisContainer.style.minHeight = 'auto';\n  ellipsisContainer.style.maxHeight = 'auto';\n  ellipsisContainer.style.top = '-999999px';\n  ellipsisContainer.style.zIndex = '-1000';\n  // clean up css overflow\n  ellipsisContainer.style.textOverflow = 'clip';\n  ellipsisContainer.style.whiteSpace = 'normal';\n  ellipsisContainer.style.webkitLineClamp = 'none';\n  const contentList = mergeChildren(contentNodes);\n  const container = document.createElement('div');\n  const contentContainer = document.createElement('span');\n  const suffixContainer = document.createTextNode(suffixStr);\n  const fixedContainer = document.createElement('span');\n  // Add styles in container\n  Object.assign(container.style, wrapperStyle);\n  Object.assign(contentContainer.style, wrapperStyle);\n  Object.assign(fixedContainer.style, wrapperStyle);\n  contentList.forEach(n => {\n    contentContainer.appendChild(n);\n  });\n  contentContainer.appendChild(suffixContainer);\n  fixedContent.forEach(node => {\n    fixedContainer.appendChild(node.cloneNode(true));\n  });\n  container.appendChild(contentContainer);\n  container.appendChild(fixedContainer);\n  // Render in the fake container\n  ellipsisContainer.appendChild(container);\n  // Check if ellipsis in measure div is height enough for content\n  function inRange() {\n    return ellipsisContainer.offsetHeight < maxHeight;\n  }\n  if (inRange()) {\n    const text = ellipsisContainer.innerHTML;\n    ellipsisContainer.removeChild(container);\n    return {\n      contentNodes,\n      text,\n      ellipsis: false\n    };\n  }\n  // We should clone the childNode since they're controlled by React and we can't reuse it without warning\n  const childNodes = Array.prototype.slice.apply(ellipsisContainer.childNodes[0].childNodes[0].cloneNode(true).childNodes).filter(({\n    nodeType\n  }) => nodeType !== COMMENT_NODE);\n  const fixedNodes = Array.prototype.slice.apply(ellipsisContainer.childNodes[0].childNodes[1].cloneNode(true).childNodes);\n  ellipsisContainer.removeChild(container);\n  // ========================= Find match ellipsis content =========================\n  ellipsisContainer.innerHTML = '';\n  // Create origin content holder\n  const ellipsisContentHolder = document.createElement('span');\n  ellipsisContainer.appendChild(ellipsisContentHolder);\n  const ellipsisTextNode = document.createTextNode(ellipsisStr + suffixStr);\n  ellipsisContentHolder.appendChild(ellipsisTextNode);\n  fixedNodes.forEach(childNode => {\n    ellipsisContainer.appendChild(childNode);\n  });\n  // Append before fixed nodes\n  function appendChildNode(node) {\n    ellipsisContentHolder.insertBefore(node, ellipsisTextNode);\n  }\n  // Get maximum text\n  function measureText(textNode, fullText, startLoc = 0, endLoc = fullText.length, lastSuccessLoc = 0) {\n    const midLoc = Math.floor((startLoc + endLoc) / 2);\n    textNode.textContent = fullText.slice(0, midLoc);\n    if (startLoc >= endLoc - 1) {\n      // Loop when step is small\n      for (let step = endLoc; step >= startLoc; step -= 1) {\n        const currentStepText = fullText.slice(0, step);\n        textNode.textContent = currentStepText;\n        if (inRange() || !currentStepText) {\n          return step === fullText.length ? {\n            finished: false,\n            node: document.createTextNode(fullText)\n          } : {\n            finished: true,\n            node: document.createTextNode(currentStepText)\n          };\n        }\n      }\n    }\n    if (inRange()) {\n      return measureText(textNode, fullText, midLoc, endLoc, midLoc);\n    } else {\n      return measureText(textNode, fullText, startLoc, midLoc, lastSuccessLoc);\n    }\n  }\n  function measureNode(childNode, index) {\n    const type = childNode.nodeType;\n    if (type === ELEMENT_NODE) {\n      // We don't split element, it will keep if whole element can be displayed.\n      // appendChildNode(childNode);\n      if (inRange()) {\n        return {\n          finished: false,\n          node: contentList[index]\n        };\n      }\n      // Clean up if can not pull in\n      ellipsisContentHolder.removeChild(childNode);\n      return {\n        finished: true,\n        node: null\n      };\n    } else if (type === TEXT_NODE) {\n      const fullText = childNode.textContent || '';\n      const textNode = document.createTextNode(fullText);\n      appendChildNode(textNode);\n      return measureText(textNode, fullText);\n    }\n    // Not handle other type of content\n    // PS: This code should not be attached after react 16\n    return {\n      finished: false,\n      node: null\n    };\n  }\n  const ellipsisNodes = [];\n  childNodes.some((childNode, index) => {\n    const {\n      finished,\n      node\n    } = measureNode(childNode, index);\n    if (node) {\n      ellipsisNodes.push(node);\n    }\n    return finished;\n  });\n  const result = {\n    contentNodes: ellipsisNodes,\n    text: ellipsisContainer.innerHTML,\n    ellipsis: true\n  };\n  while (ellipsisContainer.firstChild) {\n    ellipsisContainer.removeChild(ellipsisContainer.firstChild);\n  }\n  return result;\n}\nfunction toArray(value) {\n  let ret;\n  if (value == null) {\n    ret = [];\n  } else if (!Array.isArray(value)) {\n    ret = [value];\n  } else {\n    ret = value;\n  }\n  return ret;\n}\nfunction arraysEqual(array1, array2) {\n  if (!array1 || !array2 || array1.length !== array2.length) {\n    return false;\n  }\n  const len = array1.length;\n  for (let i = 0; i < len; i++) {\n    if (array1[i] !== array2[i]) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction shallowCopyArray(source) {\n  return source.slice();\n}\nlet scrollbarVerticalSize;\nlet scrollbarHorizontalSize;\n// Measure scrollbar width for padding body during modal show/hide\nconst scrollbarMeasure = {\n  position: 'absolute',\n  top: '-9999px',\n  width: '50px',\n  height: '50px'\n};\nfunction measureScrollbar(direction = 'vertical', prefix = 'tds') {\n  if (typeof document === 'undefined' || typeof window === 'undefined') {\n    return 0;\n  }\n  const isVertical = direction === 'vertical';\n  if (isVertical && scrollbarVerticalSize) {\n    return scrollbarVerticalSize;\n  } else if (!isVertical && scrollbarHorizontalSize) {\n    return scrollbarHorizontalSize;\n  }\n  const scrollDiv = document.createElement('div');\n  Object.keys(scrollbarMeasure).forEach(scrollProp => {\n    // @ts-ignore\n    scrollDiv.style[scrollProp] = scrollbarMeasure[scrollProp];\n  });\n  // apply hide scrollbar className ahead\n  scrollDiv.className = `${prefix}-hide-scrollbar scroll-div-append-to-body tds-custom-scroll`;\n  // Append related overflow style\n  if (isVertical) {\n    scrollDiv.style.overflowY = 'scroll';\n  } else {\n    scrollDiv.style.overflowX = 'scroll';\n  }\n  document.body.appendChild(scrollDiv);\n  let size = 0;\n  if (isVertical) {\n    size = scrollDiv.offsetWidth - scrollDiv.clientWidth;\n    scrollbarVerticalSize = size;\n  } else {\n    size = scrollDiv.offsetHeight - scrollDiv.clientHeight;\n    scrollbarHorizontalSize = size;\n  }\n  document.body.removeChild(scrollDiv);\n  return size;\n}\nfunction getPercent(min, max, value) {\n  return (value - min) / (max - min) * 100;\n}\nfunction getPrecision(num) {\n  const numStr = num.toString();\n  const dotIndex = numStr.indexOf('.');\n  return dotIndex >= 0 ? numStr.length - dotIndex - 1 : 0;\n}\nfunction ensureNumberInRange(num, min, max) {\n  if (isNaN(num) || num < min) {\n    return min;\n  } else if (num > max) {\n    return max;\n  } else {\n    return num;\n  }\n}\nfunction isNumberFinite(value) {\n  return typeof value === 'number' && isFinite(value);\n}\nfunction toDecimal(value, decimal) {\n  return Math.round(value * Math.pow(10, decimal)) / Math.pow(10, decimal);\n}\nfunction sum(input, initial = 0) {\n  return input.reduce((previous, current) => previous + current, initial);\n}\nfunction formatNumberWithCommas(value, commas = ',') {\n  if (value != null) {\n    return value.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, commas);\n  }\n  return value;\n}\n;\nfunction parserNumberWithCommas(value, commas = ',') {\n  if (value != null) {\n    return TDSHelperString.replaceAll(value, commas, '');\n  }\n  return value;\n}\n;\nfunction wrapIntoObservable(value) {\n  if (isObservable(value)) {\n    return value;\n  }\n  if (isPromise(value)) {\n    // Use `Promise.resolve()` to wrap promise-like instances.\n    return from(Promise.resolve(value));\n  }\n  return of(value);\n}\nclass KeyboardUtil {\n  static ifBackspaceOrDelete(event) {\n    return this.ifKey(event, 'Backspace;Delete;Del');\n  }\n  static ifRightArrow(event) {\n    return this.ifKey(event, 'ArrowRight;Right');\n  }\n  static ifLeftArrow(event) {\n    return this.ifKey(event, 'ArrowLeft;Left');\n  }\n  static ifSpacebar(event) {\n    return this.ifKey(event, 'Spacebar; '); //don't remove the space after ; as this will check for space key\n  }\n  static ifKey(event, keys) {\n    let keysToCheck = keys.split(';');\n    return keysToCheck.some(k => k === event.key);\n  }\n}\n\n/**\n * Much like lodash.\n */\nfunction padStart(toPad, length, element) {\n  if (toPad.length > length) {\n    return toPad;\n  }\n  const joined = `${getRepeatedElement(length, element)}${toPad}`;\n  return joined.slice(joined.length - length, joined.length);\n}\nfunction padEnd(toPad, length, element) {\n  const joined = `${toPad}${getRepeatedElement(length, element)}`;\n  return joined.slice(0, length);\n}\nfunction getRepeatedElement(length, element) {\n  return Array(length).fill(element).join('');\n}\nconst isChanged = (propertyName, changes, skipFirstChange = true) => typeof changes[propertyName] !== 'undefined' && (!changes[propertyName].isFirstChange() || !skipFirstChange) && changes[propertyName].previousValue !== changes[propertyName].currentValue;\nconst anyChanged = (propertyNames, changes, skipFirstChange = true) => propertyNames.some(name => isChanged(name, changes, skipFirstChange));\nfunction runOutsideAngular(fn) {\n  // The function that does the same job as `NgZone.runOutsideAngular`.\n  // The difference is that we don't need to rely on the `NgZone` service,\n  // allowing `fromEventOutsideAngular` to function without requiring an explicit\n  // injection context (where we might otherwise call `inject(NgZone)`).\n  return typeof Zone !== 'undefined' ? Zone.root.run(fn) : fn();\n}\n/**\n * This function replaces `runOutsideAngular` with `fromEvent`, introducing a\n * lot of boilerplate where we need to inject the `NgZone` service and then subscribe\n * to `fromEvent` within the `runOutsideAngular` callback.\n */\nfunction fromEventOutsideAngular(target, name, options) {\n  // Allow the event target to be nullable to avoid requiring callers to check\n  // if the target exists. We simply complete the observable immediately,\n  // as this might potentially be used within a `switchMap`.\n  if (!target) {\n    return EMPTY;\n  }\n  return new Observable(subscriber => {\n    // Note that we're wrapping fromEvent with an observable because `fromEvent`\n    // is eager and only calls `addEventListener` when a new subscriber comes in.\n    // Therefore, we're wrapping the subscription with `runOutsideAngular` to ensure\n    // that `addEventListener` is also called outside of Angular when there's a subscriber.\n    return runOutsideAngular(() =>\n    // Casting because the inferred overload is incorrect :(\n    fromEvent(target, name, options).subscribe(subscriber));\n  });\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { KeyboardUtil, anyChanged, arraysEqual, ensureNumberInRange, formatNumberWithCommas, fromEventOutsideAngular, getElementOffset, getEventPosition, getPercent, getPrecision, getRepeatedElement, getStyleAsText, isChanged, isNil, isNonEmptyString, isNotNil, isNumberFinite, isPromise, isStyleSupport, isTemplateRef, isTouchEvent, measure, measureScrollbar, padEnd, padStart, parserNumberWithCommas, pxToNumber, scrollIntoView, shallowCopyArray, shallowEqual, silentEvent, sum, toArray, toDecimal, wrapIntoObservable };\n", "import * as i0 from '@angular/core';\nimport { Injectable, Inject } from '@angular/core';\nimport { DOCUMENT } from '@angular/common';\nimport { reqAnimFrame } from 'tds-ui/core/animation';\nimport { auditTime, finalize, map, startWith, distinctUntilChanged, filter } from 'rxjs/operators';\nimport { Subject, ReplaySubject } from 'rxjs';\nimport * as i2 from '@angular/cdk/layout';\nimport { getEventPosition, isTouchEvent } from 'tds-ui/core/util';\nconst environment = {\n  isTestMode: false\n};\n\n/**\n * When running in test, singletons should not be destroyed. So we keep references of singletons\n * in this global variable.\n */\nconst testSingleRegistry = new Map();\n/**\n * Some singletons should have life cycle that is same to Angular's. This service make sure that\n * those singletons get destroyed in HMR.\n */\nclass TDSSingletonService {\n  constructor() {\n    /**\n     * This registry is used to register singleton in dev mode.\n     * So that singletons get destroyed when hot module reload happens.\n     *\n     * This works in prod mode too but with no specific effect.\n     */\n    this._singletonRegistry = new Map();\n  }\n  get singletonRegistry() {\n    return environment.isTestMode ? testSingleRegistry : this._singletonRegistry;\n  }\n  registerSingletonWithKey(key, target) {\n    const alreadyHave = this.singletonRegistry.has(key);\n    const item = alreadyHave ? this.singletonRegistry.get(key) : this.withNewTarget(target);\n    if (!alreadyHave) {\n      this.singletonRegistry.set(key, item);\n    }\n  }\n  unregisterSingletonWithKey(key) {\n    if (this.singletonRegistry.has(key)) {\n      this.singletonRegistry.delete(key);\n    }\n  }\n  getSingletonWithKey(key) {\n    return this.singletonRegistry.has(key) ? this.singletonRegistry.get(key).target : null;\n  }\n  withNewTarget(target) {\n    return {\n      target\n    };\n  }\n  static {\n    this.ɵfac = function TDSSingletonService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSSingletonService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: TDSSingletonService,\n      factory: TDSSingletonService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSSingletonService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nfunction easeInOutCubic(t, b, c, d) {\n  const cc = c - b;\n  let tt = t / (d / 2);\n  if (tt < 1) {\n    return cc / 2 * tt * tt * tt + b;\n  } else {\n    return cc / 2 * ((tt -= 2) * tt * tt + 2) + b;\n  }\n}\nclass TDSScrollService {\n  constructor(doc) {\n    this.doc = doc;\n  }\n  /** Set the position of the scroll bar of `el`. */\n  setScrollTop(el, topValue = 0) {\n    if (el === window) {\n      this.doc.body.scrollTop = topValue;\n      this.doc.documentElement.scrollTop = topValue;\n    } else {\n      el.scrollTop = topValue;\n    }\n  }\n  /** Get position of `el` against window. */\n  getOffset(el) {\n    const ret = {\n      top: 0,\n      left: 0\n    };\n    if (!el || !el.getClientRects().length) {\n      return ret;\n    }\n    const rect = el.getBoundingClientRect();\n    if (rect.width || rect.height) {\n      const doc = el.ownerDocument.documentElement;\n      ret.top = rect.top - doc.clientTop;\n      ret.left = rect.left - doc.clientLeft;\n    } else {\n      ret.top = rect.top;\n      ret.left = rect.left;\n    }\n    return ret;\n  }\n  /** Get the position of the scoll bar of `el`. */\n  // TODO: remove '| Window' as the fallback already happens here\n  getScroll(target, top = true) {\n    if (typeof window === 'undefined') {\n      return 0;\n    }\n    const method = top ? 'scrollTop' : 'scrollLeft';\n    let result = 0;\n    if (this.isWindow(target)) {\n      result = target[top ? 'pageYOffset' : 'pageXOffset'];\n    } else if (target instanceof Document) {\n      result = target.documentElement[method];\n    } else if (target) {\n      result = target[method];\n    }\n    if (target && !this.isWindow(target) && typeof result !== 'number') {\n      result = (target.ownerDocument || target).documentElement[method];\n    }\n    return result;\n  }\n  isWindow(obj) {\n    return obj !== null && obj !== undefined && obj === obj.window;\n  }\n  /**\n   * Scroll `el` to some position with animation.\n   *\n   * @param containerEl container, `window` by default\n   * @param y Scroll to `top`, 0 by default\n   */\n  scrollTo(containerEl, y = 0, options = {}) {\n    const target = containerEl ? containerEl : window;\n    const scrollTop = this.getScroll(target);\n    const startTime = Date.now();\n    const {\n      easing,\n      callback,\n      duration = 450\n    } = options;\n    const frameFunc = () => {\n      const timestamp = Date.now();\n      const time = timestamp - startTime;\n      const nextScrollTop = (easing || easeInOutCubic)(time > duration ? duration : time, scrollTop, y, duration);\n      if (this.isWindow(target)) {\n        target.scrollTo(window.pageXOffset, nextScrollTop);\n      } else if (target instanceof HTMLDocument || target.constructor.name === 'HTMLDocument') {\n        target.documentElement.scrollTop = nextScrollTop;\n      } else {\n        target.scrollTop = nextScrollTop;\n      }\n      if (time < duration) {\n        reqAnimFrame(frameFunc);\n      } else if (typeof callback === 'function') {\n        callback();\n      }\n    };\n    reqAnimFrame(frameFunc);\n  }\n  static {\n    this.ɵfac = function TDSScrollService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSScrollService)(i0.ɵɵinject(DOCUMENT));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: TDSScrollService,\n      factory: TDSScrollService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSScrollService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\nconst NOOP = () => {};\nclass TDSResizeService {\n  constructor(ngZone, rendererFactory2) {\n    this.ngZone = ngZone;\n    this.rendererFactory2 = rendererFactory2;\n    this.resizeSource$ = new Subject();\n    this.listeners = 0;\n    this.disposeHandle = NOOP;\n    this.handler = () => {\n      this.ngZone.run(() => {\n        this.resizeSource$.next();\n      });\n    };\n    this.renderer = this.rendererFactory2.createRenderer(null, null);\n  }\n  subscribe() {\n    this.registerListener();\n    return this.resizeSource$.pipe(auditTime(16), finalize(() => this.unregisterListener()));\n  }\n  unsubscribe() {\n    this.unregisterListener();\n  }\n  registerListener() {\n    if (this.listeners === 0) {\n      this.ngZone.runOutsideAngular(() => {\n        this.disposeHandle = this.renderer.listen('window', 'resize', this.handler);\n      });\n    }\n    this.listeners += 1;\n  }\n  unregisterListener() {\n    this.listeners -= 1;\n    if (this.listeners === 0) {\n      this.disposeHandle();\n      this.disposeHandle = NOOP;\n    }\n  }\n  static {\n    this.ɵfac = function TDSResizeService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSResizeService)(i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(i0.RendererFactory2));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: TDSResizeService,\n      factory: TDSResizeService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSResizeService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i0.NgZone\n  }, {\n    type: i0.RendererFactory2\n  }], null);\n})();\nvar TDSBreakpointEnum;\n(function (TDSBreakpointEnum) {\n  TDSBreakpointEnum[\"xxl\"] = \"xxl\";\n  TDSBreakpointEnum[\"xl\"] = \"xl\";\n  TDSBreakpointEnum[\"lg\"] = \"lg\";\n  TDSBreakpointEnum[\"md\"] = \"md\";\n  TDSBreakpointEnum[\"sm\"] = \"sm\";\n  TDSBreakpointEnum[\"xs\"] = \"xs\";\n})(TDSBreakpointEnum || (TDSBreakpointEnum = {}));\nconst gridResponsiveMap = {\n  xs: '(max-width: 575px)',\n  sm: '(min-width: 576px)',\n  md: '(min-width: 768px)',\n  lg: '(min-width: 992px)',\n  xl: '(min-width: 1200px)',\n  xxl: '(min-width: 1600px)'\n};\nconst tdsLayoutResponsiveMap = {\n  xs: '(max-width: 375.98px)',\n  sm: '(min-width: 640px)',\n  md: '(min-width: 768px)',\n  lg: '(min-width: 1024px)',\n  xl: '(min-width: 1280px)',\n  xxl: '(min-width: 1536px)'\n};\nconst siderResponsiveMap = {\n  xs: '(max-width: 479.98px)',\n  sm: '(max-width: 575.98px)',\n  md: '(max-width: 767.98px)',\n  lg: '(max-width: 991.98px)',\n  xl: '(max-width: 1199.98px)',\n  xxl: '(max-width: 1599.98px)'\n};\nclass TDSBreakpointService {\n  constructor(resizeService, mediaMatcher) {\n    this.resizeService = resizeService;\n    this.mediaMatcher = mediaMatcher;\n    this.resizeService.subscribe().subscribe(() => {});\n  }\n  subscribe(breakpointMap, fullMap) {\n    if (fullMap) {\n      const get = () => this.matchMedia(breakpointMap, true);\n      return this.resizeService.subscribe().pipe(map(get), startWith(get()), distinctUntilChanged((x, y) => x[0] === y[0]), map(x => x[1]));\n    } else {\n      const get = () => this.matchMedia(breakpointMap);\n      return this.resizeService.subscribe().pipe(map(get), startWith(get()), distinctUntilChanged());\n    }\n  }\n  matchMedia(breakpointMap, fullMap) {\n    let bp = TDSBreakpointEnum.md;\n    const breakpointBooleanMap = {};\n    Object.keys(breakpointMap).map(breakpoint => {\n      const castBP = breakpoint;\n      const matched = this.mediaMatcher.matchMedia(breakpointMap[castBP]).matches;\n      breakpointBooleanMap[breakpoint] = matched;\n      if (matched) {\n        bp = castBP;\n      }\n    });\n    if (fullMap) {\n      return [bp, breakpointBooleanMap];\n    } else {\n      return bp;\n    }\n  }\n  static {\n    this.ɵfac = function TDSBreakpointService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSBreakpointService)(i0.ɵɵinject(TDSResizeService), i0.ɵɵinject(i2.MediaMatcher));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: TDSBreakpointService,\n      factory: TDSBreakpointService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSBreakpointService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: TDSResizeService\n  }, {\n    type: i2.MediaMatcher\n  }], null);\n})();\nclass TDSDestroyService extends ReplaySubject {\n  constructor() {\n    super(1);\n  }\n  ngOnDestroy() {\n    this.next();\n    this.complete();\n  }\n  static {\n    this.ɵfac = function TDSDestroyService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSDestroyService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: TDSDestroyService,\n      factory: TDSDestroyService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSDestroyService, [{\n    type: Injectable\n  }], () => [], null);\n})();\nfunction getPagePosition(event) {\n  const e = getEventPosition(event);\n  return {\n    x: e.pageX,\n    y: e.pageY\n  };\n}\n/**\n * This module provide a global dragging service to other components.\n */\nclass TDSDragService {\n  constructor(rendererFactory2) {\n    this.draggingThreshold = 5;\n    this.currentDraggingSequence = null;\n    this.currentStartingPoint = null;\n    this.handleRegistry = new Set();\n    this.renderer = rendererFactory2.createRenderer(null, null);\n  }\n  requestDraggingSequence(event) {\n    if (!this.handleRegistry.size) {\n      this.registerDraggingHandler(isTouchEvent(event));\n    }\n    // Complete last dragging sequence if a new target is dragged.\n    if (this.currentDraggingSequence) {\n      this.currentDraggingSequence.complete();\n    }\n    this.currentStartingPoint = getPagePosition(event);\n    this.currentDraggingSequence = new Subject();\n    return this.currentDraggingSequence.pipe(map(e => {\n      return {\n        x: e.pageX - this.currentStartingPoint.x,\n        y: e.pageY - this.currentStartingPoint.y\n      };\n    }), filter(e => Math.abs(e.x) > this.draggingThreshold || Math.abs(e.y) > this.draggingThreshold), finalize(() => this.teardownDraggingSequence()));\n  }\n  registerDraggingHandler(isTouch) {\n    if (isTouch) {\n      this.handleRegistry.add({\n        teardown: this.renderer.listen('document', 'touchmove', e => {\n          if (this.currentDraggingSequence) {\n            this.currentDraggingSequence.next(e.touches[0] || e.changedTouches[0]);\n          }\n        })\n      });\n      this.handleRegistry.add({\n        teardown: this.renderer.listen('document', 'touchend', () => {\n          if (this.currentDraggingSequence) {\n            this.currentDraggingSequence.complete();\n          }\n        })\n      });\n    } else {\n      this.handleRegistry.add({\n        teardown: this.renderer.listen('document', 'mousemove', e => {\n          if (this.currentDraggingSequence) {\n            this.currentDraggingSequence.next(e);\n          }\n        })\n      });\n      this.handleRegistry.add({\n        teardown: this.renderer.listen('document', 'mouseup', () => {\n          if (this.currentDraggingSequence) {\n            this.currentDraggingSequence.complete();\n          }\n        })\n      });\n    }\n  }\n  teardownDraggingSequence() {\n    this.currentDraggingSequence = null;\n  }\n  static {\n    this.ɵfac = function TDSDragService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSDragService)(i0.ɵɵinject(i0.RendererFactory2));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: TDSDragService,\n      factory: TDSDragService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSDragService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i0.RendererFactory2\n  }], null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TDSBreakpointEnum, TDSBreakpointService, TDSDestroyService, TDSDragService, TDSResizeService, TDSScrollService, TDSSingletonService, environment, gridResponsiveMap, siderResponsiveMap, tdsLayoutResponsiveMap };\n", "import * as i0 from '@angular/core';\nimport { ElementRef, Injector, Directive, EventEmitter, Inject, Input, Output, NgModule } from '@angular/core';\nimport { DOCUMENT } from '@angular/common';\n\n/**\n * Throws an exception when attempting to attach a null portal to a host.\n * @docs-private\n */\nfunction throwNullPortalError() {\n  throw Error('Must provide a portal to attach');\n}\n/**\n * Throws an exception when attempting to attach a portal to a host that is already attached.\n * @docs-private\n */\nfunction throwPortalAlreadyAttachedError() {\n  throw Error('Host already has a portal attached');\n}\n/**\n * Throws an exception when attempting to attach a portal to an already-disposed host.\n * @docs-private\n */\nfunction throwPortalOutletAlreadyDisposedError() {\n  throw Error('This PortalOutlet has already been disposed');\n}\n/**\n * Throws an exception when attempting to attach an unknown portal type.\n * @docs-private\n */\nfunction throwUnknownPortalTypeError() {\n  throw Error('Attempting to attach an unknown Portal type. BasePortalOutlet accepts either ' + 'a ComponentPortal or a TemplatePortal.');\n}\n/**\n * Throws an exception when attempting to attach a portal to a null host.\n * @docs-private\n */\nfunction throwNullPortalOutletError() {\n  throw Error('Attempting to attach a portal to a null PortalOutlet');\n}\n/**\n * Throws an exception when attempting to detach a portal that is not attached.\n * @docs-private\n */\nfunction throwNoPortalAttachedError() {\n  throw Error('Attempting to detach a portal that is not attached to a host');\n}\n\n/**\n * A `Portal` is something that you want to render somewhere else.\n * It can be attach to / detached from a `PortalOutlet`.\n */\nclass Portal {\n  /** Attach this portal to a host. */\n  attach(host) {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (host == null) {\n        throwNullPortalOutletError();\n      }\n      if (host.hasAttached()) {\n        throwPortalAlreadyAttachedError();\n      }\n    }\n    this._attachedHost = host;\n    return host.attach(this);\n  }\n  /** Detach this portal from its host */\n  detach() {\n    let host = this._attachedHost;\n    if (host != null) {\n      this._attachedHost = null;\n      host.detach();\n    } else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      throwNoPortalAttachedError();\n    }\n  }\n  /** Whether this portal is attached to a host. */\n  get isAttached() {\n    return this._attachedHost != null;\n  }\n  /**\n   * Sets the PortalOutlet reference without performing `attach()`. This is used directly by\n   * the PortalOutlet when it is performing an `attach()` or `detach()`.\n   */\n  setAttachedHost(host) {\n    this._attachedHost = host;\n  }\n}\n/**\n * A `ComponentPortal` is a portal that instantiates some Component upon attachment.\n */\nclass ComponentPortal extends Portal {\n  constructor(component, viewContainerRef, injector, componentFactoryResolver, projectableNodes) {\n    super();\n    this.component = component;\n    this.viewContainerRef = viewContainerRef;\n    this.injector = injector;\n    this.componentFactoryResolver = componentFactoryResolver;\n    this.projectableNodes = projectableNodes;\n  }\n}\n/**\n * A `TemplatePortal` is a portal that represents some embedded template (TemplateRef).\n */\nclass TemplatePortal extends Portal {\n  constructor(/** The embedded template that will be used to instantiate an embedded View in the host. */\n  templateRef, /** Reference to the ViewContainer into which the template will be stamped out. */\n  viewContainerRef, /** Contextual data to be passed in to the embedded view. */\n  context, /** The injector to use for the embedded view. */\n  injector) {\n    super();\n    this.templateRef = templateRef;\n    this.viewContainerRef = viewContainerRef;\n    this.context = context;\n    this.injector = injector;\n  }\n  get origin() {\n    return this.templateRef.elementRef;\n  }\n  /**\n   * Attach the portal to the provided `PortalOutlet`.\n   * When a context is provided it will override the `context` property of the `TemplatePortal`\n   * instance.\n   */\n  attach(host, context = this.context) {\n    this.context = context;\n    return super.attach(host);\n  }\n  detach() {\n    this.context = undefined;\n    return super.detach();\n  }\n}\n/**\n * A `DomPortal` is a portal whose DOM element will be taken from its current position\n * in the DOM and moved into a portal outlet, when it is attached. On detach, the content\n * will be restored to its original position.\n */\nclass DomPortal extends Portal {\n  constructor(element) {\n    super();\n    this.element = element instanceof ElementRef ? element.nativeElement : element;\n  }\n}\n/**\n * Partial implementation of PortalOutlet that handles attaching\n * ComponentPortal and TemplatePortal.\n */\nclass BasePortalOutlet {\n  constructor() {\n    /** Whether this host has already been permanently disposed. */\n    this._isDisposed = false;\n    // @breaking-change 10.0.0 `attachDomPortal` to become a required abstract method.\n    this.attachDomPortal = null;\n  }\n  /** Whether this host has an attached portal. */\n  hasAttached() {\n    return !!this._attachedPortal;\n  }\n  /** Attaches a portal. */\n  attach(portal) {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (!portal) {\n        throwNullPortalError();\n      }\n      if (this.hasAttached()) {\n        throwPortalAlreadyAttachedError();\n      }\n      if (this._isDisposed) {\n        throwPortalOutletAlreadyDisposedError();\n      }\n    }\n    if (portal instanceof ComponentPortal) {\n      this._attachedPortal = portal;\n      return this.attachComponentPortal(portal);\n    } else if (portal instanceof TemplatePortal) {\n      this._attachedPortal = portal;\n      return this.attachTemplatePortal(portal);\n      // @breaking-change 10.0.0 remove null check for `this.attachDomPortal`.\n    } else if (this.attachDomPortal && portal instanceof DomPortal) {\n      this._attachedPortal = portal;\n      return this.attachDomPortal(portal);\n    }\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      throwUnknownPortalTypeError();\n    }\n  }\n  /** Detaches a previously attached portal. */\n  detach() {\n    if (this._attachedPortal) {\n      this._attachedPortal.setAttachedHost(null);\n      this._attachedPortal = null;\n    }\n    this._invokeDisposeFn();\n  }\n  /** Permanently dispose of this portal host. */\n  dispose() {\n    if (this.hasAttached()) {\n      this.detach();\n    }\n    this._invokeDisposeFn();\n    this._isDisposed = true;\n  }\n  /** @docs-private */\n  setDisposeFn(fn) {\n    this._disposeFn = fn;\n  }\n  _invokeDisposeFn() {\n    if (this._disposeFn) {\n      this._disposeFn();\n      this._disposeFn = null;\n    }\n  }\n}\n/**\n * @deprecated Use `BasePortalOutlet` instead.\n * @breaking-change 9.0.0\n */\nclass BasePortalHost extends BasePortalOutlet {}\n\n/**\n * A PortalOutlet for attaching portals to an arbitrary DOM element outside of the Angular\n * application context.\n */\nclass DomPortalOutlet extends BasePortalOutlet {\n  /**\n   * @param outletElement Element into which the content is projected.\n   * @param _componentFactoryResolver Used to resolve the component factory.\n   *   Only required when attaching component portals.\n   * @param _appRef Reference to the application. Only used in component portals when there\n   *   is no `ViewContainerRef` available.\n   * @param _defaultInjector Injector to use as a fallback when the portal being attached doesn't\n   *   have one. Only used for component portals.\n   * @param _document Reference to the document. Used when attaching a DOM portal. Will eventually\n   *   become a required parameter.\n   */\n  constructor(/** Element into which the content is projected. */\n  outletElement, _componentFactoryResolver, _appRef, _defaultInjector,\n  /**\n   * @deprecated `_document` Parameter to be made required.\n   * @breaking-change 10.0.0\n   */\n  _document) {\n    super();\n    this.outletElement = outletElement;\n    this._componentFactoryResolver = _componentFactoryResolver;\n    this._appRef = _appRef;\n    this._defaultInjector = _defaultInjector;\n    /**\n     * Attaches a DOM portal by transferring its content into the outlet.\n     * @param portal Portal to be attached.\n     * @deprecated To be turned into a method.\n     * @breaking-change 10.0.0\n     */\n    this.attachDomPortal = portal => {\n      // @breaking-change 10.0.0 Remove check and error once the\n      // `_document` constructor parameter is required.\n      if (!this._document && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error('Cannot attach DOM portal without _document constructor parameter');\n      }\n      const element = portal.element;\n      if (!element.parentNode && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error('DOM portal content must be attached to a parent node.');\n      }\n      // Anchor used to save the element's previous position so\n      // that we can restore it when the portal is detached.\n      const anchorNode = this._document.createComment('dom-portal');\n      element.parentNode.insertBefore(anchorNode, element);\n      this.outletElement.appendChild(element);\n      this._attachedPortal = portal;\n      super.setDisposeFn(() => {\n        // We can't use `replaceWith` here because IE doesn't support it.\n        if (anchorNode.parentNode) {\n          anchorNode.parentNode.replaceChild(element, anchorNode);\n        }\n      });\n    };\n    this._document = _document;\n  }\n  /**\n   * Attach the given ComponentPortal to DOM element using the ComponentFactoryResolver.\n   * @param portal Portal to be attached\n   * @returns Reference to the created component.\n   */\n  attachComponentPortal(portal) {\n    const resolver = portal.componentFactoryResolver || this._componentFactoryResolver;\n    if ((typeof ngDevMode === 'undefined' || ngDevMode) && !resolver) {\n      throw Error('Cannot attach component portal to outlet without a ComponentFactoryResolver.');\n    }\n    const componentFactory = resolver.resolveComponentFactory(portal.component);\n    let componentRef;\n    // If the portal specifies a ViewContainerRef, we will use that as the attachment point\n    // for the component (in terms of Angular's component tree, not rendering).\n    // When the ViewContainerRef is missing, we use the factory to create the component directly\n    // and then manually attach the view to the application.\n    if (portal.viewContainerRef) {\n      componentRef = portal.viewContainerRef.createComponent(componentFactory, portal.viewContainerRef.length, portal.injector || portal.viewContainerRef.injector, portal.projectableNodes || undefined);\n      this.setDisposeFn(() => componentRef.destroy());\n    } else {\n      if ((typeof ngDevMode === 'undefined' || ngDevMode) && !this._appRef) {\n        throw Error('Cannot attach component portal to outlet without an ApplicationRef.');\n      }\n      componentRef = componentFactory.create(portal.injector || this._defaultInjector || Injector.NULL);\n      this._appRef.attachView(componentRef.hostView);\n      this.setDisposeFn(() => {\n        // Verify that the ApplicationRef has registered views before trying to detach a host view.\n        // This check also protects the `detachView` from being called on a destroyed ApplicationRef.\n        if (this._appRef.viewCount > 0) {\n          this._appRef.detachView(componentRef.hostView);\n        }\n        componentRef.destroy();\n      });\n    }\n    // At this point the component has been instantiated, so we move it to the location in the DOM\n    // where we want it to be rendered.\n    this.outletElement.appendChild(this._getComponentRootNode(componentRef));\n    this._attachedPortal = portal;\n    return componentRef;\n  }\n  /**\n   * Attaches a template portal to the DOM as an embedded view.\n   * @param portal Portal to be attached.\n   * @returns Reference to the created embedded view.\n   */\n  attachTemplatePortal(portal) {\n    let viewContainer = portal.viewContainerRef;\n    let viewRef = viewContainer.createEmbeddedView(portal.templateRef, portal.context, {\n      injector: portal.injector\n    });\n    // The method `createEmbeddedView` will add the view as a child of the viewContainer.\n    // But for the DomPortalOutlet the view can be added everywhere in the DOM\n    // (e.g Overlay Container) To move the view to the specified host element. We just\n    // re-append the existing root nodes.\n    viewRef.rootNodes.forEach(rootNode => this.outletElement.appendChild(rootNode));\n    // Note that we want to detect changes after the nodes have been moved so that\n    // any directives inside the portal that are looking at the DOM inside a lifecycle\n    // hook won't be invoked too early.\n    viewRef.detectChanges();\n    this.setDisposeFn(() => {\n      let index = viewContainer.indexOf(viewRef);\n      if (index !== -1) {\n        viewContainer.remove(index);\n      }\n    });\n    this._attachedPortal = portal;\n    // TODO(jelbourn): Return locals from view.\n    return viewRef;\n  }\n  /**\n   * Clears out a portal from the DOM.\n   */\n  dispose() {\n    super.dispose();\n    this.outletElement.remove();\n  }\n  /** Gets the root HTMLElement for an instantiated component. */\n  _getComponentRootNode(componentRef) {\n    return componentRef.hostView.rootNodes[0];\n  }\n}\n/**\n * @deprecated Use `DomPortalOutlet` instead.\n * @breaking-change 9.0.0\n */\nclass DomPortalHost extends DomPortalOutlet {}\n\n/**\n * Directive version of a `TemplatePortal`. Because the directive *is* a TemplatePortal,\n * the directive instance itself can be attached to a host, enabling declarative use of portals.\n */\nclass CdkPortal extends TemplatePortal {\n  constructor(templateRef, viewContainerRef) {\n    super(templateRef, viewContainerRef);\n  }\n  static {\n    this.ɵfac = function CdkPortal_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkPortal)(i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i0.ViewContainerRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkPortal,\n      selectors: [[\"\", \"cdkPortal\", \"\"]],\n      exportAs: [\"cdkPortal\"],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkPortal, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkPortal]',\n      exportAs: 'cdkPortal',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }, {\n    type: i0.ViewContainerRef\n  }], null);\n})();\n/**\n * @deprecated Use `CdkPortal` instead.\n * @breaking-change 9.0.0\n */\nclass TemplatePortalDirective extends CdkPortal {\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵTemplatePortalDirective_BaseFactory;\n      return function TemplatePortalDirective_Factory(__ngFactoryType__) {\n        return (ɵTemplatePortalDirective_BaseFactory || (ɵTemplatePortalDirective_BaseFactory = i0.ɵɵgetInheritedFactory(TemplatePortalDirective)))(__ngFactoryType__ || TemplatePortalDirective);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TemplatePortalDirective,\n      selectors: [[\"\", \"cdk-portal\", \"\"], [\"\", \"portal\", \"\"]],\n      exportAs: [\"cdkPortal\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkPortal,\n        useExisting: TemplatePortalDirective\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TemplatePortalDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[cdk-portal], [portal]',\n      exportAs: 'cdkPortal',\n      providers: [{\n        provide: CdkPortal,\n        useExisting: TemplatePortalDirective\n      }],\n      standalone: true\n    }]\n  }], null, null);\n})();\n/**\n * Directive version of a PortalOutlet. Because the directive *is* a PortalOutlet, portals can be\n * directly attached to it, enabling declarative use.\n *\n * Usage:\n * `<ng-template [cdkPortalOutlet]=\"greeting\"></ng-template>`\n */\nclass CdkPortalOutlet extends BasePortalOutlet {\n  constructor(_componentFactoryResolver, _viewContainerRef,\n  /**\n   * @deprecated `_document` parameter to be made required.\n   * @breaking-change 9.0.0\n   */\n  _document) {\n    super();\n    this._componentFactoryResolver = _componentFactoryResolver;\n    this._viewContainerRef = _viewContainerRef;\n    /** Whether the portal component is initialized. */\n    this._isInitialized = false;\n    /** Emits when a portal is attached to the outlet. */\n    this.attached = new EventEmitter();\n    /**\n     * Attaches the given DomPortal to this PortalHost by moving all of the portal content into it.\n     * @param portal Portal to be attached.\n     * @deprecated To be turned into a method.\n     * @breaking-change 10.0.0\n     */\n    this.attachDomPortal = portal => {\n      // @breaking-change 9.0.0 Remove check and error once the\n      // `_document` constructor parameter is required.\n      if (!this._document && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error('Cannot attach DOM portal without _document constructor parameter');\n      }\n      const element = portal.element;\n      if (!element.parentNode && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error('DOM portal content must be attached to a parent node.');\n      }\n      // Anchor used to save the element's previous position so\n      // that we can restore it when the portal is detached.\n      const anchorNode = this._document.createComment('dom-portal');\n      portal.setAttachedHost(this);\n      element.parentNode.insertBefore(anchorNode, element);\n      this._getRootNode().appendChild(element);\n      this._attachedPortal = portal;\n      super.setDisposeFn(() => {\n        if (anchorNode.parentNode) {\n          anchorNode.parentNode.replaceChild(element, anchorNode);\n        }\n      });\n    };\n    this._document = _document;\n  }\n  /** Portal associated with the Portal outlet. */\n  get portal() {\n    return this._attachedPortal;\n  }\n  set portal(portal) {\n    // Ignore the cases where the `portal` is set to a falsy value before the lifecycle hooks have\n    // run. This handles the cases where the user might do something like `<div cdkPortalOutlet>`\n    // and attach a portal programmatically in the parent component. When Angular does the first CD\n    // round, it will fire the setter with empty string, causing the user's content to be cleared.\n    if (this.hasAttached() && !portal && !this._isInitialized) {\n      return;\n    }\n    if (this.hasAttached()) {\n      super.detach();\n    }\n    if (portal) {\n      super.attach(portal);\n    }\n    this._attachedPortal = portal || null;\n  }\n  /** Component or view reference that is attached to the portal. */\n  get attachedRef() {\n    return this._attachedRef;\n  }\n  ngOnInit() {\n    this._isInitialized = true;\n  }\n  ngOnDestroy() {\n    super.dispose();\n    this._attachedRef = this._attachedPortal = null;\n  }\n  /**\n   * Attach the given ComponentPortal to this PortalOutlet using the ComponentFactoryResolver.\n   *\n   * @param portal Portal to be attached to the portal outlet.\n   * @returns Reference to the created component.\n   */\n  attachComponentPortal(portal) {\n    portal.setAttachedHost(this);\n    // If the portal specifies an origin, use that as the logical location of the component\n    // in the application tree. Otherwise use the location of this PortalOutlet.\n    const viewContainerRef = portal.viewContainerRef != null ? portal.viewContainerRef : this._viewContainerRef;\n    const resolver = portal.componentFactoryResolver || this._componentFactoryResolver;\n    const componentFactory = resolver.resolveComponentFactory(portal.component);\n    const ref = viewContainerRef.createComponent(componentFactory, viewContainerRef.length, portal.injector || viewContainerRef.injector, portal.projectableNodes || undefined);\n    // If we're using a view container that's different from the injected one (e.g. when the portal\n    // specifies its own) we need to move the component into the outlet, otherwise it'll be rendered\n    // inside of the alternate view container.\n    if (viewContainerRef !== this._viewContainerRef) {\n      this._getRootNode().appendChild(ref.hostView.rootNodes[0]);\n    }\n    super.setDisposeFn(() => ref.destroy());\n    this._attachedPortal = portal;\n    this._attachedRef = ref;\n    this.attached.emit(ref);\n    return ref;\n  }\n  /**\n   * Attach the given TemplatePortal to this PortalHost as an embedded View.\n   * @param portal Portal to be attached.\n   * @returns Reference to the created embedded view.\n   */\n  attachTemplatePortal(portal) {\n    portal.setAttachedHost(this);\n    const viewRef = this._viewContainerRef.createEmbeddedView(portal.templateRef, portal.context, {\n      injector: portal.injector\n    });\n    super.setDisposeFn(() => this._viewContainerRef.clear());\n    this._attachedPortal = portal;\n    this._attachedRef = viewRef;\n    this.attached.emit(viewRef);\n    return viewRef;\n  }\n  /** Gets the root node of the portal outlet. */\n  _getRootNode() {\n    const nativeElement = this._viewContainerRef.element.nativeElement;\n    // The directive could be set on a template which will result in a comment\n    // node being the root. Use the comment's parent node if that is the case.\n    return nativeElement.nodeType === nativeElement.ELEMENT_NODE ? nativeElement : nativeElement.parentNode;\n  }\n  static {\n    this.ɵfac = function CdkPortalOutlet_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkPortalOutlet)(i0.ɵɵdirectiveInject(i0.ComponentFactoryResolver), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(DOCUMENT));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkPortalOutlet,\n      selectors: [[\"\", \"cdkPortalOutlet\", \"\"]],\n      inputs: {\n        portal: [0, \"cdkPortalOutlet\", \"portal\"]\n      },\n      outputs: {\n        attached: \"attached\"\n      },\n      exportAs: [\"cdkPortalOutlet\"],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkPortalOutlet, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkPortalOutlet]',\n      exportAs: 'cdkPortalOutlet',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ComponentFactoryResolver\n  }, {\n    type: i0.ViewContainerRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], {\n    portal: [{\n      type: Input,\n      args: ['cdkPortalOutlet']\n    }],\n    attached: [{\n      type: Output\n    }]\n  });\n})();\n/**\n * @deprecated Use `CdkPortalOutlet` instead.\n * @breaking-change 9.0.0\n */\nclass PortalHostDirective extends CdkPortalOutlet {\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵPortalHostDirective_BaseFactory;\n      return function PortalHostDirective_Factory(__ngFactoryType__) {\n        return (ɵPortalHostDirective_BaseFactory || (ɵPortalHostDirective_BaseFactory = i0.ɵɵgetInheritedFactory(PortalHostDirective)))(__ngFactoryType__ || PortalHostDirective);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: PortalHostDirective,\n      selectors: [[\"\", \"cdkPortalHost\", \"\"], [\"\", \"portalHost\", \"\"]],\n      inputs: {\n        portal: [0, \"cdkPortalHost\", \"portal\"]\n      },\n      exportAs: [\"cdkPortalHost\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkPortalOutlet,\n        useExisting: PortalHostDirective\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PortalHostDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkPortalHost], [portalHost]',\n      exportAs: 'cdkPortalHost',\n      inputs: [{\n        name: 'portal',\n        alias: 'cdkPortalHost'\n      }],\n      providers: [{\n        provide: CdkPortalOutlet,\n        useExisting: PortalHostDirective\n      }],\n      standalone: true\n    }]\n  }], null, null);\n})();\nclass PortalModule {\n  static {\n    this.ɵfac = function PortalModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PortalModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: PortalModule,\n      imports: [CdkPortal, CdkPortalOutlet, TemplatePortalDirective, PortalHostDirective],\n      exports: [CdkPortal, CdkPortalOutlet, TemplatePortalDirective, PortalHostDirective]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PortalModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CdkPortal, CdkPortalOutlet, TemplatePortalDirective, PortalHostDirective],\n      exports: [CdkPortal, CdkPortalOutlet, TemplatePortalDirective, PortalHostDirective]\n    }]\n  }], null, null);\n})();\n\n/**\n * Custom injector to be used when providing custom\n * injection tokens to components inside a portal.\n * @docs-private\n * @deprecated Use `Injector.create` instead.\n * @breaking-change 11.0.0\n */\nclass PortalInjector {\n  constructor(_parentInjector, _customTokens) {\n    this._parentInjector = _parentInjector;\n    this._customTokens = _customTokens;\n  }\n  get(token, notFoundValue) {\n    const value = this._customTokens.get(token);\n    if (typeof value !== 'undefined') {\n      return value;\n    }\n    return this._parentInjector.get(token, notFoundValue);\n  }\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BasePortalHost, BasePortalOutlet, CdkPortal, CdkPortalOutlet, ComponentPortal, DomPortal, DomPortalHost, DomPortalOutlet, Portal, PortalHostDirective, PortalInjector, PortalModule, TemplatePortal, TemplatePortalDirective };\n", "import * as i1 from '@angular/cdk/scrolling';\nimport { ScrollingModule } from '@angular/cdk/scrolling';\nexport { CdkScrollable, ScrollDispatcher, ViewportRuler } from '@angular/cdk/scrolling';\nimport * as i6 from '@angular/common';\nimport { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, Inject, Optional, untracked, afterRender, afterNextRender, ElementRef, EnvironmentInjector, ApplicationRef, ANIMATION_MODULE_TYPE, InjectionToken, inject, Directive, NgZone, EventEmitter, booleanAttribute, Input, Output, NgModule } from '@angular/core';\nimport { coerceCssPixelValue, coerceArray } from '@angular/cdk/coercion';\nimport * as i1$1 from '@angular/cdk/platform';\nimport { supportsScrollBehavior, _getEventTarget, _isTestEnvironment } from '@angular/cdk/platform';\nimport { filter, takeUntil, takeWhile } from 'rxjs/operators';\nimport * as i5 from '@angular/cdk/bidi';\nimport { BidiModule } from '@angular/cdk/bidi';\nimport { DomPortalOutlet, TemplatePortal, PortalModule } from '@angular/cdk/portal';\nimport { Subject, Subscription, merge } from 'rxjs';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\nconst scrollBehaviorSupported = supportsScrollBehavior();\n/**\n * Strategy that will prevent the user from scrolling while the overlay is visible.\n */\nclass BlockScrollStrategy {\n  constructor(_viewportRuler, document) {\n    this._viewportRuler = _viewportRuler;\n    this._previousHTMLStyles = {\n      top: '',\n      left: ''\n    };\n    this._isEnabled = false;\n    this._document = document;\n  }\n  /** Attaches this scroll strategy to an overlay. */\n  attach() {}\n  /** Blocks page-level scroll while the attached overlay is open. */\n  enable() {\n    if (this._canBeEnabled()) {\n      const root = this._document.documentElement;\n      this._previousScrollPosition = this._viewportRuler.getViewportScrollPosition();\n      // Cache the previous inline styles in case the user had set them.\n      this._previousHTMLStyles.left = root.style.left || '';\n      this._previousHTMLStyles.top = root.style.top || '';\n      // Note: we're using the `html` node, instead of the `body`, because the `body` may\n      // have the user agent margin, whereas the `html` is guaranteed not to have one.\n      root.style.left = coerceCssPixelValue(-this._previousScrollPosition.left);\n      root.style.top = coerceCssPixelValue(-this._previousScrollPosition.top);\n      root.classList.add('cdk-global-scrollblock');\n      this._isEnabled = true;\n    }\n  }\n  /** Unblocks page-level scroll while the attached overlay is open. */\n  disable() {\n    if (this._isEnabled) {\n      const html = this._document.documentElement;\n      const body = this._document.body;\n      const htmlStyle = html.style;\n      const bodyStyle = body.style;\n      const previousHtmlScrollBehavior = htmlStyle.scrollBehavior || '';\n      const previousBodyScrollBehavior = bodyStyle.scrollBehavior || '';\n      this._isEnabled = false;\n      htmlStyle.left = this._previousHTMLStyles.left;\n      htmlStyle.top = this._previousHTMLStyles.top;\n      html.classList.remove('cdk-global-scrollblock');\n      // Disable user-defined smooth scrolling temporarily while we restore the scroll position.\n      // See https://developer.mozilla.org/en-US/docs/Web/CSS/scroll-behavior\n      // Note that we don't mutate the property if the browser doesn't support `scroll-behavior`,\n      // because it can throw off feature detections in `supportsScrollBehavior` which\n      // checks for `'scrollBehavior' in documentElement.style`.\n      if (scrollBehaviorSupported) {\n        htmlStyle.scrollBehavior = bodyStyle.scrollBehavior = 'auto';\n      }\n      window.scroll(this._previousScrollPosition.left, this._previousScrollPosition.top);\n      if (scrollBehaviorSupported) {\n        htmlStyle.scrollBehavior = previousHtmlScrollBehavior;\n        bodyStyle.scrollBehavior = previousBodyScrollBehavior;\n      }\n    }\n  }\n  _canBeEnabled() {\n    // Since the scroll strategies can't be singletons, we have to use a global CSS class\n    // (`cdk-global-scrollblock`) to make sure that we don't try to disable global\n    // scrolling multiple times.\n    const html = this._document.documentElement;\n    if (html.classList.contains('cdk-global-scrollblock') || this._isEnabled) {\n      return false;\n    }\n    const body = this._document.body;\n    const viewport = this._viewportRuler.getViewportSize();\n    return body.scrollHeight > viewport.height || body.scrollWidth > viewport.width;\n  }\n}\n\n/**\n * Returns an error to be thrown when attempting to attach an already-attached scroll strategy.\n */\nfunction getMatScrollStrategyAlreadyAttachedError() {\n  return Error(`Scroll strategy has already been attached.`);\n}\n\n/**\n * Strategy that will close the overlay as soon as the user starts scrolling.\n */\nclass CloseScrollStrategy {\n  constructor(_scrollDispatcher, _ngZone, _viewportRuler, _config) {\n    this._scrollDispatcher = _scrollDispatcher;\n    this._ngZone = _ngZone;\n    this._viewportRuler = _viewportRuler;\n    this._config = _config;\n    this._scrollSubscription = null;\n    /** Detaches the overlay ref and disables the scroll strategy. */\n    this._detach = () => {\n      this.disable();\n      if (this._overlayRef.hasAttached()) {\n        this._ngZone.run(() => this._overlayRef.detach());\n      }\n    };\n  }\n  /** Attaches this scroll strategy to an overlay. */\n  attach(overlayRef) {\n    if (this._overlayRef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatScrollStrategyAlreadyAttachedError();\n    }\n    this._overlayRef = overlayRef;\n  }\n  /** Enables the closing of the attached overlay on scroll. */\n  enable() {\n    if (this._scrollSubscription) {\n      return;\n    }\n    const stream = this._scrollDispatcher.scrolled(0).pipe(filter(scrollable => {\n      return !scrollable || !this._overlayRef.overlayElement.contains(scrollable.getElementRef().nativeElement);\n    }));\n    if (this._config && this._config.threshold && this._config.threshold > 1) {\n      this._initialScrollPosition = this._viewportRuler.getViewportScrollPosition().top;\n      this._scrollSubscription = stream.subscribe(() => {\n        const scrollPosition = this._viewportRuler.getViewportScrollPosition().top;\n        if (Math.abs(scrollPosition - this._initialScrollPosition) > this._config.threshold) {\n          this._detach();\n        } else {\n          this._overlayRef.updatePosition();\n        }\n      });\n    } else {\n      this._scrollSubscription = stream.subscribe(this._detach);\n    }\n  }\n  /** Disables the closing the attached overlay on scroll. */\n  disable() {\n    if (this._scrollSubscription) {\n      this._scrollSubscription.unsubscribe();\n      this._scrollSubscription = null;\n    }\n  }\n  detach() {\n    this.disable();\n    this._overlayRef = null;\n  }\n}\n\n/** Scroll strategy that doesn't do anything. */\nclass NoopScrollStrategy {\n  /** Does nothing, as this scroll strategy is a no-op. */\n  enable() {}\n  /** Does nothing, as this scroll strategy is a no-op. */\n  disable() {}\n  /** Does nothing, as this scroll strategy is a no-op. */\n  attach() {}\n}\n\n/**\n * Gets whether an element is scrolled outside of view by any of its parent scrolling containers.\n * @param element Dimensions of the element (from getBoundingClientRect)\n * @param scrollContainers Dimensions of element's scrolling containers (from getBoundingClientRect)\n * @returns Whether the element is scrolled out of view\n * @docs-private\n */\nfunction isElementScrolledOutsideView(element, scrollContainers) {\n  return scrollContainers.some(containerBounds => {\n    const outsideAbove = element.bottom < containerBounds.top;\n    const outsideBelow = element.top > containerBounds.bottom;\n    const outsideLeft = element.right < containerBounds.left;\n    const outsideRight = element.left > containerBounds.right;\n    return outsideAbove || outsideBelow || outsideLeft || outsideRight;\n  });\n}\n/**\n * Gets whether an element is clipped by any of its scrolling containers.\n * @param element Dimensions of the element (from getBoundingClientRect)\n * @param scrollContainers Dimensions of element's scrolling containers (from getBoundingClientRect)\n * @returns Whether the element is clipped\n * @docs-private\n */\nfunction isElementClippedByScrolling(element, scrollContainers) {\n  return scrollContainers.some(scrollContainerRect => {\n    const clippedAbove = element.top < scrollContainerRect.top;\n    const clippedBelow = element.bottom > scrollContainerRect.bottom;\n    const clippedLeft = element.left < scrollContainerRect.left;\n    const clippedRight = element.right > scrollContainerRect.right;\n    return clippedAbove || clippedBelow || clippedLeft || clippedRight;\n  });\n}\n\n/**\n * Strategy that will update the element position as the user is scrolling.\n */\nclass RepositionScrollStrategy {\n  constructor(_scrollDispatcher, _viewportRuler, _ngZone, _config) {\n    this._scrollDispatcher = _scrollDispatcher;\n    this._viewportRuler = _viewportRuler;\n    this._ngZone = _ngZone;\n    this._config = _config;\n    this._scrollSubscription = null;\n  }\n  /** Attaches this scroll strategy to an overlay. */\n  attach(overlayRef) {\n    if (this._overlayRef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatScrollStrategyAlreadyAttachedError();\n    }\n    this._overlayRef = overlayRef;\n  }\n  /** Enables repositioning of the attached overlay on scroll. */\n  enable() {\n    if (!this._scrollSubscription) {\n      const throttle = this._config ? this._config.scrollThrottle : 0;\n      this._scrollSubscription = this._scrollDispatcher.scrolled(throttle).subscribe(() => {\n        this._overlayRef.updatePosition();\n        // TODO(crisbeto): make `close` on by default once all components can handle it.\n        if (this._config && this._config.autoClose) {\n          const overlayRect = this._overlayRef.overlayElement.getBoundingClientRect();\n          const {\n            width,\n            height\n          } = this._viewportRuler.getViewportSize();\n          // TODO(crisbeto): include all ancestor scroll containers here once\n          // we have a way of exposing the trigger element to the scroll strategy.\n          const parentRects = [{\n            width,\n            height,\n            bottom: height,\n            right: width,\n            top: 0,\n            left: 0\n          }];\n          if (isElementScrolledOutsideView(overlayRect, parentRects)) {\n            this.disable();\n            this._ngZone.run(() => this._overlayRef.detach());\n          }\n        }\n      });\n    }\n  }\n  /** Disables repositioning of the attached overlay on scroll. */\n  disable() {\n    if (this._scrollSubscription) {\n      this._scrollSubscription.unsubscribe();\n      this._scrollSubscription = null;\n    }\n  }\n  detach() {\n    this.disable();\n    this._overlayRef = null;\n  }\n}\n\n/**\n * Options for how an overlay will handle scrolling.\n *\n * Users can provide a custom value for `ScrollStrategyOptions` to replace the default\n * behaviors. This class primarily acts as a factory for ScrollStrategy instances.\n */\nclass ScrollStrategyOptions {\n  constructor(_scrollDispatcher, _viewportRuler, _ngZone, document) {\n    this._scrollDispatcher = _scrollDispatcher;\n    this._viewportRuler = _viewportRuler;\n    this._ngZone = _ngZone;\n    /** Do nothing on scroll. */\n    this.noop = () => new NoopScrollStrategy();\n    /**\n     * Close the overlay as soon as the user scrolls.\n     * @param config Configuration to be used inside the scroll strategy.\n     */\n    this.close = config => new CloseScrollStrategy(this._scrollDispatcher, this._ngZone, this._viewportRuler, config);\n    /** Block scrolling. */\n    this.block = () => new BlockScrollStrategy(this._viewportRuler, this._document);\n    /**\n     * Update the overlay's position on scroll.\n     * @param config Configuration to be used inside the scroll strategy.\n     * Allows debouncing the reposition calls.\n     */\n    this.reposition = config => new RepositionScrollStrategy(this._scrollDispatcher, this._viewportRuler, this._ngZone, config);\n    this._document = document;\n  }\n  static {\n    this.ɵfac = function ScrollStrategyOptions_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ScrollStrategyOptions)(i0.ɵɵinject(i1.ScrollDispatcher), i0.ɵɵinject(i1.ViewportRuler), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(DOCUMENT));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ScrollStrategyOptions,\n      factory: ScrollStrategyOptions.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ScrollStrategyOptions, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i1.ScrollDispatcher\n  }, {\n    type: i1.ViewportRuler\n  }, {\n    type: i0.NgZone\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\n\n/** Initial configuration used when creating an overlay. */\nclass OverlayConfig {\n  constructor(config) {\n    /** Strategy to be used when handling scroll events while the overlay is open. */\n    this.scrollStrategy = new NoopScrollStrategy();\n    /** Custom class to add to the overlay pane. */\n    this.panelClass = '';\n    /** Whether the overlay has a backdrop. */\n    this.hasBackdrop = false;\n    /** Custom class to add to the backdrop */\n    this.backdropClass = 'cdk-overlay-dark-backdrop';\n    /**\n     * Whether the overlay should be disposed of when the user goes backwards/forwards in history.\n     * Note that this usually doesn't include clicking on links (unless the user is using\n     * the `HashLocationStrategy`).\n     */\n    this.disposeOnNavigation = false;\n    if (config) {\n      // Use `Iterable` instead of `Array` because TypeScript, as of 3.6.3,\n      // loses the array generic type in the `for of`. But we *also* have to use `Array` because\n      // typescript won't iterate over an `Iterable` unless you compile with `--downlevelIteration`\n      const configKeys = Object.keys(config);\n      for (const key of configKeys) {\n        if (config[key] !== undefined) {\n          // TypeScript, as of version 3.5, sees the left-hand-side of this expression\n          // as \"I don't know *which* key this is, so the only valid value is the intersection\n          // of all the possible values.\" In this case, that happens to be `undefined`. TypeScript\n          // is not smart enough to see that the right-hand-side is actually an access of the same\n          // exact type with the same exact key, meaning that the value type must be identical.\n          // So we use `any` to work around this.\n          this[key] = config[key];\n        }\n      }\n    }\n  }\n}\n\n/** The points of the origin element and the overlay element to connect. */\nclass ConnectionPositionPair {\n  constructor(origin, overlay, /** Offset along the X axis. */\n  offsetX, /** Offset along the Y axis. */\n  offsetY, /** Class(es) to be applied to the panel while this position is active. */\n  panelClass) {\n    this.offsetX = offsetX;\n    this.offsetY = offsetY;\n    this.panelClass = panelClass;\n    this.originX = origin.originX;\n    this.originY = origin.originY;\n    this.overlayX = overlay.overlayX;\n    this.overlayY = overlay.overlayY;\n  }\n}\n/**\n * Set of properties regarding the position of the origin and overlay relative to the viewport\n * with respect to the containing Scrollable elements.\n *\n * The overlay and origin are clipped if any part of their bounding client rectangle exceeds the\n * bounds of any one of the strategy's Scrollable's bounding client rectangle.\n *\n * The overlay and origin are outside view if there is no overlap between their bounding client\n * rectangle and any one of the strategy's Scrollable's bounding client rectangle.\n *\n *       -----------                    -----------\n *       | outside |                    | clipped |\n *       |  view   |              --------------------------\n *       |         |              |     |         |        |\n *       ----------               |     -----------        |\n *  --------------------------    |                        |\n *  |                        |    |      Scrollable        |\n *  |                        |    |                        |\n *  |                        |     --------------------------\n *  |      Scrollable        |\n *  |                        |\n *  --------------------------\n *\n *  @docs-private\n */\nclass ScrollingVisibility {}\n/** The change event emitted by the strategy when a fallback position is used. */\nclass ConnectedOverlayPositionChange {\n  constructor(/** The position used as a result of this change. */\n  connectionPair, /** @docs-private */\n  scrollableViewProperties) {\n    this.connectionPair = connectionPair;\n    this.scrollableViewProperties = scrollableViewProperties;\n  }\n}\n/**\n * Validates whether a vertical position property matches the expected values.\n * @param property Name of the property being validated.\n * @param value Value of the property being validated.\n * @docs-private\n */\nfunction validateVerticalPosition(property, value) {\n  if (value !== 'top' && value !== 'bottom' && value !== 'center') {\n    throw Error(`ConnectedPosition: Invalid ${property} \"${value}\". ` + `Expected \"top\", \"bottom\" or \"center\".`);\n  }\n}\n/**\n * Validates whether a horizontal position property matches the expected values.\n * @param property Name of the property being validated.\n * @param value Value of the property being validated.\n * @docs-private\n */\nfunction validateHorizontalPosition(property, value) {\n  if (value !== 'start' && value !== 'end' && value !== 'center') {\n    throw Error(`ConnectedPosition: Invalid ${property} \"${value}\". ` + `Expected \"start\", \"end\" or \"center\".`);\n  }\n}\n\n/**\n * Service for dispatching events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\nclass BaseOverlayDispatcher {\n  constructor(document) {\n    /** Currently attached overlays in the order they were attached. */\n    this._attachedOverlays = [];\n    this._document = document;\n  }\n  ngOnDestroy() {\n    this.detach();\n  }\n  /** Add a new overlay to the list of attached overlay refs. */\n  add(overlayRef) {\n    // Ensure that we don't get the same overlay multiple times.\n    this.remove(overlayRef);\n    this._attachedOverlays.push(overlayRef);\n  }\n  /** Remove an overlay from the list of attached overlay refs. */\n  remove(overlayRef) {\n    const index = this._attachedOverlays.indexOf(overlayRef);\n    if (index > -1) {\n      this._attachedOverlays.splice(index, 1);\n    }\n    // Remove the global listener once there are no more overlays.\n    if (this._attachedOverlays.length === 0) {\n      this.detach();\n    }\n  }\n  static {\n    this.ɵfac = function BaseOverlayDispatcher_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || BaseOverlayDispatcher)(i0.ɵɵinject(DOCUMENT));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: BaseOverlayDispatcher,\n      factory: BaseOverlayDispatcher.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BaseOverlayDispatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\n\n/**\n * Service for dispatching keyboard events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\nclass OverlayKeyboardDispatcher extends BaseOverlayDispatcher {\n  constructor(document, /** @breaking-change 14.0.0 _ngZone will be required. */\n  _ngZone) {\n    super(document);\n    this._ngZone = _ngZone;\n    /** Keyboard event listener that will be attached to the body. */\n    this._keydownListener = event => {\n      const overlays = this._attachedOverlays;\n      for (let i = overlays.length - 1; i > -1; i--) {\n        // Dispatch the keydown event to the top overlay which has subscribers to its keydown events.\n        // We want to target the most recent overlay, rather than trying to match where the event came\n        // from, because some components might open an overlay, but keep focus on a trigger element\n        // (e.g. for select and autocomplete). We skip overlays without keydown event subscriptions,\n        // because we don't want overlays that don't handle keyboard events to block the ones below\n        // them that do.\n        if (overlays[i]._keydownEvents.observers.length > 0) {\n          const keydownEvents = overlays[i]._keydownEvents;\n          /** @breaking-change 14.0.0 _ngZone will be required. */\n          if (this._ngZone) {\n            this._ngZone.run(() => keydownEvents.next(event));\n          } else {\n            keydownEvents.next(event);\n          }\n          break;\n        }\n      }\n    };\n  }\n  /** Add a new overlay to the list of attached overlay refs. */\n  add(overlayRef) {\n    super.add(overlayRef);\n    // Lazily start dispatcher once first overlay is added\n    if (!this._isAttached) {\n      /** @breaking-change 14.0.0 _ngZone will be required. */\n      if (this._ngZone) {\n        this._ngZone.runOutsideAngular(() => this._document.body.addEventListener('keydown', this._keydownListener));\n      } else {\n        this._document.body.addEventListener('keydown', this._keydownListener);\n      }\n      this._isAttached = true;\n    }\n  }\n  /** Detaches the global keyboard event listener. */\n  detach() {\n    if (this._isAttached) {\n      this._document.body.removeEventListener('keydown', this._keydownListener);\n      this._isAttached = false;\n    }\n  }\n  static {\n    this.ɵfac = function OverlayKeyboardDispatcher_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || OverlayKeyboardDispatcher)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i0.NgZone, 8));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: OverlayKeyboardDispatcher,\n      factory: OverlayKeyboardDispatcher.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayKeyboardDispatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: i0.NgZone,\n    decorators: [{\n      type: Optional\n    }]\n  }], null);\n})();\n\n/**\n * Service for dispatching mouse click events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\nclass OverlayOutsideClickDispatcher extends BaseOverlayDispatcher {\n  constructor(document, _platform, /** @breaking-change 14.0.0 _ngZone will be required. */\n  _ngZone) {\n    super(document);\n    this._platform = _platform;\n    this._ngZone = _ngZone;\n    this._cursorStyleIsSet = false;\n    /** Store pointerdown event target to track origin of click. */\n    this._pointerDownListener = event => {\n      this._pointerDownEventTarget = _getEventTarget(event);\n    };\n    /** Click event listener that will be attached to the body propagate phase. */\n    this._clickListener = event => {\n      const target = _getEventTarget(event);\n      // In case of a click event, we want to check the origin of the click\n      // (e.g. in case where a user starts a click inside the overlay and\n      // releases the click outside of it).\n      // This is done by using the event target of the preceding pointerdown event.\n      // Every click event caused by a pointer device has a preceding pointerdown\n      // event, unless the click was programmatically triggered (e.g. in a unit test).\n      const origin = event.type === 'click' && this._pointerDownEventTarget ? this._pointerDownEventTarget : target;\n      // Reset the stored pointerdown event target, to avoid having it interfere\n      // in subsequent events.\n      this._pointerDownEventTarget = null;\n      // We copy the array because the original may be modified asynchronously if the\n      // outsidePointerEvents listener decides to detach overlays resulting in index errors inside\n      // the for loop.\n      const overlays = this._attachedOverlays.slice();\n      // Dispatch the mouse event to the top overlay which has subscribers to its mouse events.\n      // We want to target all overlays for which the click could be considered as outside click.\n      // As soon as we reach an overlay for which the click is not outside click we break off\n      // the loop.\n      for (let i = overlays.length - 1; i > -1; i--) {\n        const overlayRef = overlays[i];\n        if (overlayRef._outsidePointerEvents.observers.length < 1 || !overlayRef.hasAttached()) {\n          continue;\n        }\n        // If it's a click inside the overlay, just break - we should do nothing\n        // If it's an outside click (both origin and target of the click) dispatch the mouse event,\n        // and proceed with the next overlay\n        if (containsPierceShadowDom(overlayRef.overlayElement, target) || containsPierceShadowDom(overlayRef.overlayElement, origin)) {\n          break;\n        }\n        const outsidePointerEvents = overlayRef._outsidePointerEvents;\n        /** @breaking-change 14.0.0 _ngZone will be required. */\n        if (this._ngZone) {\n          this._ngZone.run(() => outsidePointerEvents.next(event));\n        } else {\n          outsidePointerEvents.next(event);\n        }\n      }\n    };\n  }\n  /** Add a new overlay to the list of attached overlay refs. */\n  add(overlayRef) {\n    super.add(overlayRef);\n    // Safari on iOS does not generate click events for non-interactive\n    // elements. However, we want to receive a click for any element outside\n    // the overlay. We can force a \"clickable\" state by setting\n    // `cursor: pointer` on the document body. See:\n    // https://developer.mozilla.org/en-US/docs/Web/API/Element/click_event#Safari_Mobile\n    // https://developer.apple.com/library/archive/documentation/AppleApplications/Reference/SafariWebContent/HandlingEvents/HandlingEvents.html\n    if (!this._isAttached) {\n      const body = this._document.body;\n      /** @breaking-change 14.0.0 _ngZone will be required. */\n      if (this._ngZone) {\n        this._ngZone.runOutsideAngular(() => this._addEventListeners(body));\n      } else {\n        this._addEventListeners(body);\n      }\n      // click event is not fired on iOS. To make element \"clickable\" we are\n      // setting the cursor to pointer\n      if (this._platform.IOS && !this._cursorStyleIsSet) {\n        this._cursorOriginalValue = body.style.cursor;\n        body.style.cursor = 'pointer';\n        this._cursorStyleIsSet = true;\n      }\n      this._isAttached = true;\n    }\n  }\n  /** Detaches the global keyboard event listener. */\n  detach() {\n    if (this._isAttached) {\n      const body = this._document.body;\n      body.removeEventListener('pointerdown', this._pointerDownListener, true);\n      body.removeEventListener('click', this._clickListener, true);\n      body.removeEventListener('auxclick', this._clickListener, true);\n      body.removeEventListener('contextmenu', this._clickListener, true);\n      if (this._platform.IOS && this._cursorStyleIsSet) {\n        body.style.cursor = this._cursorOriginalValue;\n        this._cursorStyleIsSet = false;\n      }\n      this._isAttached = false;\n    }\n  }\n  _addEventListeners(body) {\n    body.addEventListener('pointerdown', this._pointerDownListener, true);\n    body.addEventListener('click', this._clickListener, true);\n    body.addEventListener('auxclick', this._clickListener, true);\n    body.addEventListener('contextmenu', this._clickListener, true);\n  }\n  static {\n    this.ɵfac = function OverlayOutsideClickDispatcher_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || OverlayOutsideClickDispatcher)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i1$1.Platform), i0.ɵɵinject(i0.NgZone, 8));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: OverlayOutsideClickDispatcher,\n      factory: OverlayOutsideClickDispatcher.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayOutsideClickDispatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: i1$1.Platform\n  }, {\n    type: i0.NgZone,\n    decorators: [{\n      type: Optional\n    }]\n  }], null);\n})();\n/** Version of `Element.contains` that transcends shadow DOM boundaries. */\nfunction containsPierceShadowDom(parent, child) {\n  const supportsShadowRoot = typeof ShadowRoot !== 'undefined' && ShadowRoot;\n  let current = child;\n  while (current) {\n    if (current === parent) {\n      return true;\n    }\n    current = supportsShadowRoot && current instanceof ShadowRoot ? current.host : current.parentNode;\n  }\n  return false;\n}\n\n/** Container inside which all overlays will render. */\nclass OverlayContainer {\n  constructor(document, _platform) {\n    this._platform = _platform;\n    this._document = document;\n  }\n  ngOnDestroy() {\n    this._containerElement?.remove();\n  }\n  /**\n   * This method returns the overlay container element. It will lazily\n   * create the element the first time it is called to facilitate using\n   * the container in non-browser environments.\n   * @returns the container element\n   */\n  getContainerElement() {\n    if (!this._containerElement) {\n      this._createContainer();\n    }\n    return this._containerElement;\n  }\n  /**\n   * Create the overlay container element, which is simply a div\n   * with the 'cdk-overlay-container' class on the document body.\n   */\n  _createContainer() {\n    const containerClass = 'cdk-overlay-container';\n    // TODO(crisbeto): remove the testing check once we have an overlay testing\n    // module or Angular starts tearing down the testing `NgModule`. See:\n    // https://github.com/angular/angular/issues/18831\n    if (this._platform.isBrowser || _isTestEnvironment()) {\n      const oppositePlatformContainers = this._document.querySelectorAll(`.${containerClass}[platform=\"server\"], ` + `.${containerClass}[platform=\"test\"]`);\n      // Remove any old containers from the opposite platform.\n      // This can happen when transitioning from the server to the client.\n      for (let i = 0; i < oppositePlatformContainers.length; i++) {\n        oppositePlatformContainers[i].remove();\n      }\n    }\n    const container = this._document.createElement('div');\n    container.classList.add(containerClass);\n    // A long time ago we kept adding new overlay containers whenever a new app was instantiated,\n    // but at some point we added logic which clears the duplicate ones in order to avoid leaks.\n    // The new logic was a little too aggressive since it was breaking some legitimate use cases.\n    // To mitigate the problem we made it so that only containers from a different platform are\n    // cleared, but the side-effect was that people started depending on the overly-aggressive\n    // logic to clean up their tests for them. Until we can introduce an overlay-specific testing\n    // module which does the cleanup, we try to detect that we're in a test environment and we\n    // always clear the container. See #17006.\n    // TODO(crisbeto): remove the test environment check once we have an overlay testing module.\n    if (_isTestEnvironment()) {\n      container.setAttribute('platform', 'test');\n    } else if (!this._platform.isBrowser) {\n      container.setAttribute('platform', 'server');\n    }\n    this._document.body.appendChild(container);\n    this._containerElement = container;\n  }\n  static {\n    this.ɵfac = function OverlayContainer_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || OverlayContainer)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i1$1.Platform));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: OverlayContainer,\n      factory: OverlayContainer.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayContainer, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: i1$1.Platform\n  }], null);\n})();\n\n/**\n * Reference to an overlay that has been created with the Overlay service.\n * Used to manipulate or dispose of said overlay.\n */\nclass OverlayRef {\n  constructor(_portalOutlet, _host, _pane, _config, _ngZone, _keyboardDispatcher, _document, _location, _outsideClickDispatcher, _animationsDisabled = false, _injector) {\n    this._portalOutlet = _portalOutlet;\n    this._host = _host;\n    this._pane = _pane;\n    this._config = _config;\n    this._ngZone = _ngZone;\n    this._keyboardDispatcher = _keyboardDispatcher;\n    this._document = _document;\n    this._location = _location;\n    this._outsideClickDispatcher = _outsideClickDispatcher;\n    this._animationsDisabled = _animationsDisabled;\n    this._injector = _injector;\n    this._backdropElement = null;\n    this._backdropClick = new Subject();\n    this._attachments = new Subject();\n    this._detachments = new Subject();\n    this._locationChanges = Subscription.EMPTY;\n    this._backdropClickHandler = event => this._backdropClick.next(event);\n    this._backdropTransitionendHandler = event => {\n      this._disposeBackdrop(event.target);\n    };\n    /** Stream of keydown events dispatched to this overlay. */\n    this._keydownEvents = new Subject();\n    /** Stream of mouse outside events dispatched to this overlay. */\n    this._outsidePointerEvents = new Subject();\n    this._renders = new Subject();\n    if (_config.scrollStrategy) {\n      this._scrollStrategy = _config.scrollStrategy;\n      this._scrollStrategy.attach(this);\n    }\n    this._positionStrategy = _config.positionStrategy;\n    // Users could open the overlay from an `effect`, in which case we need to\n    // run the `afterRender` as `untracked`. We don't recommend that users do\n    // this, but we also don't want to break users who are doing it.\n    this._afterRenderRef = untracked(() => afterRender(() => {\n      this._renders.next();\n    }, {\n      injector: this._injector\n    }));\n  }\n  /** The overlay's HTML element */\n  get overlayElement() {\n    return this._pane;\n  }\n  /** The overlay's backdrop HTML element. */\n  get backdropElement() {\n    return this._backdropElement;\n  }\n  /**\n   * Wrapper around the panel element. Can be used for advanced\n   * positioning where a wrapper with specific styling is\n   * required around the overlay pane.\n   */\n  get hostElement() {\n    return this._host;\n  }\n  /**\n   * Attaches content, given via a Portal, to the overlay.\n   * If the overlay is configured to have a backdrop, it will be created.\n   *\n   * @param portal Portal instance to which to attach the overlay.\n   * @returns The portal attachment result.\n   */\n  attach(portal) {\n    // Insert the host into the DOM before attaching the portal, otherwise\n    // the animations module will skip animations on repeat attachments.\n    if (!this._host.parentElement && this._previousHostParent) {\n      this._previousHostParent.appendChild(this._host);\n    }\n    const attachResult = this._portalOutlet.attach(portal);\n    if (this._positionStrategy) {\n      this._positionStrategy.attach(this);\n    }\n    this._updateStackingOrder();\n    this._updateElementSize();\n    this._updateElementDirection();\n    if (this._scrollStrategy) {\n      this._scrollStrategy.enable();\n    }\n    // We need to clean this up ourselves, because we're passing in an\n    // `EnvironmentInjector` below which won't ever be destroyed.\n    // Otherwise it causes some callbacks to be retained (see #29696).\n    this._afterNextRenderRef?.destroy();\n    // Update the position once the overlay is fully rendered before attempting to position it,\n    // as the position may depend on the size of the rendered content.\n    this._afterNextRenderRef = afterNextRender(() => {\n      // The overlay could've been detached before the callback executed.\n      if (this.hasAttached()) {\n        this.updatePosition();\n      }\n    }, {\n      injector: this._injector\n    });\n    // Enable pointer events for the overlay pane element.\n    this._togglePointerEvents(true);\n    if (this._config.hasBackdrop) {\n      this._attachBackdrop();\n    }\n    if (this._config.panelClass) {\n      this._toggleClasses(this._pane, this._config.panelClass, true);\n    }\n    // Only emit the `attachments` event once all other setup is done.\n    this._attachments.next();\n    // Track this overlay by the keyboard dispatcher\n    this._keyboardDispatcher.add(this);\n    if (this._config.disposeOnNavigation) {\n      this._locationChanges = this._location.subscribe(() => this.dispose());\n    }\n    this._outsideClickDispatcher.add(this);\n    // TODO(crisbeto): the null check is here, because the portal outlet returns `any`.\n    // We should be guaranteed for the result to be `ComponentRef | EmbeddedViewRef`, but\n    // `instanceof EmbeddedViewRef` doesn't appear to work at the moment.\n    if (typeof attachResult?.onDestroy === 'function') {\n      // In most cases we control the portal and we know when it is being detached so that\n      // we can finish the disposal process. The exception is if the user passes in a custom\n      // `ViewContainerRef` that isn't destroyed through the overlay API. Note that we use\n      // `detach` here instead of `dispose`, because we don't know if the user intends to\n      // reattach the overlay at a later point. It also has the advantage of waiting for animations.\n      attachResult.onDestroy(() => {\n        if (this.hasAttached()) {\n          // We have to delay the `detach` call, because detaching immediately prevents\n          // other destroy hooks from running. This is likely a framework bug similar to\n          // https://github.com/angular/angular/issues/46119\n          this._ngZone.runOutsideAngular(() => Promise.resolve().then(() => this.detach()));\n        }\n      });\n    }\n    return attachResult;\n  }\n  /**\n   * Detaches an overlay from a portal.\n   * @returns The portal detachment result.\n   */\n  detach() {\n    if (!this.hasAttached()) {\n      return;\n    }\n    this.detachBackdrop();\n    // When the overlay is detached, the pane element should disable pointer events.\n    // This is necessary because otherwise the pane element will cover the page and disable\n    // pointer events therefore. Depends on the position strategy and the applied pane boundaries.\n    this._togglePointerEvents(false);\n    if (this._positionStrategy && this._positionStrategy.detach) {\n      this._positionStrategy.detach();\n    }\n    if (this._scrollStrategy) {\n      this._scrollStrategy.disable();\n    }\n    const detachmentResult = this._portalOutlet.detach();\n    // Only emit after everything is detached.\n    this._detachments.next();\n    // Remove this overlay from keyboard dispatcher tracking.\n    this._keyboardDispatcher.remove(this);\n    // Keeping the host element in the DOM can cause scroll jank, because it still gets\n    // rendered, even though it's transparent and unclickable which is why we remove it.\n    this._detachContentWhenEmpty();\n    this._locationChanges.unsubscribe();\n    this._outsideClickDispatcher.remove(this);\n    return detachmentResult;\n  }\n  /** Cleans up the overlay from the DOM. */\n  dispose() {\n    const isAttached = this.hasAttached();\n    if (this._positionStrategy) {\n      this._positionStrategy.dispose();\n    }\n    this._disposeScrollStrategy();\n    this._disposeBackdrop(this._backdropElement);\n    this._locationChanges.unsubscribe();\n    this._keyboardDispatcher.remove(this);\n    this._portalOutlet.dispose();\n    this._attachments.complete();\n    this._backdropClick.complete();\n    this._keydownEvents.complete();\n    this._outsidePointerEvents.complete();\n    this._outsideClickDispatcher.remove(this);\n    this._host?.remove();\n    this._afterNextRenderRef?.destroy();\n    this._previousHostParent = this._pane = this._host = null;\n    if (isAttached) {\n      this._detachments.next();\n    }\n    this._detachments.complete();\n    this._afterRenderRef.destroy();\n    this._renders.complete();\n  }\n  /** Whether the overlay has attached content. */\n  hasAttached() {\n    return this._portalOutlet.hasAttached();\n  }\n  /** Gets an observable that emits when the backdrop has been clicked. */\n  backdropClick() {\n    return this._backdropClick;\n  }\n  /** Gets an observable that emits when the overlay has been attached. */\n  attachments() {\n    return this._attachments;\n  }\n  /** Gets an observable that emits when the overlay has been detached. */\n  detachments() {\n    return this._detachments;\n  }\n  /** Gets an observable of keydown events targeted to this overlay. */\n  keydownEvents() {\n    return this._keydownEvents;\n  }\n  /** Gets an observable of pointer events targeted outside this overlay. */\n  outsidePointerEvents() {\n    return this._outsidePointerEvents;\n  }\n  /** Gets the current overlay configuration, which is immutable. */\n  getConfig() {\n    return this._config;\n  }\n  /** Updates the position of the overlay based on the position strategy. */\n  updatePosition() {\n    if (this._positionStrategy) {\n      this._positionStrategy.apply();\n    }\n  }\n  /** Switches to a new position strategy and updates the overlay position. */\n  updatePositionStrategy(strategy) {\n    if (strategy === this._positionStrategy) {\n      return;\n    }\n    if (this._positionStrategy) {\n      this._positionStrategy.dispose();\n    }\n    this._positionStrategy = strategy;\n    if (this.hasAttached()) {\n      strategy.attach(this);\n      this.updatePosition();\n    }\n  }\n  /** Update the size properties of the overlay. */\n  updateSize(sizeConfig) {\n    this._config = {\n      ...this._config,\n      ...sizeConfig\n    };\n    this._updateElementSize();\n  }\n  /** Sets the LTR/RTL direction for the overlay. */\n  setDirection(dir) {\n    this._config = {\n      ...this._config,\n      direction: dir\n    };\n    this._updateElementDirection();\n  }\n  /** Add a CSS class or an array of classes to the overlay pane. */\n  addPanelClass(classes) {\n    if (this._pane) {\n      this._toggleClasses(this._pane, classes, true);\n    }\n  }\n  /** Remove a CSS class or an array of classes from the overlay pane. */\n  removePanelClass(classes) {\n    if (this._pane) {\n      this._toggleClasses(this._pane, classes, false);\n    }\n  }\n  /**\n   * Returns the layout direction of the overlay panel.\n   */\n  getDirection() {\n    const direction = this._config.direction;\n    if (!direction) {\n      return 'ltr';\n    }\n    return typeof direction === 'string' ? direction : direction.value;\n  }\n  /** Switches to a new scroll strategy. */\n  updateScrollStrategy(strategy) {\n    if (strategy === this._scrollStrategy) {\n      return;\n    }\n    this._disposeScrollStrategy();\n    this._scrollStrategy = strategy;\n    if (this.hasAttached()) {\n      strategy.attach(this);\n      strategy.enable();\n    }\n  }\n  /** Updates the text direction of the overlay panel. */\n  _updateElementDirection() {\n    this._host.setAttribute('dir', this.getDirection());\n  }\n  /** Updates the size of the overlay element based on the overlay config. */\n  _updateElementSize() {\n    if (!this._pane) {\n      return;\n    }\n    const style = this._pane.style;\n    style.width = coerceCssPixelValue(this._config.width);\n    style.height = coerceCssPixelValue(this._config.height);\n    style.minWidth = coerceCssPixelValue(this._config.minWidth);\n    style.minHeight = coerceCssPixelValue(this._config.minHeight);\n    style.maxWidth = coerceCssPixelValue(this._config.maxWidth);\n    style.maxHeight = coerceCssPixelValue(this._config.maxHeight);\n  }\n  /** Toggles the pointer events for the overlay pane element. */\n  _togglePointerEvents(enablePointer) {\n    this._pane.style.pointerEvents = enablePointer ? '' : 'none';\n  }\n  /** Attaches a backdrop for this overlay. */\n  _attachBackdrop() {\n    const showingClass = 'cdk-overlay-backdrop-showing';\n    this._backdropElement = this._document.createElement('div');\n    this._backdropElement.classList.add('cdk-overlay-backdrop');\n    if (this._animationsDisabled) {\n      this._backdropElement.classList.add('cdk-overlay-backdrop-noop-animation');\n    }\n    if (this._config.backdropClass) {\n      this._toggleClasses(this._backdropElement, this._config.backdropClass, true);\n    }\n    // Insert the backdrop before the pane in the DOM order,\n    // in order to handle stacked overlays properly.\n    this._host.parentElement.insertBefore(this._backdropElement, this._host);\n    // Forward backdrop clicks such that the consumer of the overlay can perform whatever\n    // action desired when such a click occurs (usually closing the overlay).\n    this._backdropElement.addEventListener('click', this._backdropClickHandler);\n    // Add class to fade-in the backdrop after one frame.\n    if (!this._animationsDisabled && typeof requestAnimationFrame !== 'undefined') {\n      this._ngZone.runOutsideAngular(() => {\n        requestAnimationFrame(() => {\n          if (this._backdropElement) {\n            this._backdropElement.classList.add(showingClass);\n          }\n        });\n      });\n    } else {\n      this._backdropElement.classList.add(showingClass);\n    }\n  }\n  /**\n   * Updates the stacking order of the element, moving it to the top if necessary.\n   * This is required in cases where one overlay was detached, while another one,\n   * that should be behind it, was destroyed. The next time both of them are opened,\n   * the stacking will be wrong, because the detached element's pane will still be\n   * in its original DOM position.\n   */\n  _updateStackingOrder() {\n    if (this._host.nextSibling) {\n      this._host.parentNode.appendChild(this._host);\n    }\n  }\n  /** Detaches the backdrop (if any) associated with the overlay. */\n  detachBackdrop() {\n    const backdropToDetach = this._backdropElement;\n    if (!backdropToDetach) {\n      return;\n    }\n    if (this._animationsDisabled) {\n      this._disposeBackdrop(backdropToDetach);\n      return;\n    }\n    backdropToDetach.classList.remove('cdk-overlay-backdrop-showing');\n    this._ngZone.runOutsideAngular(() => {\n      backdropToDetach.addEventListener('transitionend', this._backdropTransitionendHandler);\n    });\n    // If the backdrop doesn't have a transition, the `transitionend` event won't fire.\n    // In this case we make it unclickable and we try to remove it after a delay.\n    backdropToDetach.style.pointerEvents = 'none';\n    // Run this outside the Angular zone because there's nothing that Angular cares about.\n    // If it were to run inside the Angular zone, every test that used Overlay would have to be\n    // either async or fakeAsync.\n    this._backdropTimeout = this._ngZone.runOutsideAngular(() => setTimeout(() => {\n      this._disposeBackdrop(backdropToDetach);\n    }, 500));\n  }\n  /** Toggles a single CSS class or an array of classes on an element. */\n  _toggleClasses(element, cssClasses, isAdd) {\n    const classes = coerceArray(cssClasses || []).filter(c => !!c);\n    if (classes.length) {\n      isAdd ? element.classList.add(...classes) : element.classList.remove(...classes);\n    }\n  }\n  /** Detaches the overlay content next time the zone stabilizes. */\n  _detachContentWhenEmpty() {\n    // Normally we wouldn't have to explicitly run this outside the `NgZone`, however\n    // if the consumer is using `zone-patch-rxjs`, the `Subscription.unsubscribe` call will\n    // be patched to run inside the zone, which will throw us into an infinite loop.\n    this._ngZone.runOutsideAngular(() => {\n      // We can't remove the host here immediately, because the overlay pane's content\n      // might still be animating. This stream helps us avoid interrupting the animation\n      // by waiting for the pane to become empty.\n      const subscription = this._renders.pipe(takeUntil(merge(this._attachments, this._detachments))).subscribe(() => {\n        // Needs a couple of checks for the pane and host, because\n        // they may have been removed by the time the zone stabilizes.\n        if (!this._pane || !this._host || this._pane.children.length === 0) {\n          if (this._pane && this._config.panelClass) {\n            this._toggleClasses(this._pane, this._config.panelClass, false);\n          }\n          if (this._host && this._host.parentElement) {\n            this._previousHostParent = this._host.parentElement;\n            this._host.remove();\n          }\n          subscription.unsubscribe();\n        }\n      });\n    });\n  }\n  /** Disposes of a scroll strategy. */\n  _disposeScrollStrategy() {\n    const scrollStrategy = this._scrollStrategy;\n    if (scrollStrategy) {\n      scrollStrategy.disable();\n      if (scrollStrategy.detach) {\n        scrollStrategy.detach();\n      }\n    }\n  }\n  /** Removes a backdrop element from the DOM. */\n  _disposeBackdrop(backdrop) {\n    if (backdrop) {\n      backdrop.removeEventListener('click', this._backdropClickHandler);\n      backdrop.removeEventListener('transitionend', this._backdropTransitionendHandler);\n      backdrop.remove();\n      // It is possible that a new portal has been attached to this overlay since we started\n      // removing the backdrop. If that is the case, only clear the backdrop reference if it\n      // is still the same instance that we started to remove.\n      if (this._backdropElement === backdrop) {\n        this._backdropElement = null;\n      }\n    }\n    if (this._backdropTimeout) {\n      clearTimeout(this._backdropTimeout);\n      this._backdropTimeout = undefined;\n    }\n  }\n}\n\n// TODO: refactor clipping detection into a separate thing (part of scrolling module)\n// TODO: doesn't handle both flexible width and height when it has to scroll along both axis.\n/** Class to be added to the overlay bounding box. */\nconst boundingBoxClass = 'cdk-overlay-connected-position-bounding-box';\n/** Regex used to split a string on its CSS units. */\nconst cssUnitPattern = /([A-Za-z%]+)$/;\n/**\n * A strategy for positioning overlays. Using this strategy, an overlay is given an\n * implicit position relative some origin element. The relative position is defined in terms of\n * a point on the origin element that is connected to a point on the overlay element. For example,\n * a basic dropdown is connecting the bottom-left corner of the origin to the top-left corner\n * of the overlay.\n */\nclass FlexibleConnectedPositionStrategy {\n  /** Ordered list of preferred positions, from most to least desirable. */\n  get positions() {\n    return this._preferredPositions;\n  }\n  constructor(connectedTo, _viewportRuler, _document, _platform, _overlayContainer) {\n    this._viewportRuler = _viewportRuler;\n    this._document = _document;\n    this._platform = _platform;\n    this._overlayContainer = _overlayContainer;\n    /** Last size used for the bounding box. Used to avoid resizing the overlay after open. */\n    this._lastBoundingBoxSize = {\n      width: 0,\n      height: 0\n    };\n    /** Whether the overlay was pushed in a previous positioning. */\n    this._isPushed = false;\n    /** Whether the overlay can be pushed on-screen on the initial open. */\n    this._canPush = true;\n    /** Whether the overlay can grow via flexible width/height after the initial open. */\n    this._growAfterOpen = false;\n    /** Whether the overlay's width and height can be constrained to fit within the viewport. */\n    this._hasFlexibleDimensions = true;\n    /** Whether the overlay position is locked. */\n    this._positionLocked = false;\n    /** Amount of space that must be maintained between the overlay and the edge of the viewport. */\n    this._viewportMargin = 0;\n    /** The Scrollable containers used to check scrollable view properties on position change. */\n    this._scrollables = [];\n    /** Ordered list of preferred positions, from most to least desirable. */\n    this._preferredPositions = [];\n    /** Subject that emits whenever the position changes. */\n    this._positionChanges = new Subject();\n    /** Subscription to viewport size changes. */\n    this._resizeSubscription = Subscription.EMPTY;\n    /** Default offset for the overlay along the x axis. */\n    this._offsetX = 0;\n    /** Default offset for the overlay along the y axis. */\n    this._offsetY = 0;\n    /** Keeps track of the CSS classes that the position strategy has applied on the overlay panel. */\n    this._appliedPanelClasses = [];\n    /** Observable sequence of position changes. */\n    this.positionChanges = this._positionChanges;\n    this.setOrigin(connectedTo);\n  }\n  /** Attaches this position strategy to an overlay. */\n  attach(overlayRef) {\n    if (this._overlayRef && overlayRef !== this._overlayRef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('This position strategy is already attached to an overlay');\n    }\n    this._validatePositions();\n    overlayRef.hostElement.classList.add(boundingBoxClass);\n    this._overlayRef = overlayRef;\n    this._boundingBox = overlayRef.hostElement;\n    this._pane = overlayRef.overlayElement;\n    this._isDisposed = false;\n    this._isInitialRender = true;\n    this._lastPosition = null;\n    this._resizeSubscription.unsubscribe();\n    this._resizeSubscription = this._viewportRuler.change().subscribe(() => {\n      // When the window is resized, we want to trigger the next reposition as if it\n      // was an initial render, in order for the strategy to pick a new optimal position,\n      // otherwise position locking will cause it to stay at the old one.\n      this._isInitialRender = true;\n      this.apply();\n    });\n  }\n  /**\n   * Updates the position of the overlay element, using whichever preferred position relative\n   * to the origin best fits on-screen.\n   *\n   * The selection of a position goes as follows:\n   *  - If any positions fit completely within the viewport as-is,\n   *      choose the first position that does so.\n   *  - If flexible dimensions are enabled and at least one satisfies the given minimum width/height,\n   *      choose the position with the greatest available size modified by the positions' weight.\n   *  - If pushing is enabled, take the position that went off-screen the least and push it\n   *      on-screen.\n   *  - If none of the previous criteria were met, use the position that goes off-screen the least.\n   * @docs-private\n   */\n  apply() {\n    // We shouldn't do anything if the strategy was disposed or we're on the server.\n    if (this._isDisposed || !this._platform.isBrowser) {\n      return;\n    }\n    // If the position has been applied already (e.g. when the overlay was opened) and the\n    // consumer opted into locking in the position, re-use the old position, in order to\n    // prevent the overlay from jumping around.\n    if (!this._isInitialRender && this._positionLocked && this._lastPosition) {\n      this.reapplyLastPosition();\n      return;\n    }\n    this._clearPanelClasses();\n    this._resetOverlayElementStyles();\n    this._resetBoundingBoxStyles();\n    // We need the bounding rects for the origin, the overlay and the container to determine how to position\n    // the overlay relative to the origin.\n    // We use the viewport rect to determine whether a position would go off-screen.\n    this._viewportRect = this._getNarrowedViewportRect();\n    this._originRect = this._getOriginRect();\n    this._overlayRect = this._pane.getBoundingClientRect();\n    this._containerRect = this._overlayContainer.getContainerElement().getBoundingClientRect();\n    const originRect = this._originRect;\n    const overlayRect = this._overlayRect;\n    const viewportRect = this._viewportRect;\n    const containerRect = this._containerRect;\n    // Positions where the overlay will fit with flexible dimensions.\n    const flexibleFits = [];\n    // Fallback if none of the preferred positions fit within the viewport.\n    let fallback;\n    // Go through each of the preferred positions looking for a good fit.\n    // If a good fit is found, it will be applied immediately.\n    for (let pos of this._preferredPositions) {\n      // Get the exact (x, y) coordinate for the point-of-origin on the origin element.\n      let originPoint = this._getOriginPoint(originRect, containerRect, pos);\n      // From that point-of-origin, get the exact (x, y) coordinate for the top-left corner of the\n      // overlay in this position. We use the top-left corner for calculations and later translate\n      // this into an appropriate (top, left, bottom, right) style.\n      let overlayPoint = this._getOverlayPoint(originPoint, overlayRect, pos);\n      // Calculate how well the overlay would fit into the viewport with this point.\n      let overlayFit = this._getOverlayFit(overlayPoint, overlayRect, viewportRect, pos);\n      // If the overlay, without any further work, fits into the viewport, use this position.\n      if (overlayFit.isCompletelyWithinViewport) {\n        this._isPushed = false;\n        this._applyPosition(pos, originPoint);\n        return;\n      }\n      // If the overlay has flexible dimensions, we can use this position\n      // so long as there's enough space for the minimum dimensions.\n      if (this._canFitWithFlexibleDimensions(overlayFit, overlayPoint, viewportRect)) {\n        // Save positions where the overlay will fit with flexible dimensions. We will use these\n        // if none of the positions fit *without* flexible dimensions.\n        flexibleFits.push({\n          position: pos,\n          origin: originPoint,\n          overlayRect,\n          boundingBoxRect: this._calculateBoundingBoxRect(originPoint, pos)\n        });\n        continue;\n      }\n      // If the current preferred position does not fit on the screen, remember the position\n      // if it has more visible area on-screen than we've seen and move onto the next preferred\n      // position.\n      if (!fallback || fallback.overlayFit.visibleArea < overlayFit.visibleArea) {\n        fallback = {\n          overlayFit,\n          overlayPoint,\n          originPoint,\n          position: pos,\n          overlayRect\n        };\n      }\n    }\n    // If there are any positions where the overlay would fit with flexible dimensions, choose the\n    // one that has the greatest area available modified by the position's weight\n    if (flexibleFits.length) {\n      let bestFit = null;\n      let bestScore = -1;\n      for (const fit of flexibleFits) {\n        const score = fit.boundingBoxRect.width * fit.boundingBoxRect.height * (fit.position.weight || 1);\n        if (score > bestScore) {\n          bestScore = score;\n          bestFit = fit;\n        }\n      }\n      this._isPushed = false;\n      this._applyPosition(bestFit.position, bestFit.origin);\n      return;\n    }\n    // When none of the preferred positions fit within the viewport, take the position\n    // that went off-screen the least and attempt to push it on-screen.\n    if (this._canPush) {\n      // TODO(jelbourn): after pushing, the opening \"direction\" of the overlay might not make sense.\n      this._isPushed = true;\n      this._applyPosition(fallback.position, fallback.originPoint);\n      return;\n    }\n    // All options for getting the overlay within the viewport have been exhausted, so go with the\n    // position that went off-screen the least.\n    this._applyPosition(fallback.position, fallback.originPoint);\n  }\n  detach() {\n    this._clearPanelClasses();\n    this._lastPosition = null;\n    this._previousPushAmount = null;\n    this._resizeSubscription.unsubscribe();\n  }\n  /** Cleanup after the element gets destroyed. */\n  dispose() {\n    if (this._isDisposed) {\n      return;\n    }\n    // We can't use `_resetBoundingBoxStyles` here, because it resets\n    // some properties to zero, rather than removing them.\n    if (this._boundingBox) {\n      extendStyles(this._boundingBox.style, {\n        top: '',\n        left: '',\n        right: '',\n        bottom: '',\n        height: '',\n        width: '',\n        alignItems: '',\n        justifyContent: ''\n      });\n    }\n    if (this._pane) {\n      this._resetOverlayElementStyles();\n    }\n    if (this._overlayRef) {\n      this._overlayRef.hostElement.classList.remove(boundingBoxClass);\n    }\n    this.detach();\n    this._positionChanges.complete();\n    this._overlayRef = this._boundingBox = null;\n    this._isDisposed = true;\n  }\n  /**\n   * This re-aligns the overlay element with the trigger in its last calculated position,\n   * even if a position higher in the \"preferred positions\" list would now fit. This\n   * allows one to re-align the panel without changing the orientation of the panel.\n   */\n  reapplyLastPosition() {\n    if (this._isDisposed || !this._platform.isBrowser) {\n      return;\n    }\n    const lastPosition = this._lastPosition;\n    if (lastPosition) {\n      this._originRect = this._getOriginRect();\n      this._overlayRect = this._pane.getBoundingClientRect();\n      this._viewportRect = this._getNarrowedViewportRect();\n      this._containerRect = this._overlayContainer.getContainerElement().getBoundingClientRect();\n      const originPoint = this._getOriginPoint(this._originRect, this._containerRect, lastPosition);\n      this._applyPosition(lastPosition, originPoint);\n    } else {\n      this.apply();\n    }\n  }\n  /**\n   * Sets the list of Scrollable containers that host the origin element so that\n   * on reposition we can evaluate if it or the overlay has been clipped or outside view. Every\n   * Scrollable must be an ancestor element of the strategy's origin element.\n   */\n  withScrollableContainers(scrollables) {\n    this._scrollables = scrollables;\n    return this;\n  }\n  /**\n   * Adds new preferred positions.\n   * @param positions List of positions options for this overlay.\n   */\n  withPositions(positions) {\n    this._preferredPositions = positions;\n    // If the last calculated position object isn't part of the positions anymore, clear\n    // it in order to avoid it being picked up if the consumer tries to re-apply.\n    if (positions.indexOf(this._lastPosition) === -1) {\n      this._lastPosition = null;\n    }\n    this._validatePositions();\n    return this;\n  }\n  /**\n   * Sets a minimum distance the overlay may be positioned to the edge of the viewport.\n   * @param margin Required margin between the overlay and the viewport edge in pixels.\n   */\n  withViewportMargin(margin) {\n    this._viewportMargin = margin;\n    return this;\n  }\n  /** Sets whether the overlay's width and height can be constrained to fit within the viewport. */\n  withFlexibleDimensions(flexibleDimensions = true) {\n    this._hasFlexibleDimensions = flexibleDimensions;\n    return this;\n  }\n  /** Sets whether the overlay can grow after the initial open via flexible width/height. */\n  withGrowAfterOpen(growAfterOpen = true) {\n    this._growAfterOpen = growAfterOpen;\n    return this;\n  }\n  /** Sets whether the overlay can be pushed on-screen if none of the provided positions fit. */\n  withPush(canPush = true) {\n    this._canPush = canPush;\n    return this;\n  }\n  /**\n   * Sets whether the overlay's position should be locked in after it is positioned\n   * initially. When an overlay is locked in, it won't attempt to reposition itself\n   * when the position is re-applied (e.g. when the user scrolls away).\n   * @param isLocked Whether the overlay should locked in.\n   */\n  withLockedPosition(isLocked = true) {\n    this._positionLocked = isLocked;\n    return this;\n  }\n  /**\n   * Sets the origin, relative to which to position the overlay.\n   * Using an element origin is useful for building components that need to be positioned\n   * relatively to a trigger (e.g. dropdown menus or tooltips), whereas using a point can be\n   * used for cases like contextual menus which open relative to the user's pointer.\n   * @param origin Reference to the new origin.\n   */\n  setOrigin(origin) {\n    this._origin = origin;\n    return this;\n  }\n  /**\n   * Sets the default offset for the overlay's connection point on the x-axis.\n   * @param offset New offset in the X axis.\n   */\n  withDefaultOffsetX(offset) {\n    this._offsetX = offset;\n    return this;\n  }\n  /**\n   * Sets the default offset for the overlay's connection point on the y-axis.\n   * @param offset New offset in the Y axis.\n   */\n  withDefaultOffsetY(offset) {\n    this._offsetY = offset;\n    return this;\n  }\n  /**\n   * Configures that the position strategy should set a `transform-origin` on some elements\n   * inside the overlay, depending on the current position that is being applied. This is\n   * useful for the cases where the origin of an animation can change depending on the\n   * alignment of the overlay.\n   * @param selector CSS selector that will be used to find the target\n   *    elements onto which to set the transform origin.\n   */\n  withTransformOriginOn(selector) {\n    this._transformOriginSelector = selector;\n    return this;\n  }\n  /**\n   * Gets the (x, y) coordinate of a connection point on the origin based on a relative position.\n   */\n  _getOriginPoint(originRect, containerRect, pos) {\n    let x;\n    if (pos.originX == 'center') {\n      // Note: when centering we should always use the `left`\n      // offset, otherwise the position will be wrong in RTL.\n      x = originRect.left + originRect.width / 2;\n    } else {\n      const startX = this._isRtl() ? originRect.right : originRect.left;\n      const endX = this._isRtl() ? originRect.left : originRect.right;\n      x = pos.originX == 'start' ? startX : endX;\n    }\n    // When zooming in Safari the container rectangle contains negative values for the position\n    // and we need to re-add them to the calculated coordinates.\n    if (containerRect.left < 0) {\n      x -= containerRect.left;\n    }\n    let y;\n    if (pos.originY == 'center') {\n      y = originRect.top + originRect.height / 2;\n    } else {\n      y = pos.originY == 'top' ? originRect.top : originRect.bottom;\n    }\n    // Normally the containerRect's top value would be zero, however when the overlay is attached to an input\n    // (e.g. in an autocomplete), mobile browsers will shift everything in order to put the input in the middle\n    // of the screen and to make space for the virtual keyboard. We need to account for this offset,\n    // otherwise our positioning will be thrown off.\n    // Additionally, when zooming in Safari this fixes the vertical position.\n    if (containerRect.top < 0) {\n      y -= containerRect.top;\n    }\n    return {\n      x,\n      y\n    };\n  }\n  /**\n   * Gets the (x, y) coordinate of the top-left corner of the overlay given a given position and\n   * origin point to which the overlay should be connected.\n   */\n  _getOverlayPoint(originPoint, overlayRect, pos) {\n    // Calculate the (overlayStartX, overlayStartY), the start of the\n    // potential overlay position relative to the origin point.\n    let overlayStartX;\n    if (pos.overlayX == 'center') {\n      overlayStartX = -overlayRect.width / 2;\n    } else if (pos.overlayX === 'start') {\n      overlayStartX = this._isRtl() ? -overlayRect.width : 0;\n    } else {\n      overlayStartX = this._isRtl() ? 0 : -overlayRect.width;\n    }\n    let overlayStartY;\n    if (pos.overlayY == 'center') {\n      overlayStartY = -overlayRect.height / 2;\n    } else {\n      overlayStartY = pos.overlayY == 'top' ? 0 : -overlayRect.height;\n    }\n    // The (x, y) coordinates of the overlay.\n    return {\n      x: originPoint.x + overlayStartX,\n      y: originPoint.y + overlayStartY\n    };\n  }\n  /** Gets how well an overlay at the given point will fit within the viewport. */\n  _getOverlayFit(point, rawOverlayRect, viewport, position) {\n    // Round the overlay rect when comparing against the\n    // viewport, because the viewport is always rounded.\n    const overlay = getRoundedBoundingClientRect(rawOverlayRect);\n    let {\n      x,\n      y\n    } = point;\n    let offsetX = this._getOffset(position, 'x');\n    let offsetY = this._getOffset(position, 'y');\n    // Account for the offsets since they could push the overlay out of the viewport.\n    if (offsetX) {\n      x += offsetX;\n    }\n    if (offsetY) {\n      y += offsetY;\n    }\n    // How much the overlay would overflow at this position, on each side.\n    let leftOverflow = 0 - x;\n    let rightOverflow = x + overlay.width - viewport.width;\n    let topOverflow = 0 - y;\n    let bottomOverflow = y + overlay.height - viewport.height;\n    // Visible parts of the element on each axis.\n    let visibleWidth = this._subtractOverflows(overlay.width, leftOverflow, rightOverflow);\n    let visibleHeight = this._subtractOverflows(overlay.height, topOverflow, bottomOverflow);\n    let visibleArea = visibleWidth * visibleHeight;\n    return {\n      visibleArea,\n      isCompletelyWithinViewport: overlay.width * overlay.height === visibleArea,\n      fitsInViewportVertically: visibleHeight === overlay.height,\n      fitsInViewportHorizontally: visibleWidth == overlay.width\n    };\n  }\n  /**\n   * Whether the overlay can fit within the viewport when it may resize either its width or height.\n   * @param fit How well the overlay fits in the viewport at some position.\n   * @param point The (x, y) coordinates of the overlay at some position.\n   * @param viewport The geometry of the viewport.\n   */\n  _canFitWithFlexibleDimensions(fit, point, viewport) {\n    if (this._hasFlexibleDimensions) {\n      const availableHeight = viewport.bottom - point.y;\n      const availableWidth = viewport.right - point.x;\n      const minHeight = getPixelValue(this._overlayRef.getConfig().minHeight);\n      const minWidth = getPixelValue(this._overlayRef.getConfig().minWidth);\n      const verticalFit = fit.fitsInViewportVertically || minHeight != null && minHeight <= availableHeight;\n      const horizontalFit = fit.fitsInViewportHorizontally || minWidth != null && minWidth <= availableWidth;\n      return verticalFit && horizontalFit;\n    }\n    return false;\n  }\n  /**\n   * Gets the point at which the overlay can be \"pushed\" on-screen. If the overlay is larger than\n   * the viewport, the top-left corner will be pushed on-screen (with overflow occurring on the\n   * right and bottom).\n   *\n   * @param start Starting point from which the overlay is pushed.\n   * @param rawOverlayRect Dimensions of the overlay.\n   * @param scrollPosition Current viewport scroll position.\n   * @returns The point at which to position the overlay after pushing. This is effectively a new\n   *     originPoint.\n   */\n  _pushOverlayOnScreen(start, rawOverlayRect, scrollPosition) {\n    // If the position is locked and we've pushed the overlay already, reuse the previous push\n    // amount, rather than pushing it again. If we were to continue pushing, the element would\n    // remain in the viewport, which goes against the expectations when position locking is enabled.\n    if (this._previousPushAmount && this._positionLocked) {\n      return {\n        x: start.x + this._previousPushAmount.x,\n        y: start.y + this._previousPushAmount.y\n      };\n    }\n    // Round the overlay rect when comparing against the\n    // viewport, because the viewport is always rounded.\n    const overlay = getRoundedBoundingClientRect(rawOverlayRect);\n    const viewport = this._viewportRect;\n    // Determine how much the overlay goes outside the viewport on each\n    // side, which we'll use to decide which direction to push it.\n    const overflowRight = Math.max(start.x + overlay.width - viewport.width, 0);\n    const overflowBottom = Math.max(start.y + overlay.height - viewport.height, 0);\n    const overflowTop = Math.max(viewport.top - scrollPosition.top - start.y, 0);\n    const overflowLeft = Math.max(viewport.left - scrollPosition.left - start.x, 0);\n    // Amount by which to push the overlay in each axis such that it remains on-screen.\n    let pushX = 0;\n    let pushY = 0;\n    // If the overlay fits completely within the bounds of the viewport, push it from whichever\n    // direction is goes off-screen. Otherwise, push the top-left corner such that its in the\n    // viewport and allow for the trailing end of the overlay to go out of bounds.\n    if (overlay.width <= viewport.width) {\n      pushX = overflowLeft || -overflowRight;\n    } else {\n      pushX = start.x < this._viewportMargin ? viewport.left - scrollPosition.left - start.x : 0;\n    }\n    if (overlay.height <= viewport.height) {\n      pushY = overflowTop || -overflowBottom;\n    } else {\n      pushY = start.y < this._viewportMargin ? viewport.top - scrollPosition.top - start.y : 0;\n    }\n    this._previousPushAmount = {\n      x: pushX,\n      y: pushY\n    };\n    return {\n      x: start.x + pushX,\n      y: start.y + pushY\n    };\n  }\n  /**\n   * Applies a computed position to the overlay and emits a position change.\n   * @param position The position preference\n   * @param originPoint The point on the origin element where the overlay is connected.\n   */\n  _applyPosition(position, originPoint) {\n    this._setTransformOrigin(position);\n    this._setOverlayElementStyles(originPoint, position);\n    this._setBoundingBoxStyles(originPoint, position);\n    if (position.panelClass) {\n      this._addPanelClasses(position.panelClass);\n    }\n    // Notify that the position has been changed along with its change properties.\n    // We only emit if we've got any subscriptions, because the scroll visibility\n    // calculations can be somewhat expensive.\n    if (this._positionChanges.observers.length) {\n      const scrollVisibility = this._getScrollVisibility();\n      // We're recalculating on scroll, but we only want to emit if anything\n      // changed since downstream code might be hitting the `NgZone`.\n      if (position !== this._lastPosition || !this._lastScrollVisibility || !compareScrollVisibility(this._lastScrollVisibility, scrollVisibility)) {\n        const changeEvent = new ConnectedOverlayPositionChange(position, scrollVisibility);\n        this._positionChanges.next(changeEvent);\n      }\n      this._lastScrollVisibility = scrollVisibility;\n    }\n    // Save the last connected position in case the position needs to be re-calculated.\n    this._lastPosition = position;\n    this._isInitialRender = false;\n  }\n  /** Sets the transform origin based on the configured selector and the passed-in position.  */\n  _setTransformOrigin(position) {\n    if (!this._transformOriginSelector) {\n      return;\n    }\n    const elements = this._boundingBox.querySelectorAll(this._transformOriginSelector);\n    let xOrigin;\n    let yOrigin = position.overlayY;\n    if (position.overlayX === 'center') {\n      xOrigin = 'center';\n    } else if (this._isRtl()) {\n      xOrigin = position.overlayX === 'start' ? 'right' : 'left';\n    } else {\n      xOrigin = position.overlayX === 'start' ? 'left' : 'right';\n    }\n    for (let i = 0; i < elements.length; i++) {\n      elements[i].style.transformOrigin = `${xOrigin} ${yOrigin}`;\n    }\n  }\n  /**\n   * Gets the position and size of the overlay's sizing container.\n   *\n   * This method does no measuring and applies no styles so that we can cheaply compute the\n   * bounds for all positions and choose the best fit based on these results.\n   */\n  _calculateBoundingBoxRect(origin, position) {\n    const viewport = this._viewportRect;\n    const isRtl = this._isRtl();\n    let height, top, bottom;\n    if (position.overlayY === 'top') {\n      // Overlay is opening \"downward\" and thus is bound by the bottom viewport edge.\n      top = origin.y;\n      height = viewport.height - top + this._viewportMargin;\n    } else if (position.overlayY === 'bottom') {\n      // Overlay is opening \"upward\" and thus is bound by the top viewport edge. We need to add\n      // the viewport margin back in, because the viewport rect is narrowed down to remove the\n      // margin, whereas the `origin` position is calculated based on its `DOMRect`.\n      bottom = viewport.height - origin.y + this._viewportMargin * 2;\n      height = viewport.height - bottom + this._viewportMargin;\n    } else {\n      // If neither top nor bottom, it means that the overlay is vertically centered on the\n      // origin point. Note that we want the position relative to the viewport, rather than\n      // the page, which is why we don't use something like `viewport.bottom - origin.y` and\n      // `origin.y - viewport.top`.\n      const smallestDistanceToViewportEdge = Math.min(viewport.bottom - origin.y + viewport.top, origin.y);\n      const previousHeight = this._lastBoundingBoxSize.height;\n      height = smallestDistanceToViewportEdge * 2;\n      top = origin.y - smallestDistanceToViewportEdge;\n      if (height > previousHeight && !this._isInitialRender && !this._growAfterOpen) {\n        top = origin.y - previousHeight / 2;\n      }\n    }\n    // The overlay is opening 'right-ward' (the content flows to the right).\n    const isBoundedByRightViewportEdge = position.overlayX === 'start' && !isRtl || position.overlayX === 'end' && isRtl;\n    // The overlay is opening 'left-ward' (the content flows to the left).\n    const isBoundedByLeftViewportEdge = position.overlayX === 'end' && !isRtl || position.overlayX === 'start' && isRtl;\n    let width, left, right;\n    if (isBoundedByLeftViewportEdge) {\n      right = viewport.width - origin.x + this._viewportMargin * 2;\n      width = origin.x - this._viewportMargin;\n    } else if (isBoundedByRightViewportEdge) {\n      left = origin.x;\n      width = viewport.right - origin.x;\n    } else {\n      // If neither start nor end, it means that the overlay is horizontally centered on the\n      // origin point. Note that we want the position relative to the viewport, rather than\n      // the page, which is why we don't use something like `viewport.right - origin.x` and\n      // `origin.x - viewport.left`.\n      const smallestDistanceToViewportEdge = Math.min(viewport.right - origin.x + viewport.left, origin.x);\n      const previousWidth = this._lastBoundingBoxSize.width;\n      width = smallestDistanceToViewportEdge * 2;\n      left = origin.x - smallestDistanceToViewportEdge;\n      if (width > previousWidth && !this._isInitialRender && !this._growAfterOpen) {\n        left = origin.x - previousWidth / 2;\n      }\n    }\n    return {\n      top: top,\n      left: left,\n      bottom: bottom,\n      right: right,\n      width,\n      height\n    };\n  }\n  /**\n   * Sets the position and size of the overlay's sizing wrapper. The wrapper is positioned on the\n   * origin's connection point and stretches to the bounds of the viewport.\n   *\n   * @param origin The point on the origin element where the overlay is connected.\n   * @param position The position preference\n   */\n  _setBoundingBoxStyles(origin, position) {\n    const boundingBoxRect = this._calculateBoundingBoxRect(origin, position);\n    // It's weird if the overlay *grows* while scrolling, so we take the last size into account\n    // when applying a new size.\n    if (!this._isInitialRender && !this._growAfterOpen) {\n      boundingBoxRect.height = Math.min(boundingBoxRect.height, this._lastBoundingBoxSize.height);\n      boundingBoxRect.width = Math.min(boundingBoxRect.width, this._lastBoundingBoxSize.width);\n    }\n    const styles = {};\n    if (this._hasExactPosition()) {\n      styles.top = styles.left = '0';\n      styles.bottom = styles.right = styles.maxHeight = styles.maxWidth = '';\n      styles.width = styles.height = '100%';\n    } else {\n      const maxHeight = this._overlayRef.getConfig().maxHeight;\n      const maxWidth = this._overlayRef.getConfig().maxWidth;\n      styles.height = coerceCssPixelValue(boundingBoxRect.height);\n      styles.top = coerceCssPixelValue(boundingBoxRect.top);\n      styles.bottom = coerceCssPixelValue(boundingBoxRect.bottom);\n      styles.width = coerceCssPixelValue(boundingBoxRect.width);\n      styles.left = coerceCssPixelValue(boundingBoxRect.left);\n      styles.right = coerceCssPixelValue(boundingBoxRect.right);\n      // Push the pane content towards the proper direction.\n      if (position.overlayX === 'center') {\n        styles.alignItems = 'center';\n      } else {\n        styles.alignItems = position.overlayX === 'end' ? 'flex-end' : 'flex-start';\n      }\n      if (position.overlayY === 'center') {\n        styles.justifyContent = 'center';\n      } else {\n        styles.justifyContent = position.overlayY === 'bottom' ? 'flex-end' : 'flex-start';\n      }\n      if (maxHeight) {\n        styles.maxHeight = coerceCssPixelValue(maxHeight);\n      }\n      if (maxWidth) {\n        styles.maxWidth = coerceCssPixelValue(maxWidth);\n      }\n    }\n    this._lastBoundingBoxSize = boundingBoxRect;\n    extendStyles(this._boundingBox.style, styles);\n  }\n  /** Resets the styles for the bounding box so that a new positioning can be computed. */\n  _resetBoundingBoxStyles() {\n    extendStyles(this._boundingBox.style, {\n      top: '0',\n      left: '0',\n      right: '0',\n      bottom: '0',\n      height: '',\n      width: '',\n      alignItems: '',\n      justifyContent: ''\n    });\n  }\n  /** Resets the styles for the overlay pane so that a new positioning can be computed. */\n  _resetOverlayElementStyles() {\n    extendStyles(this._pane.style, {\n      top: '',\n      left: '',\n      bottom: '',\n      right: '',\n      position: '',\n      transform: ''\n    });\n  }\n  /** Sets positioning styles to the overlay element. */\n  _setOverlayElementStyles(originPoint, position) {\n    const styles = {};\n    const hasExactPosition = this._hasExactPosition();\n    const hasFlexibleDimensions = this._hasFlexibleDimensions;\n    const config = this._overlayRef.getConfig();\n    if (hasExactPosition) {\n      const scrollPosition = this._viewportRuler.getViewportScrollPosition();\n      extendStyles(styles, this._getExactOverlayY(position, originPoint, scrollPosition));\n      extendStyles(styles, this._getExactOverlayX(position, originPoint, scrollPosition));\n    } else {\n      styles.position = 'static';\n    }\n    // Use a transform to apply the offsets. We do this because the `center` positions rely on\n    // being in the normal flex flow and setting a `top` / `left` at all will completely throw\n    // off the position. We also can't use margins, because they won't have an effect in some\n    // cases where the element doesn't have anything to \"push off of\". Finally, this works\n    // better both with flexible and non-flexible positioning.\n    let transformString = '';\n    let offsetX = this._getOffset(position, 'x');\n    let offsetY = this._getOffset(position, 'y');\n    if (offsetX) {\n      transformString += `translateX(${offsetX}px) `;\n    }\n    if (offsetY) {\n      transformString += `translateY(${offsetY}px)`;\n    }\n    styles.transform = transformString.trim();\n    // If a maxWidth or maxHeight is specified on the overlay, we remove them. We do this because\n    // we need these values to both be set to \"100%\" for the automatic flexible sizing to work.\n    // The maxHeight and maxWidth are set on the boundingBox in order to enforce the constraint.\n    // Note that this doesn't apply when we have an exact position, in which case we do want to\n    // apply them because they'll be cleared from the bounding box.\n    if (config.maxHeight) {\n      if (hasExactPosition) {\n        styles.maxHeight = coerceCssPixelValue(config.maxHeight);\n      } else if (hasFlexibleDimensions) {\n        styles.maxHeight = '';\n      }\n    }\n    if (config.maxWidth) {\n      if (hasExactPosition) {\n        styles.maxWidth = coerceCssPixelValue(config.maxWidth);\n      } else if (hasFlexibleDimensions) {\n        styles.maxWidth = '';\n      }\n    }\n    extendStyles(this._pane.style, styles);\n  }\n  /** Gets the exact top/bottom for the overlay when not using flexible sizing or when pushing. */\n  _getExactOverlayY(position, originPoint, scrollPosition) {\n    // Reset any existing styles. This is necessary in case the\n    // preferred position has changed since the last `apply`.\n    let styles = {\n      top: '',\n      bottom: ''\n    };\n    let overlayPoint = this._getOverlayPoint(originPoint, this._overlayRect, position);\n    if (this._isPushed) {\n      overlayPoint = this._pushOverlayOnScreen(overlayPoint, this._overlayRect, scrollPosition);\n    }\n    // We want to set either `top` or `bottom` based on whether the overlay wants to appear\n    // above or below the origin and the direction in which the element will expand.\n    if (position.overlayY === 'bottom') {\n      // When using `bottom`, we adjust the y position such that it is the distance\n      // from the bottom of the viewport rather than the top.\n      const documentHeight = this._document.documentElement.clientHeight;\n      styles.bottom = `${documentHeight - (overlayPoint.y + this._overlayRect.height)}px`;\n    } else {\n      styles.top = coerceCssPixelValue(overlayPoint.y);\n    }\n    return styles;\n  }\n  /** Gets the exact left/right for the overlay when not using flexible sizing or when pushing. */\n  _getExactOverlayX(position, originPoint, scrollPosition) {\n    // Reset any existing styles. This is necessary in case the preferred position has\n    // changed since the last `apply`.\n    let styles = {\n      left: '',\n      right: ''\n    };\n    let overlayPoint = this._getOverlayPoint(originPoint, this._overlayRect, position);\n    if (this._isPushed) {\n      overlayPoint = this._pushOverlayOnScreen(overlayPoint, this._overlayRect, scrollPosition);\n    }\n    // We want to set either `left` or `right` based on whether the overlay wants to appear \"before\"\n    // or \"after\" the origin, which determines the direction in which the element will expand.\n    // For the horizontal axis, the meaning of \"before\" and \"after\" change based on whether the\n    // page is in RTL or LTR.\n    let horizontalStyleProperty;\n    if (this._isRtl()) {\n      horizontalStyleProperty = position.overlayX === 'end' ? 'left' : 'right';\n    } else {\n      horizontalStyleProperty = position.overlayX === 'end' ? 'right' : 'left';\n    }\n    // When we're setting `right`, we adjust the x position such that it is the distance\n    // from the right edge of the viewport rather than the left edge.\n    if (horizontalStyleProperty === 'right') {\n      const documentWidth = this._document.documentElement.clientWidth;\n      styles.right = `${documentWidth - (overlayPoint.x + this._overlayRect.width)}px`;\n    } else {\n      styles.left = coerceCssPixelValue(overlayPoint.x);\n    }\n    return styles;\n  }\n  /**\n   * Gets the view properties of the trigger and overlay, including whether they are clipped\n   * or completely outside the view of any of the strategy's scrollables.\n   */\n  _getScrollVisibility() {\n    // Note: needs fresh rects since the position could've changed.\n    const originBounds = this._getOriginRect();\n    const overlayBounds = this._pane.getBoundingClientRect();\n    // TODO(jelbourn): instead of needing all of the client rects for these scrolling containers\n    // every time, we should be able to use the scrollTop of the containers if the size of those\n    // containers hasn't changed.\n    const scrollContainerBounds = this._scrollables.map(scrollable => {\n      return scrollable.getElementRef().nativeElement.getBoundingClientRect();\n    });\n    return {\n      isOriginClipped: isElementClippedByScrolling(originBounds, scrollContainerBounds),\n      isOriginOutsideView: isElementScrolledOutsideView(originBounds, scrollContainerBounds),\n      isOverlayClipped: isElementClippedByScrolling(overlayBounds, scrollContainerBounds),\n      isOverlayOutsideView: isElementScrolledOutsideView(overlayBounds, scrollContainerBounds)\n    };\n  }\n  /** Subtracts the amount that an element is overflowing on an axis from its length. */\n  _subtractOverflows(length, ...overflows) {\n    return overflows.reduce((currentValue, currentOverflow) => {\n      return currentValue - Math.max(currentOverflow, 0);\n    }, length);\n  }\n  /** Narrows the given viewport rect by the current _viewportMargin. */\n  _getNarrowedViewportRect() {\n    // We recalculate the viewport rect here ourselves, rather than using the ViewportRuler,\n    // because we want to use the `clientWidth` and `clientHeight` as the base. The difference\n    // being that the client properties don't include the scrollbar, as opposed to `innerWidth`\n    // and `innerHeight` that do. This is necessary, because the overlay container uses\n    // 100% `width` and `height` which don't include the scrollbar either.\n    const width = this._document.documentElement.clientWidth;\n    const height = this._document.documentElement.clientHeight;\n    const scrollPosition = this._viewportRuler.getViewportScrollPosition();\n    return {\n      top: scrollPosition.top + this._viewportMargin,\n      left: scrollPosition.left + this._viewportMargin,\n      right: scrollPosition.left + width - this._viewportMargin,\n      bottom: scrollPosition.top + height - this._viewportMargin,\n      width: width - 2 * this._viewportMargin,\n      height: height - 2 * this._viewportMargin\n    };\n  }\n  /** Whether the we're dealing with an RTL context */\n  _isRtl() {\n    return this._overlayRef.getDirection() === 'rtl';\n  }\n  /** Determines whether the overlay uses exact or flexible positioning. */\n  _hasExactPosition() {\n    return !this._hasFlexibleDimensions || this._isPushed;\n  }\n  /** Retrieves the offset of a position along the x or y axis. */\n  _getOffset(position, axis) {\n    if (axis === 'x') {\n      // We don't do something like `position['offset' + axis]` in\n      // order to avoid breaking minifiers that rename properties.\n      return position.offsetX == null ? this._offsetX : position.offsetX;\n    }\n    return position.offsetY == null ? this._offsetY : position.offsetY;\n  }\n  /** Validates that the current position match the expected values. */\n  _validatePositions() {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (!this._preferredPositions.length) {\n        throw Error('FlexibleConnectedPositionStrategy: At least one position is required.');\n      }\n      // TODO(crisbeto): remove these once Angular's template type\n      // checking is advanced enough to catch these cases.\n      this._preferredPositions.forEach(pair => {\n        validateHorizontalPosition('originX', pair.originX);\n        validateVerticalPosition('originY', pair.originY);\n        validateHorizontalPosition('overlayX', pair.overlayX);\n        validateVerticalPosition('overlayY', pair.overlayY);\n      });\n    }\n  }\n  /** Adds a single CSS class or an array of classes on the overlay panel. */\n  _addPanelClasses(cssClasses) {\n    if (this._pane) {\n      coerceArray(cssClasses).forEach(cssClass => {\n        if (cssClass !== '' && this._appliedPanelClasses.indexOf(cssClass) === -1) {\n          this._appliedPanelClasses.push(cssClass);\n          this._pane.classList.add(cssClass);\n        }\n      });\n    }\n  }\n  /** Clears the classes that the position strategy has applied from the overlay panel. */\n  _clearPanelClasses() {\n    if (this._pane) {\n      this._appliedPanelClasses.forEach(cssClass => {\n        this._pane.classList.remove(cssClass);\n      });\n      this._appliedPanelClasses = [];\n    }\n  }\n  /** Returns the DOMRect of the current origin. */\n  _getOriginRect() {\n    const origin = this._origin;\n    if (origin instanceof ElementRef) {\n      return origin.nativeElement.getBoundingClientRect();\n    }\n    // Check for Element so SVG elements are also supported.\n    if (origin instanceof Element) {\n      return origin.getBoundingClientRect();\n    }\n    const width = origin.width || 0;\n    const height = origin.height || 0;\n    // If the origin is a point, return a client rect as if it was a 0x0 element at the point.\n    return {\n      top: origin.y,\n      bottom: origin.y + height,\n      left: origin.x,\n      right: origin.x + width,\n      height,\n      width\n    };\n  }\n}\n/** Shallow-extends a stylesheet object with another stylesheet object. */\nfunction extendStyles(destination, source) {\n  for (let key in source) {\n    if (source.hasOwnProperty(key)) {\n      destination[key] = source[key];\n    }\n  }\n  return destination;\n}\n/**\n * Extracts the pixel value as a number from a value, if it's a number\n * or a CSS pixel string (e.g. `1337px`). Otherwise returns null.\n */\nfunction getPixelValue(input) {\n  if (typeof input !== 'number' && input != null) {\n    const [value, units] = input.split(cssUnitPattern);\n    return !units || units === 'px' ? parseFloat(value) : null;\n  }\n  return input || null;\n}\n/**\n * Gets a version of an element's bounding `DOMRect` where all the values are rounded down to\n * the nearest pixel. This allows us to account for the cases where there may be sub-pixel\n * deviations in the `DOMRect` returned by the browser (e.g. when zoomed in with a percentage\n * size, see #21350).\n */\nfunction getRoundedBoundingClientRect(clientRect) {\n  return {\n    top: Math.floor(clientRect.top),\n    right: Math.floor(clientRect.right),\n    bottom: Math.floor(clientRect.bottom),\n    left: Math.floor(clientRect.left),\n    width: Math.floor(clientRect.width),\n    height: Math.floor(clientRect.height)\n  };\n}\n/** Returns whether two `ScrollingVisibility` objects are identical. */\nfunction compareScrollVisibility(a, b) {\n  if (a === b) {\n    return true;\n  }\n  return a.isOriginClipped === b.isOriginClipped && a.isOriginOutsideView === b.isOriginOutsideView && a.isOverlayClipped === b.isOverlayClipped && a.isOverlayOutsideView === b.isOverlayOutsideView;\n}\nconst STANDARD_DROPDOWN_BELOW_POSITIONS = [{\n  originX: 'start',\n  originY: 'bottom',\n  overlayX: 'start',\n  overlayY: 'top'\n}, {\n  originX: 'start',\n  originY: 'top',\n  overlayX: 'start',\n  overlayY: 'bottom'\n}, {\n  originX: 'end',\n  originY: 'bottom',\n  overlayX: 'end',\n  overlayY: 'top'\n}, {\n  originX: 'end',\n  originY: 'top',\n  overlayX: 'end',\n  overlayY: 'bottom'\n}];\nconst STANDARD_DROPDOWN_ADJACENT_POSITIONS = [{\n  originX: 'end',\n  originY: 'top',\n  overlayX: 'start',\n  overlayY: 'top'\n}, {\n  originX: 'end',\n  originY: 'bottom',\n  overlayX: 'start',\n  overlayY: 'bottom'\n}, {\n  originX: 'start',\n  originY: 'top',\n  overlayX: 'end',\n  overlayY: 'top'\n}, {\n  originX: 'start',\n  originY: 'bottom',\n  overlayX: 'end',\n  overlayY: 'bottom'\n}];\n\n/** Class to be added to the overlay pane wrapper. */\nconst wrapperClass = 'cdk-global-overlay-wrapper';\n/**\n * A strategy for positioning overlays. Using this strategy, an overlay is given an\n * explicit position relative to the browser's viewport. We use flexbox, instead of\n * transforms, in order to avoid issues with subpixel rendering which can cause the\n * element to become blurry.\n */\nclass GlobalPositionStrategy {\n  constructor() {\n    this._cssPosition = 'static';\n    this._topOffset = '';\n    this._bottomOffset = '';\n    this._alignItems = '';\n    this._xPosition = '';\n    this._xOffset = '';\n    this._width = '';\n    this._height = '';\n    this._isDisposed = false;\n  }\n  attach(overlayRef) {\n    const config = overlayRef.getConfig();\n    this._overlayRef = overlayRef;\n    if (this._width && !config.width) {\n      overlayRef.updateSize({\n        width: this._width\n      });\n    }\n    if (this._height && !config.height) {\n      overlayRef.updateSize({\n        height: this._height\n      });\n    }\n    overlayRef.hostElement.classList.add(wrapperClass);\n    this._isDisposed = false;\n  }\n  /**\n   * Sets the top position of the overlay. Clears any previously set vertical position.\n   * @param value New top offset.\n   */\n  top(value = '') {\n    this._bottomOffset = '';\n    this._topOffset = value;\n    this._alignItems = 'flex-start';\n    return this;\n  }\n  /**\n   * Sets the left position of the overlay. Clears any previously set horizontal position.\n   * @param value New left offset.\n   */\n  left(value = '') {\n    this._xOffset = value;\n    this._xPosition = 'left';\n    return this;\n  }\n  /**\n   * Sets the bottom position of the overlay. Clears any previously set vertical position.\n   * @param value New bottom offset.\n   */\n  bottom(value = '') {\n    this._topOffset = '';\n    this._bottomOffset = value;\n    this._alignItems = 'flex-end';\n    return this;\n  }\n  /**\n   * Sets the right position of the overlay. Clears any previously set horizontal position.\n   * @param value New right offset.\n   */\n  right(value = '') {\n    this._xOffset = value;\n    this._xPosition = 'right';\n    return this;\n  }\n  /**\n   * Sets the overlay to the start of the viewport, depending on the overlay direction.\n   * This will be to the left in LTR layouts and to the right in RTL.\n   * @param offset Offset from the edge of the screen.\n   */\n  start(value = '') {\n    this._xOffset = value;\n    this._xPosition = 'start';\n    return this;\n  }\n  /**\n   * Sets the overlay to the end of the viewport, depending on the overlay direction.\n   * This will be to the right in LTR layouts and to the left in RTL.\n   * @param offset Offset from the edge of the screen.\n   */\n  end(value = '') {\n    this._xOffset = value;\n    this._xPosition = 'end';\n    return this;\n  }\n  /**\n   * Sets the overlay width and clears any previously set width.\n   * @param value New width for the overlay\n   * @deprecated Pass the `width` through the `OverlayConfig`.\n   * @breaking-change 8.0.0\n   */\n  width(value = '') {\n    if (this._overlayRef) {\n      this._overlayRef.updateSize({\n        width: value\n      });\n    } else {\n      this._width = value;\n    }\n    return this;\n  }\n  /**\n   * Sets the overlay height and clears any previously set height.\n   * @param value New height for the overlay\n   * @deprecated Pass the `height` through the `OverlayConfig`.\n   * @breaking-change 8.0.0\n   */\n  height(value = '') {\n    if (this._overlayRef) {\n      this._overlayRef.updateSize({\n        height: value\n      });\n    } else {\n      this._height = value;\n    }\n    return this;\n  }\n  /**\n   * Centers the overlay horizontally with an optional offset.\n   * Clears any previously set horizontal position.\n   *\n   * @param offset Overlay offset from the horizontal center.\n   */\n  centerHorizontally(offset = '') {\n    this.left(offset);\n    this._xPosition = 'center';\n    return this;\n  }\n  /**\n   * Centers the overlay vertically with an optional offset.\n   * Clears any previously set vertical position.\n   *\n   * @param offset Overlay offset from the vertical center.\n   */\n  centerVertically(offset = '') {\n    this.top(offset);\n    this._alignItems = 'center';\n    return this;\n  }\n  /**\n   * Apply the position to the element.\n   * @docs-private\n   */\n  apply() {\n    // Since the overlay ref applies the strategy asynchronously, it could\n    // have been disposed before it ends up being applied. If that is the\n    // case, we shouldn't do anything.\n    if (!this._overlayRef || !this._overlayRef.hasAttached()) {\n      return;\n    }\n    const styles = this._overlayRef.overlayElement.style;\n    const parentStyles = this._overlayRef.hostElement.style;\n    const config = this._overlayRef.getConfig();\n    const {\n      width,\n      height,\n      maxWidth,\n      maxHeight\n    } = config;\n    const shouldBeFlushHorizontally = (width === '100%' || width === '100vw') && (!maxWidth || maxWidth === '100%' || maxWidth === '100vw');\n    const shouldBeFlushVertically = (height === '100%' || height === '100vh') && (!maxHeight || maxHeight === '100%' || maxHeight === '100vh');\n    const xPosition = this._xPosition;\n    const xOffset = this._xOffset;\n    const isRtl = this._overlayRef.getConfig().direction === 'rtl';\n    let marginLeft = '';\n    let marginRight = '';\n    let justifyContent = '';\n    if (shouldBeFlushHorizontally) {\n      justifyContent = 'flex-start';\n    } else if (xPosition === 'center') {\n      justifyContent = 'center';\n      if (isRtl) {\n        marginRight = xOffset;\n      } else {\n        marginLeft = xOffset;\n      }\n    } else if (isRtl) {\n      if (xPosition === 'left' || xPosition === 'end') {\n        justifyContent = 'flex-end';\n        marginLeft = xOffset;\n      } else if (xPosition === 'right' || xPosition === 'start') {\n        justifyContent = 'flex-start';\n        marginRight = xOffset;\n      }\n    } else if (xPosition === 'left' || xPosition === 'start') {\n      justifyContent = 'flex-start';\n      marginLeft = xOffset;\n    } else if (xPosition === 'right' || xPosition === 'end') {\n      justifyContent = 'flex-end';\n      marginRight = xOffset;\n    }\n    styles.position = this._cssPosition;\n    styles.marginLeft = shouldBeFlushHorizontally ? '0' : marginLeft;\n    styles.marginTop = shouldBeFlushVertically ? '0' : this._topOffset;\n    styles.marginBottom = this._bottomOffset;\n    styles.marginRight = shouldBeFlushHorizontally ? '0' : marginRight;\n    parentStyles.justifyContent = justifyContent;\n    parentStyles.alignItems = shouldBeFlushVertically ? 'flex-start' : this._alignItems;\n  }\n  /**\n   * Cleans up the DOM changes from the position strategy.\n   * @docs-private\n   */\n  dispose() {\n    if (this._isDisposed || !this._overlayRef) {\n      return;\n    }\n    const styles = this._overlayRef.overlayElement.style;\n    const parent = this._overlayRef.hostElement;\n    const parentStyles = parent.style;\n    parent.classList.remove(wrapperClass);\n    parentStyles.justifyContent = parentStyles.alignItems = styles.marginTop = styles.marginBottom = styles.marginLeft = styles.marginRight = styles.position = '';\n    this._overlayRef = null;\n    this._isDisposed = true;\n  }\n}\n\n/** Builder for overlay position strategy. */\nclass OverlayPositionBuilder {\n  constructor(_viewportRuler, _document, _platform, _overlayContainer) {\n    this._viewportRuler = _viewportRuler;\n    this._document = _document;\n    this._platform = _platform;\n    this._overlayContainer = _overlayContainer;\n  }\n  /**\n   * Creates a global position strategy.\n   */\n  global() {\n    return new GlobalPositionStrategy();\n  }\n  /**\n   * Creates a flexible position strategy.\n   * @param origin Origin relative to which to position the overlay.\n   */\n  flexibleConnectedTo(origin) {\n    return new FlexibleConnectedPositionStrategy(origin, this._viewportRuler, this._document, this._platform, this._overlayContainer);\n  }\n  static {\n    this.ɵfac = function OverlayPositionBuilder_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || OverlayPositionBuilder)(i0.ɵɵinject(i1.ViewportRuler), i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i1$1.Platform), i0.ɵɵinject(OverlayContainer));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: OverlayPositionBuilder,\n      factory: OverlayPositionBuilder.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayPositionBuilder, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i1.ViewportRuler\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: i1$1.Platform\n  }, {\n    type: OverlayContainer\n  }], null);\n})();\n\n/** Next overlay unique ID. */\nlet nextUniqueId = 0;\n// Note that Overlay is *not* scoped to the app root because of the ComponentFactoryResolver\n// which needs to be different depending on where OverlayModule is imported.\n/**\n * Service to create Overlays. Overlays are dynamically added pieces of floating UI, meant to be\n * used as a low-level building block for other components. Dialogs, tooltips, menus,\n * selects, etc. can all be built using overlays. The service should primarily be used by authors\n * of re-usable components rather than developers building end-user applications.\n *\n * An overlay *is* a PortalOutlet, so any kind of Portal can be loaded into one.\n */\nclass Overlay {\n  constructor(/** Scrolling strategies that can be used when creating an overlay. */\n  scrollStrategies, _overlayContainer, _componentFactoryResolver, _positionBuilder, _keyboardDispatcher, _injector, _ngZone, _document, _directionality, _location, _outsideClickDispatcher, _animationsModuleType) {\n    this.scrollStrategies = scrollStrategies;\n    this._overlayContainer = _overlayContainer;\n    this._componentFactoryResolver = _componentFactoryResolver;\n    this._positionBuilder = _positionBuilder;\n    this._keyboardDispatcher = _keyboardDispatcher;\n    this._injector = _injector;\n    this._ngZone = _ngZone;\n    this._document = _document;\n    this._directionality = _directionality;\n    this._location = _location;\n    this._outsideClickDispatcher = _outsideClickDispatcher;\n    this._animationsModuleType = _animationsModuleType;\n  }\n  /**\n   * Creates an overlay.\n   * @param config Configuration applied to the overlay.\n   * @returns Reference to the created overlay.\n   */\n  create(config) {\n    const host = this._createHostElement();\n    const pane = this._createPaneElement(host);\n    const portalOutlet = this._createPortalOutlet(pane);\n    const overlayConfig = new OverlayConfig(config);\n    overlayConfig.direction = overlayConfig.direction || this._directionality.value;\n    return new OverlayRef(portalOutlet, host, pane, overlayConfig, this._ngZone, this._keyboardDispatcher, this._document, this._location, this._outsideClickDispatcher, this._animationsModuleType === 'NoopAnimations', this._injector.get(EnvironmentInjector));\n  }\n  /**\n   * Gets a position builder that can be used, via fluent API,\n   * to construct and configure a position strategy.\n   * @returns An overlay position builder.\n   */\n  position() {\n    return this._positionBuilder;\n  }\n  /**\n   * Creates the DOM element for an overlay and appends it to the overlay container.\n   * @returns Newly-created pane element\n   */\n  _createPaneElement(host) {\n    const pane = this._document.createElement('div');\n    pane.id = `cdk-overlay-${nextUniqueId++}`;\n    pane.classList.add('cdk-overlay-pane');\n    host.appendChild(pane);\n    return pane;\n  }\n  /**\n   * Creates the host element that wraps around an overlay\n   * and can be used for advanced positioning.\n   * @returns Newly-create host element.\n   */\n  _createHostElement() {\n    const host = this._document.createElement('div');\n    this._overlayContainer.getContainerElement().appendChild(host);\n    return host;\n  }\n  /**\n   * Create a DomPortalOutlet into which the overlay content can be loaded.\n   * @param pane The DOM element to turn into a portal outlet.\n   * @returns A portal outlet for the given DOM element.\n   */\n  _createPortalOutlet(pane) {\n    // We have to resolve the ApplicationRef later in order to allow people\n    // to use overlay-based providers during app initialization.\n    if (!this._appRef) {\n      this._appRef = this._injector.get(ApplicationRef);\n    }\n    return new DomPortalOutlet(pane, this._componentFactoryResolver, this._appRef, this._injector, this._document);\n  }\n  static {\n    this.ɵfac = function Overlay_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || Overlay)(i0.ɵɵinject(ScrollStrategyOptions), i0.ɵɵinject(OverlayContainer), i0.ɵɵinject(i0.ComponentFactoryResolver), i0.ɵɵinject(OverlayPositionBuilder), i0.ɵɵinject(OverlayKeyboardDispatcher), i0.ɵɵinject(i0.Injector), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i5.Directionality), i0.ɵɵinject(i6.Location), i0.ɵɵinject(OverlayOutsideClickDispatcher), i0.ɵɵinject(ANIMATION_MODULE_TYPE, 8));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: Overlay,\n      factory: Overlay.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Overlay, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: ScrollStrategyOptions\n  }, {\n    type: OverlayContainer\n  }, {\n    type: i0.ComponentFactoryResolver\n  }, {\n    type: OverlayPositionBuilder\n  }, {\n    type: OverlayKeyboardDispatcher\n  }, {\n    type: i0.Injector\n  }, {\n    type: i0.NgZone\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: i5.Directionality\n  }, {\n    type: i6.Location\n  }, {\n    type: OverlayOutsideClickDispatcher\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [ANIMATION_MODULE_TYPE]\n    }, {\n      type: Optional\n    }]\n  }], null);\n})();\n\n/** Default set of positions for the overlay. Follows the behavior of a dropdown. */\nconst defaultPositionList = [{\n  originX: 'start',\n  originY: 'bottom',\n  overlayX: 'start',\n  overlayY: 'top'\n}, {\n  originX: 'start',\n  originY: 'top',\n  overlayX: 'start',\n  overlayY: 'bottom'\n}, {\n  originX: 'end',\n  originY: 'top',\n  overlayX: 'end',\n  overlayY: 'bottom'\n}, {\n  originX: 'end',\n  originY: 'bottom',\n  overlayX: 'end',\n  overlayY: 'top'\n}];\n/** Injection token that determines the scroll handling while the connected overlay is open. */\nconst CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY = new InjectionToken('cdk-connected-overlay-scroll-strategy', {\n  providedIn: 'root',\n  factory: () => {\n    const overlay = inject(Overlay);\n    return () => overlay.scrollStrategies.reposition();\n  }\n});\n/**\n * Directive applied to an element to make it usable as an origin for an Overlay using a\n * ConnectedPositionStrategy.\n */\nclass CdkOverlayOrigin {\n  constructor(/** Reference to the element on which the directive is applied. */\n  elementRef) {\n    this.elementRef = elementRef;\n  }\n  static {\n    this.ɵfac = function CdkOverlayOrigin_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkOverlayOrigin)(i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkOverlayOrigin,\n      selectors: [[\"\", \"cdk-overlay-origin\", \"\"], [\"\", \"overlay-origin\", \"\"], [\"\", \"cdkOverlayOrigin\", \"\"]],\n      exportAs: [\"cdkOverlayOrigin\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkOverlayOrigin, [{\n    type: Directive,\n    args: [{\n      selector: '[cdk-overlay-origin], [overlay-origin], [cdkOverlayOrigin]',\n      exportAs: 'cdkOverlayOrigin',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }], null);\n})();\n/**\n * Directive to facilitate declarative creation of an\n * Overlay using a FlexibleConnectedPositionStrategy.\n */\nclass CdkConnectedOverlay {\n  /** The offset in pixels for the overlay connection point on the x-axis */\n  get offsetX() {\n    return this._offsetX;\n  }\n  set offsetX(offsetX) {\n    this._offsetX = offsetX;\n    if (this._position) {\n      this._updatePositionStrategy(this._position);\n    }\n  }\n  /** The offset in pixels for the overlay connection point on the y-axis */\n  get offsetY() {\n    return this._offsetY;\n  }\n  set offsetY(offsetY) {\n    this._offsetY = offsetY;\n    if (this._position) {\n      this._updatePositionStrategy(this._position);\n    }\n  }\n  /** Whether the overlay should be disposed of when the user goes backwards/forwards in history. */\n  get disposeOnNavigation() {\n    return this._disposeOnNavigation;\n  }\n  set disposeOnNavigation(value) {\n    this._disposeOnNavigation = value;\n  }\n  // TODO(jelbourn): inputs for size, scroll behavior, animation, etc.\n  constructor(_overlay, templateRef, viewContainerRef, scrollStrategyFactory, _dir) {\n    this._overlay = _overlay;\n    this._dir = _dir;\n    this._backdropSubscription = Subscription.EMPTY;\n    this._attachSubscription = Subscription.EMPTY;\n    this._detachSubscription = Subscription.EMPTY;\n    this._positionSubscription = Subscription.EMPTY;\n    this._disposeOnNavigation = false;\n    this._ngZone = inject(NgZone);\n    /** Margin between the overlay and the viewport edges. */\n    this.viewportMargin = 0;\n    /** Whether the overlay is open. */\n    this.open = false;\n    /** Whether the overlay can be closed by user interaction. */\n    this.disableClose = false;\n    /** Whether or not the overlay should attach a backdrop. */\n    this.hasBackdrop = false;\n    /** Whether or not the overlay should be locked when scrolling. */\n    this.lockPosition = false;\n    /** Whether the overlay's width and height can be constrained to fit within the viewport. */\n    this.flexibleDimensions = false;\n    /** Whether the overlay can grow after the initial open when flexible positioning is turned on. */\n    this.growAfterOpen = false;\n    /** Whether the overlay can be pushed on-screen if none of the provided positions fit. */\n    this.push = false;\n    /** Event emitted when the backdrop is clicked. */\n    this.backdropClick = new EventEmitter();\n    /** Event emitted when the position has changed. */\n    this.positionChange = new EventEmitter();\n    /** Event emitted when the overlay has been attached. */\n    this.attach = new EventEmitter();\n    /** Event emitted when the overlay has been detached. */\n    this.detach = new EventEmitter();\n    /** Emits when there are keyboard events that are targeted at the overlay. */\n    this.overlayKeydown = new EventEmitter();\n    /** Emits when there are mouse outside click events that are targeted at the overlay. */\n    this.overlayOutsideClick = new EventEmitter();\n    this._templatePortal = new TemplatePortal(templateRef, viewContainerRef);\n    this._scrollStrategyFactory = scrollStrategyFactory;\n    this.scrollStrategy = this._scrollStrategyFactory();\n  }\n  /** The associated overlay reference. */\n  get overlayRef() {\n    return this._overlayRef;\n  }\n  /** The element's layout direction. */\n  get dir() {\n    return this._dir ? this._dir.value : 'ltr';\n  }\n  ngOnDestroy() {\n    this._attachSubscription.unsubscribe();\n    this._detachSubscription.unsubscribe();\n    this._backdropSubscription.unsubscribe();\n    this._positionSubscription.unsubscribe();\n    if (this._overlayRef) {\n      this._overlayRef.dispose();\n    }\n  }\n  ngOnChanges(changes) {\n    if (this._position) {\n      this._updatePositionStrategy(this._position);\n      this._overlayRef.updateSize({\n        width: this.width,\n        minWidth: this.minWidth,\n        height: this.height,\n        minHeight: this.minHeight\n      });\n      if (changes['origin'] && this.open) {\n        this._position.apply();\n      }\n    }\n    if (changes['open']) {\n      this.open ? this._attachOverlay() : this._detachOverlay();\n    }\n  }\n  /** Creates an overlay */\n  _createOverlay() {\n    if (!this.positions || !this.positions.length) {\n      this.positions = defaultPositionList;\n    }\n    const overlayRef = this._overlayRef = this._overlay.create(this._buildConfig());\n    this._attachSubscription = overlayRef.attachments().subscribe(() => this.attach.emit());\n    this._detachSubscription = overlayRef.detachments().subscribe(() => this.detach.emit());\n    overlayRef.keydownEvents().subscribe(event => {\n      this.overlayKeydown.next(event);\n      if (event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event)) {\n        event.preventDefault();\n        this._detachOverlay();\n      }\n    });\n    this._overlayRef.outsidePointerEvents().subscribe(event => {\n      const origin = this._getOriginElement();\n      const target = _getEventTarget(event);\n      if (!origin || origin !== target && !origin.contains(target)) {\n        this.overlayOutsideClick.next(event);\n      }\n    });\n  }\n  /** Builds the overlay config based on the directive's inputs */\n  _buildConfig() {\n    const positionStrategy = this._position = this.positionStrategy || this._createPositionStrategy();\n    const overlayConfig = new OverlayConfig({\n      direction: this._dir,\n      positionStrategy,\n      scrollStrategy: this.scrollStrategy,\n      hasBackdrop: this.hasBackdrop,\n      disposeOnNavigation: this.disposeOnNavigation\n    });\n    if (this.width || this.width === 0) {\n      overlayConfig.width = this.width;\n    }\n    if (this.height || this.height === 0) {\n      overlayConfig.height = this.height;\n    }\n    if (this.minWidth || this.minWidth === 0) {\n      overlayConfig.minWidth = this.minWidth;\n    }\n    if (this.minHeight || this.minHeight === 0) {\n      overlayConfig.minHeight = this.minHeight;\n    }\n    if (this.backdropClass) {\n      overlayConfig.backdropClass = this.backdropClass;\n    }\n    if (this.panelClass) {\n      overlayConfig.panelClass = this.panelClass;\n    }\n    return overlayConfig;\n  }\n  /** Updates the state of a position strategy, based on the values of the directive inputs. */\n  _updatePositionStrategy(positionStrategy) {\n    const positions = this.positions.map(currentPosition => ({\n      originX: currentPosition.originX,\n      originY: currentPosition.originY,\n      overlayX: currentPosition.overlayX,\n      overlayY: currentPosition.overlayY,\n      offsetX: currentPosition.offsetX || this.offsetX,\n      offsetY: currentPosition.offsetY || this.offsetY,\n      panelClass: currentPosition.panelClass || undefined\n    }));\n    return positionStrategy.setOrigin(this._getOrigin()).withPositions(positions).withFlexibleDimensions(this.flexibleDimensions).withPush(this.push).withGrowAfterOpen(this.growAfterOpen).withViewportMargin(this.viewportMargin).withLockedPosition(this.lockPosition).withTransformOriginOn(this.transformOriginSelector);\n  }\n  /** Returns the position strategy of the overlay to be set on the overlay config */\n  _createPositionStrategy() {\n    const strategy = this._overlay.position().flexibleConnectedTo(this._getOrigin());\n    this._updatePositionStrategy(strategy);\n    return strategy;\n  }\n  _getOrigin() {\n    if (this.origin instanceof CdkOverlayOrigin) {\n      return this.origin.elementRef;\n    } else {\n      return this.origin;\n    }\n  }\n  _getOriginElement() {\n    if (this.origin instanceof CdkOverlayOrigin) {\n      return this.origin.elementRef.nativeElement;\n    }\n    if (this.origin instanceof ElementRef) {\n      return this.origin.nativeElement;\n    }\n    if (typeof Element !== 'undefined' && this.origin instanceof Element) {\n      return this.origin;\n    }\n    return null;\n  }\n  /** Attaches the overlay and subscribes to backdrop clicks if backdrop exists */\n  _attachOverlay() {\n    if (!this._overlayRef) {\n      this._createOverlay();\n    } else {\n      // Update the overlay size, in case the directive's inputs have changed\n      this._overlayRef.getConfig().hasBackdrop = this.hasBackdrop;\n    }\n    if (!this._overlayRef.hasAttached()) {\n      this._overlayRef.attach(this._templatePortal);\n    }\n    if (this.hasBackdrop) {\n      this._backdropSubscription = this._overlayRef.backdropClick().subscribe(event => {\n        this.backdropClick.emit(event);\n      });\n    } else {\n      this._backdropSubscription.unsubscribe();\n    }\n    this._positionSubscription.unsubscribe();\n    // Only subscribe to `positionChanges` if requested, because putting\n    // together all the information for it can be expensive.\n    if (this.positionChange.observers.length > 0) {\n      this._positionSubscription = this._position.positionChanges.pipe(takeWhile(() => this.positionChange.observers.length > 0)).subscribe(position => {\n        this._ngZone.run(() => this.positionChange.emit(position));\n        if (this.positionChange.observers.length === 0) {\n          this._positionSubscription.unsubscribe();\n        }\n      });\n    }\n  }\n  /** Detaches the overlay and unsubscribes to backdrop clicks if backdrop exists */\n  _detachOverlay() {\n    if (this._overlayRef) {\n      this._overlayRef.detach();\n    }\n    this._backdropSubscription.unsubscribe();\n    this._positionSubscription.unsubscribe();\n  }\n  static {\n    this.ɵfac = function CdkConnectedOverlay_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkConnectedOverlay)(i0.ɵɵdirectiveInject(Overlay), i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY), i0.ɵɵdirectiveInject(i5.Directionality, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkConnectedOverlay,\n      selectors: [[\"\", \"cdk-connected-overlay\", \"\"], [\"\", \"connected-overlay\", \"\"], [\"\", \"cdkConnectedOverlay\", \"\"]],\n      inputs: {\n        origin: [0, \"cdkConnectedOverlayOrigin\", \"origin\"],\n        positions: [0, \"cdkConnectedOverlayPositions\", \"positions\"],\n        positionStrategy: [0, \"cdkConnectedOverlayPositionStrategy\", \"positionStrategy\"],\n        offsetX: [0, \"cdkConnectedOverlayOffsetX\", \"offsetX\"],\n        offsetY: [0, \"cdkConnectedOverlayOffsetY\", \"offsetY\"],\n        width: [0, \"cdkConnectedOverlayWidth\", \"width\"],\n        height: [0, \"cdkConnectedOverlayHeight\", \"height\"],\n        minWidth: [0, \"cdkConnectedOverlayMinWidth\", \"minWidth\"],\n        minHeight: [0, \"cdkConnectedOverlayMinHeight\", \"minHeight\"],\n        backdropClass: [0, \"cdkConnectedOverlayBackdropClass\", \"backdropClass\"],\n        panelClass: [0, \"cdkConnectedOverlayPanelClass\", \"panelClass\"],\n        viewportMargin: [0, \"cdkConnectedOverlayViewportMargin\", \"viewportMargin\"],\n        scrollStrategy: [0, \"cdkConnectedOverlayScrollStrategy\", \"scrollStrategy\"],\n        open: [0, \"cdkConnectedOverlayOpen\", \"open\"],\n        disableClose: [0, \"cdkConnectedOverlayDisableClose\", \"disableClose\"],\n        transformOriginSelector: [0, \"cdkConnectedOverlayTransformOriginOn\", \"transformOriginSelector\"],\n        hasBackdrop: [2, \"cdkConnectedOverlayHasBackdrop\", \"hasBackdrop\", booleanAttribute],\n        lockPosition: [2, \"cdkConnectedOverlayLockPosition\", \"lockPosition\", booleanAttribute],\n        flexibleDimensions: [2, \"cdkConnectedOverlayFlexibleDimensions\", \"flexibleDimensions\", booleanAttribute],\n        growAfterOpen: [2, \"cdkConnectedOverlayGrowAfterOpen\", \"growAfterOpen\", booleanAttribute],\n        push: [2, \"cdkConnectedOverlayPush\", \"push\", booleanAttribute],\n        disposeOnNavigation: [2, \"cdkConnectedOverlayDisposeOnNavigation\", \"disposeOnNavigation\", booleanAttribute]\n      },\n      outputs: {\n        backdropClick: \"backdropClick\",\n        positionChange: \"positionChange\",\n        attach: \"attach\",\n        detach: \"detach\",\n        overlayKeydown: \"overlayKeydown\",\n        overlayOutsideClick: \"overlayOutsideClick\"\n      },\n      exportAs: [\"cdkConnectedOverlay\"],\n      standalone: true,\n      features: [i0.ɵɵInputTransformsFeature, i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkConnectedOverlay, [{\n    type: Directive,\n    args: [{\n      selector: '[cdk-connected-overlay], [connected-overlay], [cdkConnectedOverlay]',\n      exportAs: 'cdkConnectedOverlay',\n      standalone: true\n    }]\n  }], () => [{\n    type: Overlay\n  }, {\n    type: i0.TemplateRef\n  }, {\n    type: i0.ViewContainerRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY]\n    }]\n  }, {\n    type: i5.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    origin: [{\n      type: Input,\n      args: ['cdkConnectedOverlayOrigin']\n    }],\n    positions: [{\n      type: Input,\n      args: ['cdkConnectedOverlayPositions']\n    }],\n    positionStrategy: [{\n      type: Input,\n      args: ['cdkConnectedOverlayPositionStrategy']\n    }],\n    offsetX: [{\n      type: Input,\n      args: ['cdkConnectedOverlayOffsetX']\n    }],\n    offsetY: [{\n      type: Input,\n      args: ['cdkConnectedOverlayOffsetY']\n    }],\n    width: [{\n      type: Input,\n      args: ['cdkConnectedOverlayWidth']\n    }],\n    height: [{\n      type: Input,\n      args: ['cdkConnectedOverlayHeight']\n    }],\n    minWidth: [{\n      type: Input,\n      args: ['cdkConnectedOverlayMinWidth']\n    }],\n    minHeight: [{\n      type: Input,\n      args: ['cdkConnectedOverlayMinHeight']\n    }],\n    backdropClass: [{\n      type: Input,\n      args: ['cdkConnectedOverlayBackdropClass']\n    }],\n    panelClass: [{\n      type: Input,\n      args: ['cdkConnectedOverlayPanelClass']\n    }],\n    viewportMargin: [{\n      type: Input,\n      args: ['cdkConnectedOverlayViewportMargin']\n    }],\n    scrollStrategy: [{\n      type: Input,\n      args: ['cdkConnectedOverlayScrollStrategy']\n    }],\n    open: [{\n      type: Input,\n      args: ['cdkConnectedOverlayOpen']\n    }],\n    disableClose: [{\n      type: Input,\n      args: ['cdkConnectedOverlayDisableClose']\n    }],\n    transformOriginSelector: [{\n      type: Input,\n      args: ['cdkConnectedOverlayTransformOriginOn']\n    }],\n    hasBackdrop: [{\n      type: Input,\n      args: [{\n        alias: 'cdkConnectedOverlayHasBackdrop',\n        transform: booleanAttribute\n      }]\n    }],\n    lockPosition: [{\n      type: Input,\n      args: [{\n        alias: 'cdkConnectedOverlayLockPosition',\n        transform: booleanAttribute\n      }]\n    }],\n    flexibleDimensions: [{\n      type: Input,\n      args: [{\n        alias: 'cdkConnectedOverlayFlexibleDimensions',\n        transform: booleanAttribute\n      }]\n    }],\n    growAfterOpen: [{\n      type: Input,\n      args: [{\n        alias: 'cdkConnectedOverlayGrowAfterOpen',\n        transform: booleanAttribute\n      }]\n    }],\n    push: [{\n      type: Input,\n      args: [{\n        alias: 'cdkConnectedOverlayPush',\n        transform: booleanAttribute\n      }]\n    }],\n    disposeOnNavigation: [{\n      type: Input,\n      args: [{\n        alias: 'cdkConnectedOverlayDisposeOnNavigation',\n        transform: booleanAttribute\n      }]\n    }],\n    backdropClick: [{\n      type: Output\n    }],\n    positionChange: [{\n      type: Output\n    }],\n    attach: [{\n      type: Output\n    }],\n    detach: [{\n      type: Output\n    }],\n    overlayKeydown: [{\n      type: Output\n    }],\n    overlayOutsideClick: [{\n      type: Output\n    }]\n  });\n})();\n/** @docs-private */\nfunction CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER_FACTORY(overlay) {\n  return () => overlay.scrollStrategies.reposition();\n}\n/** @docs-private */\nconst CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER = {\n  provide: CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER_FACTORY\n};\nclass OverlayModule {\n  static {\n    this.ɵfac = function OverlayModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || OverlayModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: OverlayModule,\n      imports: [BidiModule, PortalModule, ScrollingModule, CdkConnectedOverlay, CdkOverlayOrigin],\n      exports: [CdkConnectedOverlay, CdkOverlayOrigin, ScrollingModule]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [Overlay, CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER],\n      imports: [BidiModule, PortalModule, ScrollingModule, ScrollingModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayModule, [{\n    type: NgModule,\n    args: [{\n      imports: [BidiModule, PortalModule, ScrollingModule, CdkConnectedOverlay, CdkOverlayOrigin],\n      exports: [CdkConnectedOverlay, CdkOverlayOrigin, ScrollingModule],\n      providers: [Overlay, CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER]\n    }]\n  }], null, null);\n})();\n\n/**\n * Alternative to OverlayContainer that supports correct displaying of overlay elements in\n * Fullscreen mode\n * https://developer.mozilla.org/en-US/docs/Web/API/Element/requestFullScreen\n *\n * Should be provided in the root component.\n */\nclass FullscreenOverlayContainer extends OverlayContainer {\n  constructor(_document, platform) {\n    super(_document, platform);\n  }\n  ngOnDestroy() {\n    super.ngOnDestroy();\n    if (this._fullScreenEventName && this._fullScreenListener) {\n      this._document.removeEventListener(this._fullScreenEventName, this._fullScreenListener);\n    }\n  }\n  _createContainer() {\n    super._createContainer();\n    this._adjustParentForFullscreenChange();\n    this._addFullscreenChangeListener(() => this._adjustParentForFullscreenChange());\n  }\n  _adjustParentForFullscreenChange() {\n    if (!this._containerElement) {\n      return;\n    }\n    const fullscreenElement = this.getFullscreenElement();\n    const parent = fullscreenElement || this._document.body;\n    parent.appendChild(this._containerElement);\n  }\n  _addFullscreenChangeListener(fn) {\n    const eventName = this._getEventName();\n    if (eventName) {\n      if (this._fullScreenListener) {\n        this._document.removeEventListener(eventName, this._fullScreenListener);\n      }\n      this._document.addEventListener(eventName, fn);\n      this._fullScreenListener = fn;\n    }\n  }\n  _getEventName() {\n    if (!this._fullScreenEventName) {\n      const _document = this._document;\n      if (_document.fullscreenEnabled) {\n        this._fullScreenEventName = 'fullscreenchange';\n      } else if (_document.webkitFullscreenEnabled) {\n        this._fullScreenEventName = 'webkitfullscreenchange';\n      } else if (_document.mozFullScreenEnabled) {\n        this._fullScreenEventName = 'mozfullscreenchange';\n      } else if (_document.msFullscreenEnabled) {\n        this._fullScreenEventName = 'MSFullscreenChange';\n      }\n    }\n    return this._fullScreenEventName;\n  }\n  /**\n   * When the page is put into fullscreen mode, a specific element is specified.\n   * Only that element and its children are visible when in fullscreen mode.\n   */\n  getFullscreenElement() {\n    const _document = this._document;\n    return _document.fullscreenElement || _document.webkitFullscreenElement || _document.mozFullScreenElement || _document.msFullscreenElement || null;\n  }\n  static {\n    this.ɵfac = function FullscreenOverlayContainer_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FullscreenOverlayContainer)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i1$1.Platform));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: FullscreenOverlayContainer,\n      factory: FullscreenOverlayContainer.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FullscreenOverlayContainer, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: i1$1.Platform\n  }], null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BlockScrollStrategy, CdkConnectedOverlay, CdkOverlayOrigin, CloseScrollStrategy, ConnectedOverlayPositionChange, ConnectionPositionPair, FlexibleConnectedPositionStrategy, FullscreenOverlayContainer, GlobalPositionStrategy, NoopScrollStrategy, Overlay, OverlayConfig, OverlayContainer, OverlayKeyboardDispatcher, OverlayModule, OverlayOutsideClickDispatcher, OverlayPositionBuilder, OverlayRef, RepositionScrollStrategy, STANDARD_DROPDOWN_ADJACENT_POSITIONS, STANDARD_DROPDOWN_BELOW_POSITIONS, ScrollStrategyOptions, ScrollingVisibility, validateHorizontalPosition, validateVerticalPosition };\n", "import { __decorate } from 'tslib';\nimport * as i1 from '@angular/cdk/overlay';\nimport { ConnectionPositionPair, CdkOverlayOrigin, OverlayContainer } from '@angular/cdk/overlay';\nimport * as i0 from '@angular/core';\nimport { ElementRef, Directive, Input, NgModule, Injectable, Inject } from '@angular/core';\nimport { takeUntil } from 'rxjs/operators';\nimport * as i2 from 'tds-ui/core/services';\nimport { TDSDestroyService } from 'tds-ui/core/services';\nimport { InputBoolean } from 'tds-ui/shared/utility';\nimport { DOCUMENT } from '@angular/common';\nimport * as i1$1 from '@angular/cdk/platform';\nconst POSITION_MAP = {\n  top: new ConnectionPositionPair({\n    originX: 'center',\n    originY: 'top'\n  }, {\n    overlayX: 'center',\n    overlayY: 'bottom'\n  }),\n  topCenter: new ConnectionPositionPair({\n    originX: 'center',\n    originY: 'top'\n  }, {\n    overlayX: 'center',\n    overlayY: 'bottom'\n  }),\n  topLeft: new ConnectionPositionPair({\n    originX: 'start',\n    originY: 'top'\n  }, {\n    overlayX: 'start',\n    overlayY: 'bottom'\n  }),\n  topRight: new ConnectionPositionPair({\n    originX: 'end',\n    originY: 'top'\n  }, {\n    overlayX: 'end',\n    overlayY: 'bottom'\n  }),\n  right: new ConnectionPositionPair({\n    originX: 'end',\n    originY: 'center'\n  }, {\n    overlayX: 'start',\n    overlayY: 'center'\n  }),\n  rightTop: new ConnectionPositionPair({\n    originX: 'end',\n    originY: 'top'\n  }, {\n    overlayX: 'start',\n    overlayY: 'top'\n  }),\n  rightBottom: new ConnectionPositionPair({\n    originX: 'end',\n    originY: 'bottom'\n  }, {\n    overlayX: 'start',\n    overlayY: 'bottom'\n  }),\n  bottom: new ConnectionPositionPair({\n    originX: 'center',\n    originY: 'bottom'\n  }, {\n    overlayX: 'center',\n    overlayY: 'top'\n  }),\n  bottomCenter: new ConnectionPositionPair({\n    originX: 'center',\n    originY: 'bottom'\n  }, {\n    overlayX: 'center',\n    overlayY: 'top'\n  }),\n  bottomLeft: new ConnectionPositionPair({\n    originX: 'start',\n    originY: 'bottom'\n  }, {\n    overlayX: 'start',\n    overlayY: 'top'\n  }),\n  bottomRight: new ConnectionPositionPair({\n    originX: 'end',\n    originY: 'bottom'\n  }, {\n    overlayX: 'end',\n    overlayY: 'top'\n  }),\n  left: new ConnectionPositionPair({\n    originX: 'start',\n    originY: 'center'\n  }, {\n    overlayX: 'end',\n    overlayY: 'center'\n  }),\n  leftTop: new ConnectionPositionPair({\n    originX: 'start',\n    originY: 'top'\n  }, {\n    overlayX: 'end',\n    overlayY: 'top'\n  }),\n  leftBottom: new ConnectionPositionPair({\n    originX: 'start',\n    originY: 'bottom'\n  }, {\n    overlayX: 'end',\n    overlayY: 'bottom'\n  })\n};\nconst DEFAULT_TOOLTIP_POSITIONS = [POSITION_MAP.top, POSITION_MAP.right, POSITION_MAP.bottom, POSITION_MAP.left];\nconst DEFAULT_CASCADER_POSITIONS = [POSITION_MAP.bottomLeft, POSITION_MAP.bottomRight, POSITION_MAP.topLeft, POSITION_MAP.topRight];\nconst DEFAULT_MENTION_TOP_POSITIONS = [new ConnectionPositionPair({\n  originX: 'start',\n  originY: 'bottom'\n}, {\n  overlayX: 'start',\n  overlayY: 'bottom'\n}), new ConnectionPositionPair({\n  originX: 'start',\n  originY: 'bottom'\n}, {\n  overlayX: 'end',\n  overlayY: 'bottom'\n})];\nconst DEFAULT_MENTION_BOTTOM_POSITIONS = [POSITION_MAP.bottomLeft, new ConnectionPositionPair({\n  originX: 'start',\n  originY: 'bottom'\n}, {\n  overlayX: 'end',\n  overlayY: 'top'\n})];\nfunction getPlacementName(position) {\n  for (const placement in POSITION_MAP) {\n    if (position.connectionPair.originX === POSITION_MAP[placement].originX && position.connectionPair.originY === POSITION_MAP[placement].originY && position.connectionPair.overlayX === POSITION_MAP[placement].overlayX && position.connectionPair.overlayY === POSITION_MAP[placement].overlayY) {\n      return placement;\n    }\n  }\n  return undefined;\n}\nclass TDSConnectedOverlayDirective {\n  constructor(cdkConnectedOverlay, TDSDestroyService) {\n    this.cdkConnectedOverlay = cdkConnectedOverlay;\n    this.TDSDestroyService = TDSDestroyService;\n    this.tdsArrowPointAtCenter = false;\n    this.cdkConnectedOverlay.backdropClass = 'tds-overlay-transparent-backdrop';\n    this.cdkConnectedOverlay.positionChange.pipe(takeUntil(this.TDSDestroyService)).subscribe(position => {\n      if (this.tdsArrowPointAtCenter) {\n        this.updateArrowPosition(position);\n      }\n    });\n  }\n  updateArrowPosition(position) {\n    const originRect = this.getOriginRect();\n    const placement = getPlacementName(position);\n    let offsetX = 0;\n    let offsetY = 0;\n    if (placement === 'topLeft' || placement === 'bottomLeft') {\n      offsetX = originRect.width / 2 - 14;\n    } else if (placement === 'topRight' || placement === 'bottomRight') {\n      offsetX = -(originRect.width / 2 - 14);\n    } else if (placement === 'leftTop' || placement === 'rightTop') {\n      offsetY = originRect.height / 2 - 10;\n    } else if (placement === 'leftBottom' || placement === 'rightBottom') {\n      offsetY = -(originRect.height / 2 - 10);\n    }\n    if (this.cdkConnectedOverlay.offsetX !== offsetX || this.cdkConnectedOverlay.offsetY !== offsetY) {\n      this.cdkConnectedOverlay.offsetY = offsetY;\n      this.cdkConnectedOverlay.offsetX = offsetX;\n      this.cdkConnectedOverlay.overlayRef.updatePosition();\n    }\n  }\n  getFlexibleConnectedPositionStrategyOrigin() {\n    if (this.cdkConnectedOverlay.origin instanceof CdkOverlayOrigin) {\n      return this.cdkConnectedOverlay.origin.elementRef;\n    } else {\n      return this.cdkConnectedOverlay.origin;\n    }\n  }\n  getOriginRect() {\n    const origin = this.getFlexibleConnectedPositionStrategyOrigin();\n    if (origin instanceof ElementRef) {\n      return origin.nativeElement.getBoundingClientRect();\n    }\n    // Check for Element so SVG elements are also supported.\n    if (origin instanceof Element) {\n      return origin.getBoundingClientRect();\n    }\n    const width = origin.width || 0;\n    const height = origin.height || 0;\n    // If the origin is a point, return a client rect as if it was a 0x0 element at the point.\n    return {\n      top: origin.y,\n      bottom: origin.y + height,\n      left: origin.x,\n      right: origin.x + width,\n      height,\n      width\n    };\n  }\n  static {\n    this.ɵfac = function TDSConnectedOverlayDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSConnectedOverlayDirective)(i0.ɵɵdirectiveInject(i1.CdkConnectedOverlay), i0.ɵɵdirectiveInject(i2.TDSDestroyService));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TDSConnectedOverlayDirective,\n      selectors: [[\"\", \"cdkConnectedOverlay\", \"\", \"tdsConnectedOverlay\", \"\"]],\n      inputs: {\n        tdsArrowPointAtCenter: \"tdsArrowPointAtCenter\"\n      },\n      exportAs: [\"tdsConnectedOverlay\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([TDSDestroyService])]\n    });\n  }\n}\n__decorate([InputBoolean()], TDSConnectedOverlayDirective.prototype, \"tdsArrowPointAtCenter\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSConnectedOverlayDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkConnectedOverlay][tdsConnectedOverlay]',\n      exportAs: 'tdsConnectedOverlay',\n      providers: [TDSDestroyService],\n      standalone: true\n    }]\n  }], () => [{\n    type: i1.CdkConnectedOverlay\n  }, {\n    type: i2.TDSDestroyService\n  }], {\n    tdsArrowPointAtCenter: [{\n      type: Input\n    }]\n  });\n})();\nclass TDSOverlayModule {\n  static {\n    this.ɵfac = function TDSOverlayModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSOverlayModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: TDSOverlayModule,\n      imports: [TDSConnectedOverlayDirective],\n      exports: [TDSConnectedOverlayDirective]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSOverlayModule, [{\n    type: NgModule,\n    args: [{\n      imports: [TDSConnectedOverlayDirective],\n      exports: [TDSConnectedOverlayDirective]\n    }]\n  }], null, null);\n})();\n\n/** Container inside which all overlays will render. */\nclass TDSOverlayContainer extends OverlayContainer {\n  constructor(_document, platform) {\n    super(_document, platform);\n  }\n  /**\n   * Create the overlay container element, which is simply a div\n   * with the 'cdk-overlay-container' class on the document body.\n   */\n  _createContainer() {\n    const containerClass = 'cdk-overlay-container';\n    const oppositePlatformContainers = this._document.querySelectorAll(`.${containerClass}`);\n    if (containerClass) {\n      const container = this._document.createElement('div');\n      container.classList.add(containerClass);\n      container.classList.add('tds-cdk-overlay-container');\n      this._document.body.appendChild(container);\n      this._containerElement = container;\n    } else {\n      this._containerElement = oppositePlatformContainers[0];\n    }\n  }\n  static {\n    this.ɵfac = function TDSOverlayContainer_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSOverlayContainer)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i1$1.Platform));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: TDSOverlayContainer,\n      factory: TDSOverlayContainer.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSOverlayContainer, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: i1$1.Platform\n  }], null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { DEFAULT_CASCADER_POSITIONS, DEFAULT_MENTION_BOTTOM_POSITIONS, DEFAULT_MENTION_TOP_POSITIONS, DEFAULT_TOOLTIP_POSITIONS, POSITION_MAP, TDSConnectedOverlayDirective, TDSOverlayContainer, TDSOverlayModule, getPlacementName };\n", "import * as i0 from '@angular/core';\nimport { TemplateRef, Directive, Input, NgModule } from '@angular/core';\nclass TDSStringTemplateOutletDirective {\n  static ngTemplateContextGuard(_dir, _ctx) {\n    return true;\n  }\n  recreateView() {\n    this.viewContainer.clear();\n    const isTemplateRef = this.tdsStringTemplateOutlet instanceof TemplateRef;\n    const templateRef = isTemplateRef ? this.tdsStringTemplateOutlet : this.templateRef;\n    this.embeddedViewRef = this.viewContainer.createEmbeddedView(templateRef, isTemplateRef ? this.tdsStringTemplateOutletContext : this.context);\n  }\n  updateContext() {\n    const isTemplateRef = this.tdsStringTemplateOutlet instanceof TemplateRef;\n    const newCtx = isTemplateRef ? this.tdsStringTemplateOutletContext : this.context;\n    const oldCtx = this.embeddedViewRef.context;\n    if (newCtx) {\n      for (const propName of Object.keys(newCtx)) {\n        oldCtx[propName] = newCtx[propName];\n      }\n    }\n  }\n  constructor(viewContainer, templateRef) {\n    this.viewContainer = viewContainer;\n    this.templateRef = templateRef;\n    this.embeddedViewRef = null;\n    this.context = new TDSStringTemplateOutletContext();\n    this.tdsStringTemplateOutletContext = null;\n    this.tdsStringTemplateOutlet = null;\n  }\n  ngOnChanges(changes) {\n    const {\n      tdsStringTemplateOutletContext,\n      tdsStringTemplateOutlet\n    } = changes;\n    const shouldRecreateView = () => {\n      let shouldOutletRecreate = false;\n      if (tdsStringTemplateOutlet) {\n        if (tdsStringTemplateOutlet.firstChange) {\n          shouldOutletRecreate = true;\n        } else {\n          const isPreviousOutletTemplate = tdsStringTemplateOutlet.previousValue instanceof TemplateRef;\n          const isCurrentOutletTemplate = tdsStringTemplateOutlet.currentValue instanceof TemplateRef;\n          shouldOutletRecreate = isPreviousOutletTemplate || isCurrentOutletTemplate;\n        }\n      }\n      const hasContextShapeChanged = ctxChange => {\n        const prevCtxKeys = Object.keys(ctxChange.previousValue || {});\n        const currCtxKeys = Object.keys(ctxChange.currentValue || {});\n        if (prevCtxKeys.length === currCtxKeys.length) {\n          for (const propName of currCtxKeys) {\n            if (prevCtxKeys.indexOf(propName) === -1) {\n              return true;\n            }\n          }\n          return false;\n        } else {\n          return true;\n        }\n      };\n      const shouldContextRecreate = tdsStringTemplateOutletContext && hasContextShapeChanged(tdsStringTemplateOutletContext);\n      return shouldContextRecreate || shouldOutletRecreate;\n    };\n    if (tdsStringTemplateOutlet) {\n      this.context.$implicit = tdsStringTemplateOutlet.currentValue;\n    }\n    const recreateView = shouldRecreateView();\n    if (recreateView) {\n      /** recreate view when context shape or outlet change **/\n      this.recreateView();\n    } else {\n      /** update context **/\n      this.updateContext();\n    }\n  }\n  static {\n    this.ɵfac = function TDSStringTemplateOutletDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSStringTemplateOutletDirective)(i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TDSStringTemplateOutletDirective,\n      selectors: [[\"\", \"tdsStringTemplateOutlet\", \"\"]],\n      inputs: {\n        tdsStringTemplateOutletContext: \"tdsStringTemplateOutletContext\",\n        tdsStringTemplateOutlet: \"tdsStringTemplateOutlet\"\n      },\n      exportAs: [\"tdsStringTemplateOutlet\"],\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSStringTemplateOutletDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[tdsStringTemplateOutlet]',\n      exportAs: 'tdsStringTemplateOutlet',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ViewContainerRef\n  }, {\n    type: i0.TemplateRef\n  }], {\n    tdsStringTemplateOutletContext: [{\n      type: Input\n    }],\n    tdsStringTemplateOutlet: [{\n      type: Input\n    }]\n  });\n})();\nclass TDSStringTemplateOutletContext {}\nclass TDSOutletModule {\n  static {\n    this.ɵfac = function TDSOutletModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSOutletModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: TDSOutletModule,\n      imports: [TDSStringTemplateOutletDirective],\n      exports: [TDSStringTemplateOutletDirective]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSOutletModule, [{\n    type: NgModule,\n    args: [{\n      imports: [TDSStringTemplateOutletDirective],\n      exports: [TDSStringTemplateOutletDirective]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TDSOutletModule, TDSStringTemplateOutletDirective };\n", "const TDS_DEFAULT_NUMBER_FORMAT = {\n  decimalLimit: Infinity,\n  decimalSeparator: `,`,\n  thousandSeparator: '.',\n  zeroPadding: true\n};\nconst TDS_DECIMAL_SYMBOLS = [`,`, `.`];\nconst MASK_CARET_TRAP = `[]`;\nconst TDS_DIGIT_REGEXP = /\\d/;\nconst TDS_NON_DIGIT_REGEXP = /\\D/;\nconst TDS_NON_DIGITS_REGEXP = /\\D+/g;\nconst TDS_LEADING_ZEROES_REGEXP = /^0+/;\nconst TDS_MASK_SYMBOLS_REGEXP = /[ \\-_()]/g;\nconst TDS_LAST_PUNCTUATION_MARK_REGEXP = /[.,\\\\/#!$%\\\\^&\\\\*;:{}=\\\\-_`~()]$/;\nconst TDS_LATIN_REGEXP = /[A-z]/;\nconst TDS_LATIN_AND_NUMBERS_REGEXP = /[A-z|0-9]/;\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MASK_CARET_TRAP, TDS_DECIMAL_SYMBOLS, TDS_DEFAULT_NUMBER_FORMAT, TDS_DIGIT_REGEXP, TDS_LAST_PUNCTUATION_MARK_REGEXP, TDS_LATIN_AND_NUMBERS_REGEXP, TDS_LATIN_REGEXP, TDS_LEADING_ZEROES_REGEXP, TDS_MASK_SYMBOLS_REGEXP, TDS_NON_DIGITS_REGEXP, TDS_NON_DIGIT_REGEXP };\n", "import { InjectionToken, inject } from '@angular/core';\nimport { Observable, share, fromEvent, startWith, map, distinctUntilChanged, shareReplay } from 'rxjs';\nimport { DOCUMENT } from '@angular/common';\nconst WA_WINDOW = new InjectionToken('[WA_WINDOW]', {\n  factory: () => {\n    const {\n      defaultView\n    } = inject(DOCUMENT);\n    if (!defaultView) {\n      throw new Error('Window is not available');\n    }\n    return defaultView;\n  }\n});\n/**\n * @deprecated: drop in v5.0, use {@link WA_WINDOW}\n */\nconst WINDOW = WA_WINDOW;\nconst WA_ANIMATION_FRAME = new InjectionToken('[WA_ANIMATION_FRAME]', {\n  factory: () => {\n    const {\n      requestAnimationFrame,\n      cancelAnimationFrame\n    } = inject(WINDOW);\n    const animationFrame$ = new Observable(subscriber => {\n      let id = NaN;\n      const callback = timestamp => {\n        subscriber.next(timestamp);\n        id = requestAnimationFrame(callback);\n      };\n      id = requestAnimationFrame(callback);\n      return () => {\n        cancelAnimationFrame(id);\n      };\n    });\n    return animationFrame$.pipe(share());\n  }\n});\n/**\n * @deprecated: drop in v5.0, use {@link WA_ANIMATION_FRAME}\n */\nconst ANIMATION_FRAME = WA_ANIMATION_FRAME;\nconst WA_CACHES = new InjectionToken('[WA_CACHES]', {\n  factory: () => inject(WINDOW).caches\n});\n/**\n * @deprecated: drop in v5.0, use {@link WA_CACHES}\n */\nconst CACHES = WA_CACHES;\nconst WA_CRYPTO = new InjectionToken('[WA_CRYPTO]', {\n  factory: () => inject(WINDOW).crypto\n});\n/**\n * @deprecated: drop in v5.0, use {@link WA_CRYPTO}\n */\nconst CRYPTO = WA_CRYPTO;\nconst WA_CSS = new InjectionToken('[WA_CSS]', {\n  factory: () => inject(WINDOW).CSS ?? {\n    escape: v => v,\n    // eslint-disable-next-line no-restricted-syntax\n    supports: () => false\n  }\n});\n/**\n * @deprecated: drop in v5.0, use {@link WA_CSS}\n */\nconst TOKEN_CSS = WA_CSS;\nconst WA_HISTORY = new InjectionToken('[WA_HISTORY]', {\n  factory: () => inject(WINDOW).history\n});\n/**\n * @deprecated: drop in v5.0, use {@link WA_HISTORY}\n */\nconst HISTORY = WA_HISTORY;\nconst WA_LOCAL_STORAGE = new InjectionToken('[WA_LOCAL_STORAGE]', {\n  factory: () => inject(WINDOW).localStorage\n});\n/**\n * @deprecated: drop in v5.0, use {@link WA_LOCAL_STORAGE}\n */\nconst LOCAL_STORAGE = WA_LOCAL_STORAGE;\nconst WA_LOCATION = new InjectionToken('[WA_LOCATION]', {\n  factory: () => inject(WINDOW).location\n});\n/**\n * @deprecated: drop in v5.0, use {@link WA_LOCATION}\n */\nconst LOCATION = WA_LOCATION;\nconst WA_NAVIGATOR = new InjectionToken('[WA_NAVIGATOR]', {\n  factory: () => inject(WINDOW).navigator\n});\n/**\n * @deprecated: drop in v5.0, use {@link WA_NAVIGATOR}\n */\nconst NAVIGATOR = WA_NAVIGATOR;\nconst WA_MEDIA_DEVICES = new InjectionToken('[WA_MEDIA_DEVICES]', {\n  factory: () => inject(NAVIGATOR).mediaDevices\n});\n/**\n * @deprecated: drop in v5.0, use {@link WA_MEDIA_DEVICES}\n */\nconst MEDIA_DEVICES = WA_MEDIA_DEVICES;\nconst WA_NETWORK_INFORMATION = new InjectionToken('[WA_NETWORK_INFORMATION]', {\n  // @ts-ignore\n  factory: () => inject(WA_NAVIGATOR).connection || null\n});\n/**\n * @deprecated: drop in v5.0, use {@link WA_NETWORK_INFORMATION}\n */\nconst NETWORK_INFORMATION = WA_NETWORK_INFORMATION;\nconst WA_PAGE_VISIBILITY = new InjectionToken('[WA_PAGE_VISIBILITY]', {\n  factory: () => {\n    const documentRef = inject(DOCUMENT);\n    return fromEvent(documentRef, 'visibilitychange').pipe(startWith(0), map(() => documentRef.visibilityState !== 'hidden'), distinctUntilChanged(), shareReplay({\n      refCount: false,\n      bufferSize: 1\n    }));\n  }\n});\n/**\n * @deprecated: drop in v5.0, use {@link WA_PAGE_VISIBILITY}\n */\nconst PAGE_VISIBILITY = WA_PAGE_VISIBILITY;\nconst WA_PERFORMANCE = new InjectionToken('[WA_PERFORMANCE]', {\n  factory: () => inject(WINDOW).performance\n});\n/**\n * @deprecated: drop in v5.0, use {@link WA_PERFORMANCE}\n */\nconst PERFORMANCE = WA_PERFORMANCE;\nconst WA_SCREEN = new InjectionToken('[WA_SCREEN]', {\n  factory: () => inject(WINDOW).screen\n});\n/**\n * @deprecated: drop in v5.0, use {@link WA_SCREEN}\n */\nconst SCREEN = WA_SCREEN;\nconst WA_SESSION_STORAGE = new InjectionToken('[WA_SESSION_STORAGE]', {\n  factory: () => inject(WINDOW).sessionStorage\n});\n/**\n * @deprecated: drop in v5.0, use {@link WA_SESSION_STORAGE}\n */\nconst SESSION_STORAGE = WA_SESSION_STORAGE;\nconst WA_SPEECH_RECOGNITION = new InjectionToken('[WA_SPEECH_RECOGNITION]: [SPEECH_RECOGNITION]', {\n  factory: () => {\n    const windowRef = inject(WINDOW);\n    return windowRef.speechRecognition || windowRef.webkitSpeechRecognition || null;\n  }\n});\n/**\n * @deprecated: drop in v5.0, use {@link WA_SPEECH_RECOGNITION}\n */\nconst SPEECH_RECOGNITION = WA_SPEECH_RECOGNITION;\nconst WA_SPEECH_SYNTHESIS = new InjectionToken('[WA_SPEECH_SYNTHESIS]', {\n  factory: () => inject(WINDOW).speechSynthesis\n});\n/**\n * @deprecated: drop in v5.0, use {@link WA_SPEECH_SYNTHESIS}\n */\nconst SPEECH_SYNTHESIS = WA_SPEECH_SYNTHESIS;\nconst WA_USER_AGENT = new InjectionToken('[WA_USER_AGENT]', {\n  factory: () => inject(NAVIGATOR).userAgent\n});\n/**\n * @deprecated: drop in v5.0, use {@link WA_USER_AGENT}\n */\nconst USER_AGENT = WA_USER_AGENT;\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ANIMATION_FRAME, CACHES, CRYPTO, TOKEN_CSS as CSS, HISTORY, LOCAL_STORAGE, LOCATION, MEDIA_DEVICES, NAVIGATOR, NETWORK_INFORMATION, PAGE_VISIBILITY, PERFORMANCE, SCREEN, SESSION_STORAGE, SPEECH_RECOGNITION, SPEECH_SYNTHESIS, TOKEN_CSS, USER_AGENT, WA_ANIMATION_FRAME, WA_CACHES, WA_CRYPTO, WA_CSS, WA_HISTORY, WA_LOCAL_STORAGE, WA_LOCATION, WA_MEDIA_DEVICES, WA_NAVIGATOR, WA_NETWORK_INFORMATION, WA_PAGE_VISIBILITY, WA_PERFORMANCE, WA_SCREEN, WA_SESSION_STORAGE, WA_SPEECH_RECOGNITION, WA_SPEECH_SYNTHESIS, WA_USER_AGENT, WA_WINDOW, WINDOW };\n", "/**\n * @description:\n * All Chrome / Chromium-based browsers will return MacIntel on macOS,\n * no matter what the hardware architecture is. See the source code here:\n * https://source.chromium.org/chromium/chromium/src/+/master:third_party/blink/renderer/core/frame/navigator_id.cc;l=64;drc=703d3c472cf27470dad21a3f2c8972aca3732cd6\n * But maybe in future years, it will be changed to MacM1\n *\n * Documentation:\n * https://developer.mozilla.org/en-US/docs/Web/API/Navigator/platform\n */\nfunction tdsIsApplePlatform(navigator) {\n  return navigator.platform.indexOf(`Mac`) === 0 || navigator.platform === `iPhone`;\n}\nconst IOS_REG_EXP = /ipad|iphone|ipod/;\nfunction tdsIsIos(navigator) {\n  return IOS_REG_EXP.test(navigator.userAgent.toLowerCase()) || tdsIsApplePlatform(navigator) && navigator.maxTouchPoints > 1;\n}\nconst SAFARI_REG_EXP = /^((?!chrome|android).)*safari/i;\nfunction tdsIsApple(navigator) {\n  return tdsIsIos(navigator) || SAFARI_REG_EXP.test(navigator.userAgent.toLowerCase());\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { tdsIsApple, tdsIsApplePlatform, tdsIsIos };\n", "import { InjectionToken, isDevMode, inject } from '@angular/core';\nimport { TDS_DEFAULT_NUMBER_FORMAT } from 'tds-ui/core/constants';\nimport { NAVIGATOR, USER_AGENT, WINDOW } from '@ng-web-apis/common';\nimport { tdsIsIos, tdsIsApple } from 'tds-ui/cdk/utils/os';\nconst TDS_ASSERT_ENABLED = new InjectionToken(`[TDS_ASSERT_ENABLED]: Flag to enable assertions across TDS UI`, {\n  factory: () => isDevMode()\n});\nconst TDS_NUMBER_FORMAT = new InjectionToken(`[TDS_NUMBER_FORMAT]: Formatting configuration for displayed numbers`, {\n  factory: () => TDS_DEFAULT_NUMBER_FORMAT\n});\nconst TDS_IS_IOS = new InjectionToken(`[TDS_IS_IOS]: iOS browser detection`, {\n  factory: () => tdsIsIos(inject(NAVIGATOR))\n});\n\n// https://stackoverflow.com/a/11381730/2706426 http://detectmobilebrowsers.com/\nconst firstRegex = /(android|bb\\d+|meego).+mobile|avantgo|bada\\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\\.(browser|link)|vodafone|wap|windows ce|xda|xiino/;\nconst secondRegex = /1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw-(n|u)|c55\\/|capi|ccwa|cdm-|cell|chtm|cldc|cmd-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc-s|devi|dica|dmob|do(c|p)o|ds(12|-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(-|_)|g1 u|g560|gene|gf-5|g-mo|go(\\.w|od)|gr(ad|un)|haie|hcit|hd-(m|p|t)|hei-|hi(pt|ta)|hp( i|ip)|hs-c|ht(c(-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i-(20|go|ma)|i230|iac( |-|\\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\\/)|klon|kpt |kwc-|kyo(c|k)|le(no|xi)|lg( g|\\/(k|l|u)|50|54|-[a-w])|libw|lynx|m1-w|m3ga|m50\\/|ma(te|ui|xo)|mc(01|21|ca)|m-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|-([1-8]|c))|phil|pire|pl(ay|uc)|pn-2|po(ck|rt|se)|prox|psio|pt-g|qa-a|qc(07|12|21|32|60|-[2-7]|i-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h-|oo|p-)|sdk\\/|se(c(-|0|1)|47|mc|nd|ri)|sgh-|shar|sie(-|m)|sk-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h-|v-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl-|tdg-|tel(i|m)|tim-|t-mo|to(pl|sh)|ts(70|m-|m3|m5)|tx-9|up(\\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas-|your|zeto|zte-/;\nconst TDS_IS_MOBILE = new InjectionToken(`[TDS_IS_MOBILE]: Mobile browser detection`, {\n  factory: () => firstRegex.test(inject(USER_AGENT).toLowerCase()) || secondRegex.test(inject(USER_AGENT).slice(0, 4).toLowerCase())\n});\nconst TDS_IS_ANDROID = new InjectionToken(`[TDS_IS_ANDROID]: Mobile browser that is not iOS (technically includes Windows Phone, Blackberry etc.)`, {\n  factory: () => inject(TDS_IS_MOBILE) && !inject(TDS_IS_IOS)\n});\nconst TDS_IS_APPLE = new InjectionToken(`[TDS_IS_APPLE]: Apple(safari/webkit) detection`, {\n  factory: () => tdsIsApple(inject(NAVIGATOR))\n});\nconst TDS_IS_CHROMIUM = new InjectionToken(`[TDS_IS_CHROMIUM]: Chromium browser engine detection`, {\n  factory: () => !!inject(WINDOW).chrome\n});\n\n/**\n * {@link https://docs.cypress.io/faq/questions/using-cypress-faq#Is-there-any-way-to-detect-if-my-app-is-running-under-Cypress Cypress docs}\n */\nconst TDS_IS_CYPRESS = new InjectionToken(`[TDS_IS_CYPRESS]: Detect if app is running under Cypress`, {\n  factory: () => !!inject(WINDOW).Cypress\n});\nconst TDS_IS_FIREFOX = new InjectionToken(`[TDS_IS_FIREFOX]: Firefox browser engine detection`, {\n  factory: () => typeof inject(WINDOW)?.mozCancelFullScreen !== `undefined`\n});\nconst TDS_IS_WEBKIT = new InjectionToken(`[TDS_IS_WEBKIT]: Webkit browser engine detection`, {\n  factory: () => !!inject(WINDOW)?.webkitConvertPointFromNodeToPage\n});\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TDS_ASSERT_ENABLED, TDS_IS_ANDROID, TDS_IS_APPLE, TDS_IS_CHROMIUM, TDS_IS_CYPRESS, TDS_IS_FIREFOX, TDS_IS_IOS, TDS_IS_MOBILE, TDS_IS_WEBKIT, TDS_NUMBER_FORMAT };\n", "import { QueryList } from '@angular/core';\n\n/**\n * {@link https://unicode-table.com/en/00A0/ Non-breaking space}.\n */\nconst CHAR_NO_BREAK_SPACE = `\\u00A0`;\n/**\n * {@link https://unicode-table.com/en/2013/ EN dash}\n * is used to indicate a range of numbers or a span of time.\n * @example 2006–2022\n * ___\n * Don't confuse with {@link CHAR_EM_DASH} or {@link CHAR_HYPHEN}!\n */\nconst CHAR_EN_DASH = `\\u2013`;\n/**\n * {@link https://unicode-table.com/en/2014/ EM dash}\n * is used to mark a break in a sentence.\n * @example Taiga UI — powerful set of open source components for Angular\n * ___\n * Don't confuse with {@link CHAR_EN_DASH} or {@link CHAR_HYPHEN}!\n */\nconst CHAR_EM_DASH = `\\u2014`;\n/**\n * {@link https://unicode-table.com/en/00AB/ Left-Pointing Double Angle Quotation Mark}\n */\nconst CHAR_LAQUO = `\\u00AB`;\n/**\n * {@link https://unicode-table.com/en/00BB/ Right-Pointing Double Angle Quotation Mark}\n */\nconst CHAR_RAQUO = `\\u00BB`;\n/**\n * {@link https://unicode-table.com/en/002D/ Hyphen (minus sign)}\n * is used to combine words.\n * @example well-behaved\n * ___\n * Don't confuse with {@link CHAR_EN_DASH} or {@link CHAR_EM_DASH}!\n */\nconst CHAR_HYPHEN = `\\u002D`;\n/**\n * {@link https://unicode-table.com/en/2212/ Minus}\n * is used as math operator symbol or before negative digits.\n * ---\n * Can be used as `&minus;`. Don't confuse with {@link CHAR_HYPHEN}\n */\nconst CHAR_MINUS = `\\u2212`;\n/**\n * {@link https://unicode-table.com/en/002B/ Plus}\n */\nconst CHAR_PLUS = `\\u002B`;\n/**\n * {@link https://unicode-table.com/en/2022/ Bullet}.\n */\nconst CHAR_BULLET = `\\u2022`;\n/**\n * {@link https://unicode-table.com/en/2026/ Suspension points}.\n */\nconst CHAR_ELLIPSIS = `\\u2026`;\n/**\n * {@link https://unicode-table.com/en/00A4/ Suspension points}.\n */\nconst CHAR_CURRENCY_SIGN = `\\u00A4`;\n/**\n * {@link https://unicode-table.com/en/200b/ Suspension points}.\n */\nconst CHAR_ZERO_WIDTH_SPACE = `\\u200B`;\n\n/**\n * For type safety when using @ContentChildren and @ViewChildren\n *\n * NOTE: Be careful subscribing to 'changes'\n */\nconst EMPTY_QUERY = new QueryList();\nconst EMPTY_ARRAY = [];\nconst EMPTY_FUNCTION = () => {};\nconst rect = {\n  bottom: 0,\n  height: 0,\n  left: 0,\n  right: 0,\n  top: 0,\n  width: 0,\n  x: 0,\n  y: 0\n};\nconst EMPTY_CLIENT_RECT = {\n  ...rect,\n  toJSON() {\n    return rect;\n  }\n};\n\n/**\n * Handler that always returns `false`.\n */\nconst ALWAYS_FALSE_HANDLER = () => false;\n\n/**\n * Handler that always returns `true`.\n */\nconst ALWAYS_TRUE_HANDLER = () => true;\n\n// Filtering SVGElements for TreeWalker\n// Filter must be a function in IE, other modern browsers are compliant to this format\nconst svgNodeFilter = node => `ownerSVGElement` in node ? NodeFilter.FILTER_REJECT : NodeFilter.FILTER_ACCEPT;\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ALWAYS_FALSE_HANDLER, ALWAYS_TRUE_HANDLER, CHAR_BULLET, CHAR_CURRENCY_SIGN, CHAR_ELLIPSIS, CHAR_EM_DASH, CHAR_EN_DASH, CHAR_HYPHEN, CHAR_LAQUO, CHAR_MINUS, CHAR_NO_BREAK_SPACE, CHAR_PLUS, CHAR_RAQUO, CHAR_ZERO_WIDTH_SPACE, EMPTY_ARRAY, EMPTY_CLIENT_RECT, EMPTY_FUNCTION, EMPTY_QUERY, svgNodeFilter };\n", "import { CHAR_HYPHEN } from 'tds-ui/cdk/contants';\nimport { TDS_DEFAULT_NUMBER_FORMAT } from 'tds-ui/core/constants';\nimport { isNumberFinite } from 'tds-ui/core/util';\n\n/**\n * Convert number to string with replacing exponent part on decimals\n *\n * @param value the number\n * @return string representation of a number\n */\nfunction tdsNumberToStringWithoutExp(value) {\n  const valueAsString = String(value);\n  const [numberPart, expPart] = valueAsString.split(`e-`);\n  let valueWithoutExp = valueAsString;\n  if (expPart) {\n    const [, fractionalPart] = numberPart.split(`.`);\n    const decimalDigits = Number(expPart) + (fractionalPart?.length || 0);\n    valueWithoutExp = value.toFixed(decimalDigits);\n  }\n  return valueWithoutExp;\n}\n\n/**\n * Return fractional part of number\n *\n * @param value the number\n * @param precision number of digits of decimal part, null to keep untouched\n * @return the fractional part of number\n */\nfunction tdsGetFractionPartPadded(value, precision) {\n  const [, fractionPartPadded = ``] = tdsNumberToStringWithoutExp(value).split(`.`);\n  return isNumberFinite(precision) ? fractionPartPadded.slice(0, Math.max(0, precision)) : fractionPartPadded;\n}\n\n/**\n * Formats number adding a thousand separators and correct decimal separator\n * padding decimal part with zeroes to given length\n *\n * @param value the input number\n * @param settings See {@link tdsNumberFormatSettings}\n * @return the formatted string\n */\nfunction tdsFormatNumber(value, settings = {}) {\n  const {\n    decimalLimit,\n    decimalSeparator,\n    thousandSeparator,\n    zeroPadding\n  } = {\n    ...TDS_DEFAULT_NUMBER_FORMAT,\n    ...settings\n  };\n  const integerPartString = String(Math.floor(Math.abs(value)));\n  let fractionPartPadded = tdsGetFractionPartPadded(value, decimalLimit);\n  if (Number.isFinite(decimalLimit)) {\n    if (zeroPadding) {\n      const zeroPaddingSize = Math.max(decimalLimit - fractionPartPadded.length, 0);\n      const zeroPartString = `0`.repeat(zeroPaddingSize);\n      fractionPartPadded = `${fractionPartPadded}${zeroPartString}`;\n    } else {\n      fractionPartPadded = fractionPartPadded.replace(/0*$/, ``);\n    }\n  }\n  const remainder = integerPartString.length % 3;\n  const sign = value < 0 ? CHAR_HYPHEN : ``;\n  let result = sign + integerPartString.charAt(0);\n  for (let i = 1; i < integerPartString.length; i++) {\n    if (i % 3 === remainder && integerPartString.length > 3) {\n      result += thousandSeparator;\n    }\n    result += integerPartString.charAt(i);\n  }\n  return fractionPartPadded ? result + decimalSeparator + fractionPartPadded : result;\n}\nfunction tdsOtherDecimalSymbol(symbol) {\n  return symbol === `.` ? `,` : `.`;\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { tdsFormatNumber, tdsGetFractionPartPadded, tdsNumberToStringWithoutExp, tdsOtherDecimalSymbol };\n", "import * as i0 from '@angular/core';\nimport { Pi<PERSON>, Inject, NgModule } from '@angular/core';\nimport { sum, isNumberFinite, toDecimal, isNil, padStart } from 'tds-ui/core/util';\nimport * as i1 from '@angular/platform-browser';\nimport { timeUnits } from 'tds-ui/core/time';\nimport { TDS_NUMBER_FORMAT } from 'tds-ui/core/token';\nimport { tdsFormatNumber } from 'tds-ui/core/format';\nclass TDSAggregatePipe {\n  transform(value, method) {\n    if (!Array.isArray(value)) {\n      return value;\n    }\n    if (value.length === 0) {\n      return undefined;\n    }\n    switch (method) {\n      case 'sum':\n        return sum(value);\n      case 'avg':\n        return sum(value) / value.length;\n      case 'max':\n        return Math.max(...value);\n      case 'min':\n        return Math.min(...value);\n      default:\n        throw Error(`Invalid Pipe Arguments: Aggregate pipe doesn't support this type`);\n    }\n  }\n  static {\n    this.ɵfac = function TDSAggregatePipe_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSAggregatePipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"tdsAggregate\",\n      type: TDSAggregatePipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSAggregatePipe, [{\n    type: Pipe,\n    args: [{\n      name: 'tdsAggregate',\n      standalone: true\n    }]\n  }], null, null);\n})();\nclass TDSBytesPipe {\n  static {\n    this.formats = {\n      B: {\n        max: 1024\n      },\n      kB: {\n        max: Math.pow(1024, 2),\n        prev: 'B'\n      },\n      KB: {\n        max: Math.pow(1024, 2),\n        prev: 'B'\n      },\n      MB: {\n        max: Math.pow(1024, 3),\n        prev: 'kB'\n      },\n      GB: {\n        max: Math.pow(1024, 4),\n        prev: 'MB'\n      },\n      TB: {\n        max: Number.MAX_SAFE_INTEGER,\n        prev: 'GB'\n      }\n    };\n  }\n  transform(input, decimal = 0, from = 'B', to) {\n    if (!(isNumberFinite(input) && isNumberFinite(decimal) && decimal % 1 === 0 && decimal >= 0)) {\n      return input;\n    }\n    let bytes = input;\n    let unit = from;\n    while (unit !== 'B') {\n      bytes *= 1024;\n      unit = TDSBytesPipe.formats[unit].prev;\n    }\n    if (to) {\n      const format = TDSBytesPipe.formats[to];\n      const result = toDecimal(TDSBytesPipe.calculateResult(format, bytes), decimal);\n      return TDSBytesPipe.formatResult(result, to);\n    }\n    for (const key in TDSBytesPipe.formats) {\n      if (TDSBytesPipe.formats.hasOwnProperty(key)) {\n        const format = TDSBytesPipe.formats[key];\n        if (bytes < format.max) {\n          const result = toDecimal(TDSBytesPipe.calculateResult(format, bytes), decimal);\n          return TDSBytesPipe.formatResult(result, key);\n        }\n      }\n    }\n  }\n  static formatResult(result, unit) {\n    return `${result} ${unit}`;\n  }\n  static calculateResult(format, bytes) {\n    const prev = format.prev ? TDSBytesPipe.formats[format.prev] : undefined;\n    return prev ? bytes / prev.max : bytes;\n  }\n  static {\n    this.ɵfac = function TDSBytesPipe_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSBytesPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"tdsBytes\",\n      type: TDSBytesPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSBytesPipe, [{\n    type: Pipe,\n    args: [{\n      name: 'tdsBytes',\n      standalone: true\n    }]\n  }], null, null);\n})();\nclass TDSToCssUnitPipe {\n  transform(value, defaultUnit = 'px') {\n    const absoluteLengthUnit = ['cm', 'mm', 'Q', 'in', 'pc', 'pt', 'px'];\n    const relativeLengthUnit = ['em', 'ex', 'ch', 'rem', '1h', 'vw', 'vh', 'vmin', 'vmax'];\n    const percentagesUnit = ['%'];\n    const listOfUnit = [...absoluteLengthUnit, ...relativeLengthUnit, ...percentagesUnit];\n    let unit = 'px';\n    if (listOfUnit.some(u => u === defaultUnit)) {\n      unit = defaultUnit;\n    }\n    return typeof value === 'number' ? `${value}${unit}` : `${value}`;\n  }\n  static {\n    this.ɵfac = function TDSToCssUnitPipe_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSToCssUnitPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"tdsToCssUnit\",\n      type: TDSToCssUnitPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSToCssUnitPipe, [{\n    type: Pipe,\n    args: [{\n      name: 'tdsToCssUnit',\n      standalone: true\n    }]\n  }], null, null);\n})();\nclass TDSEllipsisPipe {\n  transform(value, length, suffix = '') {\n    if (typeof value !== 'string') {\n      return value;\n    }\n    const len = typeof length === 'undefined' ? value.length : length;\n    if (value.length <= len) {\n      return value;\n    }\n    return value.substring(0, len) + suffix;\n  }\n  static {\n    this.ɵfac = function TDSEllipsisPipe_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSEllipsisPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"tdsEllipsis\",\n      type: TDSEllipsisPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSEllipsisPipe, [{\n    type: Pipe,\n    args: [{\n      name: 'tdsEllipsis',\n      standalone: true\n    }]\n  }], null, null);\n})();\nclass TDSSafeNullPipe {\n  transform(value, replace = '') {\n    if (isNil(value)) {\n      return replace;\n    }\n    return value;\n  }\n  static {\n    this.ɵfac = function TDSSafeNullPipe_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSSafeNullPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"tdsSafeNull\",\n      type: TDSSafeNullPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSSafeNullPipe, [{\n    type: Pipe,\n    args: [{\n      name: 'tdsSafeNull',\n      standalone: true\n    }]\n  }], null, null);\n})();\nclass TDSSanitizerPipe {\n  constructor(sanitizer) {\n    this.sanitizer = sanitizer;\n  }\n  transform(value, type = 'html') {\n    switch (type) {\n      case 'html':\n        return this.sanitizer.bypassSecurityTrustHtml(value);\n      case 'style':\n        return this.sanitizer.bypassSecurityTrustStyle(value);\n      case 'url':\n        return this.sanitizer.bypassSecurityTrustUrl(value);\n      case 'resourceUrl':\n        return this.sanitizer.bypassSecurityTrustResourceUrl(value);\n      default:\n        throw new Error(`Invalid safe type specified`);\n    }\n  }\n  static {\n    this.ɵfac = function TDSSanitizerPipe_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSSanitizerPipe)(i0.ɵɵdirectiveInject(i1.DomSanitizer, 16));\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"tdsSanitizer\",\n      type: TDSSanitizerPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSSanitizerPipe, [{\n    type: Pipe,\n    args: [{\n      name: 'tdsSanitizer',\n      standalone: true\n    }]\n  }], () => [{\n    type: i1.DomSanitizer\n  }], null);\n})();\nclass TDSTrimPipe {\n  // TODO(chensimeng) trimEnd, trimStart\n  transform(text) {\n    return text.trim();\n  }\n  static {\n    this.ɵfac = function TDSTrimPipe_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSTrimPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"tdsTrim\",\n      type: TDSTrimPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSTrimPipe, [{\n    type: Pipe,\n    args: [{\n      name: 'tdsTrim',\n      standalone: true\n    }]\n  }], null, null);\n})();\nclass TDSTimeRangePipe {\n  transform(value, format = 'HH:mm:ss') {\n    let duration = Number(value || 0);\n    return timeUnits.reduce((current, [name, unit]) => {\n      if (current.indexOf(name) !== -1) {\n        const v = Math.floor(duration / unit);\n        duration -= v * unit;\n        return current.replace(new RegExp(`${name}+`, 'g'), match => padStart(v.toString(), match.length, '0'));\n      }\n      return current;\n    }, format);\n  }\n  static {\n    this.ɵfac = function TDSTimeRangePipe_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSTimeRangePipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"tdsTimeRange\",\n      type: TDSTimeRangePipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSTimeRangePipe, [{\n    type: Pipe,\n    args: [{\n      name: 'tdsTimeRange',\n      pure: true,\n      standalone: true\n    }]\n  }], null, null);\n})();\nclass TDSFormatNumberPipe {\n  constructor(numberFormat) {\n    this.numberFormat = numberFormat;\n  }\n  /**\n   * Formats number adding thousand separators and correct decimal separator\n   * padding decimal part with zeroes to given length\n   * @param value number\n   * @param settings\n   */\n  transform(value, settings = {}) {\n    return tdsFormatNumber(value, {\n      ...this.numberFormat,\n      ...settings\n    });\n  }\n  static {\n    this.ɵfac = function TDSFormatNumberPipe_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSFormatNumberPipe)(i0.ɵɵdirectiveInject(TDS_NUMBER_FORMAT, 16));\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"tdsFormatNumber\",\n      type: TDSFormatNumberPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSFormatNumberPipe, [{\n    type: Pipe,\n    args: [{\n      name: `tdsFormatNumber`,\n      standalone: true\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [TDS_NUMBER_FORMAT]\n    }]\n  }], null);\n})();\nconst pipes = [TDSToCssUnitPipe, TDSSafeNullPipe, TDSSanitizerPipe, TDSTrimPipe, TDSBytesPipe, TDSAggregatePipe, TDSEllipsisPipe, TDSTimeRangePipe, TDSFormatNumberPipe];\nclass TDSPipesModule {\n  static {\n    this.ɵfac = function TDSPipesModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TDSPipesModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: TDSPipesModule,\n      imports: [TDSToCssUnitPipe, TDSSafeNullPipe, TDSSanitizerPipe, TDSTrimPipe, TDSBytesPipe, TDSAggregatePipe, TDSEllipsisPipe, TDSTimeRangePipe, TDSFormatNumberPipe],\n      exports: [TDSToCssUnitPipe, TDSSafeNullPipe, TDSSanitizerPipe, TDSTrimPipe, TDSBytesPipe, TDSAggregatePipe, TDSEllipsisPipe, TDSTimeRangePipe, TDSFormatNumberPipe]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TDSPipesModule, [{\n    type: NgModule,\n    args: [{\n      imports: [pipes],\n      exports: [pipes]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TDSAggregatePipe, TDSBytesPipe, TDSEllipsisPipe, TDSFormatNumberPipe, TDSPipesModule, TDSSafeNullPipe, TDSSanitizerPipe, TDSTimeRangePipe, TDSToCssUnitPipe, TDSTrimPipe };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,IAAM,oBAAN,MAAwB;AAAA,EACtB,OAAO;AACL,SAAK,OAAO;AAAA,EACd;AAAA,EACA,OAAO;AACL,SAAK,OAAO;AAAA,EACd;AAAA,EACA,OAAO;AACL,SAAK,OAAO;AAAA,EACd;AAAA;AACF;AACA,IAAM,kBAAN,MAAsB;AAAA,EACpB,OAAO;AACL,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,OAAO;AACL,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,OAAO;AACL,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,OAAO;AACL,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,OAAO;AACL,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,OAAO;AACL,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,OAAO;AACL,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,OAAO;AACL,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EACA,OAAO;AACL,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,OAAO;AACL,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,OAAO;AACL,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EACA,OAAO;AACL,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,OAAO;AACL,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,OAAO;AACL,SAAK,oBAAoB;AAAA,EAC3B;AACF;AACA,IAAM,iBAAiB,QAAQ,kBAAkB,CAAC,MAAM,YAAY,MAAM;AAAA,EACxE,QAAQ;AAAA,EACR,UAAU;AACZ,CAAC,CAAC,GAAG,MAAM,aAAa,MAAM;AAAA,EAC5B,QAAQ;AAAA,EACR,UAAU;AACZ,CAAC,CAAC,GAAG,MAAM,UAAU,MAAM;AAAA,EACzB,QAAQ;AAAA,EACR,gBAAgB;AAAA,EAChB,UAAU;AACZ,CAAC,CAAC,GAAG,WAAW,yBAAyB,QAAQ,UAAU,gBAAgB,WAAW,EAAE,CAAC,GAAG,WAAW,sBAAsB,QAAQ,SAAS,gBAAgB,WAAW,EAAE,CAAC,GAAG,WAAW,yBAAyB,QAAQ,SAAS,gBAAgB,WAAW,EAAE,CAAC,GAAG,WAAW,sBAAsB,QAAQ,SAAS,gBAAgB,WAAW,EAAE,CAAC,CAAC,CAAC;AACvV,IAAM,qBAAqB,QAAQ,sBAAsB,CAAC,MAAM,YAAY,MAAM;AAAA,EAChF,QAAQ;AAAA,EACR,UAAU;AACZ,CAAC,CAAC,GAAG,MAAM,aAAa,MAAM;AAAA,EAC5B,QAAQ;AAAA,EACR,UAAU;AACZ,CAAC,CAAC,GAAG,MAAM,UAAU,MAAM;AAAA,EACzB,QAAQ;AAAA,EACR,gBAAgB;AAAA,EAChB,UAAU;AACZ,CAAC,CAAC,GAAG,WAAW,yBAAyB,QAAQ,SAAS,gBAAgB,WAAW,EAAE,CAAC,GAAG,WAAW,sBAAsB,QAAQ,SAAS,gBAAgB,WAAW,EAAE,CAAC,GAAG,WAAW,yBAAyB,QAAQ,SAAS,gBAAgB,WAAW,EAAE,CAAC,GAAG,WAAW,sBAAsB,QAAQ,SAAS,gBAAgB,WAAW,EAAE,CAAC,CAAC,CAAC;AACtV,IAAM,qBAAqB,QAAQ,sBAAsB,CAAC,WAAW,UAAU,CAAC,MAAM,mDAAmD,CAAC,MAAM;AAAA,EAC9I,UAAU;AACZ,CAAC,GAAG,QAAQ,GAAG,CAAC,QAAQ,SAAS,gBAAgB,WAAW,IAAI,MAAM;AAAA,EACpE,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,kBAAkB;AACpB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG;AAAA,EACN,UAAU;AACZ,CAAC,GAAG,MAAM,mDAAmD,CAAC,MAAM;AAAA,EAClE,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,kBAAkB;AACpB,CAAC,GAAG,QAAQ,GAAG,CAAC,QAAQ,SAAS,gBAAgB,WAAW,IAAI,MAAM;AAAA,EACpE,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,kBAAkB;AACpB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG;AAAA,EACN,UAAU;AACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACL,IAAM,aAAa,QAAQ,cAAc,CAAC,WAAW,UAAU,CAAC,MAAM;AAAA,EACpE,SAAS;AACX,CAAC,GAAG,QAAQ,GAAG,kBAAkB,IAAI,IAAI,MAAM;AAAA,EAC7C,SAAS;AACX,CAAC,CAAC,CAAC,CAAC,GAAG,WAAW,UAAU,CAAC,MAAM;AAAA,EACjC,SAAS;AACX,CAAC,GAAG,QAAQ,GAAG,kBAAkB,IAAI,IAAI,MAAM;AAAA,EAC7C,SAAS;AACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACN,IAAM,aAAa,QAAQ,cAAc,CAAC,WAAW,UAAU,CAAC,MAAM;AAAA,EACpE,SAAS;AAAA,EACT,WAAW;AACb,CAAC,GAAG,QAAQ,GAAG,kBAAkB,IAAI,IAAI,gBAAgB,WAAW,IAAI,MAAM;AAAA,EAC5E,SAAS;AAAA,EACT,WAAW;AACb,CAAC,CAAC,CAAC,CAAC,GAAG,WAAW,UAAU,CAAC,MAAM;AAAA,EACjC,SAAS;AAAA,EACT,WAAW;AACb,CAAC,GAAG,QAAQ,GAAG,kBAAkB,IAAI,IAAI,gBAAgB,WAAW,IAAI,MAAM;AAAA,EAC5E,SAAS;AAAA,EACT,WAAW;AACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACN,IAAM,eAAe,QAAQ,gBAAgB,CAAC,WAAW,cAAc,CAAC,MAAM;AAAA,EAC5E,iBAAiB;AAAA,EACjB,WAAW;AAAA,EACX,SAAS;AACX,CAAC,GAAG,QAAQ,GAAG,kBAAkB,IAAI,IAAI,MAAM;AAAA,EAC7C,iBAAiB;AAAA,EACjB,WAAW;AAAA,EACX,SAAS;AACX,CAAC,CAAC,CAAC,CAAC,GAAG,WAAW,cAAc,CAAC,MAAM;AAAA,EACrC,iBAAiB;AAAA,EACjB,WAAW;AAAA,EACX,SAAS;AACX,CAAC,GAAG,QAAQ,GAAG,kBAAkB,IAAI,IAAI,MAAM;AAAA,EAC7C,iBAAiB;AAAA,EACjB,WAAW;AAAA,EACX,SAAS;AACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACN,IAAM,qBAAqB,QAAQ,sBAAsB,CAAC,MAAM,cAAc,MAAM;AAAA,EAClF,SAAS;AAAA,EACT,WAAW;AACb,CAAC,CAAC,GAAG,WAAW,mBAAmB,CAAC,MAAM;AAAA,EACxC,SAAS;AAAA,EACT,WAAW;AACb,CAAC,GAAG,QAAQ,cAAc,CAAC,CAAC,GAAG,MAAM,aAAa,MAAM;AAAA,EACtD,SAAS;AAAA,EACT,WAAW;AACb,CAAC,CAAC,GAAG,WAAW,kBAAkB,CAAC,MAAM;AAAA,EACvC,SAAS;AAAA,EACT,WAAW;AACb,CAAC,GAAG,QAAQ,cAAc,CAAC,CAAC,GAAG,MAAM,SAAS,MAAM;AAAA,EAClD,SAAS;AAAA,EACT,WAAW;AAAA,EACX,iBAAiB;AACnB,CAAC,CAAC,GAAG,WAAW,cAAc,CAAC,MAAM;AAAA,EACnC,SAAS;AAAA,EACT,WAAW;AAAA,EACX,iBAAiB;AACnB,CAAC,GAAG,QAAQ,cAAc,CAAC,CAAC,CAAC,CAAC;AAC9B,IAAM,0BAA0B,GAAG,kBAAkB,IAAI,IAAI,gBAAgB,cAAc;AAC3F,IAAM,2BAA2B,GAAG,kBAAkB,IAAI,IAAI,gBAAgB,aAAa;AAC3F,IAAM,cAAc,QAAQ,eAAe,CAAC,MAAM,QAAQ,MAAM;AAAA,EAC9D,SAAS;AAAA,EACT,WAAW;AACb,CAAC,CAAC,GAAG,MAAM,SAAS,MAAM;AAAA,EACxB,SAAS;AAAA,EACT,WAAW;AACb,CAAC,CAAC,GAAG,WAAW,aAAa,CAAC,QAAQ,uBAAuB,CAAC,CAAC,GAAG,WAAW,aAAa,CAAC,QAAQ,wBAAwB,CAAC,CAAC,CAAC,CAAC;AAC/H,IAAM,mBAAmB,QAAQ,oBAAoB,CAAC,WAAW,UAAU,CAAC,MAAM;AAAA,EAChF,SAAS;AAAA,EACT,WAAW;AAAA,EACX,iBAAiB;AACnB,CAAC,GAAG,QAAQ,GAAG,kBAAkB,IAAI,IAAI,gBAAgB,gBAAgB,IAAI,MAAM;AAAA,EACjF,SAAS;AAAA,EACT,WAAW;AAAA,EACX,iBAAiB;AACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACN,IAAM,gBAAgB,QAAQ,iBAAiB,CAAC,WAAW,kBAAkB,CAAC,MAAM;AAAA,EAClF,SAAS;AAAA,EACT,WAAW;AACb,CAAC,GAAG,QAAQ,GAAG,kBAAkB,IAAI,IAAI,gBAAgB,aAAa,IAAI,MAAM;AAAA,EAC9E,SAAS;AAAA,EACT,WAAW;AACb,CAAC,CAAC,CAAC,CAAC,GAAG,WAAW,kBAAkB,CAAC,MAAM;AAAA,EACzC,SAAS;AAAA,EACT,WAAW;AACb,CAAC,GAAG,QAAQ,GAAG,kBAAkB,IAAI,IAAI,gBAAgB,gBAAgB,IAAI,MAAM;AAAA,EACjF,SAAS;AAAA,EACT,WAAW;AACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACN,IAAM,kBAAkB,QAAQ,mBAAmB,CAAC,WAAW,UAAU,CAAC,MAAM;AAAA,EAC9E,SAAS;AAAA,EACT,WAAW;AACb,CAAC,GAAG,QAAQ,GAAG,kBAAkB,IAAI,IAAI,gBAAgB,aAAa,IAAI,MAAM;AAAA,EAC9E,SAAS;AAAA,EACT,WAAW;AACb,CAAC,CAAC,CAAC,CAAC,GAAG,WAAW,UAAU,CAAC,MAAM;AAAA,EACjC,SAAS;AAAA,EACT,WAAW;AACb,CAAC,GAAG,QAAQ,GAAG,kBAAkB,IAAI,IAAI,gBAAgB,YAAY,IAAI,MAAM;AAAA,EAC7E,SAAS;AAAA,EACT,WAAW;AACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAGN,IAAM,oBAAoB,CAAC,OAAO,MAAM,QAAQ;AAChD,SAAS,gCAAgC;AACvC,MAAI,WAAW;AACf,SAAO,SAAU,UAAU;AACzB,UAAM,YAAW,oBAAI,KAAK,GAAE,QAAQ;AACpC,UAAM,aAAa,KAAK,IAAI,GAAG,MAAM,WAAW,SAAS;AACzD,UAAM,UAAU,OAAO,WAAW,MAAM;AACtC,eAAS,WAAW,UAAU;AAAA,IAChC,GAAG,UAAU;AACb,eAAW,WAAW;AACtB,WAAO;AAAA,EACT;AACF;AACA,SAAS,2BAA2B;AAClC,MAAI,OAAO,WAAW,aAAa;AACjC,WAAO,MAAM;AAAA,EACf;AACA,MAAI,OAAO,uBAAuB;AAEhC,WAAO,OAAO,sBAAsB,KAAK,MAAM;AAAA,EACjD;AACA,QAAM,SAAS,kBAAkB,OAAO,SAAO,GAAG,GAAG,2BAA2B,MAAM,EAAE,CAAC;AACzF,SAAO,SAAS,OAAO,GAAG,MAAM,uBAAuB,IAAI,8BAA8B;AAC3F;AAaA,IAAM,eAAe,yBAAyB;AAC9C,IAAM,cAAc,QAAQ,eAAe,CAAC,MAAM,QAAQ,MAAM;AAAA,EAC9D,WAAW;AAAA,EACX,OAAO;AACT,CAAC,GAAG;AAAA,EACF,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,OAAO;AAAA,EACT;AACF,CAAC,GAAG,MAAM,MAAM,MAAM;AAAA,EACpB,WAAW;AAAA,EACX,OAAO;AACT,CAAC,GAAG;AAAA,EACF,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,OAAO;AAAA,EACT;AACF,CAAC,GAAG,WAAW,cAAc,QAAQ,SAAS,gBAAgB,WAAW,EAAE,CAAC,CAAC,CAAC;;;AChQ9E,kBAAqE;AACrE,SAAS,SAAS,OAAO;AACvB,SAAO,OAAO,UAAU,eAAe,UAAU;AACnD;AACA,SAAS,MAAM,OAAO;AACpB,SAAO,OAAO,UAAU,eAAe,UAAU;AACnD;AAgEA,SAAS,aAAa,OAAO;AAC3B,SAAO,MAAM,KAAK,WAAW,OAAO;AACtC;AACA,SAAS,iBAAiB,OAAO;AAC/B,SAAO,aAAa,KAAK,IAAI,MAAM,QAAQ,CAAC,KAAK,MAAM,eAAe,CAAC,IAAI;AAC7E;AACA,SAAS,UAAU,KAAK;AACtB,SAAO,CAAC,CAAC,OAAO,OAAO,IAAI,SAAS,cAAc,OAAO,IAAI,UAAU;AACzE;AAsTA,SAAS,eAAe,OAAO;AAC7B,SAAO,OAAO,UAAU,YAAY,SAAS,KAAK;AACpD;AACA,SAAS,UAAU,OAAO,SAAS;AACjC,SAAO,KAAK,MAAM,QAAQ,KAAK,IAAI,IAAI,OAAO,CAAC,IAAI,KAAK,IAAI,IAAI,OAAO;AACzE;AACA,SAAS,IAAI,OAAO,UAAU,GAAG;AAC/B,SAAO,MAAM,OAAO,CAAC,UAAU,YAAY,WAAW,SAAS,OAAO;AACxE;AAeA,SAAS,mBAAmB,OAAO;AACjC,UAAI,0BAAa,KAAK,GAAG;AACvB,WAAO;AAAA,EACT;AACA,MAAI,UAAU,KAAK,GAAG;AAEpB,eAAO,kBAAK,QAAQ,QAAQ,KAAK,CAAC;AAAA,EACpC;AACA,aAAO,gBAAG,KAAK;AACjB;AAuBA,SAAS,SAAS,OAAO,QAAQ,SAAS;AACxC,MAAI,MAAM,SAAS,QAAQ;AACzB,WAAO;AAAA,EACT;AACA,QAAM,SAAS,GAAG,mBAAmB,QAAQ,OAAO,CAAC,GAAG,KAAK;AAC7D,SAAO,OAAO,MAAM,OAAO,SAAS,QAAQ,OAAO,MAAM;AAC3D;AAKA,SAAS,mBAAmB,QAAQ,SAAS;AAC3C,SAAO,MAAM,MAAM,EAAE,KAAK,OAAO,EAAE,KAAK,EAAE;AAC5C;AACA,IAAM,YAAY,CAAC,cAAc,SAAS,kBAAkB,SAAS,OAAO,QAAQ,YAAY,MAAM,gBAAgB,CAAC,QAAQ,YAAY,EAAE,cAAc,KAAK,CAAC,oBAAoB,QAAQ,YAAY,EAAE,kBAAkB,QAAQ,YAAY,EAAE;AACnP,IAAM,aAAa,CAAC,eAAe,SAAS,kBAAkB,SAAS,cAAc,KAAK,UAAQ,UAAU,MAAM,SAAS,eAAe,CAAC;AAC3I,SAAS,kBAAkB,IAAI;AAK7B,SAAO,OAAO,SAAS,cAAc,KAAK,KAAK,IAAI,EAAE,IAAI,GAAG;AAC9D;AAMA,SAAS,wBAAwB,QAAQ,MAAM,SAAS;AAItD,MAAI,CAAC,QAAQ;AACX,WAAO;AAAA,EACT;AACA,SAAO,IAAI,uBAAW,gBAAc;AAKlC,WAAO,kBAAkB;AAAA;AAAA,UAEzB,uBAAU,QAAQ,MAAM,OAAO,EAAE,UAAU,UAAU;AAAA,KAAC;AAAA,EACxD,CAAC;AACH;;;ACreA,uBAAkF;AAClF,IAAAA,eAAuC;AAGvC,IAAM,cAAc;AAAA,EAClB,YAAY;AACd;AAMA,IAAM,qBAAqB,oBAAI,IAAI;AAKnC,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB,cAAc;AAOZ,SAAK,qBAAqB,oBAAI,IAAI;AAAA,EACpC;AAAA,EACA,IAAI,oBAAoB;AACtB,WAAO,YAAY,aAAa,qBAAqB,KAAK;AAAA,EAC5D;AAAA,EACA,yBAAyB,KAAK,QAAQ;AACpC,UAAM,cAAc,KAAK,kBAAkB,IAAI,GAAG;AAClD,UAAM,OAAO,cAAc,KAAK,kBAAkB,IAAI,GAAG,IAAI,KAAK,cAAc,MAAM;AACtF,QAAI,CAAC,aAAa;AAChB,WAAK,kBAAkB,IAAI,KAAK,IAAI;AAAA,IACtC;AAAA,EACF;AAAA,EACA,2BAA2B,KAAK;AAC9B,QAAI,KAAK,kBAAkB,IAAI,GAAG,GAAG;AACnC,WAAK,kBAAkB,OAAO,GAAG;AAAA,IACnC;AAAA,EACF;AAAA,EACA,oBAAoB,KAAK;AACvB,WAAO,KAAK,kBAAkB,IAAI,GAAG,IAAI,KAAK,kBAAkB,IAAI,GAAG,EAAE,SAAS;AAAA,EACpF;AAAA,EACA,cAAc,QAAQ;AACpB,WAAO;AAAA,MACL;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,4BAA4B,mBAAmB;AAClE,aAAO,KAAK,qBAAqB,sBAAqB;AAAA,IACxD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,qBAAoB;AAAA,MAC7B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,SAAS,eAAe,GAAG,GAAG,GAAG,GAAG;AAClC,QAAM,KAAK,IAAI;AACf,MAAI,KAAK,KAAK,IAAI;AAClB,MAAI,KAAK,GAAG;AACV,WAAO,KAAK,IAAI,KAAK,KAAK,KAAK;AAAA,EACjC,OAAO;AACL,WAAO,KAAK,MAAM,MAAM,KAAK,KAAK,KAAK,KAAK;AAAA,EAC9C;AACF;AACA,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,YAAY,KAAK;AACf,SAAK,MAAM;AAAA,EACb;AAAA;AAAA,EAEA,aAAa,IAAI,WAAW,GAAG;AAC7B,QAAI,OAAO,QAAQ;AACjB,WAAK,IAAI,KAAK,YAAY;AAC1B,WAAK,IAAI,gBAAgB,YAAY;AAAA,IACvC,OAAO;AACL,SAAG,YAAY;AAAA,IACjB;AAAA,EACF;AAAA;AAAA,EAEA,UAAU,IAAI;AACZ,UAAM,MAAM;AAAA,MACV,KAAK;AAAA,MACL,MAAM;AAAA,IACR;AACA,QAAI,CAAC,MAAM,CAAC,GAAG,eAAe,EAAE,QAAQ;AACtC,aAAO;AAAA,IACT;AACA,UAAMC,QAAO,GAAG,sBAAsB;AACtC,QAAIA,MAAK,SAASA,MAAK,QAAQ;AAC7B,YAAM,MAAM,GAAG,cAAc;AAC7B,UAAI,MAAMA,MAAK,MAAM,IAAI;AACzB,UAAI,OAAOA,MAAK,OAAO,IAAI;AAAA,IAC7B,OAAO;AACL,UAAI,MAAMA,MAAK;AACf,UAAI,OAAOA,MAAK;AAAA,IAClB;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA,EAGA,UAAU,QAAQ,MAAM,MAAM;AAC5B,QAAI,OAAO,WAAW,aAAa;AACjC,aAAO;AAAA,IACT;AACA,UAAM,SAAS,MAAM,cAAc;AACnC,QAAI,SAAS;AACb,QAAI,KAAK,SAAS,MAAM,GAAG;AACzB,eAAS,OAAO,MAAM,gBAAgB,aAAa;AAAA,IACrD,WAAW,kBAAkB,UAAU;AACrC,eAAS,OAAO,gBAAgB,MAAM;AAAA,IACxC,WAAW,QAAQ;AACjB,eAAS,OAAO,MAAM;AAAA,IACxB;AACA,QAAI,UAAU,CAAC,KAAK,SAAS,MAAM,KAAK,OAAO,WAAW,UAAU;AAClE,gBAAU,OAAO,iBAAiB,QAAQ,gBAAgB,MAAM;AAAA,IAClE;AACA,WAAO;AAAA,EACT;AAAA,EACA,SAAS,KAAK;AACZ,WAAO,QAAQ,QAAQ,QAAQ,UAAa,QAAQ,IAAI;AAAA,EAC1D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,SAAS,aAAa,IAAI,GAAG,UAAU,CAAC,GAAG;AACzC,UAAM,SAAS,cAAc,cAAc;AAC3C,UAAM,YAAY,KAAK,UAAU,MAAM;AACvC,UAAM,YAAY,KAAK,IAAI;AAC3B,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA,WAAW;AAAA,IACb,IAAI;AACJ,UAAM,YAAY,MAAM;AACtB,YAAM,YAAY,KAAK,IAAI;AAC3B,YAAM,OAAO,YAAY;AACzB,YAAM,iBAAiB,UAAU,gBAAgB,OAAO,WAAW,WAAW,MAAM,WAAW,GAAG,QAAQ;AAC1G,UAAI,KAAK,SAAS,MAAM,GAAG;AACzB,eAAO,SAAS,OAAO,aAAa,aAAa;AAAA,MACnD,WAAW,kBAAkB,gBAAgB,OAAO,YAAY,SAAS,gBAAgB;AACvF,eAAO,gBAAgB,YAAY;AAAA,MACrC,OAAO;AACL,eAAO,YAAY;AAAA,MACrB;AACA,UAAI,OAAO,UAAU;AACnB,qBAAa,SAAS;AAAA,MACxB,WAAW,OAAO,aAAa,YAAY;AACzC,iBAAS;AAAA,MACX;AAAA,IACF;AACA,iBAAa,SAAS;AAAA,EACxB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,mBAAmB;AAC/D,aAAO,KAAK,qBAAqB,mBAAqB,SAAS,QAAQ,CAAC;AAAA,IAC1E;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,kBAAiB;AAAA,MAC1B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,OAAO,MAAM;AAAC;AACpB,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,YAAY,QAAQ,kBAAkB;AACpC,SAAK,SAAS;AACd,SAAK,mBAAmB;AACxB,SAAK,gBAAgB,IAAI,qBAAQ;AACjC,SAAK,YAAY;AACjB,SAAK,gBAAgB;AACrB,SAAK,UAAU,MAAM;AACnB,WAAK,OAAO,IAAI,MAAM;AACpB,aAAK,cAAc,KAAK;AAAA,MAC1B,CAAC;AAAA,IACH;AACA,SAAK,WAAW,KAAK,iBAAiB,eAAe,MAAM,IAAI;AAAA,EACjE;AAAA,EACA,YAAY;AACV,SAAK,iBAAiB;AACtB,WAAO,KAAK,cAAc,SAAK,4BAAU,EAAE,OAAG,2BAAS,MAAM,KAAK,mBAAmB,CAAC,CAAC;AAAA,EACzF;AAAA,EACA,cAAc;AACZ,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EACA,mBAAmB;AACjB,QAAI,KAAK,cAAc,GAAG;AACxB,WAAK,OAAO,kBAAkB,MAAM;AAClC,aAAK,gBAAgB,KAAK,SAAS,OAAO,UAAU,UAAU,KAAK,OAAO;AAAA,MAC5E,CAAC;AAAA,IACH;AACA,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,qBAAqB;AACnB,SAAK,aAAa;AAClB,QAAI,KAAK,cAAc,GAAG;AACxB,WAAK,cAAc;AACnB,WAAK,gBAAgB;AAAA,IACvB;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,mBAAmB;AAC/D,aAAO,KAAK,qBAAqB,mBAAqB,SAAY,MAAM,GAAM,SAAY,gBAAgB,CAAC;AAAA,IAC7G;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,kBAAiB;AAAA,MAC1B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAI;AAAA,CACH,SAAUC,oBAAmB;AAC5B,EAAAA,mBAAkB,KAAK,IAAI;AAC3B,EAAAA,mBAAkB,IAAI,IAAI;AAC1B,EAAAA,mBAAkB,IAAI,IAAI;AAC1B,EAAAA,mBAAkB,IAAI,IAAI;AAC1B,EAAAA,mBAAkB,IAAI,IAAI;AAC1B,EAAAA,mBAAkB,IAAI,IAAI;AAC5B,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAShD,IAAM,yBAAyB;AAAA,EAC7B,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,KAAK;AACP;AACA,IAAM,qBAAqB;AAAA,EACzB,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,KAAK;AACP;AACA,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,YAAY,eAAe,cAAc;AACvC,SAAK,gBAAgB;AACrB,SAAK,eAAe;AACpB,SAAK,cAAc,UAAU,EAAE,UAAU,MAAM;AAAA,IAAC,CAAC;AAAA,EACnD;AAAA,EACA,UAAU,eAAe,SAAS;AAChC,QAAI,SAAS;AACX,YAAM,MAAM,MAAM,KAAK,WAAW,eAAe,IAAI;AACrD,aAAO,KAAK,cAAc,UAAU,EAAE,SAAK,sBAAI,GAAG,OAAG,4BAAU,IAAI,CAAC,OAAG,uCAAqB,CAAC,GAAG,MAAM,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC,OAAG,sBAAI,OAAK,EAAE,CAAC,CAAC,CAAC;AAAA,IACtI,OAAO;AACL,YAAM,MAAM,MAAM,KAAK,WAAW,aAAa;AAC/C,aAAO,KAAK,cAAc,UAAU,EAAE,SAAK,sBAAI,GAAG,OAAG,4BAAU,IAAI,CAAC,OAAG,uCAAqB,CAAC;AAAA,IAC/F;AAAA,EACF;AAAA,EACA,WAAW,eAAe,SAAS;AACjC,QAAI,KAAK,kBAAkB;AAC3B,UAAM,uBAAuB,CAAC;AAC9B,WAAO,KAAK,aAAa,EAAE,IAAI,gBAAc;AAC3C,YAAM,SAAS;AACf,YAAM,UAAU,KAAK,aAAa,WAAW,cAAc,MAAM,CAAC,EAAE;AACpE,2BAAqB,UAAU,IAAI;AACnC,UAAI,SAAS;AACX,aAAK;AAAA,MACP;AAAA,IACF,CAAC;AACD,QAAI,SAAS;AACX,aAAO,CAAC,IAAI,oBAAoB;AAAA,IAClC,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,6BAA6B,mBAAmB;AACnE,aAAO,KAAK,qBAAqB,uBAAyB,SAAS,gBAAgB,GAAM,SAAY,YAAY,CAAC;AAAA,IACpH;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,sBAAqB;AAAA,MAC9B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,oBAAN,MAAM,2BAA0B,2BAAc;AAAA,EAC5C,cAAc;AACZ,UAAM,CAAC;AAAA,EACT;AAAA,EACA,cAAc;AACZ,SAAK,KAAK;AACV,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,0BAA0B,mBAAmB;AAChE,aAAO,KAAK,qBAAqB,oBAAmB;AAAA,IACtD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,mBAAkB;AAAA,IAC7B,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AACH,SAAS,gBAAgB,OAAO;AAC9B,QAAM,IAAI,iBAAiB,KAAK;AAChC,SAAO;AAAA,IACL,GAAG,EAAE;AAAA,IACL,GAAG,EAAE;AAAA,EACP;AACF;AAIA,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,YAAY,kBAAkB;AAC5B,SAAK,oBAAoB;AACzB,SAAK,0BAA0B;AAC/B,SAAK,uBAAuB;AAC5B,SAAK,iBAAiB,oBAAI,IAAI;AAC9B,SAAK,WAAW,iBAAiB,eAAe,MAAM,IAAI;AAAA,EAC5D;AAAA,EACA,wBAAwB,OAAO;AAC7B,QAAI,CAAC,KAAK,eAAe,MAAM;AAC7B,WAAK,wBAAwB,aAAa,KAAK,CAAC;AAAA,IAClD;AAEA,QAAI,KAAK,yBAAyB;AAChC,WAAK,wBAAwB,SAAS;AAAA,IACxC;AACA,SAAK,uBAAuB,gBAAgB,KAAK;AACjD,SAAK,0BAA0B,IAAI,qBAAQ;AAC3C,WAAO,KAAK,wBAAwB,SAAK,sBAAI,OAAK;AAChD,aAAO;AAAA,QACL,GAAG,EAAE,QAAQ,KAAK,qBAAqB;AAAA,QACvC,GAAG,EAAE,QAAQ,KAAK,qBAAqB;AAAA,MACzC;AAAA,IACF,CAAC,OAAG,yBAAO,OAAK,KAAK,IAAI,EAAE,CAAC,IAAI,KAAK,qBAAqB,KAAK,IAAI,EAAE,CAAC,IAAI,KAAK,iBAAiB,OAAG,2BAAS,MAAM,KAAK,yBAAyB,CAAC,CAAC;AAAA,EACpJ;AAAA,EACA,wBAAwB,SAAS;AAC/B,QAAI,SAAS;AACX,WAAK,eAAe,IAAI;AAAA,QACtB,UAAU,KAAK,SAAS,OAAO,YAAY,aAAa,OAAK;AAC3D,cAAI,KAAK,yBAAyB;AAChC,iBAAK,wBAAwB,KAAK,EAAE,QAAQ,CAAC,KAAK,EAAE,eAAe,CAAC,CAAC;AAAA,UACvE;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AACD,WAAK,eAAe,IAAI;AAAA,QACtB,UAAU,KAAK,SAAS,OAAO,YAAY,YAAY,MAAM;AAC3D,cAAI,KAAK,yBAAyB;AAChC,iBAAK,wBAAwB,SAAS;AAAA,UACxC;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH,OAAO;AACL,WAAK,eAAe,IAAI;AAAA,QACtB,UAAU,KAAK,SAAS,OAAO,YAAY,aAAa,OAAK;AAC3D,cAAI,KAAK,yBAAyB;AAChC,iBAAK,wBAAwB,KAAK,CAAC;AAAA,UACrC;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AACD,WAAK,eAAe,IAAI;AAAA,QACtB,UAAU,KAAK,SAAS,OAAO,YAAY,WAAW,MAAM;AAC1D,cAAI,KAAK,yBAAyB;AAChC,iBAAK,wBAAwB,SAAS;AAAA,UACxC;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,2BAA2B;AACzB,SAAK,0BAA0B;AAAA,EACjC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,uBAAuB,mBAAmB;AAC7D,aAAO,KAAK,qBAAqB,iBAAmB,SAAY,gBAAgB,CAAC;AAAA,IACnF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,gBAAe;AAAA,MACxB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;;;AC/cH,SAAS,uBAAuB;AAC9B,QAAM,MAAM,iCAAiC;AAC/C;AAKA,SAAS,kCAAkC;AACzC,QAAM,MAAM,oCAAoC;AAClD;AAKA,SAAS,wCAAwC;AAC/C,QAAM,MAAM,6CAA6C;AAC3D;AAKA,SAAS,8BAA8B;AACrC,QAAM,MAAM,qHAA0H;AACxI;AAKA,SAAS,6BAA6B;AACpC,QAAM,MAAM,sDAAsD;AACpE;AAKA,SAAS,6BAA6B;AACpC,QAAM,MAAM,8DAA8D;AAC5E;AAMA,IAAM,SAAN,MAAa;AAAA;AAAA,EAEX,OAAO,MAAM;AACX,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,UAAI,QAAQ,MAAM;AAChB,mCAA2B;AAAA,MAC7B;AACA,UAAI,KAAK,YAAY,GAAG;AACtB,wCAAgC;AAAA,MAClC;AAAA,IACF;AACA,SAAK,gBAAgB;AACrB,WAAO,KAAK,OAAO,IAAI;AAAA,EACzB;AAAA;AAAA,EAEA,SAAS;AACP,QAAI,OAAO,KAAK;AAChB,QAAI,QAAQ,MAAM;AAChB,WAAK,gBAAgB;AACrB,WAAK,OAAO;AAAA,IACd,WAAW,OAAO,cAAc,eAAe,WAAW;AACxD,iCAA2B;AAAA,IAC7B;AAAA,EACF;AAAA;AAAA,EAEA,IAAI,aAAa;AACf,WAAO,KAAK,iBAAiB;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB,MAAM;AACpB,SAAK,gBAAgB;AAAA,EACvB;AACF;AAIA,IAAM,kBAAN,cAA8B,OAAO;AAAA,EACnC,YAAY,WAAW,kBAAkB,UAAU,0BAA0B,kBAAkB;AAC7F,UAAM;AACN,SAAK,YAAY;AACjB,SAAK,mBAAmB;AACxB,SAAK,WAAW;AAChB,SAAK,2BAA2B;AAChC,SAAK,mBAAmB;AAAA,EAC1B;AACF;AAIA,IAAM,iBAAN,cAA6B,OAAO;AAAA,EAClC,YACA,aACA,kBACA,SACA,UAAU;AACR,UAAM;AACN,SAAK,cAAc;AACnB,SAAK,mBAAmB;AACxB,SAAK,UAAU;AACf,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK,YAAY;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,MAAM,UAAU,KAAK,SAAS;AACnC,SAAK,UAAU;AACf,WAAO,MAAM,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,SAAS;AACP,SAAK,UAAU;AACf,WAAO,MAAM,OAAO;AAAA,EACtB;AACF;AAMA,IAAM,YAAN,cAAwB,OAAO;AAAA,EAC7B,YAAY,SAAS;AACnB,UAAM;AACN,SAAK,UAAU,mBAAmB,aAAa,QAAQ,gBAAgB;AAAA,EACzE;AACF;AAKA,IAAM,mBAAN,MAAuB;AAAA,EACrB,cAAc;AAEZ,SAAK,cAAc;AAEnB,SAAK,kBAAkB;AAAA,EACzB;AAAA;AAAA,EAEA,cAAc;AACZ,WAAO,CAAC,CAAC,KAAK;AAAA,EAChB;AAAA;AAAA,EAEA,OAAO,QAAQ;AACb,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,UAAI,CAAC,QAAQ;AACX,6BAAqB;AAAA,MACvB;AACA,UAAI,KAAK,YAAY,GAAG;AACtB,wCAAgC;AAAA,MAClC;AACA,UAAI,KAAK,aAAa;AACpB,8CAAsC;AAAA,MACxC;AAAA,IACF;AACA,QAAI,kBAAkB,iBAAiB;AACrC,WAAK,kBAAkB;AACvB,aAAO,KAAK,sBAAsB,MAAM;AAAA,IAC1C,WAAW,kBAAkB,gBAAgB;AAC3C,WAAK,kBAAkB;AACvB,aAAO,KAAK,qBAAqB,MAAM;AAAA,IAEzC,WAAW,KAAK,mBAAmB,kBAAkB,WAAW;AAC9D,WAAK,kBAAkB;AACvB,aAAO,KAAK,gBAAgB,MAAM;AAAA,IACpC;AACA,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,kCAA4B;AAAA,IAC9B;AAAA,EACF;AAAA;AAAA,EAEA,SAAS;AACP,QAAI,KAAK,iBAAiB;AACxB,WAAK,gBAAgB,gBAAgB,IAAI;AACzC,WAAK,kBAAkB;AAAA,IACzB;AACA,SAAK,iBAAiB;AAAA,EACxB;AAAA;AAAA,EAEA,UAAU;AACR,QAAI,KAAK,YAAY,GAAG;AACtB,WAAK,OAAO;AAAA,IACd;AACA,SAAK,iBAAiB;AACtB,SAAK,cAAc;AAAA,EACrB;AAAA;AAAA,EAEA,aAAa,IAAI;AACf,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,mBAAmB;AACjB,QAAI,KAAK,YAAY;AACnB,WAAK,WAAW;AAChB,WAAK,aAAa;AAAA,IACpB;AAAA,EACF;AACF;AAWA,IAAM,kBAAN,cAA8B,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAY7C,YACA,eAAe,2BAA2B,SAAS,kBAKnD,WAAW;AACT,UAAM;AACN,SAAK,gBAAgB;AACrB,SAAK,4BAA4B;AACjC,SAAK,UAAU;AACf,SAAK,mBAAmB;AAOxB,SAAK,kBAAkB,YAAU;AAG/B,UAAI,CAAC,KAAK,cAAc,OAAO,cAAc,eAAe,YAAY;AACtE,cAAM,MAAM,kEAAkE;AAAA,MAChF;AACA,YAAM,UAAU,OAAO;AACvB,UAAI,CAAC,QAAQ,eAAe,OAAO,cAAc,eAAe,YAAY;AAC1E,cAAM,MAAM,uDAAuD;AAAA,MACrE;AAGA,YAAM,aAAa,KAAK,UAAU,cAAc,YAAY;AAC5D,cAAQ,WAAW,aAAa,YAAY,OAAO;AACnD,WAAK,cAAc,YAAY,OAAO;AACtC,WAAK,kBAAkB;AACvB,YAAM,aAAa,MAAM;AAEvB,YAAI,WAAW,YAAY;AACzB,qBAAW,WAAW,aAAa,SAAS,UAAU;AAAA,QACxD;AAAA,MACF,CAAC;AAAA,IACH;AACA,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,sBAAsB,QAAQ;AAC5B,UAAM,WAAW,OAAO,4BAA4B,KAAK;AACzD,SAAK,OAAO,cAAc,eAAe,cAAc,CAAC,UAAU;AAChE,YAAM,MAAM,8EAA8E;AAAA,IAC5F;AACA,UAAM,mBAAmB,SAAS,wBAAwB,OAAO,SAAS;AAC1E,QAAI;AAKJ,QAAI,OAAO,kBAAkB;AAC3B,qBAAe,OAAO,iBAAiB,gBAAgB,kBAAkB,OAAO,iBAAiB,QAAQ,OAAO,YAAY,OAAO,iBAAiB,UAAU,OAAO,oBAAoB,MAAS;AAClM,WAAK,aAAa,MAAM,aAAa,QAAQ,CAAC;AAAA,IAChD,OAAO;AACL,WAAK,OAAO,cAAc,eAAe,cAAc,CAAC,KAAK,SAAS;AACpE,cAAM,MAAM,qEAAqE;AAAA,MACnF;AACA,qBAAe,iBAAiB,OAAO,OAAO,YAAY,KAAK,oBAAoB,SAAS,IAAI;AAChG,WAAK,QAAQ,WAAW,aAAa,QAAQ;AAC7C,WAAK,aAAa,MAAM;AAGtB,YAAI,KAAK,QAAQ,YAAY,GAAG;AAC9B,eAAK,QAAQ,WAAW,aAAa,QAAQ;AAAA,QAC/C;AACA,qBAAa,QAAQ;AAAA,MACvB,CAAC;AAAA,IACH;AAGA,SAAK,cAAc,YAAY,KAAK,sBAAsB,YAAY,CAAC;AACvE,SAAK,kBAAkB;AACvB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,qBAAqB,QAAQ;AAC3B,QAAI,gBAAgB,OAAO;AAC3B,QAAI,UAAU,cAAc,mBAAmB,OAAO,aAAa,OAAO,SAAS;AAAA,MACjF,UAAU,OAAO;AAAA,IACnB,CAAC;AAKD,YAAQ,UAAU,QAAQ,cAAY,KAAK,cAAc,YAAY,QAAQ,CAAC;AAI9E,YAAQ,cAAc;AACtB,SAAK,aAAa,MAAM;AACtB,UAAI,QAAQ,cAAc,QAAQ,OAAO;AACzC,UAAI,UAAU,IAAI;AAChB,sBAAc,OAAO,KAAK;AAAA,MAC5B;AAAA,IACF,CAAC;AACD,SAAK,kBAAkB;AAEvB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU;AACR,UAAM,QAAQ;AACd,SAAK,cAAc,OAAO;AAAA,EAC5B;AAAA;AAAA,EAEA,sBAAsB,cAAc;AAClC,WAAO,aAAa,SAAS,UAAU,CAAC;AAAA,EAC1C;AACF;AAWA,IAAM,YAAN,MAAM,mBAAkB,eAAe;AAAA,EACrC,YAAY,aAAa,kBAAkB;AACzC,UAAM,aAAa,gBAAgB;AAAA,EACrC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,kBAAkB,mBAAmB;AACxD,aAAO,KAAK,qBAAqB,YAAc,kBAAqB,WAAW,GAAM,kBAAqB,gBAAgB,CAAC;AAAA,IAC7H;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,aAAa,EAAE,CAAC;AAAA,MACjC,UAAU,CAAC,WAAW;AAAA,MACtB,YAAY;AAAA,MACZ,UAAU,CAAI,0BAA0B;AAAA,IAC1C,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,WAAW,CAAC;AAAA,IAClF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AAKH,IAAM,0BAAN,MAAM,iCAAgC,UAAU;AAAA,EAC9C,OAAO;AACL,SAAK,OAAuB,uBAAM;AAChC,UAAI;AACJ,aAAO,SAAS,gCAAgC,mBAAmB;AACjE,gBAAQ,yCAAyC,uCAA0C,sBAAsB,wBAAuB,IAAI,qBAAqB,wBAAuB;AAAA,MAC1L;AAAA,IACF,GAAG;AAAA,EACL;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,cAAc,EAAE,GAAG,CAAC,IAAI,UAAU,EAAE,CAAC;AAAA,MACtD,UAAU,CAAC,WAAW;AAAA,MACtB,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC;AAAA,QAChC,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC,CAAC,GAAM,0BAA0B;AAAA,IACpC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAQH,IAAM,kBAAN,MAAM,yBAAwB,iBAAiB;AAAA,EAC7C,YAAY,2BAA2B,mBAKvC,WAAW;AACT,UAAM;AACN,SAAK,4BAA4B;AACjC,SAAK,oBAAoB;AAEzB,SAAK,iBAAiB;AAEtB,SAAK,WAAW,IAAI,aAAa;AAOjC,SAAK,kBAAkB,YAAU;AAG/B,UAAI,CAAC,KAAK,cAAc,OAAO,cAAc,eAAe,YAAY;AACtE,cAAM,MAAM,kEAAkE;AAAA,MAChF;AACA,YAAM,UAAU,OAAO;AACvB,UAAI,CAAC,QAAQ,eAAe,OAAO,cAAc,eAAe,YAAY;AAC1E,cAAM,MAAM,uDAAuD;AAAA,MACrE;AAGA,YAAM,aAAa,KAAK,UAAU,cAAc,YAAY;AAC5D,aAAO,gBAAgB,IAAI;AAC3B,cAAQ,WAAW,aAAa,YAAY,OAAO;AACnD,WAAK,aAAa,EAAE,YAAY,OAAO;AACvC,WAAK,kBAAkB;AACvB,YAAM,aAAa,MAAM;AACvB,YAAI,WAAW,YAAY;AACzB,qBAAW,WAAW,aAAa,SAAS,UAAU;AAAA,QACxD;AAAA,MACF,CAAC;AAAA,IACH;AACA,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA,EAEA,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,OAAO,QAAQ;AAKjB,QAAI,KAAK,YAAY,KAAK,CAAC,UAAU,CAAC,KAAK,gBAAgB;AACzD;AAAA,IACF;AACA,QAAI,KAAK,YAAY,GAAG;AACtB,YAAM,OAAO;AAAA,IACf;AACA,QAAI,QAAQ;AACV,YAAM,OAAO,MAAM;AAAA,IACrB;AACA,SAAK,kBAAkB,UAAU;AAAA,EACnC;AAAA;AAAA,EAEA,IAAI,cAAc;AAChB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,WAAW;AACT,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,cAAc;AACZ,UAAM,QAAQ;AACd,SAAK,eAAe,KAAK,kBAAkB;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,sBAAsB,QAAQ;AAC5B,WAAO,gBAAgB,IAAI;AAG3B,UAAM,mBAAmB,OAAO,oBAAoB,OAAO,OAAO,mBAAmB,KAAK;AAC1F,UAAM,WAAW,OAAO,4BAA4B,KAAK;AACzD,UAAM,mBAAmB,SAAS,wBAAwB,OAAO,SAAS;AAC1E,UAAM,MAAM,iBAAiB,gBAAgB,kBAAkB,iBAAiB,QAAQ,OAAO,YAAY,iBAAiB,UAAU,OAAO,oBAAoB,MAAS;AAI1K,QAAI,qBAAqB,KAAK,mBAAmB;AAC/C,WAAK,aAAa,EAAE,YAAY,IAAI,SAAS,UAAU,CAAC,CAAC;AAAA,IAC3D;AACA,UAAM,aAAa,MAAM,IAAI,QAAQ,CAAC;AACtC,SAAK,kBAAkB;AACvB,SAAK,eAAe;AACpB,SAAK,SAAS,KAAK,GAAG;AACtB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,qBAAqB,QAAQ;AAC3B,WAAO,gBAAgB,IAAI;AAC3B,UAAM,UAAU,KAAK,kBAAkB,mBAAmB,OAAO,aAAa,OAAO,SAAS;AAAA,MAC5F,UAAU,OAAO;AAAA,IACnB,CAAC;AACD,UAAM,aAAa,MAAM,KAAK,kBAAkB,MAAM,CAAC;AACvD,SAAK,kBAAkB;AACvB,SAAK,eAAe;AACpB,SAAK,SAAS,KAAK,OAAO;AAC1B,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,eAAe;AACb,UAAM,gBAAgB,KAAK,kBAAkB,QAAQ;AAGrD,WAAO,cAAc,aAAa,cAAc,eAAe,gBAAgB,cAAc;AAAA,EAC/F;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,wBAAwB,mBAAmB;AAC9D,aAAO,KAAK,qBAAqB,kBAAoB,kBAAqB,0BAAwB,GAAM,kBAAqB,gBAAgB,GAAM,kBAAkB,QAAQ,CAAC;AAAA,IAChL;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,mBAAmB,EAAE,CAAC;AAAA,MACvC,QAAQ;AAAA,QACN,QAAQ,CAAC,GAAG,mBAAmB,QAAQ;AAAA,MACzC;AAAA,MACA,SAAS;AAAA,QACP,UAAU;AAAA,MACZ;AAAA,MACA,UAAU,CAAC,iBAAiB;AAAA,MAC5B,YAAY;AAAA,MACZ,UAAU,CAAI,0BAA0B;AAAA,IAC1C,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAKH,IAAM,sBAAN,MAAM,6BAA4B,gBAAgB;AAAA,EAChD,OAAO;AACL,SAAK,OAAuB,uBAAM;AAChC,UAAI;AACJ,aAAO,SAAS,4BAA4B,mBAAmB;AAC7D,gBAAQ,qCAAqC,mCAAsC,sBAAsB,oBAAmB,IAAI,qBAAqB,oBAAmB;AAAA,MAC1K;AAAA,IACF,GAAG;AAAA,EACL;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,iBAAiB,EAAE,GAAG,CAAC,IAAI,cAAc,EAAE,CAAC;AAAA,MAC7D,QAAQ;AAAA,QACN,QAAQ,CAAC,GAAG,iBAAiB,QAAQ;AAAA,MACvC;AAAA,MACA,UAAU,CAAC,eAAe;AAAA,MAC1B,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC;AAAA,QAChC,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC,CAAC,GAAM,0BAA0B;AAAA,IACpC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,QAAQ,CAAC;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,MACT,CAAC;AAAA,MACD,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,OAAO;AACL,SAAK,OAAO,SAAS,qBAAqB,mBAAmB;AAC3D,aAAO,KAAK,qBAAqB,eAAc;AAAA,IACjD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,WAAW,iBAAiB,yBAAyB,mBAAmB;AAAA,MAClF,SAAS,CAAC,WAAW,iBAAiB,yBAAyB,mBAAmB;AAAA,IACpF,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB,CAAC,CAAC;AAAA,EACnD;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,WAAW,iBAAiB,yBAAyB,mBAAmB;AAAA,MAClF,SAAS,CAAC,WAAW,iBAAiB,yBAAyB,mBAAmB;AAAA,IACpF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;AC7qBH,IAAAC,oBAA6C;AAI7C,IAAAC,eAA6C;AAE7C,IAAM,0BAA0B,uBAAuB;AAIvD,IAAM,sBAAN,MAA0B;AAAA,EACxB,YAAY,gBAAgBC,WAAU;AACpC,SAAK,iBAAiB;AACtB,SAAK,sBAAsB;AAAA,MACzB,KAAK;AAAA,MACL,MAAM;AAAA,IACR;AACA,SAAK,aAAa;AAClB,SAAK,YAAYA;AAAA,EACnB;AAAA;AAAA,EAEA,SAAS;AAAA,EAAC;AAAA;AAAA,EAEV,SAAS;AACP,QAAI,KAAK,cAAc,GAAG;AACxB,YAAM,OAAO,KAAK,UAAU;AAC5B,WAAK,0BAA0B,KAAK,eAAe,0BAA0B;AAE7E,WAAK,oBAAoB,OAAO,KAAK,MAAM,QAAQ;AACnD,WAAK,oBAAoB,MAAM,KAAK,MAAM,OAAO;AAGjD,WAAK,MAAM,OAAO,oBAAoB,CAAC,KAAK,wBAAwB,IAAI;AACxE,WAAK,MAAM,MAAM,oBAAoB,CAAC,KAAK,wBAAwB,GAAG;AACtE,WAAK,UAAU,IAAI,wBAAwB;AAC3C,WAAK,aAAa;AAAA,IACpB;AAAA,EACF;AAAA;AAAA,EAEA,UAAU;AACR,QAAI,KAAK,YAAY;AACnB,YAAM,OAAO,KAAK,UAAU;AAC5B,YAAM,OAAO,KAAK,UAAU;AAC5B,YAAM,YAAY,KAAK;AACvB,YAAM,YAAY,KAAK;AACvB,YAAM,6BAA6B,UAAU,kBAAkB;AAC/D,YAAM,6BAA6B,UAAU,kBAAkB;AAC/D,WAAK,aAAa;AAClB,gBAAU,OAAO,KAAK,oBAAoB;AAC1C,gBAAU,MAAM,KAAK,oBAAoB;AACzC,WAAK,UAAU,OAAO,wBAAwB;AAM9C,UAAI,yBAAyB;AAC3B,kBAAU,iBAAiB,UAAU,iBAAiB;AAAA,MACxD;AACA,aAAO,OAAO,KAAK,wBAAwB,MAAM,KAAK,wBAAwB,GAAG;AACjF,UAAI,yBAAyB;AAC3B,kBAAU,iBAAiB;AAC3B,kBAAU,iBAAiB;AAAA,MAC7B;AAAA,IACF;AAAA,EACF;AAAA,EACA,gBAAgB;AAId,UAAM,OAAO,KAAK,UAAU;AAC5B,QAAI,KAAK,UAAU,SAAS,wBAAwB,KAAK,KAAK,YAAY;AACxE,aAAO;AAAA,IACT;AACA,UAAM,OAAO,KAAK,UAAU;AAC5B,UAAM,WAAW,KAAK,eAAe,gBAAgB;AACrD,WAAO,KAAK,eAAe,SAAS,UAAU,KAAK,cAAc,SAAS;AAAA,EAC5E;AACF;AAKA,SAAS,2CAA2C;AAClD,SAAO,MAAM,4CAA4C;AAC3D;AAKA,IAAM,sBAAN,MAA0B;AAAA,EACxB,YAAY,mBAAmB,SAAS,gBAAgB,SAAS;AAC/D,SAAK,oBAAoB;AACzB,SAAK,UAAU;AACf,SAAK,iBAAiB;AACtB,SAAK,UAAU;AACf,SAAK,sBAAsB;AAE3B,SAAK,UAAU,MAAM;AACnB,WAAK,QAAQ;AACb,UAAI,KAAK,YAAY,YAAY,GAAG;AAClC,aAAK,QAAQ,IAAI,MAAM,KAAK,YAAY,OAAO,CAAC;AAAA,MAClD;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,OAAO,YAAY;AACjB,QAAI,KAAK,gBAAgB,OAAO,cAAc,eAAe,YAAY;AACvE,YAAM,yCAAyC;AAAA,IACjD;AACA,SAAK,cAAc;AAAA,EACrB;AAAA;AAAA,EAEA,SAAS;AACP,QAAI,KAAK,qBAAqB;AAC5B;AAAA,IACF;AACA,UAAM,SAAS,KAAK,kBAAkB,SAAS,CAAC,EAAE,SAAK,0BAAO,gBAAc;AAC1E,aAAO,CAAC,cAAc,CAAC,KAAK,YAAY,eAAe,SAAS,WAAW,cAAc,EAAE,aAAa;AAAA,IAC1G,CAAC,CAAC;AACF,QAAI,KAAK,WAAW,KAAK,QAAQ,aAAa,KAAK,QAAQ,YAAY,GAAG;AACxE,WAAK,yBAAyB,KAAK,eAAe,0BAA0B,EAAE;AAC9E,WAAK,sBAAsB,OAAO,UAAU,MAAM;AAChD,cAAM,iBAAiB,KAAK,eAAe,0BAA0B,EAAE;AACvE,YAAI,KAAK,IAAI,iBAAiB,KAAK,sBAAsB,IAAI,KAAK,QAAQ,WAAW;AACnF,eAAK,QAAQ;AAAA,QACf,OAAO;AACL,eAAK,YAAY,eAAe;AAAA,QAClC;AAAA,MACF,CAAC;AAAA,IACH,OAAO;AACL,WAAK,sBAAsB,OAAO,UAAU,KAAK,OAAO;AAAA,IAC1D;AAAA,EACF;AAAA;AAAA,EAEA,UAAU;AACR,QAAI,KAAK,qBAAqB;AAC5B,WAAK,oBAAoB,YAAY;AACrC,WAAK,sBAAsB;AAAA,IAC7B;AAAA,EACF;AAAA,EACA,SAAS;AACP,SAAK,QAAQ;AACb,SAAK,cAAc;AAAA,EACrB;AACF;AAGA,IAAM,qBAAN,MAAyB;AAAA;AAAA,EAEvB,SAAS;AAAA,EAAC;AAAA;AAAA,EAEV,UAAU;AAAA,EAAC;AAAA;AAAA,EAEX,SAAS;AAAA,EAAC;AACZ;AASA,SAAS,6BAA6B,SAAS,kBAAkB;AAC/D,SAAO,iBAAiB,KAAK,qBAAmB;AAC9C,UAAM,eAAe,QAAQ,SAAS,gBAAgB;AACtD,UAAM,eAAe,QAAQ,MAAM,gBAAgB;AACnD,UAAM,cAAc,QAAQ,QAAQ,gBAAgB;AACpD,UAAM,eAAe,QAAQ,OAAO,gBAAgB;AACpD,WAAO,gBAAgB,gBAAgB,eAAe;AAAA,EACxD,CAAC;AACH;AAQA,SAAS,4BAA4B,SAAS,kBAAkB;AAC9D,SAAO,iBAAiB,KAAK,yBAAuB;AAClD,UAAM,eAAe,QAAQ,MAAM,oBAAoB;AACvD,UAAM,eAAe,QAAQ,SAAS,oBAAoB;AAC1D,UAAM,cAAc,QAAQ,OAAO,oBAAoB;AACvD,UAAM,eAAe,QAAQ,QAAQ,oBAAoB;AACzD,WAAO,gBAAgB,gBAAgB,eAAe;AAAA,EACxD,CAAC;AACH;AAKA,IAAM,2BAAN,MAA+B;AAAA,EAC7B,YAAY,mBAAmB,gBAAgB,SAAS,SAAS;AAC/D,SAAK,oBAAoB;AACzB,SAAK,iBAAiB;AACtB,SAAK,UAAU;AACf,SAAK,UAAU;AACf,SAAK,sBAAsB;AAAA,EAC7B;AAAA;AAAA,EAEA,OAAO,YAAY;AACjB,QAAI,KAAK,gBAAgB,OAAO,cAAc,eAAe,YAAY;AACvE,YAAM,yCAAyC;AAAA,IACjD;AACA,SAAK,cAAc;AAAA,EACrB;AAAA;AAAA,EAEA,SAAS;AACP,QAAI,CAAC,KAAK,qBAAqB;AAC7B,YAAM,WAAW,KAAK,UAAU,KAAK,QAAQ,iBAAiB;AAC9D,WAAK,sBAAsB,KAAK,kBAAkB,SAAS,QAAQ,EAAE,UAAU,MAAM;AACnF,aAAK,YAAY,eAAe;AAEhC,YAAI,KAAK,WAAW,KAAK,QAAQ,WAAW;AAC1C,gBAAM,cAAc,KAAK,YAAY,eAAe,sBAAsB;AAC1E,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,UACF,IAAI,KAAK,eAAe,gBAAgB;AAGxC,gBAAM,cAAc,CAAC;AAAA,YACnB;AAAA,YACA;AAAA,YACA,QAAQ;AAAA,YACR,OAAO;AAAA,YACP,KAAK;AAAA,YACL,MAAM;AAAA,UACR,CAAC;AACD,cAAI,6BAA6B,aAAa,WAAW,GAAG;AAC1D,iBAAK,QAAQ;AACb,iBAAK,QAAQ,IAAI,MAAM,KAAK,YAAY,OAAO,CAAC;AAAA,UAClD;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA,EAEA,UAAU;AACR,QAAI,KAAK,qBAAqB;AAC5B,WAAK,oBAAoB,YAAY;AACrC,WAAK,sBAAsB;AAAA,IAC7B;AAAA,EACF;AAAA,EACA,SAAS;AACP,SAAK,QAAQ;AACb,SAAK,cAAc;AAAA,EACrB;AACF;AAQA,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B,YAAY,mBAAmB,gBAAgB,SAASA,WAAU;AAChE,SAAK,oBAAoB;AACzB,SAAK,iBAAiB;AACtB,SAAK,UAAU;AAEf,SAAK,OAAO,MAAM,IAAI,mBAAmB;AAKzC,SAAK,QAAQ,YAAU,IAAI,oBAAoB,KAAK,mBAAmB,KAAK,SAAS,KAAK,gBAAgB,MAAM;AAEhH,SAAK,QAAQ,MAAM,IAAI,oBAAoB,KAAK,gBAAgB,KAAK,SAAS;AAM9E,SAAK,aAAa,YAAU,IAAI,yBAAyB,KAAK,mBAAmB,KAAK,gBAAgB,KAAK,SAAS,MAAM;AAC1H,SAAK,YAAYA;AAAA,EACnB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,8BAA8B,mBAAmB;AACpE,aAAO,KAAK,qBAAqB,wBAA0B,SAAY,gBAAgB,GAAM,SAAY,aAAa,GAAM,SAAY,MAAM,GAAM,SAAS,QAAQ,CAAC;AAAA,IACxK;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,uBAAsB;AAAA,MAC/B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AAGH,IAAM,gBAAN,MAAoB;AAAA,EAClB,YAAY,QAAQ;AAElB,SAAK,iBAAiB,IAAI,mBAAmB;AAE7C,SAAK,aAAa;AAElB,SAAK,cAAc;AAEnB,SAAK,gBAAgB;AAMrB,SAAK,sBAAsB;AAC3B,QAAI,QAAQ;AAIV,YAAM,aAAa,OAAO,KAAK,MAAM;AACrC,iBAAW,OAAO,YAAY;AAC5B,YAAI,OAAO,GAAG,MAAM,QAAW;AAO7B,eAAK,GAAG,IAAI,OAAO,GAAG;AAAA,QACxB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAGA,IAAM,yBAAN,MAA6B;AAAA,EAC3B,YAAY,QAAQ,SACpB,SACA,SACA,YAAY;AACV,SAAK,UAAU;AACf,SAAK,UAAU;AACf,SAAK,aAAa;AAClB,SAAK,UAAU,OAAO;AACtB,SAAK,UAAU,OAAO;AACtB,SAAK,WAAW,QAAQ;AACxB,SAAK,WAAW,QAAQ;AAAA,EAC1B;AACF;AA4BA,IAAM,iCAAN,MAAqC;AAAA,EACnC,YACA,gBACA,0BAA0B;AACxB,SAAK,iBAAiB;AACtB,SAAK,2BAA2B;AAAA,EAClC;AACF;AAOA,SAAS,yBAAyB,UAAU,OAAO;AACjD,MAAI,UAAU,SAAS,UAAU,YAAY,UAAU,UAAU;AAC/D,UAAM,MAAM,8BAA8B,QAAQ,KAAK,KAAK,0CAA+C;AAAA,EAC7G;AACF;AAOA,SAAS,2BAA2B,UAAU,OAAO;AACnD,MAAI,UAAU,WAAW,UAAU,SAAS,UAAU,UAAU;AAC9D,UAAM,MAAM,8BAA8B,QAAQ,KAAK,KAAK,yCAA8C;AAAA,EAC5G;AACF;AAOA,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B,YAAYC,WAAU;AAEpB,SAAK,oBAAoB,CAAC;AAC1B,SAAK,YAAYA;AAAA,EACnB;AAAA,EACA,cAAc;AACZ,SAAK,OAAO;AAAA,EACd;AAAA;AAAA,EAEA,IAAI,YAAY;AAEd,SAAK,OAAO,UAAU;AACtB,SAAK,kBAAkB,KAAK,UAAU;AAAA,EACxC;AAAA;AAAA,EAEA,OAAO,YAAY;AACjB,UAAM,QAAQ,KAAK,kBAAkB,QAAQ,UAAU;AACvD,QAAI,QAAQ,IAAI;AACd,WAAK,kBAAkB,OAAO,OAAO,CAAC;AAAA,IACxC;AAEA,QAAI,KAAK,kBAAkB,WAAW,GAAG;AACvC,WAAK,OAAO;AAAA,IACd;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,8BAA8B,mBAAmB;AACpE,aAAO,KAAK,qBAAqB,wBAA0B,SAAS,QAAQ,CAAC;AAAA,IAC/E;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,uBAAsB;AAAA,MAC/B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AAOH,IAAM,4BAAN,MAAM,mCAAkC,sBAAsB;AAAA,EAC5D,YAAYA,WACZ,SAAS;AACP,UAAMA,SAAQ;AACd,SAAK,UAAU;AAEf,SAAK,mBAAmB,WAAS;AAC/B,YAAM,WAAW,KAAK;AACtB,eAAS,IAAI,SAAS,SAAS,GAAG,IAAI,IAAI,KAAK;AAO7C,YAAI,SAAS,CAAC,EAAE,eAAe,UAAU,SAAS,GAAG;AACnD,gBAAM,gBAAgB,SAAS,CAAC,EAAE;AAElC,cAAI,KAAK,SAAS;AAChB,iBAAK,QAAQ,IAAI,MAAM,cAAc,KAAK,KAAK,CAAC;AAAA,UAClD,OAAO;AACL,0BAAc,KAAK,KAAK;AAAA,UAC1B;AACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,IAAI,YAAY;AACd,UAAM,IAAI,UAAU;AAEpB,QAAI,CAAC,KAAK,aAAa;AAErB,UAAI,KAAK,SAAS;AAChB,aAAK,QAAQ,kBAAkB,MAAM,KAAK,UAAU,KAAK,iBAAiB,WAAW,KAAK,gBAAgB,CAAC;AAAA,MAC7G,OAAO;AACL,aAAK,UAAU,KAAK,iBAAiB,WAAW,KAAK,gBAAgB;AAAA,MACvE;AACA,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA;AAAA,EAEA,SAAS;AACP,QAAI,KAAK,aAAa;AACpB,WAAK,UAAU,KAAK,oBAAoB,WAAW,KAAK,gBAAgB;AACxE,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,kCAAkC,mBAAmB;AACxE,aAAO,KAAK,qBAAqB,4BAA8B,SAAS,QAAQ,GAAM,SAAY,QAAQ,CAAC,CAAC;AAAA,IAC9G;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,2BAA0B;AAAA,MACnC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,2BAA2B,CAAC;AAAA,IAClG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AAOH,IAAM,gCAAN,MAAM,uCAAsC,sBAAsB;AAAA,EAChE,YAAYA,WAAU,WACtB,SAAS;AACP,UAAMA,SAAQ;AACd,SAAK,YAAY;AACjB,SAAK,UAAU;AACf,SAAK,oBAAoB;AAEzB,SAAK,uBAAuB,WAAS;AACnC,WAAK,0BAA0B,gBAAgB,KAAK;AAAA,IACtD;AAEA,SAAK,iBAAiB,WAAS;AAC7B,YAAM,SAAS,gBAAgB,KAAK;AAOpC,YAAM,SAAS,MAAM,SAAS,WAAW,KAAK,0BAA0B,KAAK,0BAA0B;AAGvG,WAAK,0BAA0B;AAI/B,YAAM,WAAW,KAAK,kBAAkB,MAAM;AAK9C,eAAS,IAAI,SAAS,SAAS,GAAG,IAAI,IAAI,KAAK;AAC7C,cAAM,aAAa,SAAS,CAAC;AAC7B,YAAI,WAAW,sBAAsB,UAAU,SAAS,KAAK,CAAC,WAAW,YAAY,GAAG;AACtF;AAAA,QACF;AAIA,YAAI,wBAAwB,WAAW,gBAAgB,MAAM,KAAK,wBAAwB,WAAW,gBAAgB,MAAM,GAAG;AAC5H;AAAA,QACF;AACA,cAAM,uBAAuB,WAAW;AAExC,YAAI,KAAK,SAAS;AAChB,eAAK,QAAQ,IAAI,MAAM,qBAAqB,KAAK,KAAK,CAAC;AAAA,QACzD,OAAO;AACL,+BAAqB,KAAK,KAAK;AAAA,QACjC;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,IAAI,YAAY;AACd,UAAM,IAAI,UAAU;AAOpB,QAAI,CAAC,KAAK,aAAa;AACrB,YAAM,OAAO,KAAK,UAAU;AAE5B,UAAI,KAAK,SAAS;AAChB,aAAK,QAAQ,kBAAkB,MAAM,KAAK,mBAAmB,IAAI,CAAC;AAAA,MACpE,OAAO;AACL,aAAK,mBAAmB,IAAI;AAAA,MAC9B;AAGA,UAAI,KAAK,UAAU,OAAO,CAAC,KAAK,mBAAmB;AACjD,aAAK,uBAAuB,KAAK,MAAM;AACvC,aAAK,MAAM,SAAS;AACpB,aAAK,oBAAoB;AAAA,MAC3B;AACA,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA;AAAA,EAEA,SAAS;AACP,QAAI,KAAK,aAAa;AACpB,YAAM,OAAO,KAAK,UAAU;AAC5B,WAAK,oBAAoB,eAAe,KAAK,sBAAsB,IAAI;AACvE,WAAK,oBAAoB,SAAS,KAAK,gBAAgB,IAAI;AAC3D,WAAK,oBAAoB,YAAY,KAAK,gBAAgB,IAAI;AAC9D,WAAK,oBAAoB,eAAe,KAAK,gBAAgB,IAAI;AACjE,UAAI,KAAK,UAAU,OAAO,KAAK,mBAAmB;AAChD,aAAK,MAAM,SAAS,KAAK;AACzB,aAAK,oBAAoB;AAAA,MAC3B;AACA,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA,EACA,mBAAmB,MAAM;AACvB,SAAK,iBAAiB,eAAe,KAAK,sBAAsB,IAAI;AACpE,SAAK,iBAAiB,SAAS,KAAK,gBAAgB,IAAI;AACxD,SAAK,iBAAiB,YAAY,KAAK,gBAAgB,IAAI;AAC3D,SAAK,iBAAiB,eAAe,KAAK,gBAAgB,IAAI;AAAA,EAChE;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,sCAAsC,mBAAmB;AAC5E,aAAO,KAAK,qBAAqB,gCAAkC,SAAS,QAAQ,GAAM,SAAc,QAAQ,GAAM,SAAY,QAAQ,CAAC,CAAC;AAAA,IAC9I;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,+BAA8B;AAAA,MACvC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,+BAA+B,CAAC;AAAA,IACtG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AAEH,SAAS,wBAAwB,QAAQ,OAAO;AAC9C,QAAM,qBAAqB,OAAO,eAAe,eAAe;AAChE,MAAI,UAAU;AACd,SAAO,SAAS;AACd,QAAI,YAAY,QAAQ;AACtB,aAAO;AAAA,IACT;AACA,cAAU,sBAAsB,mBAAmB,aAAa,QAAQ,OAAO,QAAQ;AAAA,EACzF;AACA,SAAO;AACT;AAGA,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,YAAYA,WAAU,WAAW;AAC/B,SAAK,YAAY;AACjB,SAAK,YAAYA;AAAA,EACnB;AAAA,EACA,cAAc;AACZ,SAAK,mBAAmB,OAAO;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,sBAAsB;AACpB,QAAI,CAAC,KAAK,mBAAmB;AAC3B,WAAK,iBAAiB;AAAA,IACxB;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB;AACjB,UAAM,iBAAiB;AAIvB,QAAI,KAAK,UAAU,aAAa,mBAAmB,GAAG;AACpD,YAAM,6BAA6B,KAAK,UAAU,iBAAiB,IAAI,cAAc,yBAA8B,cAAc,mBAAmB;AAGpJ,eAAS,IAAI,GAAG,IAAI,2BAA2B,QAAQ,KAAK;AAC1D,mCAA2B,CAAC,EAAE,OAAO;AAAA,MACvC;AAAA,IACF;AACA,UAAM,YAAY,KAAK,UAAU,cAAc,KAAK;AACpD,cAAU,UAAU,IAAI,cAAc;AAUtC,QAAI,mBAAmB,GAAG;AACxB,gBAAU,aAAa,YAAY,MAAM;AAAA,IAC3C,WAAW,CAAC,KAAK,UAAU,WAAW;AACpC,gBAAU,aAAa,YAAY,QAAQ;AAAA,IAC7C;AACA,SAAK,UAAU,KAAK,YAAY,SAAS;AACzC,SAAK,oBAAoB;AAAA,EAC3B;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,mBAAmB;AAC/D,aAAO,KAAK,qBAAqB,mBAAqB,SAAS,QAAQ,GAAM,SAAc,QAAQ,CAAC;AAAA,IACtG;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,kBAAiB;AAAA,MAC1B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAW;AAAA,EACb,CAAC,GAAG,IAAI;AACV,GAAG;AAMH,IAAM,aAAN,MAAiB;AAAA,EACf,YAAY,eAAe,OAAO,OAAO,SAAS,SAAS,qBAAqB,WAAW,WAAW,yBAAyB,sBAAsB,OAAO,WAAW;AACrK,SAAK,gBAAgB;AACrB,SAAK,QAAQ;AACb,SAAK,QAAQ;AACb,SAAK,UAAU;AACf,SAAK,UAAU;AACf,SAAK,sBAAsB;AAC3B,SAAK,YAAY;AACjB,SAAK,YAAY;AACjB,SAAK,0BAA0B;AAC/B,SAAK,sBAAsB;AAC3B,SAAK,YAAY;AACjB,SAAK,mBAAmB;AACxB,SAAK,iBAAiB,IAAI,qBAAQ;AAClC,SAAK,eAAe,IAAI,qBAAQ;AAChC,SAAK,eAAe,IAAI,qBAAQ;AAChC,SAAK,mBAAmB,0BAAa;AACrC,SAAK,wBAAwB,WAAS,KAAK,eAAe,KAAK,KAAK;AACpE,SAAK,gCAAgC,WAAS;AAC5C,WAAK,iBAAiB,MAAM,MAAM;AAAA,IACpC;AAEA,SAAK,iBAAiB,IAAI,qBAAQ;AAElC,SAAK,wBAAwB,IAAI,qBAAQ;AACzC,SAAK,WAAW,IAAI,qBAAQ;AAC5B,QAAI,QAAQ,gBAAgB;AAC1B,WAAK,kBAAkB,QAAQ;AAC/B,WAAK,gBAAgB,OAAO,IAAI;AAAA,IAClC;AACA,SAAK,oBAAoB,QAAQ;AAIjC,SAAK,kBAAkB,UAAU,MAAM,YAAY,MAAM;AACvD,WAAK,SAAS,KAAK;AAAA,IACrB,GAAG;AAAA,MACD,UAAU,KAAK;AAAA,IACjB,CAAC,CAAC;AAAA,EACJ;AAAA;AAAA,EAEA,IAAI,iBAAiB;AACnB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,IAAI,kBAAkB;AACpB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,cAAc;AAChB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,QAAQ;AAGb,QAAI,CAAC,KAAK,MAAM,iBAAiB,KAAK,qBAAqB;AACzD,WAAK,oBAAoB,YAAY,KAAK,KAAK;AAAA,IACjD;AACA,UAAM,eAAe,KAAK,cAAc,OAAO,MAAM;AACrD,QAAI,KAAK,mBAAmB;AAC1B,WAAK,kBAAkB,OAAO,IAAI;AAAA,IACpC;AACA,SAAK,qBAAqB;AAC1B,SAAK,mBAAmB;AACxB,SAAK,wBAAwB;AAC7B,QAAI,KAAK,iBAAiB;AACxB,WAAK,gBAAgB,OAAO;AAAA,IAC9B;AAIA,SAAK,qBAAqB,QAAQ;AAGlC,SAAK,sBAAsB,gBAAgB,MAAM;AAE/C,UAAI,KAAK,YAAY,GAAG;AACtB,aAAK,eAAe;AAAA,MACtB;AAAA,IACF,GAAG;AAAA,MACD,UAAU,KAAK;AAAA,IACjB,CAAC;AAED,SAAK,qBAAqB,IAAI;AAC9B,QAAI,KAAK,QAAQ,aAAa;AAC5B,WAAK,gBAAgB;AAAA,IACvB;AACA,QAAI,KAAK,QAAQ,YAAY;AAC3B,WAAK,eAAe,KAAK,OAAO,KAAK,QAAQ,YAAY,IAAI;AAAA,IAC/D;AAEA,SAAK,aAAa,KAAK;AAEvB,SAAK,oBAAoB,IAAI,IAAI;AACjC,QAAI,KAAK,QAAQ,qBAAqB;AACpC,WAAK,mBAAmB,KAAK,UAAU,UAAU,MAAM,KAAK,QAAQ,CAAC;AAAA,IACvE;AACA,SAAK,wBAAwB,IAAI,IAAI;AAIrC,QAAI,OAAO,cAAc,cAAc,YAAY;AAMjD,mBAAa,UAAU,MAAM;AAC3B,YAAI,KAAK,YAAY,GAAG;AAItB,eAAK,QAAQ,kBAAkB,MAAM,QAAQ,QAAQ,EAAE,KAAK,MAAM,KAAK,OAAO,CAAC,CAAC;AAAA,QAClF;AAAA,MACF,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS;AACP,QAAI,CAAC,KAAK,YAAY,GAAG;AACvB;AAAA,IACF;AACA,SAAK,eAAe;AAIpB,SAAK,qBAAqB,KAAK;AAC/B,QAAI,KAAK,qBAAqB,KAAK,kBAAkB,QAAQ;AAC3D,WAAK,kBAAkB,OAAO;AAAA,IAChC;AACA,QAAI,KAAK,iBAAiB;AACxB,WAAK,gBAAgB,QAAQ;AAAA,IAC/B;AACA,UAAM,mBAAmB,KAAK,cAAc,OAAO;AAEnD,SAAK,aAAa,KAAK;AAEvB,SAAK,oBAAoB,OAAO,IAAI;AAGpC,SAAK,wBAAwB;AAC7B,SAAK,iBAAiB,YAAY;AAClC,SAAK,wBAAwB,OAAO,IAAI;AACxC,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,UAAU;AACR,UAAM,aAAa,KAAK,YAAY;AACpC,QAAI,KAAK,mBAAmB;AAC1B,WAAK,kBAAkB,QAAQ;AAAA,IACjC;AACA,SAAK,uBAAuB;AAC5B,SAAK,iBAAiB,KAAK,gBAAgB;AAC3C,SAAK,iBAAiB,YAAY;AAClC,SAAK,oBAAoB,OAAO,IAAI;AACpC,SAAK,cAAc,QAAQ;AAC3B,SAAK,aAAa,SAAS;AAC3B,SAAK,eAAe,SAAS;AAC7B,SAAK,eAAe,SAAS;AAC7B,SAAK,sBAAsB,SAAS;AACpC,SAAK,wBAAwB,OAAO,IAAI;AACxC,SAAK,OAAO,OAAO;AACnB,SAAK,qBAAqB,QAAQ;AAClC,SAAK,sBAAsB,KAAK,QAAQ,KAAK,QAAQ;AACrD,QAAI,YAAY;AACd,WAAK,aAAa,KAAK;AAAA,IACzB;AACA,SAAK,aAAa,SAAS;AAC3B,SAAK,gBAAgB,QAAQ;AAC7B,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA;AAAA,EAEA,cAAc;AACZ,WAAO,KAAK,cAAc,YAAY;AAAA,EACxC;AAAA;AAAA,EAEA,gBAAgB;AACd,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,cAAc;AACZ,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,cAAc;AACZ,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,gBAAgB;AACd,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,uBAAuB;AACrB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,YAAY;AACV,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,iBAAiB;AACf,QAAI,KAAK,mBAAmB;AAC1B,WAAK,kBAAkB,MAAM;AAAA,IAC/B;AAAA,EACF;AAAA;AAAA,EAEA,uBAAuB,UAAU;AAC/B,QAAI,aAAa,KAAK,mBAAmB;AACvC;AAAA,IACF;AACA,QAAI,KAAK,mBAAmB;AAC1B,WAAK,kBAAkB,QAAQ;AAAA,IACjC;AACA,SAAK,oBAAoB;AACzB,QAAI,KAAK,YAAY,GAAG;AACtB,eAAS,OAAO,IAAI;AACpB,WAAK,eAAe;AAAA,IACtB;AAAA,EACF;AAAA;AAAA,EAEA,WAAW,YAAY;AACrB,SAAK,UAAU,kCACV,KAAK,UACL;AAEL,SAAK,mBAAmB;AAAA,EAC1B;AAAA;AAAA,EAEA,aAAa,KAAK;AAChB,SAAK,UAAU,iCACV,KAAK,UADK;AAAA,MAEb,WAAW;AAAA,IACb;AACA,SAAK,wBAAwB;AAAA,EAC/B;AAAA;AAAA,EAEA,cAAc,SAAS;AACrB,QAAI,KAAK,OAAO;AACd,WAAK,eAAe,KAAK,OAAO,SAAS,IAAI;AAAA,IAC/C;AAAA,EACF;AAAA;AAAA,EAEA,iBAAiB,SAAS;AACxB,QAAI,KAAK,OAAO;AACd,WAAK,eAAe,KAAK,OAAO,SAAS,KAAK;AAAA,IAChD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe;AACb,UAAM,YAAY,KAAK,QAAQ;AAC/B,QAAI,CAAC,WAAW;AACd,aAAO;AAAA,IACT;AACA,WAAO,OAAO,cAAc,WAAW,YAAY,UAAU;AAAA,EAC/D;AAAA;AAAA,EAEA,qBAAqB,UAAU;AAC7B,QAAI,aAAa,KAAK,iBAAiB;AACrC;AAAA,IACF;AACA,SAAK,uBAAuB;AAC5B,SAAK,kBAAkB;AACvB,QAAI,KAAK,YAAY,GAAG;AACtB,eAAS,OAAO,IAAI;AACpB,eAAS,OAAO;AAAA,IAClB;AAAA,EACF;AAAA;AAAA,EAEA,0BAA0B;AACxB,SAAK,MAAM,aAAa,OAAO,KAAK,aAAa,CAAC;AAAA,EACpD;AAAA;AAAA,EAEA,qBAAqB;AACnB,QAAI,CAAC,KAAK,OAAO;AACf;AAAA,IACF;AACA,UAAMC,SAAQ,KAAK,MAAM;AACzB,IAAAA,OAAM,QAAQ,oBAAoB,KAAK,QAAQ,KAAK;AACpD,IAAAA,OAAM,SAAS,oBAAoB,KAAK,QAAQ,MAAM;AACtD,IAAAA,OAAM,WAAW,oBAAoB,KAAK,QAAQ,QAAQ;AAC1D,IAAAA,OAAM,YAAY,oBAAoB,KAAK,QAAQ,SAAS;AAC5D,IAAAA,OAAM,WAAW,oBAAoB,KAAK,QAAQ,QAAQ;AAC1D,IAAAA,OAAM,YAAY,oBAAoB,KAAK,QAAQ,SAAS;AAAA,EAC9D;AAAA;AAAA,EAEA,qBAAqB,eAAe;AAClC,SAAK,MAAM,MAAM,gBAAgB,gBAAgB,KAAK;AAAA,EACxD;AAAA;AAAA,EAEA,kBAAkB;AAChB,UAAM,eAAe;AACrB,SAAK,mBAAmB,KAAK,UAAU,cAAc,KAAK;AAC1D,SAAK,iBAAiB,UAAU,IAAI,sBAAsB;AAC1D,QAAI,KAAK,qBAAqB;AAC5B,WAAK,iBAAiB,UAAU,IAAI,qCAAqC;AAAA,IAC3E;AACA,QAAI,KAAK,QAAQ,eAAe;AAC9B,WAAK,eAAe,KAAK,kBAAkB,KAAK,QAAQ,eAAe,IAAI;AAAA,IAC7E;AAGA,SAAK,MAAM,cAAc,aAAa,KAAK,kBAAkB,KAAK,KAAK;AAGvE,SAAK,iBAAiB,iBAAiB,SAAS,KAAK,qBAAqB;AAE1E,QAAI,CAAC,KAAK,uBAAuB,OAAO,0BAA0B,aAAa;AAC7E,WAAK,QAAQ,kBAAkB,MAAM;AACnC,8BAAsB,MAAM;AAC1B,cAAI,KAAK,kBAAkB;AACzB,iBAAK,iBAAiB,UAAU,IAAI,YAAY;AAAA,UAClD;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH,OAAO;AACL,WAAK,iBAAiB,UAAU,IAAI,YAAY;AAAA,IAClD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,uBAAuB;AACrB,QAAI,KAAK,MAAM,aAAa;AAC1B,WAAK,MAAM,WAAW,YAAY,KAAK,KAAK;AAAA,IAC9C;AAAA,EACF;AAAA;AAAA,EAEA,iBAAiB;AACf,UAAM,mBAAmB,KAAK;AAC9B,QAAI,CAAC,kBAAkB;AACrB;AAAA,IACF;AACA,QAAI,KAAK,qBAAqB;AAC5B,WAAK,iBAAiB,gBAAgB;AACtC;AAAA,IACF;AACA,qBAAiB,UAAU,OAAO,8BAA8B;AAChE,SAAK,QAAQ,kBAAkB,MAAM;AACnC,uBAAiB,iBAAiB,iBAAiB,KAAK,6BAA6B;AAAA,IACvF,CAAC;AAGD,qBAAiB,MAAM,gBAAgB;AAIvC,SAAK,mBAAmB,KAAK,QAAQ,kBAAkB,MAAM,WAAW,MAAM;AAC5E,WAAK,iBAAiB,gBAAgB;AAAA,IACxC,GAAG,GAAG,CAAC;AAAA,EACT;AAAA;AAAA,EAEA,eAAe,SAAS,YAAY,OAAO;AACzC,UAAM,UAAU,YAAY,cAAc,CAAC,CAAC,EAAE,OAAO,OAAK,CAAC,CAAC,CAAC;AAC7D,QAAI,QAAQ,QAAQ;AAClB,cAAQ,QAAQ,UAAU,IAAI,GAAG,OAAO,IAAI,QAAQ,UAAU,OAAO,GAAG,OAAO;AAAA,IACjF;AAAA,EACF;AAAA;AAAA,EAEA,0BAA0B;AAIxB,SAAK,QAAQ,kBAAkB,MAAM;AAInC,YAAM,eAAe,KAAK,SAAS,SAAK,iCAAU,oBAAM,KAAK,cAAc,KAAK,YAAY,CAAC,CAAC,EAAE,UAAU,MAAM;AAG9G,YAAI,CAAC,KAAK,SAAS,CAAC,KAAK,SAAS,KAAK,MAAM,SAAS,WAAW,GAAG;AAClE,cAAI,KAAK,SAAS,KAAK,QAAQ,YAAY;AACzC,iBAAK,eAAe,KAAK,OAAO,KAAK,QAAQ,YAAY,KAAK;AAAA,UAChE;AACA,cAAI,KAAK,SAAS,KAAK,MAAM,eAAe;AAC1C,iBAAK,sBAAsB,KAAK,MAAM;AACtC,iBAAK,MAAM,OAAO;AAAA,UACpB;AACA,uBAAa,YAAY;AAAA,QAC3B;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,yBAAyB;AACvB,UAAM,iBAAiB,KAAK;AAC5B,QAAI,gBAAgB;AAClB,qBAAe,QAAQ;AACvB,UAAI,eAAe,QAAQ;AACzB,uBAAe,OAAO;AAAA,MACxB;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,iBAAiB,UAAU;AACzB,QAAI,UAAU;AACZ,eAAS,oBAAoB,SAAS,KAAK,qBAAqB;AAChE,eAAS,oBAAoB,iBAAiB,KAAK,6BAA6B;AAChF,eAAS,OAAO;AAIhB,UAAI,KAAK,qBAAqB,UAAU;AACtC,aAAK,mBAAmB;AAAA,MAC1B;AAAA,IACF;AACA,QAAI,KAAK,kBAAkB;AACzB,mBAAa,KAAK,gBAAgB;AAClC,WAAK,mBAAmB;AAAA,IAC1B;AAAA,EACF;AACF;AAKA,IAAM,mBAAmB;AAEzB,IAAM,iBAAiB;AAQvB,IAAM,oCAAN,MAAwC;AAAA;AAAA,EAEtC,IAAI,YAAY;AACd,WAAO,KAAK;AAAA,EACd;AAAA,EACA,YAAY,aAAa,gBAAgB,WAAW,WAAW,mBAAmB;AAChF,SAAK,iBAAiB;AACtB,SAAK,YAAY;AACjB,SAAK,YAAY;AACjB,SAAK,oBAAoB;AAEzB,SAAK,uBAAuB;AAAA,MAC1B,OAAO;AAAA,MACP,QAAQ;AAAA,IACV;AAEA,SAAK,YAAY;AAEjB,SAAK,WAAW;AAEhB,SAAK,iBAAiB;AAEtB,SAAK,yBAAyB;AAE9B,SAAK,kBAAkB;AAEvB,SAAK,kBAAkB;AAEvB,SAAK,eAAe,CAAC;AAErB,SAAK,sBAAsB,CAAC;AAE5B,SAAK,mBAAmB,IAAI,qBAAQ;AAEpC,SAAK,sBAAsB,0BAAa;AAExC,SAAK,WAAW;AAEhB,SAAK,WAAW;AAEhB,SAAK,uBAAuB,CAAC;AAE7B,SAAK,kBAAkB,KAAK;AAC5B,SAAK,UAAU,WAAW;AAAA,EAC5B;AAAA;AAAA,EAEA,OAAO,YAAY;AACjB,QAAI,KAAK,eAAe,eAAe,KAAK,gBAAgB,OAAO,cAAc,eAAe,YAAY;AAC1G,YAAM,MAAM,0DAA0D;AAAA,IACxE;AACA,SAAK,mBAAmB;AACxB,eAAW,YAAY,UAAU,IAAI,gBAAgB;AACrD,SAAK,cAAc;AACnB,SAAK,eAAe,WAAW;AAC/B,SAAK,QAAQ,WAAW;AACxB,SAAK,cAAc;AACnB,SAAK,mBAAmB;AACxB,SAAK,gBAAgB;AACrB,SAAK,oBAAoB,YAAY;AACrC,SAAK,sBAAsB,KAAK,eAAe,OAAO,EAAE,UAAU,MAAM;AAItE,WAAK,mBAAmB;AACxB,WAAK,MAAM;AAAA,IACb,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,QAAQ;AAEN,QAAI,KAAK,eAAe,CAAC,KAAK,UAAU,WAAW;AACjD;AAAA,IACF;AAIA,QAAI,CAAC,KAAK,oBAAoB,KAAK,mBAAmB,KAAK,eAAe;AACxE,WAAK,oBAAoB;AACzB;AAAA,IACF;AACA,SAAK,mBAAmB;AACxB,SAAK,2BAA2B;AAChC,SAAK,wBAAwB;AAI7B,SAAK,gBAAgB,KAAK,yBAAyB;AACnD,SAAK,cAAc,KAAK,eAAe;AACvC,SAAK,eAAe,KAAK,MAAM,sBAAsB;AACrD,SAAK,iBAAiB,KAAK,kBAAkB,oBAAoB,EAAE,sBAAsB;AACzF,UAAM,aAAa,KAAK;AACxB,UAAM,cAAc,KAAK;AACzB,UAAM,eAAe,KAAK;AAC1B,UAAM,gBAAgB,KAAK;AAE3B,UAAM,eAAe,CAAC;AAEtB,QAAI;AAGJ,aAAS,OAAO,KAAK,qBAAqB;AAExC,UAAI,cAAc,KAAK,gBAAgB,YAAY,eAAe,GAAG;AAIrE,UAAI,eAAe,KAAK,iBAAiB,aAAa,aAAa,GAAG;AAEtE,UAAI,aAAa,KAAK,eAAe,cAAc,aAAa,cAAc,GAAG;AAEjF,UAAI,WAAW,4BAA4B;AACzC,aAAK,YAAY;AACjB,aAAK,eAAe,KAAK,WAAW;AACpC;AAAA,MACF;AAGA,UAAI,KAAK,8BAA8B,YAAY,cAAc,YAAY,GAAG;AAG9E,qBAAa,KAAK;AAAA,UAChB,UAAU;AAAA,UACV,QAAQ;AAAA,UACR;AAAA,UACA,iBAAiB,KAAK,0BAA0B,aAAa,GAAG;AAAA,QAClE,CAAC;AACD;AAAA,MACF;AAIA,UAAI,CAAC,YAAY,SAAS,WAAW,cAAc,WAAW,aAAa;AACzE,mBAAW;AAAA,UACT;AAAA,UACA;AAAA,UACA;AAAA,UACA,UAAU;AAAA,UACV;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAGA,QAAI,aAAa,QAAQ;AACvB,UAAI,UAAU;AACd,UAAI,YAAY;AAChB,iBAAW,OAAO,cAAc;AAC9B,cAAM,QAAQ,IAAI,gBAAgB,QAAQ,IAAI,gBAAgB,UAAU,IAAI,SAAS,UAAU;AAC/F,YAAI,QAAQ,WAAW;AACrB,sBAAY;AACZ,oBAAU;AAAA,QACZ;AAAA,MACF;AACA,WAAK,YAAY;AACjB,WAAK,eAAe,QAAQ,UAAU,QAAQ,MAAM;AACpD;AAAA,IACF;AAGA,QAAI,KAAK,UAAU;AAEjB,WAAK,YAAY;AACjB,WAAK,eAAe,SAAS,UAAU,SAAS,WAAW;AAC3D;AAAA,IACF;AAGA,SAAK,eAAe,SAAS,UAAU,SAAS,WAAW;AAAA,EAC7D;AAAA,EACA,SAAS;AACP,SAAK,mBAAmB;AACxB,SAAK,gBAAgB;AACrB,SAAK,sBAAsB;AAC3B,SAAK,oBAAoB,YAAY;AAAA,EACvC;AAAA;AAAA,EAEA,UAAU;AACR,QAAI,KAAK,aAAa;AACpB;AAAA,IACF;AAGA,QAAI,KAAK,cAAc;AACrB,mBAAa,KAAK,aAAa,OAAO;AAAA,QACpC,KAAK;AAAA,QACL,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,gBAAgB;AAAA,MAClB,CAAC;AAAA,IACH;AACA,QAAI,KAAK,OAAO;AACd,WAAK,2BAA2B;AAAA,IAClC;AACA,QAAI,KAAK,aAAa;AACpB,WAAK,YAAY,YAAY,UAAU,OAAO,gBAAgB;AAAA,IAChE;AACA,SAAK,OAAO;AACZ,SAAK,iBAAiB,SAAS;AAC/B,SAAK,cAAc,KAAK,eAAe;AACvC,SAAK,cAAc;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,sBAAsB;AACpB,QAAI,KAAK,eAAe,CAAC,KAAK,UAAU,WAAW;AACjD;AAAA,IACF;AACA,UAAM,eAAe,KAAK;AAC1B,QAAI,cAAc;AAChB,WAAK,cAAc,KAAK,eAAe;AACvC,WAAK,eAAe,KAAK,MAAM,sBAAsB;AACrD,WAAK,gBAAgB,KAAK,yBAAyB;AACnD,WAAK,iBAAiB,KAAK,kBAAkB,oBAAoB,EAAE,sBAAsB;AACzF,YAAM,cAAc,KAAK,gBAAgB,KAAK,aAAa,KAAK,gBAAgB,YAAY;AAC5F,WAAK,eAAe,cAAc,WAAW;AAAA,IAC/C,OAAO;AACL,WAAK,MAAM;AAAA,IACb;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,yBAAyB,aAAa;AACpC,SAAK,eAAe;AACpB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc,WAAW;AACvB,SAAK,sBAAsB;AAG3B,QAAI,UAAU,QAAQ,KAAK,aAAa,MAAM,IAAI;AAChD,WAAK,gBAAgB;AAAA,IACvB;AACA,SAAK,mBAAmB;AACxB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB,QAAQ;AACzB,SAAK,kBAAkB;AACvB,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,uBAAuB,qBAAqB,MAAM;AAChD,SAAK,yBAAyB;AAC9B,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,kBAAkB,gBAAgB,MAAM;AACtC,SAAK,iBAAiB;AACtB,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,SAAS,UAAU,MAAM;AACvB,SAAK,WAAW;AAChB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,mBAAmB,WAAW,MAAM;AAClC,SAAK,kBAAkB;AACvB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,UAAU,QAAQ;AAChB,SAAK,UAAU;AACf,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB,QAAQ;AACzB,SAAK,WAAW;AAChB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB,QAAQ;AACzB,SAAK,WAAW;AAChB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,sBAAsB,UAAU;AAC9B,SAAK,2BAA2B;AAChC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,gBAAgB,YAAY,eAAe,KAAK;AAC9C,QAAI;AACJ,QAAI,IAAI,WAAW,UAAU;AAG3B,UAAI,WAAW,OAAO,WAAW,QAAQ;AAAA,IAC3C,OAAO;AACL,YAAM,SAAS,KAAK,OAAO,IAAI,WAAW,QAAQ,WAAW;AAC7D,YAAM,OAAO,KAAK,OAAO,IAAI,WAAW,OAAO,WAAW;AAC1D,UAAI,IAAI,WAAW,UAAU,SAAS;AAAA,IACxC;AAGA,QAAI,cAAc,OAAO,GAAG;AAC1B,WAAK,cAAc;AAAA,IACrB;AACA,QAAI;AACJ,QAAI,IAAI,WAAW,UAAU;AAC3B,UAAI,WAAW,MAAM,WAAW,SAAS;AAAA,IAC3C,OAAO;AACL,UAAI,IAAI,WAAW,QAAQ,WAAW,MAAM,WAAW;AAAA,IACzD;AAMA,QAAI,cAAc,MAAM,GAAG;AACzB,WAAK,cAAc;AAAA,IACrB;AACA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB,aAAa,aAAa,KAAK;AAG9C,QAAI;AACJ,QAAI,IAAI,YAAY,UAAU;AAC5B,sBAAgB,CAAC,YAAY,QAAQ;AAAA,IACvC,WAAW,IAAI,aAAa,SAAS;AACnC,sBAAgB,KAAK,OAAO,IAAI,CAAC,YAAY,QAAQ;AAAA,IACvD,OAAO;AACL,sBAAgB,KAAK,OAAO,IAAI,IAAI,CAAC,YAAY;AAAA,IACnD;AACA,QAAI;AACJ,QAAI,IAAI,YAAY,UAAU;AAC5B,sBAAgB,CAAC,YAAY,SAAS;AAAA,IACxC,OAAO;AACL,sBAAgB,IAAI,YAAY,QAAQ,IAAI,CAAC,YAAY;AAAA,IAC3D;AAEA,WAAO;AAAA,MACL,GAAG,YAAY,IAAI;AAAA,MACnB,GAAG,YAAY,IAAI;AAAA,IACrB;AAAA,EACF;AAAA;AAAA,EAEA,eAAe,OAAO,gBAAgB,UAAU,UAAU;AAGxD,UAAM,UAAU,6BAA6B,cAAc;AAC3D,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,UAAU,KAAK,WAAW,UAAU,GAAG;AAC3C,QAAI,UAAU,KAAK,WAAW,UAAU,GAAG;AAE3C,QAAI,SAAS;AACX,WAAK;AAAA,IACP;AACA,QAAI,SAAS;AACX,WAAK;AAAA,IACP;AAEA,QAAI,eAAe,IAAI;AACvB,QAAI,gBAAgB,IAAI,QAAQ,QAAQ,SAAS;AACjD,QAAI,cAAc,IAAI;AACtB,QAAI,iBAAiB,IAAI,QAAQ,SAAS,SAAS;AAEnD,QAAI,eAAe,KAAK,mBAAmB,QAAQ,OAAO,cAAc,aAAa;AACrF,QAAI,gBAAgB,KAAK,mBAAmB,QAAQ,QAAQ,aAAa,cAAc;AACvF,QAAI,cAAc,eAAe;AACjC,WAAO;AAAA,MACL;AAAA,MACA,4BAA4B,QAAQ,QAAQ,QAAQ,WAAW;AAAA,MAC/D,0BAA0B,kBAAkB,QAAQ;AAAA,MACpD,4BAA4B,gBAAgB,QAAQ;AAAA,IACtD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,8BAA8B,KAAK,OAAO,UAAU;AAClD,QAAI,KAAK,wBAAwB;AAC/B,YAAM,kBAAkB,SAAS,SAAS,MAAM;AAChD,YAAM,iBAAiB,SAAS,QAAQ,MAAM;AAC9C,YAAM,YAAY,cAAc,KAAK,YAAY,UAAU,EAAE,SAAS;AACtE,YAAM,WAAW,cAAc,KAAK,YAAY,UAAU,EAAE,QAAQ;AACpE,YAAM,cAAc,IAAI,4BAA4B,aAAa,QAAQ,aAAa;AACtF,YAAM,gBAAgB,IAAI,8BAA8B,YAAY,QAAQ,YAAY;AACxF,aAAO,eAAe;AAAA,IACxB;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,qBAAqB,OAAO,gBAAgB,gBAAgB;AAI1D,QAAI,KAAK,uBAAuB,KAAK,iBAAiB;AACpD,aAAO;AAAA,QACL,GAAG,MAAM,IAAI,KAAK,oBAAoB;AAAA,QACtC,GAAG,MAAM,IAAI,KAAK,oBAAoB;AAAA,MACxC;AAAA,IACF;AAGA,UAAM,UAAU,6BAA6B,cAAc;AAC3D,UAAM,WAAW,KAAK;AAGtB,UAAM,gBAAgB,KAAK,IAAI,MAAM,IAAI,QAAQ,QAAQ,SAAS,OAAO,CAAC;AAC1E,UAAM,iBAAiB,KAAK,IAAI,MAAM,IAAI,QAAQ,SAAS,SAAS,QAAQ,CAAC;AAC7E,UAAM,cAAc,KAAK,IAAI,SAAS,MAAM,eAAe,MAAM,MAAM,GAAG,CAAC;AAC3E,UAAM,eAAe,KAAK,IAAI,SAAS,OAAO,eAAe,OAAO,MAAM,GAAG,CAAC;AAE9E,QAAI,QAAQ;AACZ,QAAI,QAAQ;AAIZ,QAAI,QAAQ,SAAS,SAAS,OAAO;AACnC,cAAQ,gBAAgB,CAAC;AAAA,IAC3B,OAAO;AACL,cAAQ,MAAM,IAAI,KAAK,kBAAkB,SAAS,OAAO,eAAe,OAAO,MAAM,IAAI;AAAA,IAC3F;AACA,QAAI,QAAQ,UAAU,SAAS,QAAQ;AACrC,cAAQ,eAAe,CAAC;AAAA,IAC1B,OAAO;AACL,cAAQ,MAAM,IAAI,KAAK,kBAAkB,SAAS,MAAM,eAAe,MAAM,MAAM,IAAI;AAAA,IACzF;AACA,SAAK,sBAAsB;AAAA,MACzB,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AACA,WAAO;AAAA,MACL,GAAG,MAAM,IAAI;AAAA,MACb,GAAG,MAAM,IAAI;AAAA,IACf;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAe,UAAU,aAAa;AACpC,SAAK,oBAAoB,QAAQ;AACjC,SAAK,yBAAyB,aAAa,QAAQ;AACnD,SAAK,sBAAsB,aAAa,QAAQ;AAChD,QAAI,SAAS,YAAY;AACvB,WAAK,iBAAiB,SAAS,UAAU;AAAA,IAC3C;AAIA,QAAI,KAAK,iBAAiB,UAAU,QAAQ;AAC1C,YAAM,mBAAmB,KAAK,qBAAqB;AAGnD,UAAI,aAAa,KAAK,iBAAiB,CAAC,KAAK,yBAAyB,CAAC,wBAAwB,KAAK,uBAAuB,gBAAgB,GAAG;AAC5I,cAAM,cAAc,IAAI,+BAA+B,UAAU,gBAAgB;AACjF,aAAK,iBAAiB,KAAK,WAAW;AAAA,MACxC;AACA,WAAK,wBAAwB;AAAA,IAC/B;AAEA,SAAK,gBAAgB;AACrB,SAAK,mBAAmB;AAAA,EAC1B;AAAA;AAAA,EAEA,oBAAoB,UAAU;AAC5B,QAAI,CAAC,KAAK,0BAA0B;AAClC;AAAA,IACF;AACA,UAAM,WAAW,KAAK,aAAa,iBAAiB,KAAK,wBAAwB;AACjF,QAAI;AACJ,QAAI,UAAU,SAAS;AACvB,QAAI,SAAS,aAAa,UAAU;AAClC,gBAAU;AAAA,IACZ,WAAW,KAAK,OAAO,GAAG;AACxB,gBAAU,SAAS,aAAa,UAAU,UAAU;AAAA,IACtD,OAAO;AACL,gBAAU,SAAS,aAAa,UAAU,SAAS;AAAA,IACrD;AACA,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,eAAS,CAAC,EAAE,MAAM,kBAAkB,GAAG,OAAO,IAAI,OAAO;AAAA,IAC3D;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,0BAA0B,QAAQ,UAAU;AAC1C,UAAM,WAAW,KAAK;AACtB,UAAM,QAAQ,KAAK,OAAO;AAC1B,QAAI,QAAQ,KAAK;AACjB,QAAI,SAAS,aAAa,OAAO;AAE/B,YAAM,OAAO;AACb,eAAS,SAAS,SAAS,MAAM,KAAK;AAAA,IACxC,WAAW,SAAS,aAAa,UAAU;AAIzC,eAAS,SAAS,SAAS,OAAO,IAAI,KAAK,kBAAkB;AAC7D,eAAS,SAAS,SAAS,SAAS,KAAK;AAAA,IAC3C,OAAO;AAKL,YAAM,iCAAiC,KAAK,IAAI,SAAS,SAAS,OAAO,IAAI,SAAS,KAAK,OAAO,CAAC;AACnG,YAAM,iBAAiB,KAAK,qBAAqB;AACjD,eAAS,iCAAiC;AAC1C,YAAM,OAAO,IAAI;AACjB,UAAI,SAAS,kBAAkB,CAAC,KAAK,oBAAoB,CAAC,KAAK,gBAAgB;AAC7E,cAAM,OAAO,IAAI,iBAAiB;AAAA,MACpC;AAAA,IACF;AAEA,UAAM,+BAA+B,SAAS,aAAa,WAAW,CAAC,SAAS,SAAS,aAAa,SAAS;AAE/G,UAAM,8BAA8B,SAAS,aAAa,SAAS,CAAC,SAAS,SAAS,aAAa,WAAW;AAC9G,QAAI,OAAO,MAAM;AACjB,QAAI,6BAA6B;AAC/B,cAAQ,SAAS,QAAQ,OAAO,IAAI,KAAK,kBAAkB;AAC3D,cAAQ,OAAO,IAAI,KAAK;AAAA,IAC1B,WAAW,8BAA8B;AACvC,aAAO,OAAO;AACd,cAAQ,SAAS,QAAQ,OAAO;AAAA,IAClC,OAAO;AAKL,YAAM,iCAAiC,KAAK,IAAI,SAAS,QAAQ,OAAO,IAAI,SAAS,MAAM,OAAO,CAAC;AACnG,YAAM,gBAAgB,KAAK,qBAAqB;AAChD,cAAQ,iCAAiC;AACzC,aAAO,OAAO,IAAI;AAClB,UAAI,QAAQ,iBAAiB,CAAC,KAAK,oBAAoB,CAAC,KAAK,gBAAgB;AAC3E,eAAO,OAAO,IAAI,gBAAgB;AAAA,MACpC;AAAA,IACF;AACA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,sBAAsB,QAAQ,UAAU;AACtC,UAAM,kBAAkB,KAAK,0BAA0B,QAAQ,QAAQ;AAGvE,QAAI,CAAC,KAAK,oBAAoB,CAAC,KAAK,gBAAgB;AAClD,sBAAgB,SAAS,KAAK,IAAI,gBAAgB,QAAQ,KAAK,qBAAqB,MAAM;AAC1F,sBAAgB,QAAQ,KAAK,IAAI,gBAAgB,OAAO,KAAK,qBAAqB,KAAK;AAAA,IACzF;AACA,UAAM,SAAS,CAAC;AAChB,QAAI,KAAK,kBAAkB,GAAG;AAC5B,aAAO,MAAM,OAAO,OAAO;AAC3B,aAAO,SAAS,OAAO,QAAQ,OAAO,YAAY,OAAO,WAAW;AACpE,aAAO,QAAQ,OAAO,SAAS;AAAA,IACjC,OAAO;AACL,YAAM,YAAY,KAAK,YAAY,UAAU,EAAE;AAC/C,YAAM,WAAW,KAAK,YAAY,UAAU,EAAE;AAC9C,aAAO,SAAS,oBAAoB,gBAAgB,MAAM;AAC1D,aAAO,MAAM,oBAAoB,gBAAgB,GAAG;AACpD,aAAO,SAAS,oBAAoB,gBAAgB,MAAM;AAC1D,aAAO,QAAQ,oBAAoB,gBAAgB,KAAK;AACxD,aAAO,OAAO,oBAAoB,gBAAgB,IAAI;AACtD,aAAO,QAAQ,oBAAoB,gBAAgB,KAAK;AAExD,UAAI,SAAS,aAAa,UAAU;AAClC,eAAO,aAAa;AAAA,MACtB,OAAO;AACL,eAAO,aAAa,SAAS,aAAa,QAAQ,aAAa;AAAA,MACjE;AACA,UAAI,SAAS,aAAa,UAAU;AAClC,eAAO,iBAAiB;AAAA,MAC1B,OAAO;AACL,eAAO,iBAAiB,SAAS,aAAa,WAAW,aAAa;AAAA,MACxE;AACA,UAAI,WAAW;AACb,eAAO,YAAY,oBAAoB,SAAS;AAAA,MAClD;AACA,UAAI,UAAU;AACZ,eAAO,WAAW,oBAAoB,QAAQ;AAAA,MAChD;AAAA,IACF;AACA,SAAK,uBAAuB;AAC5B,iBAAa,KAAK,aAAa,OAAO,MAAM;AAAA,EAC9C;AAAA;AAAA,EAEA,0BAA0B;AACxB,iBAAa,KAAK,aAAa,OAAO;AAAA,MACpC,KAAK;AAAA,MACL,MAAM;AAAA,MACN,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,6BAA6B;AAC3B,iBAAa,KAAK,MAAM,OAAO;AAAA,MAC7B,KAAK;AAAA,MACL,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,UAAU;AAAA,MACV,WAAW;AAAA,IACb,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,yBAAyB,aAAa,UAAU;AAC9C,UAAM,SAAS,CAAC;AAChB,UAAM,mBAAmB,KAAK,kBAAkB;AAChD,UAAM,wBAAwB,KAAK;AACnC,UAAM,SAAS,KAAK,YAAY,UAAU;AAC1C,QAAI,kBAAkB;AACpB,YAAM,iBAAiB,KAAK,eAAe,0BAA0B;AACrE,mBAAa,QAAQ,KAAK,kBAAkB,UAAU,aAAa,cAAc,CAAC;AAClF,mBAAa,QAAQ,KAAK,kBAAkB,UAAU,aAAa,cAAc,CAAC;AAAA,IACpF,OAAO;AACL,aAAO,WAAW;AAAA,IACpB;AAMA,QAAI,kBAAkB;AACtB,QAAI,UAAU,KAAK,WAAW,UAAU,GAAG;AAC3C,QAAI,UAAU,KAAK,WAAW,UAAU,GAAG;AAC3C,QAAI,SAAS;AACX,yBAAmB,cAAc,OAAO;AAAA,IAC1C;AACA,QAAI,SAAS;AACX,yBAAmB,cAAc,OAAO;AAAA,IAC1C;AACA,WAAO,YAAY,gBAAgB,KAAK;AAMxC,QAAI,OAAO,WAAW;AACpB,UAAI,kBAAkB;AACpB,eAAO,YAAY,oBAAoB,OAAO,SAAS;AAAA,MACzD,WAAW,uBAAuB;AAChC,eAAO,YAAY;AAAA,MACrB;AAAA,IACF;AACA,QAAI,OAAO,UAAU;AACnB,UAAI,kBAAkB;AACpB,eAAO,WAAW,oBAAoB,OAAO,QAAQ;AAAA,MACvD,WAAW,uBAAuB;AAChC,eAAO,WAAW;AAAA,MACpB;AAAA,IACF;AACA,iBAAa,KAAK,MAAM,OAAO,MAAM;AAAA,EACvC;AAAA;AAAA,EAEA,kBAAkB,UAAU,aAAa,gBAAgB;AAGvD,QAAI,SAAS;AAAA,MACX,KAAK;AAAA,MACL,QAAQ;AAAA,IACV;AACA,QAAI,eAAe,KAAK,iBAAiB,aAAa,KAAK,cAAc,QAAQ;AACjF,QAAI,KAAK,WAAW;AAClB,qBAAe,KAAK,qBAAqB,cAAc,KAAK,cAAc,cAAc;AAAA,IAC1F;AAGA,QAAI,SAAS,aAAa,UAAU;AAGlC,YAAM,iBAAiB,KAAK,UAAU,gBAAgB;AACtD,aAAO,SAAS,GAAG,kBAAkB,aAAa,IAAI,KAAK,aAAa,OAAO;AAAA,IACjF,OAAO;AACL,aAAO,MAAM,oBAAoB,aAAa,CAAC;AAAA,IACjD;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,kBAAkB,UAAU,aAAa,gBAAgB;AAGvD,QAAI,SAAS;AAAA,MACX,MAAM;AAAA,MACN,OAAO;AAAA,IACT;AACA,QAAI,eAAe,KAAK,iBAAiB,aAAa,KAAK,cAAc,QAAQ;AACjF,QAAI,KAAK,WAAW;AAClB,qBAAe,KAAK,qBAAqB,cAAc,KAAK,cAAc,cAAc;AAAA,IAC1F;AAKA,QAAI;AACJ,QAAI,KAAK,OAAO,GAAG;AACjB,gCAA0B,SAAS,aAAa,QAAQ,SAAS;AAAA,IACnE,OAAO;AACL,gCAA0B,SAAS,aAAa,QAAQ,UAAU;AAAA,IACpE;AAGA,QAAI,4BAA4B,SAAS;AACvC,YAAM,gBAAgB,KAAK,UAAU,gBAAgB;AACrD,aAAO,QAAQ,GAAG,iBAAiB,aAAa,IAAI,KAAK,aAAa,MAAM;AAAA,IAC9E,OAAO;AACL,aAAO,OAAO,oBAAoB,aAAa,CAAC;AAAA,IAClD;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAuB;AAErB,UAAM,eAAe,KAAK,eAAe;AACzC,UAAM,gBAAgB,KAAK,MAAM,sBAAsB;AAIvD,UAAM,wBAAwB,KAAK,aAAa,IAAI,gBAAc;AAChE,aAAO,WAAW,cAAc,EAAE,cAAc,sBAAsB;AAAA,IACxE,CAAC;AACD,WAAO;AAAA,MACL,iBAAiB,4BAA4B,cAAc,qBAAqB;AAAA,MAChF,qBAAqB,6BAA6B,cAAc,qBAAqB;AAAA,MACrF,kBAAkB,4BAA4B,eAAe,qBAAqB;AAAA,MAClF,sBAAsB,6BAA6B,eAAe,qBAAqB;AAAA,IACzF;AAAA,EACF;AAAA;AAAA,EAEA,mBAAmB,WAAW,WAAW;AACvC,WAAO,UAAU,OAAO,CAAC,cAAc,oBAAoB;AACzD,aAAO,eAAe,KAAK,IAAI,iBAAiB,CAAC;AAAA,IACnD,GAAG,MAAM;AAAA,EACX;AAAA;AAAA,EAEA,2BAA2B;AAMzB,UAAM,QAAQ,KAAK,UAAU,gBAAgB;AAC7C,UAAM,SAAS,KAAK,UAAU,gBAAgB;AAC9C,UAAM,iBAAiB,KAAK,eAAe,0BAA0B;AACrE,WAAO;AAAA,MACL,KAAK,eAAe,MAAM,KAAK;AAAA,MAC/B,MAAM,eAAe,OAAO,KAAK;AAAA,MACjC,OAAO,eAAe,OAAO,QAAQ,KAAK;AAAA,MAC1C,QAAQ,eAAe,MAAM,SAAS,KAAK;AAAA,MAC3C,OAAO,QAAQ,IAAI,KAAK;AAAA,MACxB,QAAQ,SAAS,IAAI,KAAK;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA,EAEA,SAAS;AACP,WAAO,KAAK,YAAY,aAAa,MAAM;AAAA,EAC7C;AAAA;AAAA,EAEA,oBAAoB;AAClB,WAAO,CAAC,KAAK,0BAA0B,KAAK;AAAA,EAC9C;AAAA;AAAA,EAEA,WAAW,UAAU,MAAM;AACzB,QAAI,SAAS,KAAK;AAGhB,aAAO,SAAS,WAAW,OAAO,KAAK,WAAW,SAAS;AAAA,IAC7D;AACA,WAAO,SAAS,WAAW,OAAO,KAAK,WAAW,SAAS;AAAA,EAC7D;AAAA;AAAA,EAEA,qBAAqB;AACnB,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,UAAI,CAAC,KAAK,oBAAoB,QAAQ;AACpC,cAAM,MAAM,uEAAuE;AAAA,MACrF;AAGA,WAAK,oBAAoB,QAAQ,UAAQ;AACvC,mCAA2B,WAAW,KAAK,OAAO;AAClD,iCAAyB,WAAW,KAAK,OAAO;AAChD,mCAA2B,YAAY,KAAK,QAAQ;AACpD,iCAAyB,YAAY,KAAK,QAAQ;AAAA,MACpD,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA,EAEA,iBAAiB,YAAY;AAC3B,QAAI,KAAK,OAAO;AACd,kBAAY,UAAU,EAAE,QAAQ,cAAY;AAC1C,YAAI,aAAa,MAAM,KAAK,qBAAqB,QAAQ,QAAQ,MAAM,IAAI;AACzE,eAAK,qBAAqB,KAAK,QAAQ;AACvC,eAAK,MAAM,UAAU,IAAI,QAAQ;AAAA,QACnC;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA,EAEA,qBAAqB;AACnB,QAAI,KAAK,OAAO;AACd,WAAK,qBAAqB,QAAQ,cAAY;AAC5C,aAAK,MAAM,UAAU,OAAO,QAAQ;AAAA,MACtC,CAAC;AACD,WAAK,uBAAuB,CAAC;AAAA,IAC/B;AAAA,EACF;AAAA;AAAA,EAEA,iBAAiB;AACf,UAAM,SAAS,KAAK;AACpB,QAAI,kBAAkB,YAAY;AAChC,aAAO,OAAO,cAAc,sBAAsB;AAAA,IACpD;AAEA,QAAI,kBAAkB,SAAS;AAC7B,aAAO,OAAO,sBAAsB;AAAA,IACtC;AACA,UAAM,QAAQ,OAAO,SAAS;AAC9B,UAAM,SAAS,OAAO,UAAU;AAEhC,WAAO;AAAA,MACL,KAAK,OAAO;AAAA,MACZ,QAAQ,OAAO,IAAI;AAAA,MACnB,MAAM,OAAO;AAAA,MACb,OAAO,OAAO,IAAI;AAAA,MAClB;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;AAEA,SAAS,aAAa,aAAa,QAAQ;AACzC,WAAS,OAAO,QAAQ;AACtB,QAAI,OAAO,eAAe,GAAG,GAAG;AAC9B,kBAAY,GAAG,IAAI,OAAO,GAAG;AAAA,IAC/B;AAAA,EACF;AACA,SAAO;AACT;AAKA,SAAS,cAAc,OAAO;AAC5B,MAAI,OAAO,UAAU,YAAY,SAAS,MAAM;AAC9C,UAAM,CAAC,OAAO,KAAK,IAAI,MAAM,MAAM,cAAc;AACjD,WAAO,CAAC,SAAS,UAAU,OAAO,WAAW,KAAK,IAAI;AAAA,EACxD;AACA,SAAO,SAAS;AAClB;AAOA,SAAS,6BAA6B,YAAY;AAChD,SAAO;AAAA,IACL,KAAK,KAAK,MAAM,WAAW,GAAG;AAAA,IAC9B,OAAO,KAAK,MAAM,WAAW,KAAK;AAAA,IAClC,QAAQ,KAAK,MAAM,WAAW,MAAM;AAAA,IACpC,MAAM,KAAK,MAAM,WAAW,IAAI;AAAA,IAChC,OAAO,KAAK,MAAM,WAAW,KAAK;AAAA,IAClC,QAAQ,KAAK,MAAM,WAAW,MAAM;AAAA,EACtC;AACF;AAEA,SAAS,wBAAwB,GAAG,GAAG;AACrC,MAAI,MAAM,GAAG;AACX,WAAO;AAAA,EACT;AACA,SAAO,EAAE,oBAAoB,EAAE,mBAAmB,EAAE,wBAAwB,EAAE,uBAAuB,EAAE,qBAAqB,EAAE,oBAAoB,EAAE,yBAAyB,EAAE;AACjL;AA6CA,IAAM,eAAe;AAOrB,IAAM,yBAAN,MAA6B;AAAA,EAC3B,cAAc;AACZ,SAAK,eAAe;AACpB,SAAK,aAAa;AAClB,SAAK,gBAAgB;AACrB,SAAK,cAAc;AACnB,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,SAAS;AACd,SAAK,UAAU;AACf,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,OAAO,YAAY;AACjB,UAAM,SAAS,WAAW,UAAU;AACpC,SAAK,cAAc;AACnB,QAAI,KAAK,UAAU,CAAC,OAAO,OAAO;AAChC,iBAAW,WAAW;AAAA,QACpB,OAAO,KAAK;AAAA,MACd,CAAC;AAAA,IACH;AACA,QAAI,KAAK,WAAW,CAAC,OAAO,QAAQ;AAClC,iBAAW,WAAW;AAAA,QACpB,QAAQ,KAAK;AAAA,MACf,CAAC;AAAA,IACH;AACA,eAAW,YAAY,UAAU,IAAI,YAAY;AACjD,SAAK,cAAc;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,QAAQ,IAAI;AACd,SAAK,gBAAgB;AACrB,SAAK,aAAa;AAClB,SAAK,cAAc;AACnB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,KAAK,QAAQ,IAAI;AACf,SAAK,WAAW;AAChB,SAAK,aAAa;AAClB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,QAAQ,IAAI;AACjB,SAAK,aAAa;AAClB,SAAK,gBAAgB;AACrB,SAAK,cAAc;AACnB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,QAAQ,IAAI;AAChB,SAAK,WAAW;AAChB,SAAK,aAAa;AAClB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,QAAQ,IAAI;AAChB,SAAK,WAAW;AAChB,SAAK,aAAa;AAClB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,QAAQ,IAAI;AACd,SAAK,WAAW;AAChB,SAAK,aAAa;AAClB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,QAAQ,IAAI;AAChB,QAAI,KAAK,aAAa;AACpB,WAAK,YAAY,WAAW;AAAA,QAC1B,OAAO;AAAA,MACT,CAAC;AAAA,IACH,OAAO;AACL,WAAK,SAAS;AAAA,IAChB;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,QAAQ,IAAI;AACjB,QAAI,KAAK,aAAa;AACpB,WAAK,YAAY,WAAW;AAAA,QAC1B,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,OAAO;AACL,WAAK,UAAU;AAAA,IACjB;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,mBAAmB,SAAS,IAAI;AAC9B,SAAK,KAAK,MAAM;AAChB,SAAK,aAAa;AAClB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,iBAAiB,SAAS,IAAI;AAC5B,SAAK,IAAI,MAAM;AACf,SAAK,cAAc;AACnB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ;AAIN,QAAI,CAAC,KAAK,eAAe,CAAC,KAAK,YAAY,YAAY,GAAG;AACxD;AAAA,IACF;AACA,UAAM,SAAS,KAAK,YAAY,eAAe;AAC/C,UAAM,eAAe,KAAK,YAAY,YAAY;AAClD,UAAM,SAAS,KAAK,YAAY,UAAU;AAC1C,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,6BAA6B,UAAU,UAAU,UAAU,aAAa,CAAC,YAAY,aAAa,UAAU,aAAa;AAC/H,UAAM,2BAA2B,WAAW,UAAU,WAAW,aAAa,CAAC,aAAa,cAAc,UAAU,cAAc;AAClI,UAAM,YAAY,KAAK;AACvB,UAAM,UAAU,KAAK;AACrB,UAAM,QAAQ,KAAK,YAAY,UAAU,EAAE,cAAc;AACzD,QAAI,aAAa;AACjB,QAAI,cAAc;AAClB,QAAI,iBAAiB;AACrB,QAAI,2BAA2B;AAC7B,uBAAiB;AAAA,IACnB,WAAW,cAAc,UAAU;AACjC,uBAAiB;AACjB,UAAI,OAAO;AACT,sBAAc;AAAA,MAChB,OAAO;AACL,qBAAa;AAAA,MACf;AAAA,IACF,WAAW,OAAO;AAChB,UAAI,cAAc,UAAU,cAAc,OAAO;AAC/C,yBAAiB;AACjB,qBAAa;AAAA,MACf,WAAW,cAAc,WAAW,cAAc,SAAS;AACzD,yBAAiB;AACjB,sBAAc;AAAA,MAChB;AAAA,IACF,WAAW,cAAc,UAAU,cAAc,SAAS;AACxD,uBAAiB;AACjB,mBAAa;AAAA,IACf,WAAW,cAAc,WAAW,cAAc,OAAO;AACvD,uBAAiB;AACjB,oBAAc;AAAA,IAChB;AACA,WAAO,WAAW,KAAK;AACvB,WAAO,aAAa,4BAA4B,MAAM;AACtD,WAAO,YAAY,0BAA0B,MAAM,KAAK;AACxD,WAAO,eAAe,KAAK;AAC3B,WAAO,cAAc,4BAA4B,MAAM;AACvD,iBAAa,iBAAiB;AAC9B,iBAAa,aAAa,0BAA0B,eAAe,KAAK;AAAA,EAC1E;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AACR,QAAI,KAAK,eAAe,CAAC,KAAK,aAAa;AACzC;AAAA,IACF;AACA,UAAM,SAAS,KAAK,YAAY,eAAe;AAC/C,UAAM,SAAS,KAAK,YAAY;AAChC,UAAM,eAAe,OAAO;AAC5B,WAAO,UAAU,OAAO,YAAY;AACpC,iBAAa,iBAAiB,aAAa,aAAa,OAAO,YAAY,OAAO,eAAe,OAAO,aAAa,OAAO,cAAc,OAAO,WAAW;AAC5J,SAAK,cAAc;AACnB,SAAK,cAAc;AAAA,EACrB;AACF;AAGA,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B,YAAY,gBAAgB,WAAW,WAAW,mBAAmB;AACnE,SAAK,iBAAiB;AACtB,SAAK,YAAY;AACjB,SAAK,YAAY;AACjB,SAAK,oBAAoB;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS;AACP,WAAO,IAAI,uBAAuB;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB,QAAQ;AAC1B,WAAO,IAAI,kCAAkC,QAAQ,KAAK,gBAAgB,KAAK,WAAW,KAAK,WAAW,KAAK,iBAAiB;AAAA,EAClI;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,+BAA+B,mBAAmB;AACrE,aAAO,KAAK,qBAAqB,yBAA2B,SAAY,aAAa,GAAM,SAAS,QAAQ,GAAM,SAAc,QAAQ,GAAM,SAAS,gBAAgB,CAAC;AAAA,IAC1K;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,wBAAuB;AAAA,MAChC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAM;AAAA,EACR,CAAC,GAAG,IAAI;AACV,GAAG;AAGH,IAAI,eAAe;AAWnB,IAAM,UAAN,MAAM,SAAQ;AAAA,EACZ,YACA,kBAAkB,mBAAmB,2BAA2B,kBAAkB,qBAAqB,WAAW,SAAS,WAAW,iBAAiB,WAAW,yBAAyB,uBAAuB;AAChN,SAAK,mBAAmB;AACxB,SAAK,oBAAoB;AACzB,SAAK,4BAA4B;AACjC,SAAK,mBAAmB;AACxB,SAAK,sBAAsB;AAC3B,SAAK,YAAY;AACjB,SAAK,UAAU;AACf,SAAK,YAAY;AACjB,SAAK,kBAAkB;AACvB,SAAK,YAAY;AACjB,SAAK,0BAA0B;AAC/B,SAAK,wBAAwB;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,QAAQ;AACb,UAAM,OAAO,KAAK,mBAAmB;AACrC,UAAM,OAAO,KAAK,mBAAmB,IAAI;AACzC,UAAM,eAAe,KAAK,oBAAoB,IAAI;AAClD,UAAM,gBAAgB,IAAI,cAAc,MAAM;AAC9C,kBAAc,YAAY,cAAc,aAAa,KAAK,gBAAgB;AAC1E,WAAO,IAAI,WAAW,cAAc,MAAM,MAAM,eAAe,KAAK,SAAS,KAAK,qBAAqB,KAAK,WAAW,KAAK,WAAW,KAAK,yBAAyB,KAAK,0BAA0B,kBAAkB,KAAK,UAAU,IAAI,mBAAmB,CAAC;AAAA,EAC/P;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB,MAAM;AACvB,UAAM,OAAO,KAAK,UAAU,cAAc,KAAK;AAC/C,SAAK,KAAK,eAAe,cAAc;AACvC,SAAK,UAAU,IAAI,kBAAkB;AACrC,SAAK,YAAY,IAAI;AACrB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,qBAAqB;AACnB,UAAM,OAAO,KAAK,UAAU,cAAc,KAAK;AAC/C,SAAK,kBAAkB,oBAAoB,EAAE,YAAY,IAAI;AAC7D,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,oBAAoB,MAAM;AAGxB,QAAI,CAAC,KAAK,SAAS;AACjB,WAAK,UAAU,KAAK,UAAU,IAAI,cAAc;AAAA,IAClD;AACA,WAAO,IAAI,gBAAgB,MAAM,KAAK,2BAA2B,KAAK,SAAS,KAAK,WAAW,KAAK,SAAS;AAAA,EAC/G;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,gBAAgB,mBAAmB;AACtD,aAAO,KAAK,qBAAqB,UAAY,SAAS,qBAAqB,GAAM,SAAS,gBAAgB,GAAM,SAAY,0BAAwB,GAAM,SAAS,sBAAsB,GAAM,SAAS,yBAAyB,GAAM,SAAY,QAAQ,GAAM,SAAY,MAAM,GAAM,SAAS,QAAQ,GAAM,SAAY,cAAc,GAAM,SAAY,QAAQ,GAAM,SAAS,6BAA6B,GAAM,SAAS,uBAAuB,CAAC,CAAC;AAAA,IAC1b;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,SAAQ;AAAA,MACjB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,SAAS,CAAC;AAAA,IAChF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AAGH,IAAM,sBAAsB,CAAC;AAAA,EAC3B,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AACZ,GAAG;AAAA,EACD,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AACZ,GAAG;AAAA,EACD,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AACZ,GAAG;AAAA,EACD,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AACZ,CAAC;AAED,IAAM,wCAAwC,IAAI,eAAe,yCAAyC;AAAA,EACxG,YAAY;AAAA,EACZ,SAAS,MAAM;AACb,UAAM,UAAU,OAAO,OAAO;AAC9B,WAAO,MAAM,QAAQ,iBAAiB,WAAW;AAAA,EACnD;AACF,CAAC;AAKD,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,YACA,YAAY;AACV,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,mBAAmB;AAC/D,aAAO,KAAK,qBAAqB,mBAAqB,kBAAqB,UAAU,CAAC;AAAA,IACxF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,sBAAsB,EAAE,GAAG,CAAC,IAAI,kBAAkB,EAAE,GAAG,CAAC,IAAI,oBAAoB,EAAE,CAAC;AAAA,MACpG,UAAU,CAAC,kBAAkB;AAAA,MAC7B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AAKH,IAAM,sBAAN,MAAM,qBAAoB;AAAA;AAAA,EAExB,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,SAAS;AACnB,SAAK,WAAW;AAChB,QAAI,KAAK,WAAW;AAClB,WAAK,wBAAwB,KAAK,SAAS;AAAA,IAC7C;AAAA,EACF;AAAA;AAAA,EAEA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,SAAS;AACnB,SAAK,WAAW;AAChB,QAAI,KAAK,WAAW;AAClB,WAAK,wBAAwB,KAAK,SAAS;AAAA,IAC7C;AAAA,EACF;AAAA;AAAA,EAEA,IAAI,sBAAsB;AACxB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,oBAAoB,OAAO;AAC7B,SAAK,uBAAuB;AAAA,EAC9B;AAAA;AAAA,EAEA,YAAY,UAAU,aAAa,kBAAkB,uBAAuB,MAAM;AAChF,SAAK,WAAW;AAChB,SAAK,OAAO;AACZ,SAAK,wBAAwB,0BAAa;AAC1C,SAAK,sBAAsB,0BAAa;AACxC,SAAK,sBAAsB,0BAAa;AACxC,SAAK,wBAAwB,0BAAa;AAC1C,SAAK,uBAAuB;AAC5B,SAAK,UAAU,OAAO,MAAM;AAE5B,SAAK,iBAAiB;AAEtB,SAAK,OAAO;AAEZ,SAAK,eAAe;AAEpB,SAAK,cAAc;AAEnB,SAAK,eAAe;AAEpB,SAAK,qBAAqB;AAE1B,SAAK,gBAAgB;AAErB,SAAK,OAAO;AAEZ,SAAK,gBAAgB,IAAI,aAAa;AAEtC,SAAK,iBAAiB,IAAI,aAAa;AAEvC,SAAK,SAAS,IAAI,aAAa;AAE/B,SAAK,SAAS,IAAI,aAAa;AAE/B,SAAK,iBAAiB,IAAI,aAAa;AAEvC,SAAK,sBAAsB,IAAI,aAAa;AAC5C,SAAK,kBAAkB,IAAI,eAAe,aAAa,gBAAgB;AACvE,SAAK,yBAAyB;AAC9B,SAAK,iBAAiB,KAAK,uBAAuB;AAAA,EACpD;AAAA;AAAA,EAEA,IAAI,aAAa;AACf,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,IAAI,MAAM;AACR,WAAO,KAAK,OAAO,KAAK,KAAK,QAAQ;AAAA,EACvC;AAAA,EACA,cAAc;AACZ,SAAK,oBAAoB,YAAY;AACrC,SAAK,oBAAoB,YAAY;AACrC,SAAK,sBAAsB,YAAY;AACvC,SAAK,sBAAsB,YAAY;AACvC,QAAI,KAAK,aAAa;AACpB,WAAK,YAAY,QAAQ;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,KAAK,WAAW;AAClB,WAAK,wBAAwB,KAAK,SAAS;AAC3C,WAAK,YAAY,WAAW;AAAA,QAC1B,OAAO,KAAK;AAAA,QACZ,UAAU,KAAK;AAAA,QACf,QAAQ,KAAK;AAAA,QACb,WAAW,KAAK;AAAA,MAClB,CAAC;AACD,UAAI,QAAQ,QAAQ,KAAK,KAAK,MAAM;AAClC,aAAK,UAAU,MAAM;AAAA,MACvB;AAAA,IACF;AACA,QAAI,QAAQ,MAAM,GAAG;AACnB,WAAK,OAAO,KAAK,eAAe,IAAI,KAAK,eAAe;AAAA,IAC1D;AAAA,EACF;AAAA;AAAA,EAEA,iBAAiB;AACf,QAAI,CAAC,KAAK,aAAa,CAAC,KAAK,UAAU,QAAQ;AAC7C,WAAK,YAAY;AAAA,IACnB;AACA,UAAM,aAAa,KAAK,cAAc,KAAK,SAAS,OAAO,KAAK,aAAa,CAAC;AAC9E,SAAK,sBAAsB,WAAW,YAAY,EAAE,UAAU,MAAM,KAAK,OAAO,KAAK,CAAC;AACtF,SAAK,sBAAsB,WAAW,YAAY,EAAE,UAAU,MAAM,KAAK,OAAO,KAAK,CAAC;AACtF,eAAW,cAAc,EAAE,UAAU,WAAS;AAC5C,WAAK,eAAe,KAAK,KAAK;AAC9B,UAAI,MAAM,YAAY,UAAU,CAAC,KAAK,gBAAgB,CAAC,eAAe,KAAK,GAAG;AAC5E,cAAM,eAAe;AACrB,aAAK,eAAe;AAAA,MACtB;AAAA,IACF,CAAC;AACD,SAAK,YAAY,qBAAqB,EAAE,UAAU,WAAS;AACzD,YAAM,SAAS,KAAK,kBAAkB;AACtC,YAAM,SAAS,gBAAgB,KAAK;AACpC,UAAI,CAAC,UAAU,WAAW,UAAU,CAAC,OAAO,SAAS,MAAM,GAAG;AAC5D,aAAK,oBAAoB,KAAK,KAAK;AAAA,MACrC;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,eAAe;AACb,UAAM,mBAAmB,KAAK,YAAY,KAAK,oBAAoB,KAAK,wBAAwB;AAChG,UAAM,gBAAgB,IAAI,cAAc;AAAA,MACtC,WAAW,KAAK;AAAA,MAChB;AAAA,MACA,gBAAgB,KAAK;AAAA,MACrB,aAAa,KAAK;AAAA,MAClB,qBAAqB,KAAK;AAAA,IAC5B,CAAC;AACD,QAAI,KAAK,SAAS,KAAK,UAAU,GAAG;AAClC,oBAAc,QAAQ,KAAK;AAAA,IAC7B;AACA,QAAI,KAAK,UAAU,KAAK,WAAW,GAAG;AACpC,oBAAc,SAAS,KAAK;AAAA,IAC9B;AACA,QAAI,KAAK,YAAY,KAAK,aAAa,GAAG;AACxC,oBAAc,WAAW,KAAK;AAAA,IAChC;AACA,QAAI,KAAK,aAAa,KAAK,cAAc,GAAG;AAC1C,oBAAc,YAAY,KAAK;AAAA,IACjC;AACA,QAAI,KAAK,eAAe;AACtB,oBAAc,gBAAgB,KAAK;AAAA,IACrC;AACA,QAAI,KAAK,YAAY;AACnB,oBAAc,aAAa,KAAK;AAAA,IAClC;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,wBAAwB,kBAAkB;AACxC,UAAM,YAAY,KAAK,UAAU,IAAI,sBAAoB;AAAA,MACvD,SAAS,gBAAgB;AAAA,MACzB,SAAS,gBAAgB;AAAA,MACzB,UAAU,gBAAgB;AAAA,MAC1B,UAAU,gBAAgB;AAAA,MAC1B,SAAS,gBAAgB,WAAW,KAAK;AAAA,MACzC,SAAS,gBAAgB,WAAW,KAAK;AAAA,MACzC,YAAY,gBAAgB,cAAc;AAAA,IAC5C,EAAE;AACF,WAAO,iBAAiB,UAAU,KAAK,WAAW,CAAC,EAAE,cAAc,SAAS,EAAE,uBAAuB,KAAK,kBAAkB,EAAE,SAAS,KAAK,IAAI,EAAE,kBAAkB,KAAK,aAAa,EAAE,mBAAmB,KAAK,cAAc,EAAE,mBAAmB,KAAK,YAAY,EAAE,sBAAsB,KAAK,uBAAuB;AAAA,EAC1T;AAAA;AAAA,EAEA,0BAA0B;AACxB,UAAM,WAAW,KAAK,SAAS,SAAS,EAAE,oBAAoB,KAAK,WAAW,CAAC;AAC/E,SAAK,wBAAwB,QAAQ;AACrC,WAAO;AAAA,EACT;AAAA,EACA,aAAa;AACX,QAAI,KAAK,kBAAkB,kBAAkB;AAC3C,aAAO,KAAK,OAAO;AAAA,IACrB,OAAO;AACL,aAAO,KAAK;AAAA,IACd;AAAA,EACF;AAAA,EACA,oBAAoB;AAClB,QAAI,KAAK,kBAAkB,kBAAkB;AAC3C,aAAO,KAAK,OAAO,WAAW;AAAA,IAChC;AACA,QAAI,KAAK,kBAAkB,YAAY;AACrC,aAAO,KAAK,OAAO;AAAA,IACrB;AACA,QAAI,OAAO,YAAY,eAAe,KAAK,kBAAkB,SAAS;AACpE,aAAO,KAAK;AAAA,IACd;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,iBAAiB;AACf,QAAI,CAAC,KAAK,aAAa;AACrB,WAAK,eAAe;AAAA,IACtB,OAAO;AAEL,WAAK,YAAY,UAAU,EAAE,cAAc,KAAK;AAAA,IAClD;AACA,QAAI,CAAC,KAAK,YAAY,YAAY,GAAG;AACnC,WAAK,YAAY,OAAO,KAAK,eAAe;AAAA,IAC9C;AACA,QAAI,KAAK,aAAa;AACpB,WAAK,wBAAwB,KAAK,YAAY,cAAc,EAAE,UAAU,WAAS;AAC/E,aAAK,cAAc,KAAK,KAAK;AAAA,MAC/B,CAAC;AAAA,IACH,OAAO;AACL,WAAK,sBAAsB,YAAY;AAAA,IACzC;AACA,SAAK,sBAAsB,YAAY;AAGvC,QAAI,KAAK,eAAe,UAAU,SAAS,GAAG;AAC5C,WAAK,wBAAwB,KAAK,UAAU,gBAAgB,SAAK,6BAAU,MAAM,KAAK,eAAe,UAAU,SAAS,CAAC,CAAC,EAAE,UAAU,cAAY;AAChJ,aAAK,QAAQ,IAAI,MAAM,KAAK,eAAe,KAAK,QAAQ,CAAC;AACzD,YAAI,KAAK,eAAe,UAAU,WAAW,GAAG;AAC9C,eAAK,sBAAsB,YAAY;AAAA,QACzC;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA,EAEA,iBAAiB;AACf,QAAI,KAAK,aAAa;AACpB,WAAK,YAAY,OAAO;AAAA,IAC1B;AACA,SAAK,sBAAsB,YAAY;AACvC,SAAK,sBAAsB,YAAY;AAAA,EACzC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,4BAA4B,mBAAmB;AAClE,aAAO,KAAK,qBAAqB,sBAAwB,kBAAkB,OAAO,GAAM,kBAAqB,WAAW,GAAM,kBAAqB,gBAAgB,GAAM,kBAAkB,qCAAqC,GAAM,kBAAqB,gBAAgB,CAAC,CAAC;AAAA,IAC/Q;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,yBAAyB,EAAE,GAAG,CAAC,IAAI,qBAAqB,EAAE,GAAG,CAAC,IAAI,uBAAuB,EAAE,CAAC;AAAA,MAC7G,QAAQ;AAAA,QACN,QAAQ,CAAC,GAAG,6BAA6B,QAAQ;AAAA,QACjD,WAAW,CAAC,GAAG,gCAAgC,WAAW;AAAA,QAC1D,kBAAkB,CAAC,GAAG,uCAAuC,kBAAkB;AAAA,QAC/E,SAAS,CAAC,GAAG,8BAA8B,SAAS;AAAA,QACpD,SAAS,CAAC,GAAG,8BAA8B,SAAS;AAAA,QACpD,OAAO,CAAC,GAAG,4BAA4B,OAAO;AAAA,QAC9C,QAAQ,CAAC,GAAG,6BAA6B,QAAQ;AAAA,QACjD,UAAU,CAAC,GAAG,+BAA+B,UAAU;AAAA,QACvD,WAAW,CAAC,GAAG,gCAAgC,WAAW;AAAA,QAC1D,eAAe,CAAC,GAAG,oCAAoC,eAAe;AAAA,QACtE,YAAY,CAAC,GAAG,iCAAiC,YAAY;AAAA,QAC7D,gBAAgB,CAAC,GAAG,qCAAqC,gBAAgB;AAAA,QACzE,gBAAgB,CAAC,GAAG,qCAAqC,gBAAgB;AAAA,QACzE,MAAM,CAAC,GAAG,2BAA2B,MAAM;AAAA,QAC3C,cAAc,CAAC,GAAG,mCAAmC,cAAc;AAAA,QACnE,yBAAyB,CAAC,GAAG,wCAAwC,yBAAyB;AAAA,QAC9F,aAAa,CAAC,GAAG,kCAAkC,eAAe,gBAAgB;AAAA,QAClF,cAAc,CAAC,GAAG,mCAAmC,gBAAgB,gBAAgB;AAAA,QACrF,oBAAoB,CAAC,GAAG,yCAAyC,sBAAsB,gBAAgB;AAAA,QACvG,eAAe,CAAC,GAAG,oCAAoC,iBAAiB,gBAAgB;AAAA,QACxF,MAAM,CAAC,GAAG,2BAA2B,QAAQ,gBAAgB;AAAA,QAC7D,qBAAqB,CAAC,GAAG,0CAA0C,uBAAuB,gBAAgB;AAAA,MAC5G;AAAA,MACA,SAAS;AAAA,QACP,eAAe;AAAA,QACf,gBAAgB;AAAA,QAChB,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,gBAAgB;AAAA,QAChB,qBAAqB;AAAA,MACvB;AAAA,MACA,UAAU,CAAC,qBAAqB;AAAA,MAChC,YAAY;AAAA,MACZ,UAAU,CAAI,0BAA6B,oBAAoB;AAAA,IACjE,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,qCAAqC;AAAA,IAC9C,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,2BAA2B;AAAA,IACpC,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,8BAA8B;AAAA,IACvC,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,qCAAqC;AAAA,IAC9C,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,4BAA4B;AAAA,IACrC,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,4BAA4B;AAAA,IACrC,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,0BAA0B;AAAA,IACnC,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,2BAA2B;AAAA,IACpC,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,6BAA6B;AAAA,IACtC,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,8BAA8B;AAAA,IACvC,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,kCAAkC;AAAA,IAC3C,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,+BAA+B;AAAA,IACxC,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,mCAAmC;AAAA,IAC5C,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,mCAAmC;AAAA,IAC5C,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAC,yBAAyB;AAAA,IAClC,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,iCAAiC;AAAA,IAC1C,CAAC;AAAA,IACD,yBAAyB,CAAC;AAAA,MACxB,MAAM;AAAA,MACN,MAAM,CAAC,sCAAsC;AAAA,IAC/C,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAEH,SAAS,uDAAuD,SAAS;AACvE,SAAO,MAAM,QAAQ,iBAAiB,WAAW;AACnD;AAEA,IAAM,iDAAiD;AAAA,EACrD,SAAS;AAAA,EACT,MAAM,CAAC,OAAO;AAAA,EACd,YAAY;AACd;AACA,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,OAAO;AACL,SAAK,OAAO,SAAS,sBAAsB,mBAAmB;AAC5D,aAAO,KAAK,qBAAqB,gBAAe;AAAA,IAClD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,YAAY,cAAc,iBAAiB,qBAAqB,gBAAgB;AAAA,MAC1F,SAAS,CAAC,qBAAqB,kBAAkB,eAAe;AAAA,IAClE,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,WAAW,CAAC,SAAS,8CAA8C;AAAA,MACnE,SAAS,CAAC,YAAY,cAAc,iBAAiB,eAAe;AAAA,IACtE,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,YAAY,cAAc,iBAAiB,qBAAqB,gBAAgB;AAAA,MAC1F,SAAS,CAAC,qBAAqB,kBAAkB,eAAe;AAAA,MAChE,WAAW,CAAC,SAAS,8CAA8C;AAAA,IACrE,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AASH,IAAM,6BAAN,MAAM,oCAAmC,iBAAiB;AAAA,EACxD,YAAY,WAAW,UAAU;AAC/B,UAAM,WAAW,QAAQ;AAAA,EAC3B;AAAA,EACA,cAAc;AACZ,UAAM,YAAY;AAClB,QAAI,KAAK,wBAAwB,KAAK,qBAAqB;AACzD,WAAK,UAAU,oBAAoB,KAAK,sBAAsB,KAAK,mBAAmB;AAAA,IACxF;AAAA,EACF;AAAA,EACA,mBAAmB;AACjB,UAAM,iBAAiB;AACvB,SAAK,iCAAiC;AACtC,SAAK,6BAA6B,MAAM,KAAK,iCAAiC,CAAC;AAAA,EACjF;AAAA,EACA,mCAAmC;AACjC,QAAI,CAAC,KAAK,mBAAmB;AAC3B;AAAA,IACF;AACA,UAAM,oBAAoB,KAAK,qBAAqB;AACpD,UAAM,SAAS,qBAAqB,KAAK,UAAU;AACnD,WAAO,YAAY,KAAK,iBAAiB;AAAA,EAC3C;AAAA,EACA,6BAA6B,IAAI;AAC/B,UAAM,YAAY,KAAK,cAAc;AACrC,QAAI,WAAW;AACb,UAAI,KAAK,qBAAqB;AAC5B,aAAK,UAAU,oBAAoB,WAAW,KAAK,mBAAmB;AAAA,MACxE;AACA,WAAK,UAAU,iBAAiB,WAAW,EAAE;AAC7C,WAAK,sBAAsB;AAAA,IAC7B;AAAA,EACF;AAAA,EACA,gBAAgB;AACd,QAAI,CAAC,KAAK,sBAAsB;AAC9B,YAAM,YAAY,KAAK;AACvB,UAAI,UAAU,mBAAmB;AAC/B,aAAK,uBAAuB;AAAA,MAC9B,WAAW,UAAU,yBAAyB;AAC5C,aAAK,uBAAuB;AAAA,MAC9B,WAAW,UAAU,sBAAsB;AACzC,aAAK,uBAAuB;AAAA,MAC9B,WAAW,UAAU,qBAAqB;AACxC,aAAK,uBAAuB;AAAA,MAC9B;AAAA,IACF;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAuB;AACrB,UAAM,YAAY,KAAK;AACvB,WAAO,UAAU,qBAAqB,UAAU,2BAA2B,UAAU,wBAAwB,UAAU,uBAAuB;AAAA,EAChJ;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,mCAAmC,mBAAmB;AACzE,aAAO,KAAK,qBAAqB,6BAA+B,SAAS,QAAQ,GAAM,SAAc,QAAQ,CAAC;AAAA,IAChH;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,4BAA2B;AAAA,MACpC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,4BAA4B,CAAC;AAAA,IACnG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAW;AAAA,EACb,CAAC,GAAG,IAAI;AACV,GAAG;;;AC/vGH,IAAAC,oBAA0B;AAM1B,IAAM,eAAe;AAAA,EACnB,KAAK,IAAI,uBAAuB;AAAA,IAC9B,SAAS;AAAA,IACT,SAAS;AAAA,EACX,GAAG;AAAA,IACD,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,WAAW,IAAI,uBAAuB;AAAA,IACpC,SAAS;AAAA,IACT,SAAS;AAAA,EACX,GAAG;AAAA,IACD,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,SAAS,IAAI,uBAAuB;AAAA,IAClC,SAAS;AAAA,IACT,SAAS;AAAA,EACX,GAAG;AAAA,IACD,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,UAAU,IAAI,uBAAuB;AAAA,IACnC,SAAS;AAAA,IACT,SAAS;AAAA,EACX,GAAG;AAAA,IACD,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,OAAO,IAAI,uBAAuB;AAAA,IAChC,SAAS;AAAA,IACT,SAAS;AAAA,EACX,GAAG;AAAA,IACD,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,UAAU,IAAI,uBAAuB;AAAA,IACnC,SAAS;AAAA,IACT,SAAS;AAAA,EACX,GAAG;AAAA,IACD,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,aAAa,IAAI,uBAAuB;AAAA,IACtC,SAAS;AAAA,IACT,SAAS;AAAA,EACX,GAAG;AAAA,IACD,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,QAAQ,IAAI,uBAAuB;AAAA,IACjC,SAAS;AAAA,IACT,SAAS;AAAA,EACX,GAAG;AAAA,IACD,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,cAAc,IAAI,uBAAuB;AAAA,IACvC,SAAS;AAAA,IACT,SAAS;AAAA,EACX,GAAG;AAAA,IACD,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,YAAY,IAAI,uBAAuB;AAAA,IACrC,SAAS;AAAA,IACT,SAAS;AAAA,EACX,GAAG;AAAA,IACD,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,aAAa,IAAI,uBAAuB;AAAA,IACtC,SAAS;AAAA,IACT,SAAS;AAAA,EACX,GAAG;AAAA,IACD,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,MAAM,IAAI,uBAAuB;AAAA,IAC/B,SAAS;AAAA,IACT,SAAS;AAAA,EACX,GAAG;AAAA,IACD,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,SAAS,IAAI,uBAAuB;AAAA,IAClC,SAAS;AAAA,IACT,SAAS;AAAA,EACX,GAAG;AAAA,IACD,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,YAAY,IAAI,uBAAuB;AAAA,IACrC,SAAS;AAAA,IACT,SAAS;AAAA,EACX,GAAG;AAAA,IACD,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,CAAC;AACH;AACA,IAAM,4BAA4B,CAAC,aAAa,KAAK,aAAa,OAAO,aAAa,QAAQ,aAAa,IAAI;AAC/G,IAAM,6BAA6B,CAAC,aAAa,YAAY,aAAa,aAAa,aAAa,SAAS,aAAa,QAAQ;AAClI,IAAM,gCAAgC,CAAC,IAAI,uBAAuB;AAAA,EAChE,SAAS;AAAA,EACT,SAAS;AACX,GAAG;AAAA,EACD,UAAU;AAAA,EACV,UAAU;AACZ,CAAC,GAAG,IAAI,uBAAuB;AAAA,EAC7B,SAAS;AAAA,EACT,SAAS;AACX,GAAG;AAAA,EACD,UAAU;AAAA,EACV,UAAU;AACZ,CAAC,CAAC;AACF,IAAM,mCAAmC,CAAC,aAAa,YAAY,IAAI,uBAAuB;AAAA,EAC5F,SAAS;AAAA,EACT,SAAS;AACX,GAAG;AAAA,EACD,UAAU;AAAA,EACV,UAAU;AACZ,CAAC,CAAC;AACF,SAAS,iBAAiB,UAAU;AAClC,aAAW,aAAa,cAAc;AACpC,QAAI,SAAS,eAAe,YAAY,aAAa,SAAS,EAAE,WAAW,SAAS,eAAe,YAAY,aAAa,SAAS,EAAE,WAAW,SAAS,eAAe,aAAa,aAAa,SAAS,EAAE,YAAY,SAAS,eAAe,aAAa,aAAa,SAAS,EAAE,UAAU;AAChS,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAM,+BAAN,MAAM,8BAA6B;AAAA,EACjC,YAAY,qBAAqBC,oBAAmB;AAClD,SAAK,sBAAsB;AAC3B,SAAK,oBAAoBA;AACzB,SAAK,wBAAwB;AAC7B,SAAK,oBAAoB,gBAAgB;AACzC,SAAK,oBAAoB,eAAe,SAAK,6BAAU,KAAK,iBAAiB,CAAC,EAAE,UAAU,cAAY;AACpG,UAAI,KAAK,uBAAuB;AAC9B,aAAK,oBAAoB,QAAQ;AAAA,MACnC;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,oBAAoB,UAAU;AAC5B,UAAM,aAAa,KAAK,cAAc;AACtC,UAAM,YAAY,iBAAiB,QAAQ;AAC3C,QAAI,UAAU;AACd,QAAI,UAAU;AACd,QAAI,cAAc,aAAa,cAAc,cAAc;AACzD,gBAAU,WAAW,QAAQ,IAAI;AAAA,IACnC,WAAW,cAAc,cAAc,cAAc,eAAe;AAClE,gBAAU,EAAE,WAAW,QAAQ,IAAI;AAAA,IACrC,WAAW,cAAc,aAAa,cAAc,YAAY;AAC9D,gBAAU,WAAW,SAAS,IAAI;AAAA,IACpC,WAAW,cAAc,gBAAgB,cAAc,eAAe;AACpE,gBAAU,EAAE,WAAW,SAAS,IAAI;AAAA,IACtC;AACA,QAAI,KAAK,oBAAoB,YAAY,WAAW,KAAK,oBAAoB,YAAY,SAAS;AAChG,WAAK,oBAAoB,UAAU;AACnC,WAAK,oBAAoB,UAAU;AACnC,WAAK,oBAAoB,WAAW,eAAe;AAAA,IACrD;AAAA,EACF;AAAA,EACA,6CAA6C;AAC3C,QAAI,KAAK,oBAAoB,kBAAkB,kBAAkB;AAC/D,aAAO,KAAK,oBAAoB,OAAO;AAAA,IACzC,OAAO;AACL,aAAO,KAAK,oBAAoB;AAAA,IAClC;AAAA,EACF;AAAA,EACA,gBAAgB;AACd,UAAM,SAAS,KAAK,2CAA2C;AAC/D,QAAI,kBAAkB,YAAY;AAChC,aAAO,OAAO,cAAc,sBAAsB;AAAA,IACpD;AAEA,QAAI,kBAAkB,SAAS;AAC7B,aAAO,OAAO,sBAAsB;AAAA,IACtC;AACA,UAAM,QAAQ,OAAO,SAAS;AAC9B,UAAM,SAAS,OAAO,UAAU;AAEhC,WAAO;AAAA,MACL,KAAK,OAAO;AAAA,MACZ,QAAQ,OAAO,IAAI;AAAA,MACnB,MAAM,OAAO;AAAA,MACb,OAAO,OAAO,IAAI;AAAA,MAClB;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,qCAAqC,mBAAmB;AAC3E,aAAO,KAAK,qBAAqB,+BAAiC,kBAAqB,mBAAmB,GAAM,kBAAqB,iBAAiB,CAAC;AAAA,IACzJ;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,uBAAuB,IAAI,uBAAuB,EAAE,CAAC;AAAA,MACtE,QAAQ;AAAA,QACN,uBAAuB;AAAA,MACzB;AAAA,MACA,UAAU,CAAC,qBAAqB;AAAA,MAChC,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC,iBAAiB,CAAC,CAAC;AAAA,IACvD,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,aAAa,CAAC,GAAG,6BAA6B,WAAW,yBAAyB,MAAM;AAAA,CACnG,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,8BAA8B,CAAC;AAAA,IACrG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,WAAW,CAAC,iBAAiB;AAAA,MAC7B,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,mBAAmB;AAC/D,aAAO,KAAK,qBAAqB,mBAAkB;AAAA,IACrD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,4BAA4B;AAAA,MACtC,SAAS,CAAC,4BAA4B;AAAA,IACxC,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB,CAAC,CAAC;AAAA,EACnD;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,4BAA4B;AAAA,MACtC,SAAS,CAAC,4BAA4B;AAAA,IACxC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAGH,IAAM,sBAAN,MAAM,6BAA4B,iBAAiB;AAAA,EACjD,YAAY,WAAW,UAAU;AAC/B,UAAM,WAAW,QAAQ;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB;AACjB,UAAM,iBAAiB;AACvB,UAAM,6BAA6B,KAAK,UAAU,iBAAiB,IAAI,cAAc,EAAE;AACvF,QAAI,gBAAgB;AAClB,YAAM,YAAY,KAAK,UAAU,cAAc,KAAK;AACpD,gBAAU,UAAU,IAAI,cAAc;AACtC,gBAAU,UAAU,IAAI,2BAA2B;AACnD,WAAK,UAAU,KAAK,YAAY,SAAS;AACzC,WAAK,oBAAoB;AAAA,IAC3B,OAAO;AACL,WAAK,oBAAoB,2BAA2B,CAAC;AAAA,IACvD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,4BAA4B,mBAAmB;AAClE,aAAO,KAAK,qBAAqB,sBAAwB,SAAS,QAAQ,GAAM,SAAc,QAAQ,CAAC;AAAA,IACzG;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,qBAAoB;AAAA,MAC7B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAW;AAAA,EACb,CAAC,GAAG,IAAI;AACV,GAAG;;;AC1TH,IAAM,mCAAN,MAAM,kCAAiC;AAAA,EACrC,OAAO,uBAAuB,MAAM,MAAM;AACxC,WAAO;AAAA,EACT;AAAA,EACA,eAAe;AACb,SAAK,cAAc,MAAM;AACzB,UAAM,gBAAgB,KAAK,mCAAmC;AAC9D,UAAM,cAAc,gBAAgB,KAAK,0BAA0B,KAAK;AACxE,SAAK,kBAAkB,KAAK,cAAc,mBAAmB,aAAa,gBAAgB,KAAK,iCAAiC,KAAK,OAAO;AAAA,EAC9I;AAAA,EACA,gBAAgB;AACd,UAAM,gBAAgB,KAAK,mCAAmC;AAC9D,UAAM,SAAS,gBAAgB,KAAK,iCAAiC,KAAK;AAC1E,UAAM,SAAS,KAAK,gBAAgB;AACpC,QAAI,QAAQ;AACV,iBAAW,YAAY,OAAO,KAAK,MAAM,GAAG;AAC1C,eAAO,QAAQ,IAAI,OAAO,QAAQ;AAAA,MACpC;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY,eAAe,aAAa;AACtC,SAAK,gBAAgB;AACrB,SAAK,cAAc;AACnB,SAAK,kBAAkB;AACvB,SAAK,UAAU,IAAI,+BAA+B;AAClD,SAAK,iCAAiC;AACtC,SAAK,0BAA0B;AAAA,EACjC;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,qBAAqB,MAAM;AAC/B,UAAI,uBAAuB;AAC3B,UAAI,yBAAyB;AAC3B,YAAI,wBAAwB,aAAa;AACvC,iCAAuB;AAAA,QACzB,OAAO;AACL,gBAAM,2BAA2B,wBAAwB,yBAAyB;AAClF,gBAAM,0BAA0B,wBAAwB,wBAAwB;AAChF,iCAAuB,4BAA4B;AAAA,QACrD;AAAA,MACF;AACA,YAAM,yBAAyB,eAAa;AAC1C,cAAM,cAAc,OAAO,KAAK,UAAU,iBAAiB,CAAC,CAAC;AAC7D,cAAM,cAAc,OAAO,KAAK,UAAU,gBAAgB,CAAC,CAAC;AAC5D,YAAI,YAAY,WAAW,YAAY,QAAQ;AAC7C,qBAAW,YAAY,aAAa;AAClC,gBAAI,YAAY,QAAQ,QAAQ,MAAM,IAAI;AACxC,qBAAO;AAAA,YACT;AAAA,UACF;AACA,iBAAO;AAAA,QACT,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AACA,YAAM,wBAAwB,kCAAkC,uBAAuB,8BAA8B;AACrH,aAAO,yBAAyB;AAAA,IAClC;AACA,QAAI,yBAAyB;AAC3B,WAAK,QAAQ,YAAY,wBAAwB;AAAA,IACnD;AACA,UAAM,eAAe,mBAAmB;AACxC,QAAI,cAAc;AAEhB,WAAK,aAAa;AAAA,IACpB,OAAO;AAEL,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yCAAyC,mBAAmB;AAC/E,aAAO,KAAK,qBAAqB,mCAAqC,kBAAqB,gBAAgB,GAAM,kBAAqB,WAAW,CAAC;AAAA,IACpJ;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,2BAA2B,EAAE,CAAC;AAAA,MAC/C,QAAQ;AAAA,QACN,gCAAgC;AAAA,QAChC,yBAAyB;AAAA,MAC3B;AAAA,MACA,UAAU,CAAC,yBAAyB;AAAA,MACpC,YAAY;AAAA,MACZ,UAAU,CAAI,oBAAoB;AAAA,IACpC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kCAAkC,CAAC;AAAA,IACzG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,gCAAgC,CAAC;AAAA,MAC/B,MAAM;AAAA,IACR,CAAC;AAAA,IACD,yBAAyB,CAAC;AAAA,MACxB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,iCAAN,MAAqC;AAAC;AACtC,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,OAAO;AACL,SAAK,OAAO,SAAS,wBAAwB,mBAAmB;AAC9D,aAAO,KAAK,qBAAqB,kBAAiB;AAAA,IACpD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,gCAAgC;AAAA,MAC1C,SAAS,CAAC,gCAAgC;AAAA,IAC5C,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB,CAAC,CAAC;AAAA,EACnD;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,gCAAgC;AAAA,MAC1C,SAAS,CAAC,gCAAgC;AAAA,IAC5C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;AC7IH,IAAM,4BAA4B;AAAA,EAChC,cAAc;AAAA,EACd,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,aAAa;AACf;;;ACJA,IAAAC,eAAgG;AAEhG,IAAM,YAAY,IAAI,eAAe,eAAe;AAAA,EAClD,SAAS,MAAM;AACb,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,OAAO,QAAQ;AACnB,QAAI,CAAC,aAAa;AAChB,YAAM,IAAI,MAAM,yBAAyB;AAAA,IAC3C;AACA,WAAO;AAAA,EACT;AACF,CAAC;AAID,IAAM,SAAS;AACf,IAAM,qBAAqB,IAAI,eAAe,wBAAwB;AAAA,EACpE,SAAS,MAAM;AACb,UAAM;AAAA,MACJ,uBAAAC;AAAA,MACA;AAAA,IACF,IAAI,OAAO,MAAM;AACjB,UAAM,kBAAkB,IAAI,wBAAW,gBAAc;AACnD,UAAI,KAAK;AACT,YAAM,WAAW,eAAa;AAC5B,mBAAW,KAAK,SAAS;AACzB,aAAKA,uBAAsB,QAAQ;AAAA,MACrC;AACA,WAAKA,uBAAsB,QAAQ;AACnC,aAAO,MAAM;AACX,6BAAqB,EAAE;AAAA,MACzB;AAAA,IACF,CAAC;AACD,WAAO,gBAAgB,SAAK,oBAAM,CAAC;AAAA,EACrC;AACF,CAAC;AAKD,IAAM,YAAY,IAAI,eAAe,eAAe;AAAA,EAClD,SAAS,MAAM,OAAO,MAAM,EAAE;AAChC,CAAC;AAKD,IAAM,YAAY,IAAI,eAAe,eAAe;AAAA,EAClD,SAAS,MAAM,OAAO,MAAM,EAAE;AAChC,CAAC;AAKD,IAAM,SAAS,IAAI,eAAe,YAAY;AAAA,EAC5C,SAAS,MAAM,OAAO,MAAM,EAAE,OAAO;AAAA,IACnC,QAAQ,OAAK;AAAA;AAAA,IAEb,UAAU,MAAM;AAAA,EAClB;AACF,CAAC;AAKD,IAAM,aAAa,IAAI,eAAe,gBAAgB;AAAA,EACpD,SAAS,MAAM,OAAO,MAAM,EAAE;AAChC,CAAC;AAKD,IAAM,mBAAmB,IAAI,eAAe,sBAAsB;AAAA,EAChE,SAAS,MAAM,OAAO,MAAM,EAAE;AAChC,CAAC;AAKD,IAAM,cAAc,IAAI,eAAe,iBAAiB;AAAA,EACtD,SAAS,MAAM,OAAO,MAAM,EAAE;AAChC,CAAC;AAKD,IAAM,eAAe,IAAI,eAAe,kBAAkB;AAAA,EACxD,SAAS,MAAM,OAAO,MAAM,EAAE;AAChC,CAAC;AAID,IAAM,YAAY;AAClB,IAAM,mBAAmB,IAAI,eAAe,sBAAsB;AAAA,EAChE,SAAS,MAAM,OAAO,SAAS,EAAE;AACnC,CAAC;AAKD,IAAM,yBAAyB,IAAI,eAAe,4BAA4B;AAAA;AAAA,EAE5E,SAAS,MAAM,OAAO,YAAY,EAAE,cAAc;AACpD,CAAC;AAKD,IAAM,qBAAqB,IAAI,eAAe,wBAAwB;AAAA,EACpE,SAAS,MAAM;AACb,UAAM,cAAc,OAAO,QAAQ;AACnC,eAAO,wBAAU,aAAa,kBAAkB,EAAE,SAAK,wBAAU,CAAC,OAAG,kBAAI,MAAM,YAAY,oBAAoB,QAAQ,OAAG,mCAAqB,OAAG,0BAAY;AAAA,MAC5J,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC,CAAC;AAAA,EACJ;AACF,CAAC;AAKD,IAAM,iBAAiB,IAAI,eAAe,oBAAoB;AAAA,EAC5D,SAAS,MAAM,OAAO,MAAM,EAAE;AAChC,CAAC;AAKD,IAAM,YAAY,IAAI,eAAe,eAAe;AAAA,EAClD,SAAS,MAAM,OAAO,MAAM,EAAE;AAChC,CAAC;AAKD,IAAM,qBAAqB,IAAI,eAAe,wBAAwB;AAAA,EACpE,SAAS,MAAM,OAAO,MAAM,EAAE;AAChC,CAAC;AAKD,IAAM,wBAAwB,IAAI,eAAe,iDAAiD;AAAA,EAChG,SAAS,MAAM;AACb,UAAM,YAAY,OAAO,MAAM;AAC/B,WAAO,UAAU,qBAAqB,UAAU,2BAA2B;AAAA,EAC7E;AACF,CAAC;AAKD,IAAM,sBAAsB,IAAI,eAAe,yBAAyB;AAAA,EACtE,SAAS,MAAM,OAAO,MAAM,EAAE;AAChC,CAAC;AAKD,IAAM,gBAAgB,IAAI,eAAe,mBAAmB;AAAA,EAC1D,SAAS,MAAM,OAAO,SAAS,EAAE;AACnC,CAAC;AAID,IAAM,aAAa;;;AC7JnB,SAAS,mBAAmB,WAAW;AACrC,SAAO,UAAU,SAAS,QAAQ,KAAK,MAAM,KAAK,UAAU,aAAa;AAC3E;AACA,IAAM,cAAc;AACpB,SAAS,SAAS,WAAW;AAC3B,SAAO,YAAY,KAAK,UAAU,UAAU,YAAY,CAAC,KAAK,mBAAmB,SAAS,KAAK,UAAU,iBAAiB;AAC5H;AACA,IAAM,iBAAiB;AACvB,SAAS,WAAW,WAAW;AAC7B,SAAO,SAAS,SAAS,KAAK,eAAe,KAAK,UAAU,UAAU,YAAY,CAAC;AACrF;;;AChBA,IAAM,qBAAqB,IAAI,eAAe,iEAAiE;AAAA,EAC7G,SAAS,MAAM,UAAU;AAC3B,CAAC;AACD,IAAM,oBAAoB,IAAI,eAAe,uEAAuE;AAAA,EAClH,SAAS,MAAM;AACjB,CAAC;AACD,IAAM,aAAa,IAAI,eAAe,uCAAuC;AAAA,EAC3E,SAAS,MAAM,SAAS,OAAO,SAAS,CAAC;AAC3C,CAAC;AAGD,IAAM,aAAa;AACnB,IAAM,cAAc;AACpB,IAAM,gBAAgB,IAAI,eAAe,6CAA6C;AAAA,EACpF,SAAS,MAAM,WAAW,KAAK,OAAO,UAAU,EAAE,YAAY,CAAC,KAAK,YAAY,KAAK,OAAO,UAAU,EAAE,MAAM,GAAG,CAAC,EAAE,YAAY,CAAC;AACnI,CAAC;AACD,IAAM,iBAAiB,IAAI,eAAe,0GAA0G;AAAA,EAClJ,SAAS,MAAM,OAAO,aAAa,KAAK,CAAC,OAAO,UAAU;AAC5D,CAAC;AACD,IAAM,eAAe,IAAI,eAAe,kDAAkD;AAAA,EACxF,SAAS,MAAM,WAAW,OAAO,SAAS,CAAC;AAC7C,CAAC;AACD,IAAM,kBAAkB,IAAI,eAAe,wDAAwD;AAAA,EACjG,SAAS,MAAM,CAAC,CAAC,OAAO,MAAM,EAAE;AAClC,CAAC;AAKD,IAAM,iBAAiB,IAAI,eAAe,4DAA4D;AAAA,EACpG,SAAS,MAAM,CAAC,CAAC,OAAO,MAAM,EAAE;AAClC,CAAC;AACD,IAAM,iBAAiB,IAAI,eAAe,sDAAsD;AAAA,EAC9F,SAAS,MAAM,OAAO,OAAO,MAAM,GAAG,wBAAwB;AAChE,CAAC;AACD,IAAM,gBAAgB,IAAI,eAAe,oDAAoD;AAAA,EAC3F,SAAS,MAAM,CAAC,CAAC,OAAO,MAAM,GAAG;AACnC,CAAC;;;ACJD,IAAM,cAAc;AAkCpB,IAAM,cAAc,IAAI,UAAU;AAGlC,IAAM,OAAO;AAAA,EACX,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,OAAO;AAAA,EACP,KAAK;AAAA,EACL,OAAO;AAAA,EACP,GAAG;AAAA,EACH,GAAG;AACL;AACA,IAAM,oBAAoB,iCACrB,OADqB;AAAA,EAExB,SAAS;AACP,WAAO;AAAA,EACT;AACF;;;AC/EA,SAAS,4BAA4B,OAAO;AAC1C,QAAM,gBAAgB,OAAO,KAAK;AAClC,QAAM,CAAC,YAAY,OAAO,IAAI,cAAc,MAAM,IAAI;AACtD,MAAI,kBAAkB;AACtB,MAAI,SAAS;AACX,UAAM,CAAC,EAAE,cAAc,IAAI,WAAW,MAAM,GAAG;AAC/C,UAAM,gBAAgB,OAAO,OAAO,KAAK,gBAAgB,UAAU;AACnE,sBAAkB,MAAM,QAAQ,aAAa;AAAA,EAC/C;AACA,SAAO;AACT;AASA,SAAS,yBAAyB,OAAO,WAAW;AAClD,QAAM,CAAC,EAAE,qBAAqB,EAAE,IAAI,4BAA4B,KAAK,EAAE,MAAM,GAAG;AAChF,SAAO,eAAe,SAAS,IAAI,mBAAmB,MAAM,GAAG,KAAK,IAAI,GAAG,SAAS,CAAC,IAAI;AAC3F;AAUA,SAAS,gBAAgB,OAAO,WAAW,CAAC,GAAG;AAC7C,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,kCACC,4BACA;AAEL,QAAM,oBAAoB,OAAO,KAAK,MAAM,KAAK,IAAI,KAAK,CAAC,CAAC;AAC5D,MAAI,qBAAqB,yBAAyB,OAAO,YAAY;AACrE,MAAI,OAAO,SAAS,YAAY,GAAG;AACjC,QAAI,aAAa;AACf,YAAM,kBAAkB,KAAK,IAAI,eAAe,mBAAmB,QAAQ,CAAC;AAC5E,YAAM,iBAAiB,IAAI,OAAO,eAAe;AACjD,2BAAqB,GAAG,kBAAkB,GAAG,cAAc;AAAA,IAC7D,OAAO;AACL,2BAAqB,mBAAmB,QAAQ,OAAO,EAAE;AAAA,IAC3D;AAAA,EACF;AACA,QAAM,YAAY,kBAAkB,SAAS;AAC7C,QAAM,OAAO,QAAQ,IAAI,cAAc;AACvC,MAAI,SAAS,OAAO,kBAAkB,OAAO,CAAC;AAC9C,WAAS,IAAI,GAAG,IAAI,kBAAkB,QAAQ,KAAK;AACjD,QAAI,IAAI,MAAM,aAAa,kBAAkB,SAAS,GAAG;AACvD,gBAAU;AAAA,IACZ;AACA,cAAU,kBAAkB,OAAO,CAAC;AAAA,EACtC;AACA,SAAO,qBAAqB,SAAS,mBAAmB,qBAAqB;AAC/E;;;AClEA,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,UAAU,OAAO,QAAQ;AACvB,QAAI,CAAC,MAAM,QAAQ,KAAK,GAAG;AACzB,aAAO;AAAA,IACT;AACA,QAAI,MAAM,WAAW,GAAG;AACtB,aAAO;AAAA,IACT;AACA,YAAQ,QAAQ;AAAA,MACd,KAAK;AACH,eAAO,IAAI,KAAK;AAAA,MAClB,KAAK;AACH,eAAO,IAAI,KAAK,IAAI,MAAM;AAAA,MAC5B,KAAK;AACH,eAAO,KAAK,IAAI,GAAG,KAAK;AAAA,MAC1B,KAAK;AACH,eAAO,KAAK,IAAI,GAAG,KAAK;AAAA,MAC1B;AACE,cAAM,MAAM,kEAAkE;AAAA,IAClF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,mBAAmB;AAC/D,aAAO,KAAK,qBAAqB,mBAAkB;AAAA,IACrD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,aAAa;AAAA,MAC1C,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,OAAO;AACL,SAAK,UAAU;AAAA,MACb,GAAG;AAAA,QACD,KAAK;AAAA,MACP;AAAA,MACA,IAAI;AAAA,QACF,KAAK,KAAK,IAAI,MAAM,CAAC;AAAA,QACrB,MAAM;AAAA,MACR;AAAA,MACA,IAAI;AAAA,QACF,KAAK,KAAK,IAAI,MAAM,CAAC;AAAA,QACrB,MAAM;AAAA,MACR;AAAA,MACA,IAAI;AAAA,QACF,KAAK,KAAK,IAAI,MAAM,CAAC;AAAA,QACrB,MAAM;AAAA,MACR;AAAA,MACA,IAAI;AAAA,QACF,KAAK,KAAK,IAAI,MAAM,CAAC;AAAA,QACrB,MAAM;AAAA,MACR;AAAA,MACA,IAAI;AAAA,QACF,KAAK,OAAO;AAAA,QACZ,MAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAAA,EACA,UAAU,OAAO,UAAU,GAAGC,QAAO,KAAK,IAAI;AAC5C,QAAI,EAAE,eAAe,KAAK,KAAK,eAAe,OAAO,KAAK,UAAU,MAAM,KAAK,WAAW,IAAI;AAC5F,aAAO;AAAA,IACT;AACA,QAAI,QAAQ;AACZ,QAAI,OAAOA;AACX,WAAO,SAAS,KAAK;AACnB,eAAS;AACT,aAAO,cAAa,QAAQ,IAAI,EAAE;AAAA,IACpC;AACA,QAAI,IAAI;AACN,YAAM,SAAS,cAAa,QAAQ,EAAE;AACtC,YAAM,SAAS,UAAU,cAAa,gBAAgB,QAAQ,KAAK,GAAG,OAAO;AAC7E,aAAO,cAAa,aAAa,QAAQ,EAAE;AAAA,IAC7C;AACA,eAAW,OAAO,cAAa,SAAS;AACtC,UAAI,cAAa,QAAQ,eAAe,GAAG,GAAG;AAC5C,cAAM,SAAS,cAAa,QAAQ,GAAG;AACvC,YAAI,QAAQ,OAAO,KAAK;AACtB,gBAAM,SAAS,UAAU,cAAa,gBAAgB,QAAQ,KAAK,GAAG,OAAO;AAC7E,iBAAO,cAAa,aAAa,QAAQ,GAAG;AAAA,QAC9C;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,aAAa,QAAQ,MAAM;AAChC,WAAO,GAAG,MAAM,IAAI,IAAI;AAAA,EAC1B;AAAA,EACA,OAAO,gBAAgB,QAAQ,OAAO;AACpC,UAAM,OAAO,OAAO,OAAO,cAAa,QAAQ,OAAO,IAAI,IAAI;AAC/D,WAAO,OAAO,QAAQ,KAAK,MAAM;AAAA,EACnC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,qBAAqB,mBAAmB;AAC3D,aAAO,KAAK,qBAAqB,eAAc;AAAA,IACjD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,aAAa;AAAA,MAC1C,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,UAAU,OAAO,cAAc,MAAM;AACnC,UAAM,qBAAqB,CAAC,MAAM,MAAM,KAAK,MAAM,MAAM,MAAM,IAAI;AACnE,UAAM,qBAAqB,CAAC,MAAM,MAAM,MAAM,OAAO,MAAM,MAAM,MAAM,QAAQ,MAAM;AACrF,UAAM,kBAAkB,CAAC,GAAG;AAC5B,UAAM,aAAa,CAAC,GAAG,oBAAoB,GAAG,oBAAoB,GAAG,eAAe;AACpF,QAAI,OAAO;AACX,QAAI,WAAW,KAAK,OAAK,MAAM,WAAW,GAAG;AAC3C,aAAO;AAAA,IACT;AACA,WAAO,OAAO,UAAU,WAAW,GAAG,KAAK,GAAG,IAAI,KAAK,GAAG,KAAK;AAAA,EACjE;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,mBAAmB;AAC/D,aAAO,KAAK,qBAAqB,mBAAkB;AAAA,IACrD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,aAAa;AAAA,MAC1C,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,UAAU,OAAO,QAAQ,SAAS,IAAI;AACpC,QAAI,OAAO,UAAU,UAAU;AAC7B,aAAO;AAAA,IACT;AACA,UAAM,MAAM,OAAO,WAAW,cAAc,MAAM,SAAS;AAC3D,QAAI,MAAM,UAAU,KAAK;AACvB,aAAO;AAAA,IACT;AACA,WAAO,MAAM,UAAU,GAAG,GAAG,IAAI;AAAA,EACnC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,wBAAwB,mBAAmB;AAC9D,aAAO,KAAK,qBAAqB,kBAAiB;AAAA,IACpD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,aAAa;AAAA,MAC1C,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,UAAU,OAAO,UAAU,IAAI;AAC7B,QAAI,MAAM,KAAK,GAAG;AAChB,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,wBAAwB,mBAAmB;AAC9D,aAAO,KAAK,qBAAqB,kBAAiB;AAAA,IACpD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,aAAa;AAAA,MAC1C,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,YAAY,WAAW;AACrB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,UAAU,OAAO,OAAO,QAAQ;AAC9B,YAAQ,MAAM;AAAA,MACZ,KAAK;AACH,eAAO,KAAK,UAAU,wBAAwB,KAAK;AAAA,MACrD,KAAK;AACH,eAAO,KAAK,UAAU,yBAAyB,KAAK;AAAA,MACtD,KAAK;AACH,eAAO,KAAK,UAAU,uBAAuB,KAAK;AAAA,MACpD,KAAK;AACH,eAAO,KAAK,UAAU,+BAA+B,KAAK;AAAA,MAC5D;AACE,cAAM,IAAI,MAAM,6BAA6B;AAAA,IACjD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,mBAAmB;AAC/D,aAAO,KAAK,qBAAqB,mBAAqB,kBAAqB,cAAc,EAAE,CAAC;AAAA,IAC9F;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,aAAa;AAAA,MAC1C,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,cAAN,MAAM,aAAY;AAAA;AAAA,EAEhB,UAAU,MAAM;AACd,WAAO,KAAK,KAAK;AAAA,EACnB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,oBAAoB,mBAAmB;AAC1D,aAAO,KAAK,qBAAqB,cAAa;AAAA,IAChD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,aAAa;AAAA,MAC1C,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,UAAU,OAAO,SAAS,YAAY;AACpC,QAAI,WAAW,OAAO,SAAS,CAAC;AAChC,WAAO,UAAU,OAAO,CAAC,SAAS,CAAC,MAAM,IAAI,MAAM;AACjD,UAAI,QAAQ,QAAQ,IAAI,MAAM,IAAI;AAChC,cAAM,IAAI,KAAK,MAAM,WAAW,IAAI;AACpC,oBAAY,IAAI;AAChB,eAAO,QAAQ,QAAQ,IAAI,OAAO,GAAG,IAAI,KAAK,GAAG,GAAG,WAAS,SAAS,EAAE,SAAS,GAAG,MAAM,QAAQ,GAAG,CAAC;AAAA,MACxG;AACA,aAAO;AAAA,IACT,GAAG,MAAM;AAAA,EACX;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,mBAAmB;AAC/D,aAAO,KAAK,qBAAqB,mBAAkB;AAAA,IACrD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,aAAa;AAAA,MAC1C,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB,YAAY,cAAc;AACxB,SAAK,eAAe;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU,OAAO,WAAW,CAAC,GAAG;AAC9B,WAAO,gBAAgB,OAAO,kCACzB,KAAK,eACL,SACJ;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,4BAA4B,mBAAmB;AAClE,aAAO,KAAK,qBAAqB,sBAAwB,kBAAkB,mBAAmB,EAAE,CAAC;AAAA,IACnG;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,aAAa;AAAA,MAC1C,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,QAAQ,CAAC,kBAAkB,iBAAiB,kBAAkB,aAAa,cAAc,kBAAkB,iBAAiB,kBAAkB,mBAAmB;AACvK,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,OAAO;AACL,SAAK,OAAO,SAAS,uBAAuB,mBAAmB;AAC7D,aAAO,KAAK,qBAAqB,iBAAgB;AAAA,IACnD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,kBAAkB,iBAAiB,kBAAkB,aAAa,cAAc,kBAAkB,iBAAiB,kBAAkB,mBAAmB;AAAA,MAClK,SAAS,CAAC,kBAAkB,iBAAiB,kBAAkB,aAAa,cAAc,kBAAkB,iBAAiB,kBAAkB,mBAAmB;AAAA,IACpK,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB,CAAC,CAAC;AAAA,EACnD;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,KAAK;AAAA,MACf,SAAS,CAAC,KAAK;AAAA,IACjB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["import_rxjs", "rect", "TDSBreakpointEnum", "import_operators", "import_rxjs", "document", "document", "style", "import_operators", "TDSDestroyService", "import_rxjs", "requestAnimationFrame", "from"]}