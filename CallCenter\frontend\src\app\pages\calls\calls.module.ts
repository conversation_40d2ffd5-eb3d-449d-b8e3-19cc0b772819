import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { CallListComponent } from './call-list/call-list.component';
import { CallDetailComponent } from './call-detail/call-detail.component';


const routes: Routes = [
  {
    path: '',
    component: CallListComponent
  },
  {
    path: ':id',
    component: CallDetailComponent
  }
];

@NgModule({
  declarations: [],
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    CallListComponent

  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  exports: [RouterModule]
})
export class CallsModule { }
