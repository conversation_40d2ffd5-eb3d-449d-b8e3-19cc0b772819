import { Component, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CallInfoCardComponent } from "./call-info-card/call-info-card.component";
import { AudioPlayerComponent } from "./audio-player/audio-player.component";
import { TranscriptDisplayComponent } from "./transcript-display/transcript-display.component";
import { QaReviewDisplayComponent } from "./qa-review-display/qa-review-display.component";

@Component({
  selector: 'app-call-detail',
  standalone: true,
  imports: [CallInfoCardComponent, AudioPlayerComponent, TranscriptDisplayComponent, QaReviewDisplayComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './call-detail.component.html',
  styleUrl: './call-detail.component.scss'
})
export class CallDetailComponent {

}
