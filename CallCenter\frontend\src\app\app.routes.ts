import { Routes } from '@angular/router';
import { LayoutComponent } from './layout/layout.component';

export const routes: Routes = [
  {
    path: '',
    component: LayoutComponent,
    children: [
      { path: '', redirectTo: 'dashboard', pathMatch: 'full' },
      {
        path: 'dashboard',
        loadChildren: () =>
          import('./pages/dashboard/dashboard.module').then(
            (m) => m.DashboardModule
          ),
      },
      {
        path: 'calls',
        loadChildren: () =>
          import('./pages/calls/calls.module').then((m) => m.CallsModule),
      },
      {
        path: 'qa',
        loadChildren: () =>
          import('./pages/qa/qa.module').then((m) => m.QaModule),
      },
      {
        path: 'reports',
        loadChildren: () =>
          import('./pages/reports/reports.module').then((m) => m.ReportsModule),
      },
    ],
  },
  {
    path: '**',
    redirectTo: ''
  }
];