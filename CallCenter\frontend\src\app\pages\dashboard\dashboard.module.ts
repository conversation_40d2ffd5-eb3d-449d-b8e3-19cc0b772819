import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CallQualitySummaryComponent } from "./call-quality-summary/call-quality-summary.component";
import { TopAgentsListComponent } from "./top-agents-list/top-agents-list.component";
import { RecentAlertsComponent } from "./recent-alerts/recent-alerts.component";
import { QualityTrendChartComponent } from "./quality-trend-chart/quality-trend-chart.component";

import { DashboardComponent } from './dashboard/dashboard.component';
import { RouterModule, Routes } from '@angular/router';

const routes: Routes = [
  {
    path: '',
    component: DashboardComponent,
  },
];
@NgModule({
  declarations: [DashboardComponent],
  imports: [
    CallQualitySummaryComponent, TopAgentsListComponent, RecentAlertsComponent, QualityTrendChartComponent,
    CommonModule,
    RouterModule.forChild(routes),
  ],

  exports: [RouterModule],
})
export class DashboardModule {}
