import { createRequire } from 'module';const require = createRequire(import.meta.url);
import {
  Inject,
  Injectable,
  InjectionToken,
  NgModule,
  Optional,
  Pipe,
  require_cjs,
  require_operators,
  setClassMetadata,
  ɵɵdefineInjectable,
  ɵɵdefineInjector,
  ɵɵdefineNgModule,
  ɵɵdefinePipe,
  ɵɵinject
} from "./chunk-NCYSEW5N.js";
import {
  __spreadValues,
  __toESM
} from "./chunk-NQ4HTGF6.js";

// node_modules/tslib/tslib.es6.mjs
function __decorate(decorators, target, key, desc) {
  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
  if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
  else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
  return c > 3 && r && Object.defineProperty(target, key, r), r;
}

// node_modules/tds-ui/fesm2022/tds-ui-cdk-pipes-mapper.mjs
var TDSMapperPipe = class _TDSMapperPipe {
  /**
   * Maps object to an arbitrary result through a mapper function.
   *  **Recommendation:** It is advisable to use an arrow function for this purpose.
   * @param value an item to transform
   * @param mapper a mapping function
   * @param args arbitrary number of additional arguments     *
   */
  transform(value, mapper, ...args) {
    return mapper(value, ...args);
  }
  static {
    this.ɵfac = function TDSMapperPipe_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TDSMapperPipe)();
    };
  }
  static {
    this.ɵpipe = ɵɵdefinePipe({
      name: "tdsMapper",
      type: _TDSMapperPipe,
      pure: true,
      standalone: true
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TDSMapperPipe, [{
    type: Pipe,
    args: [{
      name: `tdsMapper`,
      standalone: true
    }]
  }], null, null);
})();
var TDSMapperPipeModule = class _TDSMapperPipeModule {
  static {
    this.ɵfac = function TDSMapperPipeModule_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TDSMapperPipeModule)();
    };
  }
  static {
    this.ɵmod = ɵɵdefineNgModule({
      type: _TDSMapperPipeModule,
      imports: [TDSMapperPipe],
      exports: [TDSMapperPipe]
    });
  }
  static {
    this.ɵinj = ɵɵdefineInjector({});
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TDSMapperPipeModule, [{
    type: NgModule,
    args: [{
      imports: [TDSMapperPipe],
      exports: [TDSMapperPipe]
    }]
  }], null, null);
})();

// node_modules/tds-ui/fesm2022/tds-ui-core-config.mjs
var import_rxjs = __toESM(require_cjs(), 1);
var import_operators = __toESM(require_operators(), 1);
var isDefined = function(value) {
  return value !== void 0;
};
var TDSConfigService = class _TDSConfigService {
  constructor(defaultConfig) {
    this.configUpdated$ = new import_rxjs.Subject();
    this.config = defaultConfig || {};
  }
  getConfig() {
    return this.config;
  }
  getConfigForComponent(componentName) {
    return this.config[componentName];
  }
  getConfigChangeEventForComponent(componentName) {
    return this.configUpdated$.pipe((0, import_operators.filter)((n) => n === componentName), (0, import_operators.mapTo)(void 0));
  }
  set(componentName, value) {
    this.config[componentName] = __spreadValues(__spreadValues({}, this.config[componentName]), value);
    this.configUpdated$.next(componentName);
  }
  static {
    this.ɵfac = function TDSConfigService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TDSConfigService)(ɵɵinject(TDS_CONFIG, 8));
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _TDSConfigService,
      factory: _TDSConfigService.ɵfac,
      providedIn: "root"
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TDSConfigService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{
    type: void 0,
    decorators: [{
      type: Optional
    }, {
      type: Inject,
      args: [TDS_CONFIG]
    }]
  }], null);
})();
function WithConfig() {
  return function ConfigDecorator(target, propName, originalDescriptor) {
    const privatePropName = `$$__tdsuiConfigDecorator__${propName}`;
    Object.defineProperty(target, privatePropName, {
      configurable: true,
      writable: true,
      enumerable: false
    });
    return {
      get() {
        const originalValue = originalDescriptor?.get ? originalDescriptor.get.bind(this)() : this[privatePropName];
        const assignedByUser = (this.propertyAssignCounter?.[propName] || 0) > 1;
        const configValue = this.tdsConfigService.getConfigForComponent(this._tdsModuleName)?.[propName];
        if (assignedByUser && isDefined(originalValue)) {
          return originalValue;
        } else {
          return isDefined(configValue) ? configValue : originalValue;
        }
      },
      set(value) {
        this.propertyAssignCounter = this.propertyAssignCounter || {};
        this.propertyAssignCounter[propName] = (this.propertyAssignCounter[propName] || 0) + 1;
        if (originalDescriptor?.set) {
          originalDescriptor.set.bind(this)(value);
        } else {
          this[privatePropName] = value;
        }
      },
      configurable: true,
      enumerable: true
    };
  };
}
var TDS_CONFIG = new InjectionToken("tds-config");

export {
  __decorate,
  TDSMapperPipe,
  TDSMapperPipeModule,
  TDSConfigService,
  WithConfig
};
//# sourceMappingURL=chunk-A2D67SU4.js.map
