export interface QAAssessment {
  id: string;
  callId: string;
  assessorId: string;
  assessorName: string;
  score: number;
  maxScore: number;
  percentage: number;
  status: QAAssessmentStatus;
  criteria: QACriterion[];
  overallFeedback?: string;
  strengths?: string[];
  improvements?: string[];
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
}

export enum QAAssessmentStatus {
  DRAFT = 'draft',
  SUBMITTED = 'submitted',
  APPROVED = 'approved',
  REJECTED = 'rejected'
}

export interface QACriterion {
  id: string;
  name: string;
  description: string;
  weight: number;
  maxScore: number;
  score?: number;
  feedback?: string;
  isRequired: boolean;
  category: QACategory;
  subCriteria?: QASubCriterion[];
}

export interface QASubCriterion {
  id: string;
  name: string;
  description: string;
  maxScore: number;
  score?: number;
  feedback?: string;
}

export enum QACategory {
  COMMUNICATION = 'communication',
  PRODUCT_KNOWLEDGE = 'product_knowledge',
  PROBLEM_SOLVING = 'problem_solving',
  COMPLIANCE = 'compliance',
  CUSTOMER_SATISFACTION = 'customer_satisfaction'
}

export interface QATemplate {
  id: string;
  name: string;
  description: string;
  criteria: QACriterion[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface QASummary {
  totalAssessments: number;
  averageScore: number;
  passRate: number;
  topPerformers: AgentPerformance[];
  categoryScores: CategoryScore[];
  trendData: QATrendData[];
}

export interface AgentPerformance {
  agentId: string;
  agentName: string;
  averageScore: number;
  totalAssessments: number;
  passRate: number;
  rank: number;
}

export interface CategoryScore {
  category: QACategory;
  averageScore: number;
  maxScore: number;
  percentage: number;
}

export interface QATrendData {
  date: Date;
  averageScore: number;
  totalAssessments: number;
}
